/**
 * 🔄 SYSTÈME DE SAUVEGARDE ET RESTAURATION POUR L'APPLICATION ELECTRON FINALE
 * Sauvegarde automatique de l'état complet de Louna
 * Créé par <PERSON>, Guadeloupe
 */

const fs = require('fs').promises;
const path = require('path');
const { app } = require('electron');

class ElectronAppBackupSystem {
    constructor() {
        this.isActive = false;
        this.backupInterval = null;
        this.backupFrequency = 300000; // 5 minutes par défaut
        this.maxBackups = 10; // Garder 10 sauvegardes max
        
        this.backupPath = path.join(app.getPath('userData'), 'backups');
        this.configBackupPath = path.join(this.backupPath, 'configs');
        this.memoryBackupPath = path.join(this.backupPath, 'memory');
        this.systemBackupPath = path.join(this.backupPath, 'system');
        
        console.log('🔄 Système de sauvegarde Electron initialisé');
        this.initializeBackupDirectories();
    }

    /**
     * 📁 INITIALISER LES DOSSIERS DE SAUVEGARDE
     */
    async initializeBackupDirectories() {
        try {
            await fs.mkdir(this.backupPath, { recursive: true });
            await fs.mkdir(this.configBackupPath, { recursive: true });
            await fs.mkdir(this.memoryBackupPath, { recursive: true });
            await fs.mkdir(this.systemBackupPath, { recursive: true });
            
            console.log('📁 Dossiers de sauvegarde créés');
        } catch (error) {
            console.error('❌ Erreur création dossiers sauvegarde:', error);
        }
    }

    /**
     * 🚀 DÉMARRER LE SYSTÈME DE SAUVEGARDE AUTOMATIQUE
     */
    async startAutoBackup() {
        if (this.isActive) {
            console.log('⚠️ Système de sauvegarde déjà actif');
            return;
        }

        console.log('🚀 Démarrage du système de sauvegarde automatique...');
        this.isActive = true;

        // Sauvegarde immédiate
        await this.createFullBackup();

        // Programmer les sauvegardes automatiques
        this.backupInterval = setInterval(async () => {
            try {
                await this.createFullBackup();
            } catch (error) {
                console.error('❌ Erreur sauvegarde automatique:', error);
            }
        }, this.backupFrequency);

        console.log(`✅ Sauvegarde automatique activée (toutes les ${this.backupFrequency / 1000}s)`);
    }

    /**
     * 🛑 ARRÊTER LE SYSTÈME DE SAUVEGARDE
     */
    stopAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
        this.isActive = false;
        console.log('🛑 Système de sauvegarde automatique arrêté');
    }

    /**
     * 💾 CRÉER UNE SAUVEGARDE COMPLÈTE
     */
    async createFullBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupId = `backup-${timestamp}`;
        
        console.log(`💾 Création de la sauvegarde complète: ${backupId}`);

        try {
            const backupData = {
                id: backupId,
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                components: {}
            };

            // 1. Sauvegarder la mémoire thermique
            if (global.thermalMemory) {
                console.log('🧠 Sauvegarde de la mémoire thermique...');
                backupData.components.thermalMemory = {
                    config: global.thermalMemory.config,
                    stats: global.thermalMemory.getDetailedStats(),
                    entries: global.thermalMemory.getAllEntries(),
                    temperatures: global.thermalMemory.getGlobalTemperature ? 
                        global.thermalMemory.getGlobalTemperature() : null
                };
            }

            // 2. Sauvegarder les accélérateurs KYBER
            if (global.kyberAccelerators) {
                console.log('⚡ Sauvegarde des accélérateurs KYBER...');
                backupData.components.kyberAccelerators = {
                    config: global.kyberAccelerators.config,
                    stats: global.kyberAccelerators.getAcceleratorStats(),
                    state: global.kyberAccelerators.getState ? 
                        global.kyberAccelerators.getState() : null
                };
            }

            // 3. Sauvegarder l'optimiseur Flash
            if (global.flashOptimizer) {
                console.log('🚀 Sauvegarde de l\'optimiseur Flash...');
                backupData.components.flashOptimizer = {
                    metrics: global.flashOptimizer.getPerformanceMetrics(),
                    isActive: global.flashOptimizer.isActive,
                    flashMode: global.flashOptimizer.flashMode
                };
            }

            // 4. Sauvegarder le système de réflexion ultra-rapide
            if (global.ultraFastReflection) {
                console.log('🧠 Sauvegarde du système de réflexion...');
                backupData.components.ultraFastReflection = {
                    metrics: global.ultraFastReflection.getMetrics(),
                    isActive: global.ultraFastReflection.isActive,
                    config: global.ultraFastReflection.config
                };
            }

            // 5. Sauvegarder l'agent amélioré
            if (global.enhancedAgent) {
                console.log('🤖 Sauvegarde de l\'agent amélioré...');
                backupData.components.enhancedAgent = {
                    state: global.enhancedAgent.state,
                    stats: global.enhancedAgent.getAgentStats ? 
                        global.enhancedAgent.getAgentStats() : null,
                    config: global.enhancedAgent.config
                };
            }

            // 6. Sauvegarder les configurations système
            console.log('⚙️ Sauvegarde des configurations système...');
            backupData.components.systemConfig = {
                nodeEnv: process.env.NODE_ENV,
                platform: process.platform,
                arch: process.arch,
                electronVersion: process.versions.electron,
                nodeVersion: process.versions.node,
                appVersion: app.getVersion(),
                userData: app.getPath('userData')
            };

            // 7. Sauvegarder l'état de l'hibernation
            if (global.hibernationSystem) {
                console.log('💤 Sauvegarde de l\'état d\'hibernation...');
                backupData.components.hibernation = {
                    state: global.hibernationSystem.getCurrentState(),
                    config: global.hibernationSystem.config
                };
            }

            // 8. Sauvegarder les métriques de performance
            if (global.ultraMonitor) {
                console.log('📊 Sauvegarde des métriques de performance...');
                backupData.components.performance = {
                    metrics: global.ultraMonitor.getMetrics ? 
                        global.ultraMonitor.getMetrics() : null,
                    alerts: global.ultraMonitor.getAlerts ? 
                        global.ultraMonitor.getAlerts() : null
                };
            }

            // Écrire la sauvegarde
            const backupFilePath = path.join(this.systemBackupPath, `${backupId}.json`);
            await fs.writeFile(backupFilePath, JSON.stringify(backupData, null, 2));

            // Nettoyer les anciennes sauvegardes
            await this.cleanOldBackups();

            console.log(`✅ Sauvegarde complète créée: ${backupFilePath}`);
            return { success: true, backupId, filePath: backupFilePath };

        } catch (error) {
            console.error('❌ Erreur création sauvegarde:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔄 RESTAURER UNE SAUVEGARDE
     */
    async restoreBackup(backupId) {
        console.log(`🔄 Restauration de la sauvegarde: ${backupId}`);

        try {
            const backupFilePath = path.join(this.systemBackupPath, `${backupId}.json`);
            const backupContent = await fs.readFile(backupFilePath, 'utf8');
            const backupData = JSON.parse(backupContent);

            console.log(`📋 Restauration des composants: ${Object.keys(backupData.components).join(', ')}`);

            // 1. Restaurer la mémoire thermique
            if (backupData.components.thermalMemory && global.thermalMemory) {
                console.log('🧠 Restauration de la mémoire thermique...');
                
                // Restaurer la configuration
                if (backupData.components.thermalMemory.config) {
                    Object.assign(global.thermalMemory.config, backupData.components.thermalMemory.config);
                }

                // Restaurer les entrées de mémoire
                if (backupData.components.thermalMemory.entries) {
                    // Nettoyer la mémoire actuelle
                    global.thermalMemory.clear();
                    
                    // Restaurer les entrées
                    for (const entry of backupData.components.thermalMemory.entries) {
                        global.thermalMemory.addEntry(entry);
                    }
                }
            }

            // 2. Restaurer les accélérateurs KYBER
            if (backupData.components.kyberAccelerators && global.kyberAccelerators) {
                console.log('⚡ Restauration des accélérateurs KYBER...');
                
                if (backupData.components.kyberAccelerators.config) {
                    Object.assign(global.kyberAccelerators.config, backupData.components.kyberAccelerators.config);
                }

                if (global.kyberAccelerators.restoreState && backupData.components.kyberAccelerators.state) {
                    global.kyberAccelerators.restoreState(backupData.components.kyberAccelerators.state);
                }
            }

            // 3. Restaurer l'optimiseur Flash
            if (backupData.components.flashOptimizer && global.flashOptimizer) {
                console.log('🚀 Restauration de l\'optimiseur Flash...');
                
                if (backupData.components.flashOptimizer.flashMode) {
                    await global.flashOptimizer.activateFlashMode();
                } else {
                    await global.flashOptimizer.deactivateFlashMode();
                }
            }

            // 4. Restaurer le système de réflexion
            if (backupData.components.ultraFastReflection && global.ultraFastReflection) {
                console.log('🧠 Restauration du système de réflexion...');
                
                if (backupData.components.ultraFastReflection.config) {
                    Object.assign(global.ultraFastReflection.config, backupData.components.ultraFastReflection.config);
                }

                if (backupData.components.ultraFastReflection.isActive) {
                    await global.ultraFastReflection.activateInstantReflection();
                }
            }

            // 5. Restaurer l'état de l'hibernation
            if (backupData.components.hibernation && global.hibernationSystem) {
                console.log('💤 Restauration de l\'état d\'hibernation...');
                
                if (global.hibernationSystem.restoreState) {
                    global.hibernationSystem.restoreState(backupData.components.hibernation.state);
                }
            }

            console.log(`✅ Sauvegarde restaurée avec succès: ${backupId}`);
            return { success: true, backupId, restoredComponents: Object.keys(backupData.components) };

        } catch (error) {
            console.error('❌ Erreur restauration sauvegarde:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 📋 LISTER LES SAUVEGARDES DISPONIBLES
     */
    async listBackups() {
        try {
            const files = await fs.readdir(this.systemBackupPath);
            const backupFiles = files.filter(file => file.startsWith('backup-') && file.endsWith('.json'));

            const backups = await Promise.all(
                backupFiles.map(async (filename) => {
                    try {
                        const filePath = path.join(this.systemBackupPath, filename);
                        const stats = await fs.stat(filePath);
                        const content = await fs.readFile(filePath, 'utf8');
                        const data = JSON.parse(content);

                        return {
                            id: data.id,
                            filename,
                            timestamp: data.timestamp,
                            size: stats.size,
                            components: Object.keys(data.components || {}),
                            version: data.version
                        };
                    } catch (error) {
                        return {
                            filename,
                            error: 'Fichier corrompu'
                        };
                    }
                })
            );

            return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        } catch (error) {
            console.error('❌ Erreur listage sauvegardes:', error);
            return [];
        }
    }

    /**
     * 🗑️ NETTOYER LES ANCIENNES SAUVEGARDES
     */
    async cleanOldBackups() {
        try {
            const backups = await this.listBackups();
            
            if (backups.length > this.maxBackups) {
                const backupsToDelete = backups.slice(this.maxBackups);
                
                for (const backup of backupsToDelete) {
                    const filePath = path.join(this.systemBackupPath, backup.filename);
                    await fs.unlink(filePath);
                    console.log(`🗑️ Ancienne sauvegarde supprimée: ${backup.filename}`);
                }
            }

        } catch (error) {
            console.error('❌ Erreur nettoyage sauvegardes:', error);
        }
    }

    /**
     * 🔧 CONFIGURER LE SYSTÈME
     */
    configure(options = {}) {
        if (options.frequency) {
            this.backupFrequency = options.frequency;
        }
        
        if (options.maxBackups) {
            this.maxBackups = options.maxBackups;
        }

        console.log(`🔧 Configuration mise à jour: fréquence=${this.backupFrequency}ms, max=${this.maxBackups}`);
    }

    /**
     * 📊 OBTENIR LES STATISTIQUES
     */
    async getStats() {
        const backups = await this.listBackups();
        
        return {
            isActive: this.isActive,
            backupFrequency: this.backupFrequency,
            maxBackups: this.maxBackups,
            totalBackups: backups.length,
            latestBackup: backups[0] || null,
            backupPath: this.backupPath,
            totalSize: backups.reduce((sum, backup) => sum + (backup.size || 0), 0)
        };
    }
}

module.exports = ElectronAppBackupSystem;
