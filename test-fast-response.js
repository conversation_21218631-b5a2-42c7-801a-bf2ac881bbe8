#!/usr/bin/env node

/**
 * Test de réponse rapide de Louna
 */

const axios = require('axios');

async function testFastResponse() {
    console.log('🚀 Test de réponse rapide de Louna...');
    
    const startTime = Date.now();
    
    try {
        const response = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour Louna, peux-tu me répondre rapidement ?',
            history: []
        }, {
            timeout: 10000
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(`✅ Réponse reçue en ${responseTime}ms`);
        console.log(`📝 Réponse: ${response.data.response}`);
        
        if (responseTime < 3000) {
            console.log('🎉 Performance EXCELLENTE !');
        } else if (responseTime < 5000) {
            console.log('👍 Performance BONNE');
        } else {
            console.log('⚠️ Performance LENTE - Optimisations nécessaires');
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

testFastResponse();
