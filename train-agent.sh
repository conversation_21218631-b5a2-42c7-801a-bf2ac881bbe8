#!/bin/bash

# Script pour former l'agent principal avec l'agent de formation

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Fonction pour afficher un avertissement
print_warning() {
  echo -e "${YELLOW}[Attention]${NC} $1"
}

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Formation de l'agent principal avec l'agent de formation${NC}"
echo ""

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  print_error "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
  exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  print_error "npm n'est pas installé. Veuillez installer npm pour exécuter cette application."
  exit 1
fi

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
  print_error "Ollama n'est pas installé. Veuillez l'installer depuis https://ollama.com/download"
  exit 1
fi

# Vérifier si les dossiers de données existent
if [ ! -d "data" ]; then
  print_message "Création du dossier de données..."
  mkdir -p data/memory
  mkdir -p data/accelerators
  mkdir -p data/training
fi

# Vérifier si Ollama est en cours d'exécution
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
  print_message "Démarrage d'Ollama..."
  ollama serve &
  OLLAMA_PID=$!

  # Attendre que Ollama démarre
  print_message "Attente du démarrage d'Ollama..."
  for i in {1..10}; do
    if curl -s http://localhost:11434/api/version &> /dev/null; then
      print_success "Ollama est démarré!"
      break
    fi

    if [ $i -eq 10 ]; then
      print_error "Impossible de démarrer Ollama. Veuillez vérifier l'installation."
      exit 1
    fi

    echo -n "."
    sleep 1
  done
  echo ""
else
  print_success "Ollama est déjà en cours d'exécution."
fi

# Vérifier si les modèles nécessaires sont disponibles
print_message "Vérification des modèles nécessaires..."

# Vérifier si le modèle Claude est disponible
if ! curl -s http://localhost:11434/api/tags | grep -q "incept5/llama3.1-claude"; then
  print_error "Le modèle Claude n'est pas disponible. Veuillez l'installer avec 'ollama pull incept5/llama3.1-claude:latest'"
  exit 1
else
  print_success "Le modèle Claude est disponible."
fi

# Vérifier si le modèle Llama 3 est disponible
if ! curl -s http://localhost:11434/api/tags | grep -q "llama3:8b"; then
  print_warning "Le modèle Llama 3 n'est pas disponible. La formation utilisera l'agent Claude comme agent de formation."
  LLAMA3_AVAILABLE=false
else
  print_success "Le modèle Llama 3 est disponible."
  LLAMA3_AVAILABLE=true
fi

# Vérifier si les agents sont configurés
if [ ! -f "data/config/agents.json" ]; then
  print_error "Le fichier de configuration des agents n'existe pas. Veuillez exécuter './launch-louna-with-ollama.sh' pour configurer les agents."
  exit 1
fi

# Vérifier si l'agent Claude est configuré
if ! grep -q "agent_claude" data/config/agents.json; then
  print_error "L'agent Claude n'est pas configuré. Veuillez exécuter './launch-louna-with-ollama.sh' pour configurer les agents."
  exit 1
fi

# Vérifier si l'agent de formation est configuré
if ! grep -q "agent_training" data/config/agents.json; then
  if [ "$LLAMA3_AVAILABLE" = true ]; then
    print_error "L'agent de formation n'est pas configuré. Veuillez exécuter './launch-louna-with-ollama.sh' pour configurer les agents."
    exit 1
  else
    print_warning "L'agent de formation n'est pas configuré. La formation utilisera l'agent Claude comme agent de formation."
    TRAINING_AGENT_ID="agent_claude"
  fi
else
  TRAINING_AGENT_ID="agent_training"
fi

# Créer un ensemble de données de formation si nécessaire
if [ ! -d "data/training/datasets" ]; then
  print_message "Création du dossier des ensembles de données..."
  mkdir -p data/training/datasets
fi

# Créer un ensemble de données de formation de base
DATASET_ID="basic_training_$(date +%Y%m%d)"
DATASET_PATH="data/training/datasets/${DATASET_ID}.json"

if [ ! -f "$DATASET_PATH" ]; then
  print_message "Création d'un ensemble de données de formation de base..."
  cat > "$DATASET_PATH" <<EOF
{
  "id": "${DATASET_ID}",
  "name": "Formation de base",
  "description": "Ensemble de données de formation de base pour l'agent principal",
  "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
  "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
  "data": [
    {
      "input": "Bonjour, comment ça va?",
      "expectedOutput": "Bonjour! Je vais bien, merci de demander. Comment puis-je vous aider aujourd'hui?"
    },
    {
      "input": "Qu'est-ce que la mémoire thermique?",
      "expectedOutput": "La mémoire thermique est un système de gestion de la mémoire inspiré par la thermodynamique. Elle organise les informations en zones de différentes 'températures' selon leur importance et leur fréquence d'utilisation. Les informations les plus importantes et fréquemment utilisées sont stockées dans les zones 'chaudes', tandis que les informations moins importantes sont progressivement déplacées vers des zones plus 'froides'. Ce système permet une gestion efficace de la mémoire et une récupération rapide des informations pertinentes."
    },
    {
      "input": "Comment fonctionnent les accélérateurs Kyber?",
      "expectedOutput": "Les accélérateurs Kyber sont des composants logiciels qui optimisent le traitement et le flux des informations dans la mémoire thermique. Ils utilisent des algorithmes avancés pour compresser, décompresser et accélérer le mouvement des données entre les différentes zones de mémoire. Ces accélérateurs permettent d'améliorer les performances globales du système en réduisant la latence et en augmentant le débit des opérations de mémoire."
    },
    {
      "input": "Peux-tu m'expliquer ce qu'est l'IA?",
      "expectedOutput": "L'Intelligence Artificielle (IA) est un domaine de l'informatique qui vise à créer des systèmes capables d'effectuer des tâches qui nécessiteraient normalement l'intelligence humaine. Cela inclut l'apprentissage, le raisonnement, la résolution de problèmes, la perception, la compréhension du langage et la prise de décision. L'IA peut être divisée en deux catégories principales: l'IA faible (ou étroite), qui est conçue pour effectuer une tâche spécifique, et l'IA forte (ou générale), qui pourrait théoriquement effectuer n'importe quelle tâche intellectuelle qu'un humain peut faire."
    },
    {
      "input": "Quelles sont tes capacités?",
      "expectedOutput": "Je suis un assistant IA avancé avec plusieurs capacités. Je peux comprendre et générer du texte en langage naturel, répondre à vos questions, fournir des informations sur divers sujets, et vous aider dans différentes tâches. Je dispose d'une mémoire thermique qui me permet de stocker et d'organiser les informations de manière efficace. Je suis également équipé d'accélérateurs Kyber qui optimisent mes performances. Je peux traiter des entrées audio et vidéo, et interagir avec vous de manière conversationnelle. Mon objectif est de vous assister au mieux selon vos besoins."
    }
  ]
}
EOF
  print_success "Ensemble de données de formation créé: $DATASET_PATH"
else
  print_success "L'ensemble de données de formation existe déjà: $DATASET_PATH"
fi

# Lancer la formation
print_message "Lancement de la formation de l'agent principal..."
print_message "Agent principal: Claude (4GB)"

if [ "$TRAINING_AGENT_ID" = "agent_training" ]; then
  print_message "Agent de formation: Llama 3 (8B)"
else
  print_message "Agent de formation: Claude (4GB) - Mode de secours"
fi

print_message "Ensemble de données: Formation de base"

# Créer un script Node.js temporaire pour lancer la formation
TMP_SCRIPT=$(mktemp)
cat > "$TMP_SCRIPT" <<EOF
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const AGENT_ID = 'agent_claude';
const TRAINING_AGENT_ID = '${TRAINING_AGENT_ID}';
const DATASET_ID = '${DATASET_ID}';
const SERVER_PORT = 3004; // Port utilisé par l'application

// Fonction principale
async function main() {
  try {
    console.log('Démarrage de la formation...');

    // Vérifier si le serveur est en cours d'exécution
    let serverRunning = false;
    try {
      await axios.get(\`http://localhost:\${SERVER_PORT}/\`);
      serverRunning = true;
    } catch (error) {
      console.log('Le serveur n\'est pas en cours d\'exécution. Veuillez démarrer l\'application avec \'./launch-louna-with-ollama.sh\'');
      process.exit(1);
    }

    // Lancer la formation
    console.log(\`Formation de l'agent \${AGENT_ID} avec l'agent de formation \${TRAINING_AGENT_ID} et l'ensemble de données \${DATASET_ID}...\`);

    const response = await axios.post(\`http://localhost:\${SERVER_PORT}/api/training/train\`, {
      agentId: AGENT_ID,
      trainingAgentId: TRAINING_AGENT_ID,
      datasetId: DATASET_ID,
      options: {
        epochs: 1,
        batchSize: 5,
        learningRate: 0.001,
        useMemory: true,
        saveToMemory: true
      }
    });

    console.log('Réponse du serveur:', response.data);

    if (response.data.success) {
      console.log('Formation lancée avec succès!');

      // Vérifier l'état de la formation
      let trainingComplete = false;
      let attempts = 0;

      while (!trainingComplete && attempts < 30) {
        try {
          const stateResponse = await axios.get(\`http://localhost:\${SERVER_PORT}/api/training/state\`);

          if (!stateResponse.data.isTraining) {
            trainingComplete = true;
            console.log('Formation terminée!');
            console.log('Résultats:', stateResponse.data.results);
          } else {
            console.log(\`Formation en cours... Progression: \${stateResponse.data.progress}%\`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } catch (error) {
          console.error('Erreur lors de la vérification de l\'état de la formation:', error.message);
        }

        attempts++;
      }

      if (!trainingComplete) {
        console.log('La formation prend plus de temps que prévu. Veuillez vérifier l\'état dans l\'interface de l\'application.');
      }
    } else {
      console.error('Erreur lors du lancement de la formation:', response.data.error);
    }
  } catch (error) {
    console.error('Erreur lors de la formation:', error.message);
  }
}

// Exécuter la fonction principale
main();
EOF

# Exécuter le script Node.js
print_message "Exécution du script de formation..."
node "$TMP_SCRIPT"

# Supprimer le script temporaire
rm "$TMP_SCRIPT"

print_message "Formation terminée."

# Si nous avons démarré Ollama, l'arrêter
if [ -n "$OLLAMA_PID" ]; then
  print_message "Arrêt d'Ollama..."
  kill $OLLAMA_PID
fi
