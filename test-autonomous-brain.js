/**
 * Script de test pour le gestionnaire autonome du cerveau
 * Vérifie que le cerveau fonctionne de manière autonome comme un vrai cerveau
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3005';

async function testAutonomousBrain() {
    console.log('🧠 Test du gestionnaire autonome du cerveau...\n');

    try {
        // 1. Vérifier l'état du gestionnaire autonome
        console.log('1. Vérification de l\'état du gestionnaire autonome...');
        const statusResponse = await axios.get(`${BASE_URL}/api/autonomous-brain/status`);
        
        if (statusResponse.data.success) {
            const stats = statusResponse.data.stats;
            console.log('✅ Gestionnaire autonome actif');
            console.log(`   - En cours d'exécution: ${stats.isRunning ? 'Oui' : 'Non'}`);
            console.log(`   - Cycles de consolidation: ${stats.consolidationCycles}`);
            console.log(`   - Opérations d'élagage: ${stats.pruningOperations}`);
            console.log(`   - Mises à jour plasticité: ${stats.plasticityUpdates}`);
            console.log(`   - Cycles de réorganisation: ${stats.reorganizationCycles}`);
            console.log(`   - Décisions autonomes: ${stats.autonomousDecisions}`);
            console.log(`   - Temps de fonctionnement: ${Math.round(stats.uptime / 1000)}s`);
        } else {
            console.log('❌ Erreur lors de la vérification du statut:', statusResponse.data.error);
            return;
        }

        // 2. Vérifier l'état de la mémoire thermique (gestion autonome)
        console.log('\n2. Vérification de la gestion autonome de la mémoire...');
        const memoryResponse = await axios.get(`${BASE_URL}/api/thermal/memory/stats`);
        
        if (memoryResponse.data.success) {
            const stats = memoryResponse.data.stats;
            console.log('✅ Mémoire thermique en gestion autonome');
            console.log(`   - Entrées totales: ${stats.totalEntries || 0}`);
            console.log(`   - Cycles effectués: ${stats.cyclesPerformed || 0}`);
            console.log(`   - Température moyenne: ${(stats.averageTemperature || 0).toFixed(3)}`);
            console.log(`   - Dernière activité: ${new Date(stats.lastCycleTime || Date.now()).toLocaleString()}`);
        } else {
            console.log('❌ Erreur lors de la vérification de la mémoire:', memoryResponse.data.error);
        }

        // 3. Ajouter des données de test pour observer la gestion autonome
        console.log('\n3. Ajout de données de test pour observer la gestion autonome...');
        
        const testData = [
            {
                key: `autonomous_test_${Date.now()}_1`,
                data: {
                    type: 'test_autonome',
                    message: 'Données importantes pour test de consolidation',
                    category: 'apprentissage'
                },
                importance: 0.9,
                category: 'test_important'
            },
            {
                key: `autonomous_test_${Date.now()}_2`,
                data: {
                    type: 'test_autonome',
                    message: 'Données temporaires pour test d\'élagage',
                    category: 'temporaire'
                },
                importance: 0.2,
                category: 'test_temporaire'
            },
            {
                key: `autonomous_test_${Date.now()}_3`,
                data: {
                    type: 'test_autonome',
                    message: 'Données moyennes pour test de migration',
                    category: 'migration'
                },
                importance: 0.6,
                category: 'test_migration'
            }
        ];

        const addedEntries = [];
        for (const data of testData) {
            try {
                const addResponse = await axios.post(`${BASE_URL}/api/thermal/memory/add`, data);
                if (addResponse.data.success) {
                    addedEntries.push(addResponse.data.id);
                    console.log(`   ✅ Ajouté: ${data.key} (importance: ${data.importance})`);
                }
            } catch (error) {
                console.log(`   ❌ Erreur ajout ${data.key}:`, error.message);
            }
        }

        // 4. Attendre et observer la gestion autonome
        console.log('\n4. Observation de la gestion autonome (60 secondes)...');
        console.log('   Le cerveau va gérer ces données de manière autonome...');
        
        // Prendre un snapshot initial
        const initialStats = statusResponse.data.stats;
        
        // Attendre 60 secondes pour observer l'activité autonome
        await new Promise(resolve => setTimeout(resolve, 60000));
        
        // Vérifier les changements
        const finalStatusResponse = await axios.get(`${BASE_URL}/api/autonomous-brain/status`);
        if (finalStatusResponse.data.success) {
            const finalStats = finalStatusResponse.data.stats;
            
            console.log('\n   📊 Activité autonome observée:');
            console.log(`   - Consolidations: ${finalStats.consolidationCycles - initialStats.consolidationCycles}`);
            console.log(`   - Élaguages: ${finalStats.pruningOperations - initialStats.pruningOperations}`);
            console.log(`   - Plasticité: ${finalStats.plasticityUpdates - initialStats.plasticityUpdates}`);
            console.log(`   - Réorganisations: ${finalStats.reorganizationCycles - initialStats.reorganizationCycles}`);
            console.log(`   - Décisions autonomes: ${finalStats.autonomousDecisions - initialStats.autonomousDecisions}`);
        }

        // 5. Test de consolidation forcée
        console.log('\n5. Test de consolidation mémoire forcée...');
        try {
            const consolidationResponse = await axios.post(`${BASE_URL}/api/autonomous-brain/force-consolidation`);
            if (consolidationResponse.data.success) {
                console.log('✅ Consolidation mémoire forcée réussie');
            } else {
                console.log('❌ Erreur consolidation forcée:', consolidationResponse.data.error);
            }
        } catch (error) {
            console.log('❌ Erreur lors de la consolidation forcée:', error.message);
        }

        // 6. Test d'élagage synaptique forcé
        console.log('\n6. Test d\'élagage synaptique forcé...');
        try {
            const pruningResponse = await axios.post(`${BASE_URL}/api/autonomous-brain/force-pruning`);
            if (pruningResponse.data.success) {
                console.log('✅ Élagage synaptique forcé réussi');
            } else {
                console.log('❌ Erreur élagage forcé:', pruningResponse.data.error);
            }
        } catch (error) {
            console.log('❌ Erreur lors de l\'élagage forcé:', error.message);
        }

        // 7. Vérifier l'état final de la mémoire
        console.log('\n7. Vérification de l\'état final de la mémoire...');
        const finalMemoryResponse = await axios.get(`${BASE_URL}/api/thermal/memory/stats`);
        
        if (finalMemoryResponse.data.success) {
            const finalMemoryStats = finalMemoryResponse.data.stats;
            console.log('✅ État final de la mémoire thermique');
            console.log(`   - Entrées totales: ${finalMemoryStats.totalEntries || 0}`);
            console.log(`   - Cycles effectués: ${finalMemoryStats.cyclesPerformed || 0}`);
            console.log(`   - Température moyenne: ${(finalMemoryStats.averageTemperature || 0).toFixed(3)}`);
        }

        // 8. Vérifier l'état du système de protection contre les coupures
        console.log('\n8. Vérification du système de protection contre les coupures...');
        try {
            const backupResponse = await axios.get(`${BASE_URL}/api/emergency-backup/status`);
            if (backupResponse.data.success) {
                const backupStats = backupResponse.data.stats;
                console.log('✅ Système de protection actif');
                console.log(`   - Sauvegardes: ${backupStats.backupCount} (mode urgence uniquement)`);
                console.log(`   - Dernière sauvegarde: ${new Date(backupStats.lastBackupTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log('⚠️ Système de protection non accessible');
        }

        console.log('\n🎉 Test du gestionnaire autonome terminé !');
        console.log('\n📋 Résumé du fonctionnement autonome:');
        console.log('   🧠 Le cerveau gère sa propre mémoire de manière continue');
        console.log('   🔄 Consolidation automatique des mémoires importantes');
        console.log('   🧹 Élagage automatique des connexions faibles');
        console.log('   ⚡ Plasticité neuronale adaptative');
        console.log('   🌙 Réorganisation mémoire naturelle');
        console.log('   🛡️ Protection contre les coupures uniquement');
        console.log('   ✨ Fonctionnement comme un vrai cerveau humain');

    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Assurez-vous que le serveur Louna est démarré sur le port 3005');
        }
    }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
    testAutonomousBrain();
}

module.exports = { testAutonomousBrain };
