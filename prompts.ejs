<!-- Interface de prompts -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-lightning card-icon"></i>
    <h2 class="card-title">Gestionnaire de Prompts</h2>
  </div>
  
  <div style="padding: 20px;">
    <div class="grid-container">
      <div class="grid-item" style="grid-column: span 2;">
        <div style="margin-bottom: 20px;">
          <h3 style="margin-bottom: 10px;">Créer un nouveau prompt</h3>
          
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px;">Nom du prompt</label>
            <input type="text" placeholder="Entrez un nom pour ce prompt" style="width: 100%; padding: 10px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
          </div>
          
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px;">Catégorie</label>
            <select style="width: 100%; padding: 10px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
              <option>Général</option>
              <option>Créativité</option>
              <option>Analyse</option>
              <option>Programmation</option>
              <option>Personnalisé</option>
            </select>
          </div>
          
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px;">Contenu du prompt</label>
            <textarea placeholder="Entrez le contenu du prompt..." style="width: 100%; height: 150px; padding: 10px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color); resize: vertical;"></textarea>
          </div>
          
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px;">Paramètres avancés</label>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
              <div style="flex: 1; min-width: 200px;">
                <label style="display: block; margin-bottom: 5px; font-size: 14px;">Température</label>
                <input type="range" min="0" max="100" value="70" style="width: 100%;">
                <div style="display: flex; justify-content: space-between; font-size: 12px;">
                  <span>Précis</span>
                  <span>Créatif</span>
                </div>
              </div>
              
              <div style="flex: 1; min-width: 200px;">
                <label style="display: block; margin-bottom: 5px; font-size: 14px;">Zone thermique cible</label>
                <select style="width: 100%; padding: 8px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color); font-size: 14px;">
                  <option>Zone 1 (Instant)</option>
                  <option>Zone 2 (Court terme)</option>
                  <option>Zone 3 (Moyen)</option>
                  <option>Zone 4 (Moyen terme)</option>
                  <option>Zone 5 (Long terme)</option>
                  <option>Zone 6 (Rêve)</option>
                </select>
              </div>
            </div>
          </div>
          
          <div style="text-align: right;">
            <button class="action-button">
              <i class="bi bi-save"></i> Enregistrer le prompt
            </button>
          </div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="card" style="height: 100%;">
          <div class="card-header">
            <i class="bi bi-collection card-icon"></i>
            <h3 class="card-title">Prompts Enregistrés</h3>
          </div>
          
          <div style="padding: 15px; max-height: 400px; overflow-y: auto;">
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
              <div style="font-weight: bold;">Analyse de données</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Catégorie: Analyse</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
              <div style="font-weight: bold;">Génération de code</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Catégorie: Programmation</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
              <div style="font-weight: bold;">Brainstorming créatif</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Catégorie: Créativité</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
              <div style="font-weight: bold;">Résumé de texte</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Catégorie: Général</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
              <div style="font-weight: bold;">Traduction avancée</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Catégorie: Général</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="card" style="height: 100%;">
          <div class="card-header">
            <i class="bi bi-stars card-icon"></i>
            <h3 class="card-title">Prompts Recommandés</h3>
          </div>
          
          <div style="padding: 15px; max-height: 400px; overflow-y: auto;">
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 107, 107, 0.1); cursor: pointer;">
              <div style="font-weight: bold;">Exploration de la mémoire thermique</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Optimisé pour Louna</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(29, 209, 161, 0.1); cursor: pointer;">
              <div style="font-weight: bold;">Configuration des accélérateurs</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Optimisé pour Louna</div>
            </div>
            
            <div class="prompt-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(84, 160, 255, 0.1); cursor: pointer;">
              <div style="font-weight: bold;">Analyse système avancée</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Optimisé pour Louna</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Statistiques des prompts -->
<div class="card" style="margin-top: 20px;">
  <div class="card-header">
    <i class="bi bi-graph-up card-icon"></i>
    <h2 class="card-title">Statistiques des Prompts</h2>
  </div>
  
  <div style="padding: 20px;">
    <div class="grid-container">
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Prompts créés</div>
          <div class="stat-value">15</div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Prompts utilisés</div>
          <div class="stat-value">42</div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Taux de réussite</div>
          <div class="stat-value">92%</div>
        </div>
      </div>
    </div>
    
    <div style="margin-top: 20px;">
      <h3 style="margin-bottom: 15px;">Utilisation par catégorie</h3>
      
      <div style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Général</span>
          <span>35%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 35%; background-color: #54a0ff;"></div>
        </div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Créativité</span>
          <span>25%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 25%; background-color: #ff9f43;"></div>
        </div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Analyse</span>
          <span>20%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 20%; background-color: #1dd1a1;"></div>
        </div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Programmation</span>
          <span>15%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 15%; background-color: #ff6b6b;"></div>
        </div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Personnalisé</span>
          <span>5%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 5%; background-color: #a55eea;"></div>
        </div>
      </div>
    </div>
  </div>
</div>
