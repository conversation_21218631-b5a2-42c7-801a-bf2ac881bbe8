/**
 * ANALYSE ET RAPPORT DE VITESSE - OLLAMA VS CONNEXION DIRECTE
 * 
 * Ce script analyse les performances actuelles et génère un rapport
 * sur les gains potentiels en supprimant Ollama
 */

const AgentSpeedOptimizer = require('./agent-speed-optimizer');
const axios = require('axios');
const fs = require('fs');

class SpeedAnalysisReport {
    constructor() {
        this.results = {
            currentPerformance: {},
            directConnectionPerformance: {},
            comparison: {},
            recommendations: []
        };
        
        this.testMessages = [
            "Bonjour, comment allez-vous ?",
            "Pouvez-vous m'expliquer la mémoire thermique ?",
            "Quelles sont vos capacités principales ?",
            "Comment optimiser les performances de Louna ?",
            "Aidez-moi à comprendre les accélérateurs Kyber",
            "Quelle est la différence entre l'IA et l'AGI ?",
            "Comment fonctionne votre système de réflexion ?",
            "Pouvez-vous générer du code Python ?",
            "Expliquez-moi l'intelligence artificielle",
            "Merci pour votre aide !"
        ];
    }
    
    /**
     * Lance l'analyse complète
     */
    async runCompleteAnalysis() {
        console.log('🔍 DÉMARRAGE DE L\'ANALYSE DE VITESSE COMPLÈTE');
        console.log('=' .repeat(60));
        
        try {
            // 1. Analyser les performances actuelles (avec Ollama)
            await this.analyzeCurrentPerformance();
            
            // 2. Tester les connexions directes
            await this.testDirectConnections();
            
            // 3. Comparer les résultats
            this.comparePerformances();
            
            // 4. Générer les recommandations
            this.generateRecommendations();
            
            // 5. Créer le rapport final
            await this.generateReport();
            
            console.log('✅ Analyse terminée - Rapport généré');
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'analyse:', error.message);
        }
    }
    
    /**
     * Analyse les performances actuelles avec Ollama
     */
    async analyzeCurrentPerformance() {
        console.log('\n📊 ANALYSE DES PERFORMANCES ACTUELLES (AVEC OLLAMA)');
        console.log('-'.repeat(50));
        
        const startTime = Date.now();
        const results = {
            totalTests: this.testMessages.length,
            successfulTests: 0,
            failedTests: 0,
            latencies: [],
            errors: [],
            averageLatency: 0,
            minLatency: Infinity,
            maxLatency: 0,
            timeouts: 0
        };
        
        for (let i = 0; i < this.testMessages.length; i++) {
            const message = this.testMessages[i];
            console.log(`Test ${i + 1}/${this.testMessages.length}: "${message.substring(0, 30)}..."`);
            
            try {
                const testStart = Date.now();
                
                // Test avec l'API actuelle (Ollama)
                const response = await this.testCurrentAPI(message);
                
                const latency = Date.now() - testStart;
                
                if (response.success) {
                    results.successfulTests++;
                    results.latencies.push(latency);
                    results.minLatency = Math.min(results.minLatency, latency);
                    results.maxLatency = Math.max(results.maxLatency, latency);
                    console.log(`  ✅ Succès en ${latency}ms`);
                } else {
                    results.failedTests++;
                    results.errors.push(response.error);
                    console.log(`  ❌ Échec: ${response.error}`);
                }
                
            } catch (error) {
                results.failedTests++;
                results.errors.push(error.message);
                
                if (error.message.includes('timeout')) {
                    results.timeouts++;
                }
                
                console.log(`  ❌ Erreur: ${error.message}`);
            }
            
            // Pause entre les tests
            await this.sleep(500);
        }
        
        // Calculer la latence moyenne
        if (results.latencies.length > 0) {
            results.averageLatency = results.latencies.reduce((a, b) => a + b, 0) / results.latencies.length;
        }
        
        results.totalTime = Date.now() - startTime;
        results.successRate = (results.successfulTests / results.totalTests) * 100;
        
        this.results.currentPerformance = results;
        
        console.log('\n📈 RÉSULTATS ACTUELS:');
        console.log(`  Succès: ${results.successfulTests}/${results.totalTests} (${results.successRate.toFixed(1)}%)`);
        console.log(`  Latence moyenne: ${results.averageLatency.toFixed(0)}ms`);
        console.log(`  Latence min/max: ${results.minLatency}ms / ${results.maxLatency}ms`);
        console.log(`  Timeouts: ${results.timeouts}`);
        console.log(`  Temps total: ${results.totalTime}ms`);
    }
    
    /**
     * Teste l'API actuelle (Ollama)
     */
    async testCurrentAPI(message) {
        try {
            const response = await axios.post('http://localhost:3000/api/chat/message', {
                message: message,
                userId: 'speed_test'
            }, {
                timeout: 10000 // 10 secondes max
            });
            
            return {
                success: true,
                response: response.data.response,
                latency: response.data.latency || 0
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Teste les connexions directes
     */
    async testDirectConnections() {
        console.log('\n⚡ TEST DES CONNEXIONS DIRECTES (SANS OLLAMA)');
        console.log('-'.repeat(50));
        
        const optimizer = new AgentSpeedOptimizer();
        
        const startTime = Date.now();
        const results = {
            totalTests: this.testMessages.length,
            successfulTests: 0,
            failedTests: 0,
            latencies: [],
            errors: [],
            averageLatency: 0,
            minLatency: Infinity,
            maxLatency: 0,
            cacheHits: 0,
            apiUsage: {}
        };
        
        for (let i = 0; i < this.testMessages.length; i++) {
            const message = this.testMessages[i];
            console.log(`Test ${i + 1}/${this.testMessages.length}: "${message.substring(0, 30)}..."`);
            
            try {
                const response = await optimizer.processMessage(message, {
                    maxTokens: 1000,
                    temperature: 0.7
                });
                
                if (response.success) {
                    results.successfulTests++;
                    results.latencies.push(response.latency);
                    results.minLatency = Math.min(results.minLatency, response.latency);
                    results.maxLatency = Math.max(results.maxLatency, response.latency);
                    
                    if (response.source.includes('cache')) {
                        results.cacheHits++;
                    }
                    
                    if (response.api) {
                        results.apiUsage[response.api] = (results.apiUsage[response.api] || 0) + 1;
                    }
                    
                    console.log(`  ✅ Succès en ${response.latency}ms (${response.source})`);
                } else {
                    results.failedTests++;
                    results.errors.push(response.error);
                    console.log(`  ❌ Échec: ${response.error}`);
                }
                
            } catch (error) {
                results.failedTests++;
                results.errors.push(error.message);
                console.log(`  ❌ Erreur: ${error.message}`);
            }
            
            // Pause plus courte pour les connexions directes
            await this.sleep(200);
        }
        
        // Calculer la latence moyenne
        if (results.latencies.length > 0) {
            results.averageLatency = results.latencies.reduce((a, b) => a + b, 0) / results.latencies.length;
        }
        
        results.totalTime = Date.now() - startTime;
        results.successRate = (results.successfulTests / results.totalTests) * 100;
        results.cacheHitRate = (results.cacheHits / results.successfulTests) * 100;
        
        // Obtenir les stats de l'optimiseur
        results.optimizerStats = optimizer.getPerformanceStats();
        
        this.results.directConnectionPerformance = results;
        
        console.log('\n🚀 RÉSULTATS CONNEXIONS DIRECTES:');
        console.log(`  Succès: ${results.successfulTests}/${results.totalTests} (${results.successRate.toFixed(1)}%)`);
        console.log(`  Latence moyenne: ${results.averageLatency.toFixed(0)}ms`);
        console.log(`  Latence min/max: ${results.minLatency}ms / ${results.maxLatency}ms`);
        console.log(`  Cache hits: ${results.cacheHits} (${results.cacheHitRate.toFixed(1)}%)`);
        console.log(`  Temps total: ${results.totalTime}ms`);
        console.log(`  APIs utilisées:`, results.apiUsage);
    }
    
    /**
     * Compare les performances
     */
    comparePerformances() {
        console.log('\n📊 COMPARAISON DES PERFORMANCES');
        console.log('-'.repeat(50));
        
        const current = this.results.currentPerformance;
        const direct = this.results.directConnectionPerformance;
        
        const comparison = {
            latencyImprovement: current.averageLatency - direct.averageLatency,
            latencyImprovementPercent: ((current.averageLatency - direct.averageLatency) / current.averageLatency) * 100,
            reliabilityImprovement: direct.successRate - current.successRate,
            speedGain: current.totalTime - direct.totalTime,
            speedGainPercent: ((current.totalTime - direct.totalTime) / current.totalTime) * 100
        };
        
        this.results.comparison = comparison;
        
        console.log(`📈 GAINS DE PERFORMANCE:`);
        console.log(`  Amélioration latence: ${comparison.latencyImprovement.toFixed(0)}ms (${comparison.latencyImprovementPercent.toFixed(1)}%)`);
        console.log(`  Amélioration fiabilité: ${comparison.reliabilityImprovement.toFixed(1)}%`);
        console.log(`  Gain de vitesse totale: ${comparison.speedGain.toFixed(0)}ms (${comparison.speedGainPercent.toFixed(1)}%)`);
        
        if (comparison.latencyImprovement > 0) {
            console.log(`✅ Les connexions directes sont ${comparison.latencyImprovementPercent.toFixed(1)}% plus rapides`);
        } else {
            console.log(`⚠️ Ollama est actuellement plus rapide de ${Math.abs(comparison.latencyImprovementPercent).toFixed(1)}%`);
        }
    }
    
    /**
     * Génère les recommandations
     */
    generateRecommendations() {
        console.log('\n💡 RECOMMANDATIONS');
        console.log('-'.repeat(50));
        
        const comparison = this.results.comparison;
        const current = this.results.currentPerformance;
        const direct = this.results.directConnectionPerformance;
        
        const recommendations = [];
        
        // Analyse de la latence
        if (comparison.latencyImprovement > 500) {
            recommendations.push({
                priority: 'HIGH',
                type: 'PERFORMANCE',
                title: 'Remplacer Ollama par des connexions directes',
                description: `Gain de ${comparison.latencyImprovement.toFixed(0)}ms par requête (${comparison.latencyImprovementPercent.toFixed(1)}% plus rapide)`,
                implementation: 'Intégrer le système DirectAgentConnection et AgentSpeedOptimizer'
            });
        }
        
        // Analyse de la fiabilité
        if (comparison.reliabilityImprovement > 10) {
            recommendations.push({
                priority: 'HIGH',
                type: 'RELIABILITY',
                title: 'Améliorer la fiabilité avec le système de fallback',
                description: `Amélioration de ${comparison.reliabilityImprovement.toFixed(1)}% du taux de succès`,
                implementation: 'Activer le système de fallback intelligent multi-API'
            });
        }
        
        // Analyse des timeouts
        if (current.timeouts > 0) {
            recommendations.push({
                priority: 'MEDIUM',
                type: 'STABILITY',
                title: 'Réduire les timeouts',
                description: `${current.timeouts} timeouts détectés avec Ollama`,
                implementation: 'Implémenter des timeouts adaptatifs et le cache prédictif'
            });
        }
        
        // Recommandations de cache
        if (direct.cacheHitRate > 20) {
            recommendations.push({
                priority: 'MEDIUM',
                type: 'OPTIMIZATION',
                title: 'Optimiser le système de cache',
                description: `${direct.cacheHitRate.toFixed(1)}% de cache hits observés`,
                implementation: 'Étendre le cache prédictif et contextuel'
            });
        }
        
        // Recommandations d'APIs
        if (Object.keys(direct.apiUsage).length > 1) {
            recommendations.push({
                priority: 'LOW',
                type: 'INTEGRATION',
                title: 'Intégrer des APIs cloud pour la vitesse maximale',
                description: 'Plusieurs APIs disponibles détectées',
                implementation: 'Configurer OpenAI, Claude ou DeepSeek API pour des performances optimales'
            });
        }
        
        // Recommandation générale
        if (comparison.latencyImprovement > 0 || comparison.reliabilityImprovement > 0) {
            recommendations.push({
                priority: 'HIGH',
                type: 'GENERAL',
                title: 'Migration recommandée vers les connexions directes',
                description: 'Gains significatifs observés en vitesse et fiabilité',
                implementation: 'Déployer le système d\'optimisation de vitesse en production'
            });
        }
        
        this.results.recommendations = recommendations;
        
        recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. [${rec.priority}] ${rec.title}`);
            console.log(`   ${rec.description}`);
            console.log(`   → ${rec.implementation}\n`);
        });
    }
    
    /**
     * Génère le rapport final
     */
    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                currentSystem: 'Ollama-based',
                proposedSystem: 'Direct API connections',
                testDuration: this.results.currentPerformance.totalTime + this.results.directConnectionPerformance.totalTime,
                totalTests: this.testMessages.length * 2
            },
            performance: {
                current: this.results.currentPerformance,
                direct: this.results.directConnectionPerformance,
                comparison: this.results.comparison
            },
            recommendations: this.results.recommendations,
            conclusion: this.generateConclusion()
        };
        
        // Sauvegarder le rapport
        const reportPath = '/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/speed-analysis-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // Générer un rapport markdown
        const markdownReport = this.generateMarkdownReport(report);
        const markdownPath = '/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/speed-analysis-report.md';
        fs.writeFileSync(markdownPath, markdownReport);
        
        console.log(`\n📄 Rapport sauvegardé:`);
        console.log(`  JSON: ${reportPath}`);
        console.log(`  Markdown: ${markdownPath}`);
    }
    
    /**
     * Génère la conclusion
     */
    generateConclusion() {
        const comparison = this.results.comparison;
        
        if (comparison.latencyImprovement > 1000) {
            return {
                recommendation: 'STRONGLY_RECOMMENDED',
                reason: 'Gains de performance majeurs observés',
                action: 'Migrer immédiatement vers les connexions directes'
            };
        } else if (comparison.latencyImprovement > 500) {
            return {
                recommendation: 'RECOMMENDED',
                reason: 'Gains de performance significatifs',
                action: 'Planifier la migration vers les connexions directes'
            };
        } else if (comparison.latencyImprovement > 0) {
            return {
                recommendation: 'CONSIDER',
                reason: 'Gains de performance modérés',
                action: 'Évaluer les bénéfices vs complexité'
            };
        } else {
            return {
                recommendation: 'NOT_RECOMMENDED',
                reason: 'Ollama performe mieux actuellement',
                action: 'Optimiser Ollama ou attendre de meilleures APIs'
            };
        }
    }
    
    /**
     * Génère un rapport markdown
     */
    generateMarkdownReport(report) {
        return `# 📊 Rapport d'Analyse de Vitesse - Louna Agent

**Date:** ${new Date(report.timestamp).toLocaleString()}  
**Durée des tests:** ${report.summary.testDuration}ms  
**Tests effectués:** ${report.summary.totalTests}

## 🎯 Résumé Exécutif

### Système Actuel: ${report.summary.currentSystem}
- **Latence moyenne:** ${report.performance.current.averageLatency.toFixed(0)}ms
- **Taux de succès:** ${report.performance.current.successRate.toFixed(1)}%
- **Timeouts:** ${report.performance.current.timeouts}

### Système Proposé: ${report.summary.proposedSystem}
- **Latence moyenne:** ${report.performance.direct.averageLatency.toFixed(0)}ms
- **Taux de succès:** ${report.performance.direct.successRate.toFixed(1)}%
- **Cache hits:** ${report.performance.direct.cacheHitRate.toFixed(1)}%

## 📈 Gains de Performance

- **Amélioration latence:** ${report.performance.comparison.latencyImprovement.toFixed(0)}ms (${report.performance.comparison.latencyImprovementPercent.toFixed(1)}%)
- **Amélioration fiabilité:** ${report.performance.comparison.reliabilityImprovement.toFixed(1)}%
- **Gain vitesse totale:** ${report.performance.comparison.speedGainPercent.toFixed(1)}%

## 💡 Recommandations

${report.recommendations.map((rec, i) => `### ${i + 1}. [${rec.priority}] ${rec.title}
${rec.description}
**Implémentation:** ${rec.implementation}`).join('\n\n')}

## 🎯 Conclusion

**Recommandation:** ${report.conclusion.recommendation}  
**Raison:** ${report.conclusion.reason}  
**Action:** ${report.conclusion.action}

---
*Rapport généré automatiquement par le système d'analyse de vitesse de Louna*`;
    }
    
    /**
     * Utilitaire pour attendre
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Exécuter l'analyse si le script est lancé directement
if (require.main === module) {
    const analyzer = new SpeedAnalysisReport();
    analyzer.runCompleteAnalysis().catch(console.error);
}

module.exports = SpeedAnalysisReport;
