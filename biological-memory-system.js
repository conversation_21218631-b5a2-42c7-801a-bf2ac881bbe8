/**
 * SYSTÈME DE MÉMOIRE BIOLOGIQUE ARTIFICIELLE
 * Fonctionne exactement comme une mémoire humaine avec :
 * - Transferts automatiques entre zones comme dans le cerveau
 * - Consolidation nocturne (sommeil)
 * - Oubli naturel et sélectif
 * - Émotions liées aux souvenirs
 * - Associations spontanées
 * - Rêves pour traiter les souvenirs
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

class BiologicalMemorySystem extends EventEmitter {
    constructor(config = {}) {
        super();

        // Configuration biologique
        this.config = {
            // Capacités comme dans le cerveau humain
            sensoryBufferCapacity: 7,        // 7±2 éléments (Miller)
            workingMemoryCapacity: 4,        // 4 chunks actifs
            shortTermCapacity: 20,           // ~20 éléments
            longTermCapacity: 1000000,       // Quasi-illimitée

            // Temporalités biologiques
            sensoryDecayTime: 2000,          // 2 secondes
            workingMemoryDecayTime: 18000,   // 18 secondes
            shortTermDecayTime: 300000,      // 5 minutes
            consolidationTime: 3600000,      // 1 heure

            // Facteurs biologiques
            emotionalBoost: 2.5,             // Boost émotionnel
            repetitionBoost: 1.8,            // Boost par répétition
            associationStrength: 0.7,        // Force des associations
            forgettingCurve: 0.85,           // Courbe d'oubli d'Ebbinghaus

            // Cycles biologiques
            sleepCycleInterval: 7200000,     // 2 heures de cycle
            dreamProbability: 0.3,           // 30% de chance de rêver
            consolidationRate: 0.15,         // 15% consolidé par cycle

            ...config
        };

        // Zones de mémoire biologiques
        this.sensoryBuffer = [];           // Registre sensoriel
        this.workingMemory = [];           // Mémoire de travail
        this.shortTermMemory = [];         // Mémoire à court terme
        this.longTermMemory = [];          // Mémoire à long terme
        this.emotionalMemory = [];         // Mémoire émotionnelle
        this.proceduralMemory = [];        // Mémoire procédurale
        this.dreamMemory = [];             // Mémoire des rêves

        // État biologique
        this.isAsleep = false;
        this.sleepDepth = 0;               // 0-1 (sommeil léger à profond)
        this.emotionalState = {
            valence: 0.5,                  // Positif/négatif
            arousal: 0.5,                  // Calme/excité
            dominance: 0.5                 // Contrôle/soumission
        };

        // Métriques biologiques
        this.stats = {
            totalMemories: 0,
            consolidatedToday: 0,
            forgottenToday: 0,
            dreamsGenerated: 0,
            associationsCreated: 0,
            emotionalMemories: 0
        };

        // Associations neuronales
        this.neuralConnections = new Map();

        // Démarrer les processus biologiques
        this.startBiologicalProcesses();

        console.log('🧠 Système de mémoire biologique initialisé');
        console.log('🔬 Fonctionnement identique au cerveau humain activé');
    }

    /**
     * Ajouter une nouvelle mémoire (comme percevoir quelque chose)
     */
    addMemory(data, emotion = null, importance = 0.5) {
        const memory = {
            id: this.generateId(),
            data: data,
            timestamp: Date.now(),
            emotion: emotion,
            importance: importance,
            accessCount: 1,
            lastAccess: Date.now(),
            strength: 1.0,
            associations: [],
            zone: 'sensory'
        };

        // Boost émotionnel (comme dans le cerveau)
        if (emotion && emotion.intensity > 0.7) {
            memory.importance *= this.config.emotionalBoost;
            memory.strength *= this.config.emotionalBoost;
            this.emotionalMemory.push({...memory, zone: 'emotional'});
            this.stats.emotionalMemories++;
        }

        // Ajouter au registre sensoriel
        this.sensoryBuffer.push(memory);
        this.stats.totalMemories++;

        // Créer des associations automatiques
        this.createNeuralAssociations(memory);

        // Déclencher le transfert automatique
        this.processMemoryTransfer();

        this.emit('memoryAdded', memory);
        return memory.id;
    }

    /**
     * Récupérer une mémoire (avec renforcement biologique)
     */
    retrieveMemory(query) {
        const allMemories = this.getAllMemories();
        let bestMatch = null;
        let bestScore = 0;

        for (const memory of allMemories) {
            const score = this.calculateSimilarity(query, memory.data);
            if (score > bestScore) {
                bestScore = score;
                bestMatch = memory;
            }
        }

        if (bestMatch && bestScore > 0.3) {
            // Renforcement biologique par accès
            bestMatch.accessCount++;
            bestMatch.lastAccess = Date.now();
            bestMatch.strength *= this.config.repetitionBoost;

            // Activer les associations
            this.activateAssociations(bestMatch);

            this.emit('memoryRetrieved', bestMatch);
            return bestMatch;
        }

        return null;
    }

    /**
     * Processus de transfert automatique entre zones (comme dans le cerveau)
     */
    processMemoryTransfer() {
        const now = Date.now();

        // Sensory Buffer → Working Memory
        this.sensoryBuffer = this.sensoryBuffer.filter(memory => {
            const age = now - memory.timestamp;

            if (age > this.config.sensoryDecayTime) {
                // Transférer les plus importantes vers la mémoire de travail
                if (memory.importance > 0.6 && this.workingMemory.length < this.config.workingMemoryCapacity) {
                    memory.zone = 'working';
                    this.workingMemory.push(memory);
                    return false; // Retirer du buffer sensoriel
                }
                // Sinon, oublier (comme dans le cerveau)
                this.stats.forgottenToday++;
                return false;
            }
            return true;
        });

        // Working Memory → Short Term Memory
        this.workingMemory = this.workingMemory.filter(memory => {
            const age = now - memory.timestamp;

            if (age > this.config.workingMemoryDecayTime) {
                if (memory.importance > 0.5) {
                    memory.zone = 'shortTerm';
                    this.shortTermMemory.push(memory);
                    return false;
                }
                this.stats.forgottenToday++;
                return false;
            }
            return true;
        });

        // Short Term → Long Term (consolidation)
        this.shortTermMemory = this.shortTermMemory.filter(memory => {
            const age = now - memory.timestamp;

            if (age > this.config.shortTermDecayTime) {
                if (memory.importance > 0.4 || memory.accessCount > 2) {
                    memory.zone = 'longTerm';
                    this.longTermMemory.push(memory);
                    this.stats.consolidatedToday++;
                    return false;
                }
                this.stats.forgottenToday++;
                return false;
            }
            return true;
        });

        // Appliquer la courbe d'oubli d'Ebbinghaus
        this.applyForgettingCurve();
    }

    /**
     * Créer des associations neuronales automatiques
     */
    createNeuralAssociations(newMemory) {
        const recentMemories = this.getRecentMemories(10);

        for (const existingMemory of recentMemories) {
            if (existingMemory.id !== newMemory.id) {
                const similarity = this.calculateSimilarity(newMemory.data, existingMemory.data);

                if (similarity > this.config.associationStrength) {
                    // Créer une connexion bidirectionnelle
                    this.createConnection(newMemory.id, existingMemory.id, similarity);
                    this.stats.associationsCreated++;
                }
            }
        }
    }

    /**
     * Créer une connexion neuronale
     */
    createConnection(memoryId1, memoryId2, strength) {
        if (!this.neuralConnections.has(memoryId1)) {
            this.neuralConnections.set(memoryId1, []);
        }
        if (!this.neuralConnections.has(memoryId2)) {
            this.neuralConnections.set(memoryId2, []);
        }

        this.neuralConnections.get(memoryId1).push({
            targetId: memoryId2,
            strength: strength,
            created: Date.now()
        });

        this.neuralConnections.get(memoryId2).push({
            targetId: memoryId1,
            strength: strength,
            created: Date.now()
        });
    }

    /**
     * Activer les associations (propagation neuronale)
     */
    activateAssociations(memory) {
        const connections = this.neuralConnections.get(memory.id) || [];
        const activatedMemories = [];

        for (const connection of connections) {
            if (connection.strength > 0.5) {
                const associatedMemory = this.findMemoryById(connection.targetId);
                if (associatedMemory) {
                    // Renforcer légèrement la mémoire associée
                    associatedMemory.strength *= 1.1;
                    activatedMemories.push(associatedMemory);
                }
            }
        }

        this.emit('associationsActivated', { memory, activated: activatedMemories });
        return activatedMemories;
    }

    /**
     * Calculer la similarité entre deux contenus
     */
    calculateSimilarity(content1, content2) {
        if (typeof content1 !== 'string' || typeof content2 !== 'string') {
            return 0;
        }

        const words1 = content1.toLowerCase().split(/\s+/);
        const words2 = content2.toLowerCase().split(/\s+/);

        const intersection = words1.filter(word => words2.includes(word));
        const union = [...new Set([...words1, ...words2])];

        return intersection.length / union.length;
    }

    /**
     * Appliquer la courbe d'oubli d'Ebbinghaus
     */
    applyForgettingCurve() {
        const now = Date.now();

        [this.workingMemory, this.shortTermMemory, this.longTermMemory].forEach(memoryZone => {
            memoryZone.forEach(memory => {
                const daysSinceLastAccess = (now - memory.lastAccess) / (1000 * 60 * 60 * 24);
                const forgettingFactor = Math.pow(this.config.forgettingCurve, daysSinceLastAccess);
                memory.strength *= forgettingFactor;

                // Supprimer les mémoires trop faibles
                if (memory.strength < 0.1 && memory.importance < 0.3) {
                    const index = memoryZone.indexOf(memory);
                    if (index > -1) {
                        memoryZone.splice(index, 1);
                        this.stats.forgottenToday++;
                    }
                }
            });
        });
    }

    /**
     * Démarrer les processus biologiques automatiques
     */
    startBiologicalProcesses() {
        // Cycle de transfert de mémoire (comme les ondes cérébrales)
        setInterval(() => {
            this.processMemoryTransfer();
        }, 5000);

        // Cycle de sommeil et consolidation
        setInterval(() => {
            this.performSleepConsolidation();
        }, this.config.sleepCycleInterval);

        // Génération de rêves
        setInterval(() => {
            if (this.isAsleep && Math.random() < this.config.dreamProbability) {
                this.generateDream();
            }
        }, 30000);

        // Mise à jour de l'état émotionnel
        setInterval(() => {
            this.updateEmotionalState();
        }, 10000);
    }

    /**
     * Générer un ID unique
     */
    generateId() {
        return 'mem_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Obtenir toutes les mémoires
     */
    getAllMemories() {
        return [
            ...this.sensoryBuffer,
            ...this.workingMemory,
            ...this.shortTermMemory,
            ...this.longTermMemory,
            ...this.emotionalMemory,
            ...this.proceduralMemory,
            ...this.dreamMemory
        ];
    }

    /**
     * Obtenir les mémoires récentes
     */
    getRecentMemories(limit = 20) {
        return this.getAllMemories()
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);
    }

    /**
     * Trouver une mémoire par ID
     */
    findMemoryById(id) {
        return this.getAllMemories().find(memory => memory.id === id);
    }

    /**
     * Consolidation pendant le sommeil (comme dans le cerveau humain)
     */
    performSleepConsolidation() {
        if (!this.isAsleep) {
            this.isAsleep = true;
            this.sleepDepth = Math.random() * 0.5 + 0.5; // Sommeil profond

            console.log('😴 Entrée en mode sommeil - Consolidation mémoire...');

            // Consolider les mémoires importantes
            const memoriesToConsolidate = this.shortTermMemory
                .filter(memory => memory.importance > 0.6 || memory.accessCount > 3)
                .slice(0, Math.floor(this.shortTermMemory.length * this.config.consolidationRate));

            memoriesToConsolidate.forEach(memory => {
                memory.zone = 'longTerm';
                memory.strength *= 1.5; // Renforcement pendant le sommeil
                this.longTermMemory.push(memory);
                this.stats.consolidatedToday++;

                // Retirer de la mémoire à court terme
                const index = this.shortTermMemory.indexOf(memory);
                if (index > -1) {
                    this.shortTermMemory.splice(index, 1);
                }
            });

            // Réveil après consolidation
            setTimeout(() => {
                this.isAsleep = false;
                this.sleepDepth = 0;
                console.log('🌅 Réveil - Consolidation terminée');
                this.emit('sleepConsolidationComplete', {
                    consolidated: memoriesToConsolidate.length,
                    totalLongTerm: this.longTermMemory.length
                });
            }, 60000); // 1 minute de sommeil
        }
    }

    /**
     * Générer des rêves (traitement créatif des mémoires)
     */
    generateDream() {
        const recentMemories = this.getRecentMemories(5);
        const randomMemories = this.longTermMemory
            .sort(() => Math.random() - 0.5)
            .slice(0, 3);

        const dreamMemories = [...recentMemories, ...randomMemories];

        if (dreamMemories.length > 0) {
            const dream = {
                id: this.generateId(),
                type: 'dream',
                content: this.mixMemoriesCreatively(dreamMemories),
                timestamp: Date.now(),
                sleepDepth: this.sleepDepth,
                zone: 'dream'
            };

            this.dreamMemory.push(dream);
            this.stats.dreamsGenerated++;

            // Les rêves peuvent créer de nouvelles associations
            this.createDreamAssociations(dream, dreamMemories);

            console.log('💭 Rêve généré:', dream.content.substring(0, 50) + '...');
            this.emit('dreamGenerated', dream);
        }
    }

    /**
     * Mélanger créativement les mémoires (comme dans les rêves)
     */
    mixMemoriesCreatively(memories) {
        const elements = memories.map(m => m.data).join(' ').split(' ');
        const shuffled = elements.sort(() => Math.random() - 0.5);
        return shuffled.slice(0, 10).join(' ') + ' [rêve créatif]';
    }

    /**
     * Créer des associations oniriques
     */
    createDreamAssociations(dream, sourceMemories) {
        sourceMemories.forEach(memory => {
            this.createConnection(dream.id, memory.id, 0.3 + Math.random() * 0.4);
        });
    }

    /**
     * Mettre à jour l'état émotionnel
     */
    updateEmotionalState() {
        // Évolution naturelle des émotions
        this.emotionalState.valence += (Math.random() - 0.5) * 0.1;
        this.emotionalState.arousal += (Math.random() - 0.5) * 0.1;
        this.emotionalState.dominance += (Math.random() - 0.5) * 0.1;

        // Maintenir dans les limites
        Object.keys(this.emotionalState).forEach(key => {
            this.emotionalState[key] = Math.max(0, Math.min(1, this.emotionalState[key]));
        });

        // Influencer la formation de nouvelles mémoires
        this.emotionalInfluence = this.emotionalState.valence * this.emotionalState.arousal;
    }

    /**
     * Obtenir le statut biologique complet
     */
    getBiologicalStatus() {
        return {
            memoryZones: {
                sensoryBuffer: {
                    count: this.sensoryBuffer.length,
                    capacity: this.config.sensoryBufferCapacity,
                    usage: this.sensoryBuffer.length / this.config.sensoryBufferCapacity
                },
                workingMemory: {
                    count: this.workingMemory.length,
                    capacity: this.config.workingMemoryCapacity,
                    usage: this.workingMemory.length / this.config.workingMemoryCapacity
                },
                shortTermMemory: {
                    count: this.shortTermMemory.length,
                    capacity: this.config.shortTermCapacity,
                    usage: this.shortTermMemory.length / this.config.shortTermCapacity
                },
                longTermMemory: {
                    count: this.longTermMemory.length,
                    capacity: this.config.longTermCapacity,
                    usage: this.longTermMemory.length / this.config.longTermCapacity
                },
                emotionalMemory: {
                    count: this.emotionalMemory.length
                },
                dreamMemory: {
                    count: this.dreamMemory.length
                }
            },
            biologicalState: {
                isAsleep: this.isAsleep,
                sleepDepth: this.sleepDepth,
                emotionalState: this.emotionalState,
                emotionalInfluence: this.emotionalInfluence || 0.5
            },
            neuralConnections: {
                totalConnections: Array.from(this.neuralConnections.values())
                    .reduce((sum, connections) => sum + connections.length, 0),
                averageStrength: this.calculateAverageConnectionStrength()
            },
            dailyStats: this.stats,
            performance: {
                memoryEfficiency: this.calculateMemoryEfficiency(),
                consolidationRate: this.stats.consolidatedToday / Math.max(1, this.stats.totalMemories),
                forgettingRate: this.stats.forgottenToday / Math.max(1, this.stats.totalMemories),
                associationDensity: this.stats.associationsCreated / Math.max(1, this.stats.totalMemories)
            }
        };
    }

    /**
     * Calculer la force moyenne des connexions
     */
    calculateAverageConnectionStrength() {
        const allConnections = Array.from(this.neuralConnections.values()).flat();
        if (allConnections.length === 0) return 0;

        const totalStrength = allConnections.reduce((sum, conn) => sum + conn.strength, 0);
        return totalStrength / allConnections.length;
    }

    /**
     * Calculer l'efficacité de la mémoire
     */
    calculateMemoryEfficiency() {
        const totalMemories = this.getAllMemories().length;
        const strongMemories = this.getAllMemories().filter(m => m.strength > 0.7).length;

        return totalMemories > 0 ? strongMemories / totalMemories : 0;
    }

    /**
     * Simuler la fatigue mentale
     */
    simulateMentalFatigue() {
        const totalActivity = this.sensoryBuffer.length + this.workingMemory.length;

        if (totalActivity > this.config.workingMemoryCapacity * 1.5) {
            // Réduire l'efficacité quand surchargé
            this.getAllMemories().forEach(memory => {
                memory.strength *= 0.95;
            });

            console.log('🧠 Fatigue mentale détectée - Réduction de l\'efficacité');
            this.emit('mentalFatigue', { totalActivity, efficiency: 0.95 });
        }
    }

    /**
     * Sauvegarder l'état biologique
     */
    saveBiologicalState() {
        const state = {
            memories: this.getAllMemories(),
            connections: Array.from(this.neuralConnections.entries()),
            emotionalState: this.emotionalState,
            stats: this.stats,
            timestamp: Date.now()
        };

        try {
            fs.writeFileSync(
                path.join(__dirname, 'data', 'biological-memory-state.json'),
                JSON.stringify(state, null, 2)
            );
            console.log('💾 État biologique sauvegardé');
        } catch (error) {
            console.error('❌ Erreur sauvegarde biologique:', error);
        }
    }

    /**
     * Charger l'état biologique
     */
    loadBiologicalState() {
        try {
            const statePath = path.join(__dirname, 'data', 'biological-memory-state.json');
            if (fs.existsSync(statePath)) {
                const state = JSON.parse(fs.readFileSync(statePath, 'utf8'));

                // Restaurer les mémoires dans les bonnes zones
                state.memories.forEach(memory => {
                    switch (memory.zone) {
                        case 'sensory':
                            this.sensoryBuffer.push(memory);
                            break;
                        case 'working':
                            this.workingMemory.push(memory);
                            break;
                        case 'shortTerm':
                            this.shortTermMemory.push(memory);
                            break;
                        case 'longTerm':
                            this.longTermMemory.push(memory);
                            break;
                        case 'emotional':
                            this.emotionalMemory.push(memory);
                            break;
                        case 'dream':
                            this.dreamMemory.push(memory);
                            break;
                    }
                });

                // Restaurer les connexions
                this.neuralConnections = new Map(state.connections);
                this.emotionalState = state.emotionalState;
                this.stats = state.stats;

                console.log('🔄 État biologique restauré');
            }
        } catch (error) {
            console.error('❌ Erreur chargement biologique:', error);
        }
    }
}

module.exports = BiologicalMemorySystem;
