/**
 * Module de fusion de mémoire pour l'application Louna
 *
 * Ce module permet de fusionner intelligemment les entrées de mémoire
 * entre les différents agents, en tenant compte de la similarité
 * sémantique et des métadonnées.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, 'data');
const FUSION_DIR = path.join(DATA_DIR, 'fusion');
const FUSION_CONFIG_FILE = path.join(DATA_DIR, 'config', 'fusion-config.json');

// Configuration par défaut
const DEFAULT_CONFIG = {
    similarityThreshold: 0.75,
    importanceWeight: 0.3,
    recencyWeight: 0.2,
    categoryWeight: 0.2,
    metadataWeight: 0.3,
    enableDeepFusion: true,
    maxFusionDepth: 3,
    preserveOriginalEntries: true,
    fusionStrategy: 'weighted'
};

// Variables pour stocker les références à la mémoire thermique
let thermalMemory = null;

/**
 * Initialise le module de fusion de mémoire
 * @param {Object} memory - Instance de la mémoire thermique
 */
function initializeMemoryFusion(memory) {
    thermalMemory = memory;

    // Créer le dossier de fusion s'il n'existe pas
    if (!fs.existsSync(FUSION_DIR)) {
        fs.mkdirSync(FUSION_DIR, { recursive: true });
    }

    console.log('Module de fusion de mémoire initialisé');
}

/**
 * Charge la configuration de fusion
 * @returns {Promise<Object>} - Configuration de fusion
 */
async function loadFusionConfig() {
    try {
        if (await existsAsync(FUSION_CONFIG_FILE)) {
            const data = await readFileAsync(FUSION_CONFIG_FILE, 'utf8');
            return { ...DEFAULT_CONFIG, ...JSON.parse(data) };
        }
        return DEFAULT_CONFIG;
    } catch (error) {
        console.error('Erreur lors du chargement de la configuration de fusion:', error);
        return DEFAULT_CONFIG;
    }
}

/**
 * Sauvegarde la configuration de fusion
 * @param {Object} config - Configuration de fusion
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveFusionConfig(config) {
    try {
        await writeFileAsync(FUSION_CONFIG_FILE, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de la configuration de fusion:', error);
        return false;
    }
}

/**
 * Calcule la similarité entre deux entrées de mémoire
 * @param {Object} entry1 - Première entrée
 * @param {Object} entry2 - Deuxième entrée
 * @param {Object} config - Configuration de fusion
 * @returns {number} - Score de similarité (0-1)
 */
function calculateSimilarity(entry1, entry2, config) {
    // Similarité du contenu
    let contentSimilarity = 0;

    // Vérifier le type de données
    if (typeof entry1.data === 'string' && typeof entry2.data === 'string') {
        // Similarité textuelle
        contentSimilarity = calculateStringSimilarity(entry1.data, entry2.data);
    } else if (typeof entry1.data === 'object' && typeof entry2.data === 'object') {
        // Similarité d'objets
        contentSimilarity = calculateObjectSimilarity(entry1.data, entry2.data);
    } else {
        // Types différents, similarité faible
        contentSimilarity = 0.1;
    }

    // Similarité de la clé
    const keySimilarity = calculateStringSimilarity(entry1.key, entry2.key);

    // Similarité de catégorie
    const categorySimilarity = entry1.category === entry2.category ? 1 : 0;

    // Similarité d'importance
    const importanceDiff = Math.abs(entry1.importance - entry2.importance);
    const importanceSimilarity = 1 - importanceDiff;

    // Similarité de métadonnées
    const metadataSimilarity = calculateMetadataSimilarity(entry1.metadata, entry2.metadata);

    // Calculer le score de similarité pondéré
    const similarityScore = (
        contentSimilarity * 0.5 +
        keySimilarity * 0.2 +
        categorySimilarity * config.categoryWeight +
        importanceSimilarity * config.importanceWeight +
        metadataSimilarity * config.metadataWeight
    ) / (0.5 + 0.2 + config.categoryWeight + config.importanceWeight + config.metadataWeight);

    return similarityScore;
}

/**
 * Calcule la similarité entre deux chaînes de caractères
 * @param {string} str1 - Première chaîne
 * @param {string} str2 - Deuxième chaîne
 * @returns {number} - Score de similarité (0-1)
 */
function calculateStringSimilarity(str1, str2) {
    if (!str1 || !str2) {
        return 0;
    }

    // Convertir en minuscules
    const s1 = String(str1).toLowerCase();
    const s2 = String(str2).toLowerCase();

    // Si les chaînes sont identiques
    if (s1 === s2) {
        return 1;
    }

    // Si l'une des chaînes est vide
    if (s1.length === 0 || s2.length === 0) {
        return 0;
    }

    // Calculer la distance de Levenshtein
    const distance = levenshteinDistance(s1, s2);

    // Calculer la similarité
    const maxLength = Math.max(s1.length, s2.length);
    return 1 - (distance / maxLength);
}

/**
 * Calcule la distance de Levenshtein entre deux chaînes
 * @param {string} str1 - Première chaîne
 * @param {string} str2 - Deuxième chaîne
 * @returns {number} - Distance de Levenshtein
 */
function levenshteinDistance(str1, str2) {
    const m = str1.length;
    const n = str2.length;

    // Créer une matrice de taille (m+1) x (n+1)
    const d = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

    // Initialiser la première colonne
    for (let i = 0; i <= m; i++) {
        d[i][0] = i;
    }

    // Initialiser la première ligne
    for (let j = 0; j <= n; j++) {
        d[0][j] = j;
    }

    // Remplir la matrice
    for (let j = 1; j <= n; j++) {
        for (let i = 1; i <= m; i++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            d[i][j] = Math.min(
                d[i - 1][j] + 1,      // Suppression
                d[i][j - 1] + 1,      // Insertion
                d[i - 1][j - 1] + cost // Substitution
            );
        }
    }

    return d[m][n];
}

/**
 * Calcule la similarité entre deux objets
 * @param {Object} obj1 - Premier objet
 * @param {Object} obj2 - Deuxième objet
 * @returns {number} - Score de similarité (0-1)
 */
function calculateObjectSimilarity(obj1, obj2) {
    // Si l'un des objets est null ou undefined
    if (!obj1 || !obj2) {
        return obj1 === obj2 ? 1 : 0;
    }

    // Si les objets sont des tableaux
    if (Array.isArray(obj1) && Array.isArray(obj2)) {
        return calculateArraySimilarity(obj1, obj2);
    }

    // Si les objets sont des objets
    if (typeof obj1 === 'object' && typeof obj2 === 'object') {
        // Récupérer toutes les clés
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        const allKeys = [...new Set([...keys1, ...keys2])];

        // Si aucune clé, les objets sont vides et donc identiques
        if (allKeys.length === 0) {
            return 1;
        }

        // Calculer la similarité pour chaque clé
        let totalSimilarity = 0;

        for (const key of allKeys) {
            if (key in obj1 && key in obj2) {
                // Les deux objets ont la clé
                if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    // Récursion pour les objets imbriqués
                    totalSimilarity += calculateObjectSimilarity(obj1[key], obj2[key]);
                } else if (typeof obj1[key] === 'string' && typeof obj2[key] === 'string') {
                    // Similarité textuelle
                    totalSimilarity += stringSimilarity.compareTwoStrings(obj1[key], obj2[key]);
                } else {
                    // Autres types
                    totalSimilarity += obj1[key] === obj2[key] ? 1 : 0;
                }
            }
            // Si une clé est manquante dans l'un des objets, la similarité est 0 pour cette clé
        }

        return totalSimilarity / allKeys.length;
    }

    // Pour les autres types, comparer directement
    return obj1 === obj2 ? 1 : 0;
}

/**
 * Calcule la similarité entre deux tableaux
 * @param {Array} arr1 - Premier tableau
 * @param {Array} arr2 - Deuxième tableau
 * @returns {number} - Score de similarité (0-1)
 */
function calculateArraySimilarity(arr1, arr2) {
    // Si les tableaux sont vides
    if (arr1.length === 0 && arr2.length === 0) {
        return 1;
    }

    // Si l'un des tableaux est vide
    if (arr1.length === 0 || arr2.length === 0) {
        return 0;
    }

    // Calculer la similarité entre les éléments des tableaux
    let maxSimilarity = 0;

    // Pour chaque élément du premier tableau
    for (const item1 of arr1) {
        // Trouver l'élément le plus similaire dans le deuxième tableau
        let maxItemSimilarity = 0;

        for (const item2 of arr2) {
            let itemSimilarity = 0;

            if (typeof item1 === 'object' && typeof item2 === 'object') {
                // Récursion pour les objets
                itemSimilarity = calculateObjectSimilarity(item1, item2);
            } else if (typeof item1 === 'string' && typeof item2 === 'string') {
                // Similarité textuelle
                itemSimilarity = stringSimilarity.compareTwoStrings(item1, item2);
            } else {
                // Autres types
                itemSimilarity = item1 === item2 ? 1 : 0;
            }

            maxItemSimilarity = Math.max(maxItemSimilarity, itemSimilarity);
        }

        maxSimilarity += maxItemSimilarity;
    }

    // Normaliser le score
    return maxSimilarity / arr1.length;
}

/**
 * Calcule la similarité entre deux ensembles de métadonnées
 * @param {Object} metadata1 - Premières métadonnées
 * @param {Object} metadata2 - Deuxièmes métadonnées
 * @returns {number} - Score de similarité (0-1)
 */
function calculateMetadataSimilarity(metadata1, metadata2) {
    // Si les métadonnées sont nulles ou undefined
    if (!metadata1 || !metadata2) {
        return metadata1 === metadata2 ? 1 : 0;
    }

    // Calculer la similarité des objets
    return calculateObjectSimilarity(metadata1, metadata2);
}

/**
 * Fusionne deux entrées de mémoire
 * @param {Object} entry1 - Première entrée
 * @param {Object} entry2 - Deuxième entrée
 * @param {Object} config - Configuration de fusion
 * @returns {Object} - Entrée fusionnée
 */
function fuseEntries(entry1, entry2, config) {
    // Déterminer l'entrée la plus importante
    const primaryEntry = entry1.importance >= entry2.importance ? entry1 : entry2;
    const secondaryEntry = primaryEntry === entry1 ? entry2 : entry1;

    // Fusionner les données en fonction de la stratégie
    let fusedData;

    if (config.fusionStrategy === 'weighted') {
        // Fusion pondérée
        fusedData = fuseDataWeighted(primaryEntry.data, secondaryEntry.data, primaryEntry.importance, secondaryEntry.importance);
    } else if (config.fusionStrategy === 'primary') {
        // Utiliser les données de l'entrée principale
        fusedData = primaryEntry.data;
    } else if (config.fusionStrategy === 'combine') {
        // Combiner les données
        fusedData = fuseDataCombine(primaryEntry.data, secondaryEntry.data);
    } else {
        // Par défaut, fusion pondérée
        fusedData = fuseDataWeighted(primaryEntry.data, secondaryEntry.data, primaryEntry.importance, secondaryEntry.importance);
    }

    // Fusionner les métadonnées
    const fusedMetadata = {
        ...secondaryEntry.metadata,
        ...primaryEntry.metadata,
        fusedFrom: [
            primaryEntry.id,
            secondaryEntry.id
        ],
        fusedAt: new Date().toISOString()
    };

    // Créer l'entrée fusionnée
    const fusedEntry = {
        key: primaryEntry.key,
        data: fusedData,
        importance: Math.max(primaryEntry.importance, secondaryEntry.importance),
        category: primaryEntry.category,
        metadata: fusedMetadata
    };

    return fusedEntry;
}

/**
 * Fusionne deux données avec une pondération basée sur l'importance
 * @param {any} data1 - Première donnée
 * @param {any} data2 - Deuxième donnée
 * @param {number} importance1 - Importance de la première donnée
 * @param {number} importance2 - Importance de la deuxième donnée
 * @returns {any} - Donnée fusionnée
 */
function fuseDataWeighted(data1, data2, importance1, importance2) {
    // Si les données sont des chaînes de caractères
    if (typeof data1 === 'string' && typeof data2 === 'string') {
        // Calculer les poids
        const totalImportance = importance1 + importance2;
        const weight1 = importance1 / totalImportance;
        const weight2 = importance2 / totalImportance;

        // Si les chaînes sont très similaires, prendre la plus longue
        const similarity = stringSimilarity.compareTwoStrings(data1, data2);
        if (similarity > 0.8) {
            return data1.length >= data2.length ? data1 : data2;
        }

        // Sinon, combiner les chaînes
        if (weight1 >= 0.7) {
            return data1;
        } else if (weight2 >= 0.7) {
            return data2;
        } else {
            // Diviser les textes en phrases (séparées par des points, points d'exclamation ou d'interrogation)
            const sentences1 = data1.split(/[.!?]+/).filter(s => s.trim().length > 0);
            const sentences2 = data2.split(/[.!?]+/).filter(s => s.trim().length > 0);

            // Fusionner les phrases uniques
            const allSentences = [...sentences1];

            for (const sentence2 of sentences2) {
                // Vérifier si la phrase est similaire à une phrase existante
                let isUnique = true;

                for (const sentence1 of sentences1) {
                    const sentenceSimilarity = calculateStringSimilarity(sentence1, sentence2);
                    if (sentenceSimilarity > 0.7) {
                        isUnique = false;
                        break;
                    }
                }

                // Si la phrase est unique, l'ajouter
                if (isUnique) {
                    allSentences.push(sentence2);
                }
            }

            // Joindre les phrases
            return allSentences.join('. ');
        }
    }

    // Si les données sont des objets
    if (typeof data1 === 'object' && typeof data2 === 'object') {
        // Si les objets sont des tableaux
        if (Array.isArray(data1) && Array.isArray(data2)) {
            // Fusionner les tableaux en éliminant les doublons
            return fuseArrays(data1, data2);
        }

        // Fusionner les objets
        const fusedObject = { ...data2, ...data1 };

        // Fusionner récursivement les propriétés qui sont des objets dans les deux entrées
        for (const key in data2) {
            if (key in data1 && typeof data1[key] === 'object' && typeof data2[key] === 'object') {
                fusedObject[key] = fuseDataWeighted(data1[key], data2[key], importance1, importance2);
            }
        }

        return fusedObject;
    }

    // Pour les autres types, utiliser la donnée la plus importante
    return importance1 >= importance2 ? data1 : data2;
}

/**
 * Fusionne deux tableaux en éliminant les doublons
 * @param {Array} arr1 - Premier tableau
 * @param {Array} arr2 - Deuxième tableau
 * @returns {Array} - Tableau fusionné
 */
function fuseArrays(arr1, arr2) {
    // Copier le premier tableau
    const fusedArray = [...arr1];

    // Ajouter les éléments uniques du deuxième tableau
    for (const item2 of arr2) {
        let isUnique = true;

        for (const item1 of arr1) {
            let similarity = 0;

            if (typeof item1 === 'object' && typeof item2 === 'object') {
                // Calculer la similarité des objets
                similarity = calculateObjectSimilarity(item1, item2);
            } else if (typeof item1 === 'string' && typeof item2 === 'string') {
                // Calculer la similarité des chaînes
                similarity = stringSimilarity.compareTwoStrings(item1, item2);
            } else {
                // Pour les autres types, comparer directement
                similarity = item1 === item2 ? 1 : 0;
            }

            // Si les éléments sont similaires, ne pas ajouter l'élément
            if (similarity > 0.8) {
                isUnique = false;
                break;
            }
        }

        // Si l'élément est unique, l'ajouter
        if (isUnique) {
            fusedArray.push(item2);
        }
    }

    return fusedArray;
}

/**
 * Combine deux données sans pondération
 * @param {any} data1 - Première donnée
 * @param {any} data2 - Deuxième donnée
 * @returns {any} - Donnée combinée
 */
function fuseDataCombine(data1, data2) {
    // Si les données sont des chaînes de caractères
    if (typeof data1 === 'string' && typeof data2 === 'string') {
        // Combiner les chaînes
        return `${data1}\n\n${data2}`;
    }

    // Si les données sont des objets
    if (typeof data1 === 'object' && typeof data2 === 'object') {
        // Si les objets sont des tableaux
        if (Array.isArray(data1) && Array.isArray(data2)) {
            // Fusionner les tableaux
            return [...data1, ...data2];
        }

        // Fusionner les objets
        const combinedObject = { ...data2, ...data1 };

        // Fusionner récursivement les propriétés qui sont des objets dans les deux entrées
        for (const key in data2) {
            if (key in data1 && typeof data1[key] === 'object' && typeof data2[key] === 'object') {
                combinedObject[key] = fuseDataCombine(data1[key], data2[key]);
            }
        }

        return combinedObject;
    }

    // Pour les autres types, utiliser la première donnée
    return data1;
}

/**
 * Identifie les entrées similaires entre deux agents
 * @param {string} sourceAgentId - ID de l'agent source
 * @param {string} targetAgentId - ID de l'agent cible
 * @param {Object} config - Configuration de fusion
 * @returns {Promise<Array>} - Paires d'entrées similaires
 */
async function identifySimilarEntries(sourceAgentId, targetAgentId, config) {
    try {
        if (!thermalMemory) {
            throw new Error('Mémoire thermique non initialisée');
        }

        // Récupérer les entrées des agents
        const sourceEntries = thermalMemory.getAllEntries().filter(entry => {
            if (entry.metadata && entry.metadata.agentId) {
                return entry.metadata.agentId === sourceAgentId;
            }
            return sourceAgentId === 'agent_claude'; // Par défaut, considérer les entrées sans agentId comme appartenant à l'agent principal
        });

        const targetEntries = thermalMemory.getAllEntries().filter(entry => {
            if (entry.metadata && entry.metadata.agentId) {
                return entry.metadata.agentId === targetAgentId;
            }
            return false;
        });

        // Identifier les paires d'entrées similaires
        const similarPairs = [];

        for (const sourceEntry of sourceEntries) {
            for (const targetEntry of targetEntries) {
                // Calculer la similarité
                const similarity = calculateSimilarity(sourceEntry, targetEntry, config);

                // Si la similarité dépasse le seuil, ajouter la paire
                if (similarity >= config.similarityThreshold) {
                    similarPairs.push({
                        sourceEntry,
                        targetEntry,
                        similarity
                    });
                }
            }
        }

        // Trier les paires par similarité décroissante
        similarPairs.sort((a, b) => b.similarity - a.similarity);

        return similarPairs;
    } catch (error) {
        console.error('Erreur lors de l\'identification des entrées similaires:', error);
        return [];
    }
}

/**
 * Fusionne les entrées similaires entre deux agents
 * @param {string} sourceAgentId - ID de l'agent source
 * @param {string} targetAgentId - ID de l'agent cible
 * @param {Object} options - Options de fusion
 * @returns {Promise<Object>} - Résultat de la fusion
 */
async function fuseAgentMemories(sourceAgentId, targetAgentId, options = {}) {
    try {
        // Charger la configuration
        const config = await loadFusionConfig();

        // Fusionner les options avec la configuration
        const fusionConfig = { ...config, ...options };

        // Identifier les entrées similaires
        const similarPairs = await identifySimilarEntries(sourceAgentId, targetAgentId, fusionConfig);

        // Résultats de la fusion
        const fusionResults = {
            totalPairs: similarPairs.length,
            fusedEntries: 0,
            preservedEntries: 0,
            newEntries: 0,
            errors: 0,
            details: []
        };

        // Fusionner les entrées similaires
        for (const pair of similarPairs) {
            try {
                // Fusionner les entrées
                const fusedEntry = fuseEntries(pair.sourceEntry, pair.targetEntry, fusionConfig);

                // Ajouter l'entrée fusionnée à la mémoire thermique
                if (fusionConfig.preserveOriginalEntries) {
                    // Conserver les entrées originales et ajouter l'entrée fusionnée
                    const entryId = thermalMemory.add(
                        fusedEntry.key,
                        fusedEntry.data,
                        fusedEntry.importance,
                        fusedEntry.category,
                        fusedEntry.metadata
                    );

                    if (entryId) {
                        fusionResults.newEntries++;
                        fusionResults.details.push({
                            type: 'new',
                            sourceId: pair.sourceEntry.id,
                            targetId: pair.targetEntry.id,
                            fusedId: entryId,
                            similarity: pair.similarity
                        });
                    } else {
                        fusionResults.errors++;
                    }
                } else {
                    // Supprimer les entrées originales et ajouter l'entrée fusionnée
                    thermalMemory.remove(pair.sourceEntry.id);
                    thermalMemory.remove(pair.targetEntry.id);

                    const entryId = thermalMemory.add(
                        fusedEntry.key,
                        fusedEntry.data,
                        fusedEntry.importance,
                        fusedEntry.category,
                        fusedEntry.metadata
                    );

                    if (entryId) {
                        fusionResults.fusedEntries++;
                        fusionResults.details.push({
                            type: 'fused',
                            sourceId: pair.sourceEntry.id,
                            targetId: pair.targetEntry.id,
                            fusedId: entryId,
                            similarity: pair.similarity
                        });
                    } else {
                        fusionResults.errors++;
                    }
                }
            } catch (error) {
                console.error('Erreur lors de la fusion des entrées:', error);
                fusionResults.errors++;
            }
        }

        return fusionResults;
    } catch (error) {
        console.error('Erreur lors de la fusion des mémoires des agents:', error);
        return {
            totalPairs: 0,
            fusedEntries: 0,
            preservedEntries: 0,
            newEntries: 0,
            errors: 1,
            details: []
        };
    }
}

// Exporter le module
module.exports = {
    initializeMemoryFusion,
    loadFusionConfig,
    saveFusionConfig,
    identifySimilarEntries,
    fuseAgentMemories,
    calculateSimilarity,
    fuseEntries
};
