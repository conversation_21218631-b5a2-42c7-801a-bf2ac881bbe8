#!/bin/bash

# Raccourci pour lancer Vision Ultra
# Ce script lance l'application Vision Ultra en mode application

# Chemin vers le script de lancement
SCRIPT_PATH="$(dirname "$0")/launch-vision-ultra.sh"

# Si le script n'est pas dans le même répertoire, utiliser le chemin absolu
if [ ! -f "$SCRIPT_PATH" ]; then
  SCRIPT_PATH="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/launch-vision-ultra.sh"
fi

# Vérifier si le script existe
if [ ! -f "$SCRIPT_PATH" ]; then
  echo "Erreur: Le script de lancement n'existe pas à l'emplacement: $SCRIPT_PATH"
  echo "Création du script de lancement..."
  
  # Créer le script de lancement
  cat > "$SCRIPT_PATH" << 'EOL'
#!/bin/bash

# Script de lancement pour Vision Ultra
# Ce script lance l'application Vision Ultra en mode application

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Vision Ultra]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
SERVER_FILE="$APP_DIR/server.js"

# Vérifier si le fichier server.js existe
if [ ! -f "$SERVER_FILE" ]; then
  print_error "Le fichier server.js n'existe pas à l'emplacement: $SERVER_FILE"
  exit 1
fi

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  print_error "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
  exit 1
fi

# Vérifier si le port 3000 est déjà utilisé
if lsof -i:3000 -t &> /dev/null; then
  print_message "Le port 3000 est déjà utilisé. Voulez-vous arrêter le processus existant ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Arrêt du processus existant..."
    kill -9 $(lsof -i:3000 -t) 2>/dev/null
    sleep 2
    print_success "Processus arrêté."
  else
    print_message "Lancement de l'application sur un autre port..."
    export PORT=3001
  fi
fi

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗   ██╗██╗███████╗██╗ ██████╗ ███╗   ██╗    ██╗   ██╗██╗  ████████╗██████╗  █████╗ "
echo "██║   ██║██║██╔════╝██║██╔═══██╗████╗  ██║    ██║   ██║██║  ╚══██╔══╝██╔══██╗██╔══██╗"
echo "██║   ██║██║███████╗██║██║   ██║██╔██╗ ██║    ██║   ██║██║     ██║   ██████╔╝███████║"
echo "╚██╗ ██╔╝██║╚════██║██║██║   ██║██║╚██╗██║    ██║   ██║██║     ██║   ██╔══██╗██╔══██║"
echo " ╚████╔╝ ██║███████║██║╚██████╔╝██║ ╚████║    ╚██████╔╝███████╗██║   ██║  ██║██║  ██║"
echo "  ╚═══╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝     ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Système Cognitif Avancé avec Mémoire Thermique${NC}"
echo -e "${YELLOW}Développé par Jean Passave, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""
print_message "Initialisation du système..."
sleep 1

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  print_error "npm n'est pas installé. Vérification des dépendances impossible."
else
  # Vérifier si les dépendances sont installées
  print_message "Vérification des dépendances..."
  cd "$APP_DIR" || exit
  if [ ! -d "node_modules" ]; then
    print_message "Installation des dépendances..."
    npm install --silent
    if [ $? -ne 0 ]; then
      print_error "Erreur lors de l'installation des dépendances."
      exit 1
    fi
    print_success "Dépendances installées avec succès."
  else
    print_success "Dépendances déjà installées."
  fi
fi

# Vérifier si les répertoires de données existent
print_message "Vérification des répertoires de données..."
mkdir -p "$APP_DIR/data/memory"
mkdir -p "$APP_DIR/data/logs"
print_success "Répertoires de données vérifiés."

# Lancer l'application
print_message "Lancement de Vision Ultra..."
cd "$APP_DIR" || exit

# Déterminer le port à utiliser
PORT=${PORT:-3000}
print_message "L'application sera accessible à l'adresse: http://localhost:$PORT/luna"

# Lancer le serveur
node server.js

# Ce code ne sera exécuté que si le serveur s'arrête
print_message "Vision Ultra s'est arrêté."
EOL
  
  # Rendre le script exécutable
  chmod +x "$SCRIPT_PATH"
  echo "Script de lancement créé avec succès à l'emplacement: $SCRIPT_PATH"
fi

# Exécuter le script de lancement
"$SCRIPT_PATH"
