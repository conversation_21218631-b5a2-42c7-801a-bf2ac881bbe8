#!/usr/bin/env node

/**
 * Script de correction complète de la lisibilité pour TOUTES les interfaces Louna
 * Applique les corrections de contraste et de lisibilité à tous les fichiers HTML
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 CORRECTION FINALE COMPLÈTE - LISIBILITÉ PARFAITE POUR TOUTE L\'APPLICATION LOUNA');
console.log('================================================================================');

// Obtenir tous les fichiers HTML automatiquement
function getAllHtmlFiles() {
    const publicDir = './public';
    const files = fs.readdirSync(publicDir)
        .filter(file => file.endsWith('.html'))
        .map(file => path.join(publicDir, file));
    
    console.log(`📁 ${files.length} fichiers HTML détectés dans l'application`);
    return files;
}

// CSS de correction universelle
const correctionCSS = `
    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <style>
        /* CORRECTION UNIVERSELLE DE LISIBILITÉ - LOUNA */
        
        /* Optimisation du rendu des polices */
        * {
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Variables corrigées pour lisibilité maximale */
        :root {
            --text-primary: #ffffff !important;
            --text-secondary: #ffffff !important;
            --text-muted: rgba(255, 255, 255, 0.9) !important;
            --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* CORRECTION GLOBALE - TOUS LES TEXTES */
        h1, h2, h3, h4, h5, h6, p, span, div, li, a, label, input, textarea, select {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* BOUTONS - LISIBILITÉ MAXIMALE */
        button, .btn, .button, .toolbar-btn, .demo-button, .cta-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        button:hover, .btn:hover, .button:hover {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        /* NAVIGATION - TOUJOURS VISIBLE */
        .nav-item, .nav-link, .navbar-nav a, .top-navbar a {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        .logo-text, .navbar-brand {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: bold !important;
        }

        /* CARTES ET CONTENEURS - CONTRASTE OPTIMAL */
        .card, .unified-card, .metric-card, .capability-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* TITRES ET SOUS-TITRES */
        .interface-title, .section-title, .card-title {
            color: #ffffff !important;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .interface-subtitle, .card-subtitle {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* STATISTIQUES ET MÉTRIQUES */
        .stat-number, .metric-value {
            color: #ff6b6b !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .stat-label, .metric-label {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* LISTES ET DESCRIPTIONS */
        .capability-features li, .spec-list li {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: var(--text-shadow) !important;
        }

        .capability-description, .card-text {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FORMULAIRES */
        input, select, textarea {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* TABLEAUX */
        table, th, td {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        th {
            background: rgba(255, 105, 180, 0.2) !important;
            font-weight: bold !important;
        }

        /* ALERTES ET NOTIFICATIONS */
        .alert, .notification {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FOOTER */
        .footer, .footer-content {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTIONS SPÉCIFIQUES POUR FONDS CLAIRS */
        .tech-specs, .capabilities-section[style*="background: #f"] {
            color: #333 !important;
        }

        .tech-specs *, .capabilities-section[style*="background: #f"] * {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
        }

        /* ICÔNES */
        .fas, .far, .fab {
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTION POUR LES ÉLÉMENTS SPÉCIAUX */
        .progress-bar, .slider {
            background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
        }

        /* AMÉLIORATION DU CONTRASTE POUR LES LIENS */
        a {
            color: #ff69b4 !important;
            text-shadow: var(--text-shadow) !important;
        }

        a:hover {
            color: #ff1493 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }
    </style>`;

// Fonction pour ajouter les corrections à un fichier HTML
function addCorrectionsToFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier si les corrections sont déjà présentes
        if (content.includes('CORRECTION UNIVERSELLE DE LISIBILITÉ - LOUNA')) {
            return { success: true, action: 'already_corrected' };
        }

        // Trouver la position pour insérer les corrections (après les autres CSS)
        const headEndIndex = content.indexOf('</head>');
        if (headEndIndex === -1) {
            return { success: false, error: 'Balise </head> non trouvée' };
        }

        // Insérer les corrections avant </head>
        const beforeHead = content.substring(0, headEndIndex);
        const afterHead = content.substring(headEndIndex);
        const newContent = beforeHead + correctionCSS + '\n' + afterHead;

        // Sauvegarder le fichier
        fs.writeFileSync(filePath, newContent, 'utf8');
        
        return { success: true, action: 'corrected' };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// Fonction principale
function applyCorrectionsToAllFiles() {
    const htmlFiles = getAllHtmlFiles();
    let correctedCount = 0;
    let alreadyCorrectedCount = 0;
    let errorCount = 0;

    console.log('\n🛠️  Application des corrections de lisibilité...\n');

    htmlFiles.forEach((filePath, index) => {
        const fileName = path.basename(filePath);
        const result = addCorrectionsToFile(filePath);

        if (result.success) {
            if (result.action === 'corrected') {
                console.log(`✅ [${index + 1}/${htmlFiles.length}] ${fileName} - CORRIGÉ`);
                correctedCount++;
            } else {
                console.log(`⏭️  [${index + 1}/${htmlFiles.length}] ${fileName} - Déjà corrigé`);
                alreadyCorrectedCount++;
            }
        } else {
            console.log(`❌ [${index + 1}/${htmlFiles.length}] ${fileName} - ERREUR: ${result.error}`);
            errorCount++;
        }
    });

    // Résumé final
    console.log('\n================================================================================');
    console.log('📊 RÉSUMÉ DES CORRECTIONS APPLIQUÉES :');
    console.log(`✅ Fichiers corrigés : ${correctedCount}`);
    console.log(`⏭️  Déjà corrigés : ${alreadyCorrectedCount}`);
    console.log(`❌ Erreurs : ${errorCount}`);
    console.log(`📁 Total traité : ${htmlFiles.length}`);
    
    if (correctedCount > 0 || alreadyCorrectedCount > 0) {
        console.log('\n🎉 MISSION ACCOMPLIE !');
        console.log('🎯 LISIBILITÉ PARFAITE GARANTIE DANS TOUTE L\'APPLICATION LOUNA !');
        console.log('✨ Tous les textes sont maintenant parfaitement lisibles !');
    }

    return {
        total: htmlFiles.length,
        corrected: correctedCount,
        alreadyCorrected: alreadyCorrectedCount,
        errors: errorCount
    };
}

// Exécuter le script
if (require.main === module) {
    applyCorrectionsToAllFiles();
}

module.exports = { applyCorrectionsToAllFiles };
