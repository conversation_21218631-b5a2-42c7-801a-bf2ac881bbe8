#!/usr/bin/env node

/**
 * SCRIPT DE LANCEMENT POUR TESTER ET CORRIGER LE SYSTÈME DE FORMATION
 * Lance les tests et applique les corrections automatiquement
 */

const TrainingSystemTester = require('./training-system-tester');
const TrainingSystemFixes = require('./training-system-fixes');

class TrainingTestRunner {
    constructor() {
        this.config = {
            autoFix: true,
            runTests: true,
            saveReports: true,
            verbose: true
        };
        
        console.log('🚀 LANCEUR DE TESTS ET CORRECTIONS DE FORMATION');
        console.log('='.repeat(60));
    }

    /**
     * Lance le processus complet
     */
    async run() {
        try {
            console.log('📋 PLAN D\'EXÉCUTION:');
            console.log('   1. Application des corrections');
            console.log('   2. Attente du redémarrage du système');
            console.log('   3. Exécution des tests complets');
            console.log('   4. Génération des rapports');
            console.log('');

            // Phase 1: Appliquer les corrections
            if (this.config.autoFix) {
                await this.applyFixes();
            }

            // Phase 2: Attendre que le système redémarre
            await this.waitForSystemRestart();

            // Phase 3: Lancer les tests
            if (this.config.runTests) {
                await this.runTests();
            }

            // Phase 4: Analyser les résultats
            await this.analyzeResults();

            console.log('\n🎉 PROCESSUS TERMINÉ AVEC SUCCÈS !');
            
        } catch (error) {
            console.error('❌ Erreur lors du processus:', error.message);
            process.exit(1);
        }
    }

    /**
     * Phase 1: Appliquer les corrections
     */
    async applyFixes() {
        console.log('\n🔧 PHASE 1: APPLICATION DES CORRECTIONS');
        console.log('-'.repeat(50));

        const fixes = new TrainingSystemFixes({
            debug: this.config.verbose,
            autoApply: true,
            backupBeforeFix: true
        });

        await fixes.applyAllFixes();
        
        console.log('✅ Corrections appliquées avec succès');
        console.log('⚠️ REDÉMARRAGE DU SERVEUR REQUIS POUR APPLIQUER LES CORRECTIONS');
        console.log('   Veuillez redémarrer le serveur Louna et relancer ce script avec --skip-fixes');
    }

    /**
     * Phase 2: Attendre le redémarrage du système
     */
    async waitForSystemRestart() {
        console.log('\n⏳ PHASE 2: ATTENTE DU SYSTÈME');
        console.log('-'.repeat(50));

        // Vérifier si le serveur est accessible
        const maxAttempts = 30;
        let attempts = 0;
        let serverReady = false;

        while (attempts < maxAttempts && !serverReady) {
            try {
                const axios = require('axios');
                const response = await axios.get('http://localhost:3000/api/training/state', {
                    timeout: 5000
                });
                
                if (response.status === 200) {
                    serverReady = true;
                    console.log('✅ Serveur accessible');
                }
            } catch (error) {
                attempts++;
                console.log(`⏳ Tentative ${attempts}/${maxAttempts} - Serveur non accessible`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        if (!serverReady) {
            throw new Error('Serveur non accessible après 60 secondes');
        }

        // Attendre encore 5 secondes pour que tous les systèmes soient prêts
        console.log('⏳ Attente de l\'initialisation complète...');
        await new Promise(resolve => setTimeout(resolve, 5000));
    }

    /**
     * Phase 3: Lancer les tests
     */
    async runTests() {
        console.log('\n🧪 PHASE 3: EXÉCUTION DES TESTS');
        console.log('-'.repeat(50));

        const tester = new TrainingSystemTester({
            debug: this.config.verbose,
            saveResults: this.config.saveReports,
            timeout: 15000
        });

        await tester.runCompleteTest();
        
        // Stocker les résultats pour l'analyse
        this.testResults = tester.testResults;
    }

    /**
     * Phase 4: Analyser les résultats
     */
    async analyzeResults() {
        console.log('\n📊 PHASE 4: ANALYSE DES RÉSULTATS');
        console.log('-'.repeat(50));

        if (!this.testResults || this.testResults.length === 0) {
            console.log('⚠️ Aucun résultat de test disponible');
            return;
        }

        const analysis = this.performAnalysis();
        this.displayAnalysis(analysis);
        
        if (this.config.saveReports) {
            this.saveAnalysisReport(analysis);
        }
    }

    /**
     * Analyser les résultats des tests
     */
    performAnalysis() {
        const total = this.testResults.length;
        const successful = this.testResults.filter(r => r.success).length;
        const failed = total - successful;
        const successRate = total > 0 ? Math.round((successful / total) * 100) : 0;

        // Analyser par catégorie
        const categories = {
            connectivity: this.testResults.filter(r => r.testName.includes('ENDPOINT')),
            apis: this.testResults.filter(r => r.testName.includes('API') || r.testName.includes('GET_')),
            agents: this.testResults.filter(r => r.testName.includes('AGENT') || r.testName.includes('DEEPSEEK')),
            training: this.testResults.filter(r => r.testName.includes('TRAINING') || r.testName.includes('DATASET')),
            integration: this.testResults.filter(r => r.testName.includes('INTEGRATION') || r.testName.includes('COMMUNICATION'))
        };

        const categoryAnalysis = {};
        for (const [category, tests] of Object.entries(categories)) {
            const categoryTotal = tests.length;
            const categorySuccessful = tests.filter(t => t.success).length;
            const categoryRate = categoryTotal > 0 ? Math.round((categorySuccessful / categoryTotal) * 100) : 0;
            
            categoryAnalysis[category] = {
                total: categoryTotal,
                successful: categorySuccessful,
                failed: categoryTotal - categorySuccessful,
                successRate: categoryRate,
                status: categoryRate >= 80 ? 'excellent' : categoryRate >= 60 ? 'bon' : categoryRate >= 40 ? 'moyen' : 'critique'
            };
        }

        // Identifier les problèmes critiques
        const criticalIssues = this.testResults
            .filter(r => !r.success)
            .filter(r => r.testName.includes('DEEPSEEK') || r.testName.includes('MAIN_AGENT') || r.testName.includes('TRAINING'))
            .map(r => ({
                test: r.testName,
                error: r.data,
                priority: this.getPriority(r.testName)
            }))
            .sort((a, b) => b.priority - a.priority);

        // Recommandations
        const recommendations = this.generateRecommendations(categoryAnalysis, criticalIssues);

        return {
            overall: {
                total,
                successful,
                failed,
                successRate,
                status: successRate >= 80 ? 'excellent' : successRate >= 60 ? 'bon' : successRate >= 40 ? 'moyen' : 'critique'
            },
            categories: categoryAnalysis,
            criticalIssues,
            recommendations
        };
    }

    /**
     * Obtenir la priorité d'un problème
     */
    getPriority(testName) {
        if (testName.includes('DEEPSEEK')) return 10;
        if (testName.includes('MAIN_AGENT')) return 9;
        if (testName.includes('TRAINING')) return 8;
        if (testName.includes('COMMUNICATION')) return 7;
        if (testName.includes('API')) return 6;
        return 5;
    }

    /**
     * Générer des recommandations
     */
    generateRecommendations(categoryAnalysis, criticalIssues) {
        const recommendations = [];

        // Recommandations basées sur les catégories
        for (const [category, analysis] of Object.entries(categoryAnalysis)) {
            if (analysis.status === 'critique') {
                recommendations.push({
                    priority: 'HAUTE',
                    category: category,
                    message: `Problèmes critiques dans ${category} (${analysis.successRate}% de réussite)`,
                    action: `Vérifier et corriger les composants de ${category}`
                });
            } else if (analysis.status === 'moyen') {
                recommendations.push({
                    priority: 'MOYENNE',
                    category: category,
                    message: `Améliorations nécessaires dans ${category} (${analysis.successRate}% de réussite)`,
                    action: `Optimiser les composants de ${category}`
                });
            }
        }

        // Recommandations basées sur les problèmes critiques
        if (criticalIssues.length > 0) {
            recommendations.push({
                priority: 'HAUTE',
                category: 'general',
                message: `${criticalIssues.length} problème(s) critique(s) détecté(s)`,
                action: 'Résoudre les problèmes critiques en priorité'
            });
        }

        // Recommandations spécifiques
        if (categoryAnalysis.agents.successRate < 50) {
            recommendations.push({
                priority: 'HAUTE',
                category: 'agents',
                message: 'Système d\'agents défaillant',
                action: 'Redémarrer les agents et vérifier leur configuration'
            });
        }

        if (categoryAnalysis.training.successRate < 70) {
            recommendations.push({
                priority: 'MOYENNE',
                category: 'training',
                message: 'Système de formation sous-optimal',
                action: 'Vérifier les datasets et la configuration de formation'
            });
        }

        return recommendations.sort((a, b) => {
            const priorityOrder = { 'HAUTE': 3, 'MOYENNE': 2, 'BASSE': 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
    }

    /**
     * Afficher l'analyse
     */
    displayAnalysis(analysis) {
        console.log('\n📈 RÉSULTATS GLOBAUX:');
        console.log(`   Taux de réussite: ${analysis.overall.successRate}% (${analysis.overall.successful}/${analysis.overall.total})`);
        console.log(`   État général: ${analysis.overall.status.toUpperCase()}`);

        console.log('\n📊 ANALYSE PAR CATÉGORIE:');
        for (const [category, data] of Object.entries(analysis.categories)) {
            const icon = data.status === 'excellent' ? '✅' : data.status === 'bon' ? '🟡' : data.status === 'moyen' ? '🟠' : '❌';
            console.log(`   ${icon} ${category}: ${data.successRate}% (${data.successful}/${data.total}) - ${data.status}`);
        }

        if (analysis.criticalIssues.length > 0) {
            console.log('\n🚨 PROBLÈMES CRITIQUES:');
            analysis.criticalIssues.slice(0, 5).forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue.test}: ${issue.error}`);
            });
        }

        if (analysis.recommendations.length > 0) {
            console.log('\n💡 RECOMMANDATIONS:');
            analysis.recommendations.slice(0, 5).forEach((rec, index) => {
                const icon = rec.priority === 'HAUTE' ? '🔴' : rec.priority === 'MOYENNE' ? '🟡' : '🟢';
                console.log(`   ${icon} ${rec.message}`);
                console.log(`      → ${rec.action}`);
            });
        }
    }

    /**
     * Sauvegarder le rapport d'analyse
     */
    saveAnalysisReport(analysis) {
        try {
            const fs = require('fs');
            const path = require('path');
            
            const reportPath = path.join(__dirname, 'data', 'training', 'analysis-reports', `training_analysis_${Date.now()}.json`);
            
            // Créer le dossier s'il n'existe pas
            const reportDir = path.dirname(reportPath);
            if (!fs.existsSync(reportDir)) {
                fs.mkdirSync(reportDir, { recursive: true });
            }
            
            const report = {
                timestamp: new Date().toISOString(),
                analysis: analysis,
                rawResults: this.testResults
            };
            
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            console.log(`\n💾 Rapport d'analyse sauvegardé: ${reportPath}`);
            
        } catch (error) {
            console.log(`❌ Erreur sauvegarde rapport: ${error.message}`);
        }
    }
}

// Gestion des arguments de ligne de commande
const args = process.argv.slice(2);
const skipFixes = args.includes('--skip-fixes');
const testsOnly = args.includes('--tests-only');
const verbose = args.includes('--verbose') || args.includes('-v');

// Configuration basée sur les arguments
const config = {
    autoFix: !skipFixes && !testsOnly,
    runTests: true,
    saveReports: true,
    verbose: verbose
};

// Lancement du processus
if (require.main === module) {
    const runner = new TrainingTestRunner();
    runner.config = { ...runner.config, ...config };
    
    console.log('🎯 Configuration:');
    console.log(`   Auto-correction: ${runner.config.autoFix ? 'OUI' : 'NON'}`);
    console.log(`   Tests: ${runner.config.runTests ? 'OUI' : 'NON'}`);
    console.log(`   Rapports: ${runner.config.saveReports ? 'OUI' : 'NON'}`);
    console.log(`   Verbose: ${runner.config.verbose ? 'OUI' : 'NON'}`);
    console.log('');
    
    runner.run().catch(error => {
        console.error('❌ Erreur fatale:', error.message);
        process.exit(1);
    });
}

module.exports = TrainingTestRunner;
