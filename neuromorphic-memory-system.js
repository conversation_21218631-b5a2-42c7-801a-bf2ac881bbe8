/**
 * SYSTÈME DE MÉMOIRE NEUROMORPHIQUE ULTRA-AVANCÉ
 * 
 * Mémoire qui fonctionne comme un vrai cerveau humain :
 * - Oubli intelligent des données inutiles
 * - Priorisation automatique des informations importantes
 * - Apprentissage des patterns d'usage
 * - Transfert automatique entre niveaux de mémoire
 * - Consolidation nocturne des souvenirs
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class NeuromorphicMemorySystem extends EventEmitter {
    constructor(thermalMemory, ultraMonitor) {
        super();
        
        this.thermalMemory = thermalMemory;
        this.ultraMonitor = ultraMonitor;
        
        // Structure de mémoire neuromorphique (comme le cerveau humain)
        this.memoryLayers = {
            // Mémoire sensorielle (très courte durée - 0.5-3 secondes)
            sensory: {
                capacity: 1000,        // 1000 éléments max
                retention: 3000,       // 3 secondes
                data: new Map(),
                temperature: 0
            },
            
            // Mémoire de travail (courte durée - 15-30 secondes)
            working: {
                capacity: 200,         // 200 éléments max (7±2 règle de Miller)
                retention: 30000,      // 30 secondes
                data: new Map(),
                temperature: 0
            },
            
            // Mémoire à court terme (minutes à heures)
            shortTerm: {
                capacity: 5000,        // 5000 éléments
                retention: 3600000,    // 1 heure
                data: new Map(),
                temperature: 0
            },
            
            // Mémoire à long terme (jours à années)
            longTerm: {
                capacity: 100000,      // 100k éléments
                retention: Infinity,   // Permanent (avec oubli intelligent)
                data: new Map(),
                temperature: 0
            },
            
            // Mémoire procédurale (compétences et habitudes)
            procedural: {
                capacity: 10000,       // 10k procédures
                retention: Infinity,   // Permanent
                data: new Map(),
                temperature: 0
            },
            
            // Mémoire émotionnelle (souvenirs avec charge émotionnelle)
            emotional: {
                capacity: 20000,       // 20k souvenirs émotionnels
                retention: Infinity,   // Permanent
                data: new Map(),
                temperature: 0
            }
        };
        
        // Système de consolidation (comme le sommeil)
        this.consolidation = {
            active: false,
            lastConsolidation: 0,
            interval: 3600000,        // 1 heure
            nightlyConsolidation: {
                hour: 2,              // 2h du matin
                duration: 1800000     // 30 minutes
            }
        };
        
        // Patterns d'apprentissage
        this.learningPatterns = {
            accessFrequency: new Map(),    // Fréquence d'accès
            accessRecency: new Map(),      // Récence d'accès
            emotionalWeight: new Map(),    // Poids émotionnel
            contextualLinks: new Map(),    // Liens contextuels
            forgettingCurve: new Map()     // Courbe d'oubli
        };
        
        // Métriques neuromorphiques
        this.metrics = {
            totalMemories: 0,
            memoryEfficiency: 0,
            consolidationRate: 0,
            forgettingRate: 0,
            learningRate: 0,
            emotionalActivation: 0,
            neuralConnections: 0,
            synapticStrength: 0
        };
        
        // Configuration
        this.config = {
            // Seuils de transfert entre couches
            transferThresholds: {
                sensoryToWorking: 0.3,      // 30% d'importance
                workingToShortTerm: 0.5,    // 50% d'importance
                shortTermToLongTerm: 0.7,   // 70% d'importance
                emotionalBoost: 2.0         // Boost émotionnel x2
            },
            
            // Paramètres d'oubli intelligent
            forgetting: {
                baseRate: 0.1,              // 10% d'oubli de base
                emotionalProtection: 0.9,   // 90% protection émotionnelle
                frequencyProtection: 0.8,   // 80% protection fréquence
                recencyBoost: 1.5           // Boost récence x1.5
            },
            
            // Intervalles de traitement
            intervals: {
                memoryProcessing: 1000,     // 1 seconde
                consolidation: 60000,       // 1 minute
                forgetting: 300000,         // 5 minutes
                learning: 30000             // 30 secondes
            }
        };
        
        // État du système
        this.systemState = {
            active: false,
            intervals: {},
            lastActivity: 0,
            sleepMode: false,
            dreamingActive: false
        };
        
        this.log('🧠 Système de mémoire neuromorphique initialisé');
    }
    
    /**
     * Démarre le système de mémoire neuromorphique
     */
    start() {
        if (this.systemState.active) {
            this.log('⚠️ Système déjà actif');
            return;
        }
        
        this.systemState.active = true;
        
        // Charger la mémoire existante
        this.loadExistingMemory();
        
        // Démarrer les processus neuromorphiques
        this.startNeuromorphicProcesses();
        
        // Configurer la consolidation nocturne
        this.setupNightlyConsolidation();
        
        this.log('🚀 Système de mémoire neuromorphique démarré');
        this.emit('memorySystemStarted');
    }
    
    /**
     * Stocke une nouvelle information dans la mémoire sensorielle
     */
    store(data, context = {}) {
        const memory = {
            id: this.generateMemoryId(),
            data: data,
            context: context,
            timestamp: Date.now(),
            accessCount: 1,
            lastAccess: Date.now(),
            importance: this.calculateImportance(data, context),
            emotionalWeight: this.calculateEmotionalWeight(data, context),
            temperature: this.calculateInitialTemperature(data, context),
            connections: new Set(),
            strength: 1.0
        };
        
        // Stocker dans la mémoire sensorielle
        this.memoryLayers.sensory.data.set(memory.id, memory);
        
        // Mettre à jour les patterns d'apprentissage
        this.updateLearningPatterns(memory);
        
        // Évaluer pour transfert immédiat si très important
        if (memory.importance > 0.8 || memory.emotionalWeight > 0.8) {
            this.evaluateForTransfer(memory, 'sensory');
        }
        
        this.log(`💾 Nouvelle mémoire stockée: ${memory.id} (importance: ${memory.importance.toFixed(2)})`);
        this.emit('memoryStored', memory);
        
        return memory.id;
    }
    
    /**
     * Récupère une information de la mémoire
     */
    retrieve(query, context = {}) {
        const startTime = Date.now();
        let foundMemory = null;
        let searchLayer = null;
        
        // Rechercher dans toutes les couches (du plus récent au plus ancien)
        const searchOrder = ['working', 'sensory', 'shortTerm', 'longTerm', 'procedural', 'emotional'];
        
        for (const layerName of searchOrder) {
            const layer = this.memoryLayers[layerName];
            
            for (const [id, memory] of layer.data.entries()) {
                if (this.matchesQuery(memory, query, context)) {
                    foundMemory = memory;
                    searchLayer = layerName;
                    break;
                }
            }
            
            if (foundMemory) break;
        }
        
        if (foundMemory) {
            // Mettre à jour les statistiques d'accès
            foundMemory.accessCount++;
            foundMemory.lastAccess = Date.now();
            
            // Renforcer la mémoire (consolidation)
            this.reinforceMemory(foundMemory, searchLayer);
            
            // Créer des connexions contextuelles
            this.createContextualConnections(foundMemory, context);
            
            const retrievalTime = Date.now() - startTime;
            this.log(`🔍 Mémoire récupérée: ${foundMemory.id} depuis ${searchLayer} (${retrievalTime}ms)`);
            
            this.emit('memoryRetrieved', { memory: foundMemory, layer: searchLayer, time: retrievalTime });
            
            return foundMemory.data;
        }
        
        this.log(`❌ Mémoire non trouvée pour: ${JSON.stringify(query)}`);
        return null;
    }
    
    /**
     * Démarre les processus neuromorphiques
     */
    startNeuromorphicProcesses() {
        // Traitement de la mémoire (1s)
        this.systemState.intervals.processing = setInterval(() => {
            this.processMemoryLayers();
        }, this.config.intervals.memoryProcessing);
        
        // Consolidation (1 minute)
        this.systemState.intervals.consolidation = setInterval(() => {
            this.performConsolidation();
        }, this.config.intervals.consolidation);
        
        // Oubli intelligent (5 minutes)
        this.systemState.intervals.forgetting = setInterval(() => {
            this.performIntelligentForgetting();
        }, this.config.intervals.forgetting);
        
        // Apprentissage (30 secondes)
        this.systemState.intervals.learning = setInterval(() => {
            this.updateLearningMetrics();
        }, this.config.intervals.learning);
        
        this.log('⚙️ Processus neuromorphiques démarrés');
    }
    
    /**
     * Traite les couches de mémoire
     */
    processMemoryLayers() {
        // Traiter chaque couche
        for (const [layerName, layer] of Object.entries(this.memoryLayers)) {
            this.processLayer(layerName, layer);
        }
        
        // Mettre à jour les métriques
        this.updateMetrics();
    }
    
    /**
     * Traite une couche de mémoire spécifique
     */
    processLayer(layerName, layer) {
        const now = Date.now();
        const expiredMemories = [];
        
        // Identifier les mémoires expirées
        for (const [id, memory] of layer.data.entries()) {
            const age = now - memory.timestamp;
            
            if (layerName !== 'longTerm' && layerName !== 'procedural' && layerName !== 'emotional') {
                if (age > layer.retention) {
                    // Évaluer pour transfert avant expiration
                    if (this.shouldTransferMemory(memory, layerName)) {
                        this.transferMemory(memory, layerName);
                    } else {
                        expiredMemories.push(id);
                    }
                }
            }
        }
        
        // Supprimer les mémoires expirées
        for (const id of expiredMemories) {
            layer.data.delete(id);
        }
        
        // Gérer la capacité
        this.manageLayerCapacity(layerName, layer);
    }
    
    /**
     * Évalue si une mémoire doit être transférée
     */
    shouldTransferMemory(memory, currentLayer) {
        const importance = this.calculateCurrentImportance(memory);
        const threshold = this.config.transferThresholds[`${currentLayer}To${this.getNextLayer(currentLayer)}`];
        
        return importance >= threshold;
    }
    
    /**
     * Transfère une mémoire vers la couche suivante
     */
    transferMemory(memory, fromLayer) {
        const toLayer = this.getNextLayer(fromLayer);
        
        if (toLayer && this.memoryLayers[toLayer]) {
            // Supprimer de la couche actuelle
            this.memoryLayers[fromLayer].data.delete(memory.id);
            
            // Ajouter à la couche suivante
            this.memoryLayers[toLayer].data.set(memory.id, memory);
            
            // Augmenter la force synaptique
            memory.strength = Math.min(memory.strength * 1.2, 2.0);
            
            this.log(`🔄 Mémoire transférée: ${memory.id} de ${fromLayer} vers ${toLayer}`);
            this.emit('memoryTransferred', { memory, fromLayer, toLayer });
        }
    }
    
    /**
     * Effectue la consolidation des mémoires
     */
    performConsolidation() {
        this.log('🔄 Consolidation des mémoires en cours...');
        
        let consolidated = 0;
        
        // Consolider les mémoires importantes
        for (const [layerName, layer] of Object.entries(this.memoryLayers)) {
            for (const memory of layer.data.values()) {
                if (this.shouldConsolidate(memory)) {
                    this.consolidateMemory(memory);
                    consolidated++;
                }
            }
        }
        
        this.consolidation.lastConsolidation = Date.now();
        this.log(`✨ ${consolidated} mémoires consolidées`);
        this.emit('consolidationCompleted', { count: consolidated });
    }
    
    /**
     * Effectue l'oubli intelligent
     */
    performIntelligentForgetting() {
        this.log('🧹 Oubli intelligent en cours...');
        
        let forgotten = 0;
        
        // Appliquer l'oubli aux mémoires moins importantes
        for (const [layerName, layer] of Object.entries(this.memoryLayers)) {
            if (layerName === 'longTerm' || layerName === 'procedural' || layerName === 'emotional') {
                const toForget = [];
                
                for (const [id, memory] of layer.data.entries()) {
                    if (this.shouldForget(memory)) {
                        toForget.push(id);
                    }
                }
                
                for (const id of toForget) {
                    layer.data.delete(id);
                    forgotten++;
                }
            }
        }
        
        this.log(`🗑️ ${forgotten} mémoires oubliées intelligemment`);
        this.emit('forgettingCompleted', { count: forgotten });
    }
    
    /**
     * Configure la consolidation nocturne
     */
    setupNightlyConsolidation() {
        // Vérifier chaque heure si c'est l'heure de la consolidation nocturne
        setInterval(() => {
            const now = new Date();
            const hour = now.getHours();
            
            if (hour === this.consolidation.nightlyConsolidation.hour && !this.systemState.sleepMode) {
                this.enterSleepMode();
            }
        }, 3600000); // Vérifier chaque heure
    }
    
    /**
     * Entre en mode sommeil pour consolidation nocturne
     */
    enterSleepMode() {
        this.log('😴 Entrée en mode sommeil - Consolidation nocturne');
        this.systemState.sleepMode = true;
        this.systemState.dreamingActive = true;
        
        // Consolidation intensive
        this.performIntensiveConsolidation();
        
        // Rêves (réorganisation des connexions)
        this.performDreaming();
        
        // Sortir du mode sommeil après la durée configurée
        setTimeout(() => {
            this.exitSleepMode();
        }, this.consolidation.nightlyConsolidation.duration);
        
        this.emit('sleepModeEntered');
    }
    
    /**
     * Sort du mode sommeil
     */
    exitSleepMode() {
        this.log('🌅 Sortie du mode sommeil - Réveil');
        this.systemState.sleepMode = false;
        this.systemState.dreamingActive = false;
        
        this.emit('sleepModeExited');
    }
    
    /**
     * Effectue une consolidation intensive (pendant le sommeil)
     */
    performIntensiveConsolidation() {
        this.log('💤 Consolidation intensive en cours...');
        
        // Renforcer les connexions importantes
        // Réorganiser les mémoires
        // Créer de nouvelles associations
        
        this.emit('intensiveConsolidationCompleted');
    }
    
    /**
     * Effectue le processus de rêve (réorganisation)
     */
    performDreaming() {
        this.log('💭 Processus de rêve actif - Réorganisation des connexions');
        
        // Créer de nouvelles connexions aléatoires
        // Renforcer les patterns importants
        // Éliminer les connexions faibles
        
        this.emit('dreamingCompleted');
    }
    
    /**
     * Méthodes utilitaires
     */
    
    generateMemoryId() {
        return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    calculateImportance(data, context) {
        // Calculer l'importance basée sur le contenu et le contexte
        let importance = 0.5; // Base
        
        // Facteurs d'importance
        if (context.urgent) importance += 0.3;
        if (context.emotional) importance += 0.2;
        if (context.frequent) importance += 0.1;
        
        return Math.min(importance, 1.0);
    }
    
    calculateEmotionalWeight(data, context) {
        // Calculer le poids émotionnel
        let weight = 0.0;
        
        if (context.emotional) weight += 0.5;
        if (context.positive) weight += 0.2;
        if (context.negative) weight += 0.3;
        
        return Math.min(weight, 1.0);
    }
    
    calculateInitialTemperature(data, context) {
        // Température initiale basée sur l'activité
        return 37.0 + (Math.random() * 2.0); // 37-39°C
    }
    
    matchesQuery(memory, query, context) {
        // Logique de correspondance simple
        const dataStr = JSON.stringify(memory.data).toLowerCase();
        const queryStr = JSON.stringify(query).toLowerCase();
        
        return dataStr.includes(queryStr);
    }
    
    reinforceMemory(memory, layer) {
        // Renforcer la mémoire lors de l'accès
        memory.strength = Math.min(memory.strength * 1.1, 2.0);
        memory.temperature += 0.5;
    }
    
    createContextualConnections(memory, context) {
        // Créer des connexions contextuelles
        if (context.relatedTo) {
            memory.connections.add(context.relatedTo);
        }
    }
    
    calculateCurrentImportance(memory) {
        // Recalculer l'importance actuelle
        let importance = memory.importance;
        
        // Facteur de fréquence
        importance += (memory.accessCount / 100) * 0.2;
        
        // Facteur de récence
        const age = Date.now() - memory.lastAccess;
        const recencyFactor = Math.exp(-age / 86400000); // Décroissance exponentielle sur 24h
        importance += recencyFactor * 0.1;
        
        // Facteur émotionnel
        importance += memory.emotionalWeight * 0.3;
        
        return Math.min(importance, 1.0);
    }
    
    getNextLayer(currentLayer) {
        const layerOrder = {
            'sensory': 'working',
            'working': 'shortTerm',
            'shortTerm': 'longTerm'
        };
        
        return layerOrder[currentLayer];
    }
    
    manageLayerCapacity(layerName, layer) {
        if (layer.data.size > layer.capacity) {
            // Supprimer les mémoires les moins importantes
            const memories = Array.from(layer.data.values());
            memories.sort((a, b) => this.calculateCurrentImportance(a) - this.calculateCurrentImportance(b));
            
            const toRemove = memories.slice(0, layer.data.size - layer.capacity);
            for (const memory of toRemove) {
                layer.data.delete(memory.id);
            }
        }
    }
    
    shouldConsolidate(memory) {
        return memory.accessCount > 3 && memory.strength < 1.5;
    }
    
    consolidateMemory(memory) {
        memory.strength = Math.min(memory.strength * 1.3, 2.0);
    }
    
    shouldForget(memory) {
        const importance = this.calculateCurrentImportance(memory);
        const forgettingProbability = this.config.forgetting.baseRate * (1 - importance);
        
        return Math.random() < forgettingProbability;
    }
    
    updateLearningPatterns(memory) {
        // Mettre à jour les patterns d'apprentissage
        this.learningPatterns.accessFrequency.set(memory.id, memory.accessCount);
        this.learningPatterns.accessRecency.set(memory.id, memory.lastAccess);
        this.learningPatterns.emotionalWeight.set(memory.id, memory.emotionalWeight);
    }
    
    updateLearningMetrics() {
        // Mettre à jour les métriques d'apprentissage
        this.metrics.totalMemories = this.getTotalMemoryCount();
        this.metrics.memoryEfficiency = this.calculateMemoryEfficiency();
        this.metrics.neuralConnections = this.countNeuralConnections();
    }
    
    updateMetrics() {
        // Mettre à jour toutes les métriques
        this.updateLearningMetrics();
    }
    
    getTotalMemoryCount() {
        return Object.values(this.memoryLayers).reduce((total, layer) => total + layer.data.size, 0);
    }
    
    calculateMemoryEfficiency() {
        // Calculer l'efficacité de la mémoire
        const totalCapacity = Object.values(this.memoryLayers).reduce((total, layer) => total + layer.capacity, 0);
        const totalUsed = this.getTotalMemoryCount();
        
        return totalUsed / totalCapacity;
    }
    
    countNeuralConnections() {
        let connections = 0;
        for (const layer of Object.values(this.memoryLayers)) {
            for (const memory of layer.data.values()) {
                connections += memory.connections.size;
            }
        }
        return connections;
    }
    
    loadExistingMemory() {
        // Charger la mémoire existante depuis le stockage
        this.log('📂 Chargement de la mémoire existante...');
    }
    
    evaluateForTransfer(memory, currentLayer) {
        if (this.shouldTransferMemory(memory, currentLayer)) {
            this.transferMemory(memory, currentLayer);
        }
    }
    
    /**
     * Obtient les statistiques du système
     */
    getStats() {
        return {
            systemState: this.systemState,
            memoryLayers: Object.keys(this.memoryLayers).reduce((stats, layerName) => {
                const layer = this.memoryLayers[layerName];
                stats[layerName] = {
                    count: layer.data.size,
                    capacity: layer.capacity,
                    utilization: (layer.data.size / layer.capacity) * 100,
                    temperature: layer.temperature
                };
                return stats;
            }, {}),
            metrics: this.metrics,
            consolidation: this.consolidation,
            learningPatterns: {
                totalPatterns: this.learningPatterns.accessFrequency.size,
                averageAccess: this.calculateAverageAccess(),
                emotionalMemories: this.countEmotionalMemories()
            }
        };
    }
    
    calculateAverageAccess() {
        const frequencies = Array.from(this.learningPatterns.accessFrequency.values());
        return frequencies.length > 0 ? frequencies.reduce((sum, freq) => sum + freq, 0) / frequencies.length : 0;
    }
    
    countEmotionalMemories() {
        return Array.from(this.learningPatterns.emotionalWeight.values()).filter(weight => weight > 0.5).length;
    }
    
    /**
     * Arrête le système
     */
    stop() {
        if (!this.systemState.active) return;
        
        this.systemState.active = false;
        
        // Arrêter les intervalles
        Object.values(this.systemState.intervals).forEach(interval => {
            if (interval) clearInterval(interval);
        });
        
        this.log('🛑 Système de mémoire neuromorphique arrêté');
        this.emit('memorySystemStopped');
    }
    
    /**
     * Logging
     */
    log(message) {
        console.log(`[NeuromorphicMemory] ${new Date().toISOString()} ${message}`);
    }
}

module.exports = NeuromorphicMemorySystem;
