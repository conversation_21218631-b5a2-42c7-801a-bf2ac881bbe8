/**
 * Script de vérification de l'intégration des nouvelles fonctionnalités
 * Vérifie que toutes les corrections et améliorations ont été appliquées
 */

const fs = require('fs');
const path = require('path');

class AppIntegrationVerifier {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  /**
   * Vérifie si un fichier existe
   */
  checkFileExists(filePath, description) {
    const exists = fs.existsSync(filePath);
    this.addResult(exists, `Fichier ${description}`, filePath, exists ? 'Trouvé' : 'Manquant');
    return exists;
  }

  /**
   * Vérifie si un fichier contient du texte spécifique
   */
  checkFileContains(filePath, searchText, description) {
    if (!fs.existsSync(filePath)) {
      this.addResult(false, description, filePath, 'Fichier non trouvé');
      return false;
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const contains = content.includes(searchText);
      this.addResult(contains, description, filePath, contains ? 'Trouvé' : 'Non trouvé');
      return contains;
    } catch (error) {
      this.addResult(false, description, filePath, `Erreur: ${error.message}`);
      return false;
    }
  }

  /**
   * Ajoute un résultat de test
   */
  addResult(passed, test, file, details) {
    const result = {
      passed,
      test,
      file,
      details,
      timestamp: new Date().toISOString()
    };

    this.results.details.push(result);
    
    if (passed) {
      this.results.passed++;
    } else {
      this.results.failed++;
    }
  }

  /**
   * Ajoute un avertissement
   */
  addWarning(test, file, details) {
    const result = {
      passed: null,
      test,
      file,
      details,
      warning: true,
      timestamp: new Date().toISOString()
    };

    this.results.details.push(result);
    this.results.warnings++;
  }

  /**
   * Vérifie l'intégration de la configuration centralisée
   */
  verifyConfigIntegration() {
    console.log('\n🔧 Vérification de la configuration centralisée...');
    
    // Vérifier l'existence du fichier de configuration
    this.checkFileExists('./config/app-config.js', 'Configuration centralisée');
    
    // Vérifier l'intégration dans server.js
    this.checkFileContains('./server.js', 'require(\'./config/app-config\')', 'Import de la configuration dans server.js');
    this.checkFileContains('./server.js', 'getConfig(\'server.port\')', 'Utilisation de getConfig dans server.js');
    this.checkFileContains('./server.js', 'validateConfig()', 'Validation de la configuration');
  }

  /**
   * Vérifie l'intégration du système de logging
   */
  verifyLoggingIntegration() {
    console.log('\n📝 Vérification du système de logging...');
    
    // Vérifier l'existence du fichier de logging
    this.checkFileExists('./utils/logger.js', 'Système de logging');
    
    // Vérifier l'intégration dans server.js
    this.checkFileContains('./server.js', 'require(\'./utils/logger\')', 'Import du logger dans server.js');
    this.checkFileContains('./server.js', 'logger.info(', 'Utilisation du logger');
    this.checkFileContains('./server.js', 'logger.security(', 'Logging de sécurité');
  }

  /**
   * Vérifie l'intégration de la gestion d'erreurs
   */
  verifyErrorHandlingIntegration() {
    console.log('\n🛡️ Vérification de la gestion d\'erreurs...');
    
    // Vérifier l'existence du fichier de gestion d'erreurs
    this.checkFileExists('./utils/error-handler.js', 'Gestionnaire d\'erreurs');
    
    // Vérifier l'intégration dans server.js
    this.checkFileContains('./server.js', 'require(\'./utils/error-handler\')', 'Import du gestionnaire d\'erreurs');
    this.checkFileContains('./server.js', 'errorMiddleware', 'Middleware de gestion d\'erreurs');
    this.checkFileContains('./server.js', 'getErrorHandler()', 'Utilisation du gestionnaire d\'erreurs');
  }

  /**
   * Vérifie l'amélioration du générateur multimédia
   */
  verifyMediaGeneratorImprovements() {
    console.log('\n🎨 Vérification des améliorations du générateur multimédia...');
    
    // Vérifier les améliorations dans le générateur multimédia
    this.checkFileContains('./code/deepseek-node-ui/lib/media-generator.js', 'callImageGenerationAPI', 'API réelle de génération d\'images');
    this.checkFileContains('./code/deepseek-node-ui/lib/media-generator.js', 'callVideoGenerationAPI', 'API réelle de génération vidéo');
    this.checkFileContains('./code/deepseek-node-ui/lib/media-generator.js', 'callMusicGenerationAPI', 'API réelle de génération musicale');
    this.checkFileContains('./code/deepseek-node-ui/lib/media-generator.js', 'callCodeGenerationAPI', 'API réelle de génération de code');
  }

  /**
   * Vérifie l'amélioration du système de diagnostics
   */
  verifyDiagnosticsImprovements() {
    console.log('\n🔍 Vérification des améliorations des diagnostics...');
    
    // Vérifier les améliorations dans le système de diagnostics
    this.checkFileContains('./system-diagnostics.js', 'checkComponentHealth', 'Vérification de santé des composants');
    this.checkFileContains('./system-diagnostics.js', 'checkThermalMemoryHealth', 'Diagnostic de la mémoire thermique');
    this.checkFileContains('./system-diagnostics.js', 'checkArtificialBrainHealth', 'Diagnostic du cerveau artificiel');
  }

  /**
   * Vérifie l'amélioration de l'intégration Ollama
   */
  verifyOllamaImprovements() {
    console.log('\n🧠 Vérification des améliorations Ollama...');
    
    // Vérifier les améliorations dans l'intégration Ollama
    this.checkFileContains('./code/deepseek-node-ui/lib/memory/ollama_integration.js', 'getRelevantMemories', 'Récupération de souvenirs pertinents');
    this.checkFileContains('./code/deepseek-node-ui/lib/memory/ollama_integration.js', 'storeResponseInMemory', 'Stockage en mémoire thermique');
    this.checkFileContains('./code/deepseek-node-ui/lib/memory/ollama_integration.js', 'formatConversationHistory', 'Formatage de l\'historique');
  }

  /**
   * Vérifie l'amélioration du monitoring UX
   */
  verifyUXMonitoringImprovements() {
    console.log('\n👁️ Vérification des améliorations du monitoring UX...');
    
    // Vérifier les améliorations dans le monitoring UX
    this.checkFileContains('./user-experience-monitor.js', 'generateUsabilitySuggestions', 'Génération de suggestions d\'utilisabilité');
  }

  /**
   * Vérifie la structure des dossiers
   */
  verifyDirectoryStructure() {
    console.log('\n📁 Vérification de la structure des dossiers...');
    
    // Vérifier les nouveaux dossiers
    this.checkFileExists('./config', 'Dossier de configuration');
    this.checkFileExists('./utils', 'Dossier des utilitaires');
    this.checkFileExists('./logs', 'Dossier des logs') || this.addWarning('Dossier des logs', './logs', 'Sera créé automatiquement au démarrage');
  }

  /**
   * Vérifie les variables globales
   */
  verifyGlobalVariables() {
    console.log('\n🌐 Vérification des variables globales...');
    
    // Vérifier que les systèmes sont rendus disponibles globalement
    this.checkFileContains('./server.js', 'global.thermalMemory', 'Variable globale thermalMemory');
    this.checkFileContains('./server.js', 'global.artificialBrain', 'Variable globale artificialBrain');
    this.checkFileContains('./server.js', 'global.securitySystem', 'Variable globale securitySystem');
  }

  /**
   * Vérifie les tâches de maintenance
   */
  verifyMaintenanceTasks() {
    console.log('\n🔧 Vérification des tâches de maintenance...');
    
    // Vérifier les tâches de maintenance
    this.checkFileContains('./server.js', 'startMaintenanceTasks', 'Fonction de tâches de maintenance');
    this.checkFileContains('./server.js', 'logger.cleanup()', 'Nettoyage des logs');
    this.checkFileContains('./server.js', 'thermalMemory.saveMemory()', 'Sauvegarde de la mémoire');
  }

  /**
   * Exécute toutes les vérifications
   */
  runAllVerifications() {
    console.log('🔍 VÉRIFICATION DE L\'INTÉGRATION DE L\'APPLICATION LOUNA');
    console.log('=' * 60);

    this.verifyConfigIntegration();
    this.verifyLoggingIntegration();
    this.verifyErrorHandlingIntegration();
    this.verifyMediaGeneratorImprovements();
    this.verifyDiagnosticsImprovements();
    this.verifyOllamaImprovements();
    this.verifyUXMonitoringImprovements();
    this.verifyDirectoryStructure();
    this.verifyGlobalVariables();
    this.verifyMaintenanceTasks();

    this.displayResults();
  }

  /**
   * Affiche les résultats de la vérification
   */
  displayResults() {
    console.log('\n' + '=' * 60);
    console.log('📊 RÉSULTATS DE LA VÉRIFICATION');
    console.log('=' * 60);

    console.log(`✅ Tests réussis: ${this.results.passed}`);
    console.log(`❌ Tests échoués: ${this.results.failed}`);
    console.log(`⚠️  Avertissements: ${this.results.warnings}`);

    const total = this.results.passed + this.results.failed;
    const successRate = total > 0 ? (this.results.passed / total * 100).toFixed(1) : 0;
    console.log(`📈 Taux de réussite: ${successRate}%`);

    // Afficher les détails des échecs
    if (this.results.failed > 0) {
      console.log('\n❌ TESTS ÉCHOUÉS:');
      this.results.details
        .filter(r => r.passed === false)
        .forEach(r => {
          console.log(`  - ${r.test}: ${r.details} (${r.file})`);
        });
    }

    // Afficher les avertissements
    if (this.results.warnings > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.results.details
        .filter(r => r.warning)
        .forEach(r => {
          console.log(`  - ${r.test}: ${r.details} (${r.file})`);
        });
    }

    // Conclusion
    console.log('\n' + '=' * 60);
    if (this.results.failed === 0) {
      console.log('🎉 TOUTES LES INTÉGRATIONS SONT CORRECTES !');
      console.log('✨ Votre application Louna est prête à fonctionner avec toutes les nouvelles fonctionnalités.');
    } else {
      console.log('⚠️  CERTAINES INTÉGRATIONS NÉCESSITENT ATTENTION');
      console.log('🔧 Veuillez corriger les problèmes identifiés avant de démarrer l\'application.');
    }
    console.log('=' * 60);
  }
}

// Exécuter la vérification
const verifier = new AppIntegrationVerifier();
verifier.runAllVerifications();
