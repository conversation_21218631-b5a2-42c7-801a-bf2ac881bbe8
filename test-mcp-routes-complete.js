/**
 * Test complet de toutes les routes MCP ajoutées
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3007';
const MCP_URL = 'http://localhost:3002';

// Couleurs pour les logs
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Tests des nouvelles routes MCP
async function testMCPRoutes() {
    log('\n=== TEST DES NOUVELLES ROUTES MCP ===', 'yellow');

    const tests = [
        {
            name: 'Statut MCP via proxy',
            method: 'GET',
            url: `${BASE_URL}/api/mcp/status`
        },
        {
            name: 'Test de connectivité MCP complet',
            method: 'GET',
            url: `${BASE_URL}/api/mcp/connectivity-test`
        },
        {
            name: 'Statut agent amélio<PERSON>',
            method: 'GET',
            url: `${BASE_URL}/api/mcp/agent/status`
        },
        {
            name: 'Prédictions agent MCP',
            method: 'GET',
            url: `${BASE_URL}/api/mcp/agent/predictions`
        },
        {
            name: 'Fichiers bureau via proxy',
            method: 'GET',
            url: `${BASE_URL}/api/mcp/desktop/files`
        },
        {
            name: 'Recherche Internet via proxy',
            method: 'POST',
            url: `${BASE_URL}/api/mcp/internet/search`,
            data: { query: 'intelligence artificielle 2024', maxResults: 3 }
        },
        {
            name: 'Fetch Internet via proxy',
            method: 'POST',
            url: `${BASE_URL}/api/mcp/internet/fetch`,
            data: { url: 'https://www.google.com' }
        },
        {
            name: 'Action MCP via agent',
            method: 'POST',
            url: `${BASE_URL}/api/mcp/agent/action`,
            data: { action: 'mcp_status', parameters: {} }
        }
    ];

    const results = [];

    for (const test of tests) {
        try {
            log(`\n🧪 Test: ${test.name}`, 'blue');
            
            let response;
            if (test.method === 'GET') {
                response = await axios.get(test.url, { timeout: 30000 });
            } else {
                response = await axios.post(test.url, test.data || {}, { timeout: 30000 });
            }

            if (response.status === 200 && response.data.success !== false) {
                log(`✅ ${test.name} - SUCCÈS`, 'green');
                
                // Afficher des détails spécifiques selon le test
                if (test.name.includes('connectivité')) {
                    const summary = response.data.summary;
                    log(`   📊 Tests: ${summary.passed}/${summary.total} réussis`, 'cyan');
                } else if (test.name.includes('statut agent')) {
                    const agent = response.data.agent;
                    log(`   🤖 Agent en ligne: ${agent.isOnline}`, 'cyan');
                    log(`   🌐 Internet: ${agent.internetAccess}`, 'cyan');
                    log(`   🎛️ MCP: ${agent.mcpEnabled}`, 'cyan');
                } else if (test.name.includes('fichiers')) {
                    const files = response.data.files || [];
                    log(`   📁 Fichiers trouvés: ${files.length}`, 'cyan');
                } else if (test.name.includes('recherche')) {
                    const results = response.data.results || [];
                    log(`   🔍 Résultats: ${results.length}`, 'cyan');
                } else if (test.name.includes('fetch')) {
                    const dataSize = response.data.data?.length || 0;
                    log(`   📄 Données récupérées: ${dataSize} caractères`, 'cyan');
                }

                results.push({ test: test.name, success: true, data: response.data });
            } else {
                log(`❌ ${test.name} - ÉCHEC (statut invalide)`, 'red');
                results.push({ test: test.name, success: false, error: 'Statut invalide' });
            }

        } catch (error) {
            log(`❌ ${test.name} - ERREUR: ${error.message}`, 'red');
            results.push({ test: test.name, success: false, error: error.message });
        }
    }

    return results;
}

// Test des routes de chat avec MCP
async function testChatMCPIntegration() {
    log('\n=== TEST INTÉGRATION CHAT-MCP ===', 'yellow');

    try {
        log('\n🧪 Test: Statut agent chat', 'blue');
        const statusResponse = await axios.get(`${BASE_URL}/api/chat/agent/status`, { timeout: 10000 });
        
        if (statusResponse.data.success) {
            log('✅ Statut agent chat - SUCCÈS', 'green');
            const agent = statusResponse.data.agent;
            log(`   🤖 Agent: ${agent.isOnline ? 'En ligne' : 'Hors ligne'}`, 'cyan');
            log(`   🌐 Internet: ${agent.internetAccess ? 'Activé' : 'Désactivé'}`, 'cyan');
            log(`   🎛️ MCP: ${agent.mcpEnabled ? 'Activé' : 'Désactivé'}`, 'cyan');
        } else {
            log('❌ Statut agent chat - ÉCHEC', 'red');
        }

        log('\n🧪 Test: Test Internet chat', 'blue');
        const internetResponse = await axios.post(`${BASE_URL}/api/chat/test-internet`, {
            query: 'test MCP'
        }, { timeout: 30000 });
        
        if (internetResponse.data.success) {
            log('✅ Test Internet chat - SUCCÈS', 'green');
            log(`   📊 Résultat: ${internetResponse.data.message}`, 'cyan');
        } else {
            log('❌ Test Internet chat - ÉCHEC', 'red');
        }

    } catch (error) {
        log(`❌ Erreur test chat-MCP: ${error.message}`, 'red');
    }
}

// Fonction principale
async function main() {
    log('=== TEST COMPLET DES CORRECTIONS MCP ===', 'yellow');
    log('Vérification de toutes les nouvelles routes et fonctionnalités MCP\n', 'yellow');

    // Attendre que les serveurs soient prêts
    log('⏳ Attente du démarrage des serveurs...', 'yellow');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Tester les routes MCP
    const mcpResults = await testMCPRoutes();

    // Tester l'intégration chat-MCP
    await testChatMCPIntegration();

    // Résumé final
    log('\n=== RÉSUMÉ DES TESTS ===', 'yellow');
    const totalTests = mcpResults.length;
    const passedTests = mcpResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    log(`📊 Total des tests: ${totalTests}`, 'cyan');
    log(`✅ Tests réussis: ${passedTests}`, passedTests === totalTests ? 'green' : 'yellow');
    log(`❌ Tests échoués: ${failedTests}`, failedTests === 0 ? 'green' : 'red');

    if (passedTests === totalTests) {
        log('\n🎉 TOUS LES TESTS ONT RÉUSSI !', 'green');
        log('Toutes les corrections MCP sont fonctionnelles', 'green');
    } else {
        log('\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ', 'yellow');
        log('Vérifiez les erreurs ci-dessus', 'yellow');
    }

    // Afficher les détails des échecs
    const failures = mcpResults.filter(r => !r.success);
    if (failures.length > 0) {
        log('\n📋 DÉTAILS DES ÉCHECS:', 'red');
        failures.forEach(failure => {
            log(`   • ${failure.test}: ${failure.error}`, 'red');
        });
    }
}

// Exécuter le script
main().catch(error => {
    log(`\n❌ ERREUR CRITIQUE: ${error.message}`, 'red');
    process.exit(1);
});
