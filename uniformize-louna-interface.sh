#!/bin/bash

# Script pour uniformiser l'interface Louna
# Ce script applique le même style rose à toutes les interfaces

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
CSS_DIR="$APP_DIR/public/css"
VIEWS_DIR="$APP_DIR/views"
MAIN_CSS="$CSS_DIR/louna-style.css"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗   ██╗███╗   ██╗██╗███████╗ ██████╗ ██████╗ ███╗   ███╗██╗███████╗███████╗██████╗ "
echo "██║   ██║████╗  ██║██║██╔════╝██╔═══██╗██╔══██╗████╗ ████║██║╚══███╔╝██╔════╝██╔══██╗"
echo "██║   ██║██╔██╗ ██║██║█████╗  ██║   ██║██████╔╝██╔████╔██║██║  ███╔╝ █████╗  ██████╔╝"
echo "██║   ██║██║╚██╗██║██║██╔══╝  ██║   ██║██╔══██╗██║╚██╔╝██║██║ ███╔╝  ██╔══╝  ██╔══██╗"
echo "╚██████╔╝██║ ╚████║██║██║     ╚██████╔╝██║  ██║██║ ╚═╝ ██║██║███████╗███████╗██║  ██║"
echo " ╚═════╝ ╚═╝  ╚═══╝╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝     ╚═╝╚═╝╚══════╝╚══════╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Uniformisation de l'interface Louna${NC}"
echo ""
print_message "Début de l'uniformisation de l'interface Louna..."
sleep 1

# Étape 1 : Créer un fichier CSS unifié avec le style rose
print_message "Étape 1 : Création d'un fichier CSS unifié avec le style rose..."

# Renommer le fichier CSS principal si nécessaire
if [ -f "$CSS_DIR/luna-style.css" ] && [ ! -f "$MAIN_CSS" ]; then
  cp "$CSS_DIR/luna-style.css" "$MAIN_CSS"
  print_message "Fichier CSS principal renommé: luna-style.css -> louna-style.css"
fi

# Créer ou mettre à jour le fichier CSS principal
cat > "$MAIN_CSS" << 'EOL'
/* Styles unifiés pour Louna - Interface rose */

:root {
  --primary-color: #ff00ff;
  --primary-light: #ff66ff;
  --primary-dark: #cc00cc;
  --secondary-color: #6a0dad;
  --secondary-light: #9b59b6;
  --secondary-dark: #4a0080;
  --accent-color: #ff69b4;
  --text-color: #f8f9fa;
  --text-muted: #adb5bd;
  --bg-color: #1a1a2e;
  --bg-light: #16213e;
  --bg-dark: #0f172a;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --info-color: #2196f3;
  --border-color: rgba(255, 105, 180, 0.2);
  --shadow-color: rgba(0, 0, 0, 0.5);
  --gradient-start: #6a0dad;
  --gradient-end: #ff00ff;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Layout */
.container-fluid {
  padding: 0;
}

.main-container {
  display: flex;
  min-height: 100vh;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  margin-left: 250px;
  transition: margin-left 0.3s;
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 250px;
  height: 100vh;
  background: linear-gradient(to bottom, var(--gradient-start), var(--gradient-end));
  color: var(--text-color);
  z-index: 1000;
  transition: all 0.3s;
  box-shadow: 2px 0 10px var(--shadow-color);
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header i {
  margin-right: 10px;
  color: var(--accent-color);
}

.sidebar-nav {
  padding: 10px 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 5px;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid var(--accent-color);
}

.sidebar-nav a.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-left: 3px solid var(--accent-color);
}

.sidebar-nav i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

/* Cards */
.card {
  background-color: var(--bg-light);
  border-radius: 10px;
  border: 1px solid var(--border-color);
  margin-bottom: 20px;
  box-shadow: 0 4px 8px var(--shadow-color);
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px var(--shadow-color);
}

.card-header {
  background-color: rgba(106, 13, 173, 0.2);
  border-bottom: 1px solid var(--border-color);
  padding: 15px 20px;
  font-weight: bold;
  border-radius: 10px 10px 0 0;
  display: flex;
  align-items: center;
}

.card-header i {
  margin-right: 10px;
  color: var(--accent-color);
}

.card-body {
  padding: 20px;
}

.card-footer {
  background-color: rgba(106, 13, 173, 0.1);
  border-top: 1px solid var(--border-color);
  padding: 15px 20px;
  border-radius: 0 0 10px 10px;
}

/* Buttons */
.btn {
  border-radius: 5px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-accent {
  background-color: var(--accent-color);
  color: white;
}

.btn-accent:hover {
  background-color: #e5509f;
}

/* Forms */
.form-control {
  background-color: var(--bg-dark);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 5px;
  padding: 10px 15px;
}

.form-control:focus {
  background-color: var(--bg-dark);
  border-color: var(--accent-color);
  color: var(--text-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 105, 180, 0.25);
}

/* Tables */
.table {
  color: var(--text-color);
}

.table thead th {
  background-color: rgba(106, 13, 173, 0.2);
  border-color: var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Utilities */
.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-accent {
  color: var(--accent-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-accent {
  background-color: var(--accent-color) !important;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 105, 180, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 105, 180, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 105, 180, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }
  
  .sidebar-header span,
  .sidebar-nav span {
    display: none;
  }
  
  .content-wrapper {
    margin-left: 80px;
  }
  
  .sidebar-nav a {
    justify-content: center;
    padding: 15px 0;
  }
  
  .sidebar-nav i {
    margin-right: 0;
    font-size: 1.2rem;
  }
}
EOL

print_success "Fichier CSS unifié créé: $MAIN_CSS"

# Étape 2 : Créer un fichier CSS d'override pour appliquer le style rose à toutes les interfaces
print_message "Étape 2 : Création d'un fichier CSS d'override..."

cat > "$CSS_DIR/louna-override.css" << 'EOL'
/* Override CSS pour appliquer le style rose à toutes les interfaces */

/* Couleurs de base */
:root {
  --primary-color: #ff00ff !important;
  --primary-light: #ff66ff !important;
  --primary-dark: #cc00cc !important;
  --secondary-color: #6a0dad !important;
  --secondary-light: #9b59b6 !important;
  --secondary-dark: #4a0080 !important;
  --accent-color: #ff69b4 !important;
  --text-color: #f8f9fa !important;
  --text-muted: #adb5bd !important;
  --bg-color: #1a1a2e !important;
  --bg-light: #16213e !important;
  --bg-dark: #0f172a !important;
  --border-color: rgba(255, 105, 180, 0.2) !important;
}

/* Appliquer les couleurs à tous les éléments */
body {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.sidebar {
  background: linear-gradient(to bottom, var(--secondary-color), var(--primary-color)) !important;
}

.card-header {
  background-color: rgba(106, 13, 173, 0.2) !important;
}

.btn-primary {
  background-color: var(--primary-color) !important;
}

.btn-secondary {
  background-color: var(--secondary-color) !important;
}

/* Ajouter des effets de survol et des animations */
.card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5) !important;
  transition: transform 0.3s, box-shadow 0.3s !important;
}

.sidebar-nav a:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-left: 3px solid var(--accent-color) !important;
}

/* Appliquer des styles spécifiques aux interfaces */
.memory-container, .brain-container, .cognitive-container, .dashboard-container {
  background-color: var(--bg-color) !important;
  border-radius: 10px !important;
  border: 1px solid var(--border-color) !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
}

/* Styles pour les graphiques et visualisations */
.chart-container, .visualization-container {
  background-color: var(--bg-light) !important;
  border-radius: 10px !important;
  border: 1px solid var(--border-color) !important;
  padding: 15px !important;
  margin-bottom: 20px !important;
}

/* Styles pour les tableaux */
table {
  color: var(--text-color) !important;
  background-color: var(--bg-light) !important;
}

th {
  background-color: rgba(106, 13, 173, 0.2) !important;
}

/* Styles pour les formulaires */
input, select, textarea {
  background-color: var(--bg-dark) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--accent-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 105, 180, 0.25) !important;
}
EOL

print_success "Fichier CSS d'override créé: $CSS_DIR/louna-override.css"

# Étape 3 : Ajouter les liens CSS à toutes les vues
print_message "Étape 3 : Ajout des liens CSS à toutes les vues..."

# Trouver tous les fichiers EJS
EJS_FILES=$(find "$VIEWS_DIR" -name "*.ejs" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Ajouter les liens CSS à chaque fichier EJS
for file in $EJS_FILES; do
  # Vérifier si le fichier contient une balise head
  if grep -q "<head>" "$file"; then
    # Sauvegarder une copie du fichier original
    cp "$file" "$file.bak"
    
    # Vérifier si les liens CSS sont déjà présents
    if ! grep -q "louna-style.css" "$file" && ! grep -q "louna-override.css" "$file"; then
      # Ajouter les liens CSS après la balise head
      sed -i '' 's|<head>|<head>\n  <link rel="stylesheet" href="/css/louna-style.css">\n  <link rel="stylesheet" href="/css/louna-override.css">|g' "$file"
      print_message "Liens CSS ajoutés à: $file"
    else
      print_message "Liens CSS déjà présents dans: $file"
    fi
  fi
done

print_success "Liens CSS ajoutés à toutes les vues."

# Étape 4 : Créer un script JavaScript pour appliquer le style rose dynamiquement
print_message "Étape 4 : Création d'un script JavaScript pour appliquer le style rose dynamiquement..."

cat > "$APP_DIR/public/js/louna-style-applier.js" << 'EOL'
/**
 * Script pour appliquer le style rose à toutes les interfaces Louna
 * Ce script est chargé sur toutes les pages et applique le style rose dynamiquement
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Louna Style Applier loaded');
  
  // Appliquer le style rose à tous les éléments
  function applyRoseStyle() {
    // Définir les couleurs
    const colors = {
      primary: '#ff00ff',
      primaryLight: '#ff66ff',
      primaryDark: '#cc00cc',
      secondary: '#6a0dad',
      secondaryLight: '#9b59b6',
      secondaryDark: '#4a0080',
      accent: '#ff69b4',
      textColor: '#f8f9fa',
      textMuted: '#adb5bd',
      bgColor: '#1a1a2e',
      bgLight: '#16213e',
      bgDark: '#0f172a',
      borderColor: 'rgba(255, 105, 180, 0.2)',
      shadowColor: 'rgba(0, 0, 0, 0.5)'
    };
    
    // Appliquer les couleurs à la barre latérale
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.style.background = `linear-gradient(to bottom, ${colors.secondary}, ${colors.primary})`;
    }
    
    // Appliquer les couleurs aux cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
      card.style.backgroundColor = colors.bgLight;
      card.style.borderColor = colors.borderColor;
      card.style.boxShadow = `0 4px 8px ${colors.shadowColor}`;
      
      // Ajouter un effet de survol
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = `0 8px 16px ${colors.shadowColor}`;
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = `0 4px 8px ${colors.shadowColor}`;
      });
    });
    
    // Appliquer les couleurs aux en-têtes de carte
    const cardHeaders = document.querySelectorAll('.card-header');
    cardHeaders.forEach(header => {
      header.style.backgroundColor = 'rgba(106, 13, 173, 0.2)';
      header.style.borderBottomColor = colors.borderColor;
    });
    
    // Appliquer les couleurs aux boutons
    const primaryButtons = document.querySelectorAll('.btn-primary');
    primaryButtons.forEach(button => {
      button.style.backgroundColor = colors.primary;
      button.style.borderColor = colors.primary;
    });
    
    const secondaryButtons = document.querySelectorAll('.btn-secondary');
    secondaryButtons.forEach(button => {
      button.style.backgroundColor = colors.secondary;
      button.style.borderColor = colors.secondary;
    });
    
    // Appliquer les couleurs aux liens de navigation
    const navLinks = document.querySelectorAll('.sidebar-nav a');
    navLinks.forEach(link => {
      link.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        this.style.borderLeft = `3px solid ${colors.accent}`;
      });
      
      link.addEventListener('mouseleave', function() {
        if (!this.classList.contains('active')) {
          this.style.backgroundColor = 'transparent';
          this.style.borderLeft = '3px solid transparent';
        }
      });
    });
    
    console.log('Rose style applied to all elements');
  }
  
  // Appliquer le style rose
  applyRoseStyle();
  
  // Réappliquer le style rose lorsque le DOM change
  const observer = new MutationObserver(function(mutations) {
    applyRoseStyle();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});
EOL

print_success "Script JavaScript créé: $APP_DIR/public/js/louna-style-applier.js"

# Étape 5 : Ajouter le script JavaScript à toutes les vues
print_message "Étape 5 : Ajout du script JavaScript à toutes les vues..."

# Ajouter le script JavaScript à chaque fichier EJS
for file in $EJS_FILES; do
  # Vérifier si le fichier contient une balise body
  if grep -q "</body>" "$file"; then
    # Sauvegarder une copie du fichier original si ce n'est pas déjà fait
    if [ ! -f "$file.bak" ]; then
      cp "$file" "$file.bak"
    fi
    
    # Vérifier si le script JavaScript est déjà présent
    if ! grep -q "louna-style-applier.js" "$file"; then
      # Ajouter le script JavaScript avant la fermeture de la balise body
      sed -i '' 's|</body>|  <script src="/js/louna-style-applier.js"></script>\n</body>|g' "$file"
      print_message "Script JavaScript ajouté à: $file"
    else
      print_message "Script JavaScript déjà présent dans: $file"
    fi
  fi
done

print_success "Script JavaScript ajouté à toutes les vues."

# Étape 6 : Redémarrer le serveur
print_message "Étape 6 : Redémarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/luna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/luna"

print_success "Uniformisation de l'interface Louna terminée !"
print_message "Toutes les interfaces ont maintenant le même style rose."
