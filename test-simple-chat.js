/**
 * Test simple pour vérifier le chat sans recherche Internet
 */

const axios = require('axios');

async function testSimpleChat() {
    console.log('🚀 Test simple du chat sans recherche Internet...\n');
    
    try {
        // Test avec une question simple qui ne nécessite pas Internet
        console.log('📝 Envoi d\'une question simple...');
        
        const chatResponse = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour, comment allez-vous ?',
            useInternet: false
        }, {
            timeout: 10000, // Timeout plus court
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (chatResponse.status === 200 && chatResponse.data.success) {
            console.log('✅ Chat simple fonctionne !');
            console.log('📄 Réponse de l\'agent:');
            console.log(chatResponse.data.response);
            
            // Maintenant tester avec une question qui pourrait nécessiter Internet
            console.log('\n📝 Test avec une question plus complexe...');
            
            const complexResponse = await axios.post('http://localhost:3007/api/chat/message', {
                message: 'Quelle est la capitale de la France ?',
                useInternet: false
            }, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (complexResponse.status === 200 && complexResponse.data.success) {
                console.log('✅ Question complexe réussie !');
                console.log('📄 Réponse:');
                console.log(complexResponse.data.response);
                return true;
            }
            
        } else {
            console.log('❌ Erreur dans la réponse:', chatResponse.data);
            return false;
        }
        
    } catch (error) {
        console.log('❌ Erreur lors du test:', error.message);
        
        if (error.response) {
            console.log('📄 Détails de l\'erreur:', error.response.data);
        }
        
        return false;
    }
}

async function testInternetQuestion() {
    console.log('\n🌐 Test avec une question nécessitant Internet...');
    
    try {
        const internetResponse = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Quelles sont les dernières nouvelles en intelligence artificielle ?',
            useInternet: true
        }, {
            timeout: 15000, // Timeout plus long pour Internet
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (internetResponse.status === 200 && internetResponse.data.success) {
            console.log('✅ Question Internet réussie !');
            console.log('📄 Réponse:');
            console.log(internetResponse.data.response.substring(0, 500) + '...');
            
            // Vérifier si la réponse contient des informations d'Internet
            const response = internetResponse.data.response.toLowerCase();
            const internetIndicators = [
                'recherche internet',
                'informations récentes',
                'web',
                'source:',
                'lien:',
                'url',
                '2024',
                'actualité',
                'récent'
            ];
            
            const foundIndicators = internetIndicators.filter(indicator => 
                response.includes(indicator)
            );
            
            if (foundIndicators.length > 0) {
                console.log('✅ L\'agent a utilisé Internet ! Indicateurs:', foundIndicators);
                return true;
            } else {
                console.log('⚠️ Réponse obtenue mais pas d\'indication claire d\'Internet');
                return false;
            }
            
        } else {
            console.log('❌ Erreur dans la réponse Internet:', internetResponse.data);
            return false;
        }
        
    } catch (error) {
        console.log('❌ Erreur lors du test Internet:', error.message);
        
        if (error.code === 'ECONNABORTED') {
            console.log('⏰ Timeout - L\'agent prend trop de temps à répondre');
        }
        
        return false;
    }
}

async function runTests() {
    console.log('='.repeat(60));
    console.log('🧪 TESTS SIMPLES DU CHAT LOUNA');
    console.log('='.repeat(60));
    
    const results = {
        simpleChat: false,
        internetChat: false
    };
    
    // Test 1: Chat simple
    results.simpleChat = await testSimpleChat();
    
    // Test 2: Chat avec Internet
    results.internetChat = await testInternetQuestion();
    
    // Résultats
    console.log('\n' + '='.repeat(60));
    console.log('📊 RÉSULTATS');
    console.log('='.repeat(60));
    
    console.log(`💬 Chat simple: ${results.simpleChat ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`🌐 Chat Internet: ${results.internetChat ? '✅ OK' : '❌ ÉCHEC'}`);
    
    if (results.simpleChat && results.internetChat) {
        console.log('\n🎉 SUCCÈS COMPLET ! L\'agent peut chatter et accéder à Internet !');
    } else if (results.simpleChat) {
        console.log('\n⚠️ Chat fonctionne mais problème avec Internet');
        console.log('💡 L\'agent peut répondre mais l\'accès Internet est lent ou défaillant');
    } else {
        console.log('\n❌ Problème avec le chat de base');
        console.log('💡 Vérifiez que l\'agent est correctement configuré');
    }
    
    console.log('='.repeat(60));
}

// Exécuter les tests
if (require.main === module) {
    runTests().catch(error => {
        console.error('❌ Erreur lors des tests:', error);
        process.exit(1);
    });
}

module.exports = { testSimpleChat, testInternetQuestion };
