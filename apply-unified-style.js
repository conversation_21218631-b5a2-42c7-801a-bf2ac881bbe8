/**
 * Script pour appliquer le style unifié à toutes les pages HTML
 */

const fs = require('fs');
const path = require('path');

// Pages à traiter
const pages = [
    'qi-neuron-monitor.html',
    'kyber-dashboard.html',
    'generation-studio.html',
    'ltx-video.html',
    'code-editor.html',
    'performance.html',
    'presentation.html',
    'memory-fusion.html'
];

// Template d'en-tête unifié
function createUnifiedHeader(title, subtitle, icon) {
    return `
    <!-- En-tête unifié -->
    <div class="unified-container">
        <div class="unified-header">
            <h1><i class="${icon}"></i> ${title}</h1>
            <p class="subtitle">${subtitle}</p>
        </div>

        <!-- Navigation unifiée -->
        <div class="unified-nav">
            <div class="nav-buttons">
                <a href="/chat" class="unified-button nav-button">
                    <i class="fas fa-comments"></i> Chat Intelligent
                </a>
                <a href="/futuristic-interface.html" class="unified-button nav-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="unified-button nav-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="unified-button nav-button">
                    <i class="fas fa-yin-yang"></i> Monitoring QI
                </a>
                <a href="/generation-studio.html" class="unified-button nav-button">
                    <i class="fas fa-magic"></i> Studio Génération
                </a>
                <a href="/kyber-dashboard.html" class="unified-button nav-button">
                    <i class="fas fa-bolt"></i> Accélérateurs Kyber
                </a>
            </div>
        </div>
    `;
}

// Configuration des pages
const pageConfigs = {
    'qi-neuron-monitor.html': {
        title: 'Monitoring QI & Neurones',
        subtitle: 'Surveillance en temps réel de l\'énergie vitale et des connexions neuronales',
        icon: 'fas fa-yin-yang'
    },
    'kyber-dashboard.html': {
        title: 'Accélérateurs Kyber',
        subtitle: 'Optimisation des performances avec les accélérateurs quantiques',
        icon: 'fas fa-bolt'
    },
    'generation-studio.html': {
        title: 'Studio de Génération',
        subtitle: 'Création illimitée de contenu multimédia haute qualité',
        icon: 'fas fa-magic'
    },
    'ltx-video.html': {
        title: 'LTX Video',
        subtitle: 'Génération et traitement vidéo avancé avec IA',
        icon: 'fas fa-video'
    },
    'code-editor.html': {
        title: 'Éditeur de Code',
        subtitle: 'Environnement de développement intégré avec assistance IA',
        icon: 'fas fa-code'
    },
    'performance.html': {
        title: 'Performances Système',
        subtitle: 'Monitoring et optimisation des performances en temps réel',
        icon: 'fas fa-chart-line'
    },
    'presentation.html': {
        title: 'Présentation Louna',
        subtitle: 'Découvrez toutes les capacités de l\'agent intelligent',
        icon: 'fas fa-presentation'
    },
    'memory-fusion.html': {
        title: 'Fusion Mémoire',
        subtitle: 'Intégration et synchronisation des systèmes de mémoire',
        icon: 'fas fa-code-branch'
    }
};

function applyUnifiedStyle(filename) {
    const filePath = path.join(__dirname, 'public', filename);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ Fichier non trouvé: ${filename}`);
        return false;
    }

    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const config = pageConfigs[filename];
        
        if (!config) {
            console.log(`❌ Configuration manquante pour: ${filename}`);
            return false;
        }

        // Ajouter le CSS unifié s'il n'est pas déjà présent
        if (!content.includes('unified-interface.css')) {
            content = content.replace(
                '</head>',
                '    <link rel="stylesheet" href="/css/unified-interface.css">\n</head>'
            );
        }

        // Chercher la fin de la navigation
        const navEndRegex = /<\/nav>/;
        const navEndMatch = content.match(navEndRegex);
        
        if (navEndMatch) {
            const navEndIndex = content.indexOf('</nav>') + 6;
            
            // Vérifier si l'en-tête unifié existe déjà
            if (!content.includes('unified-header')) {
                const beforeNav = content.substring(0, navEndIndex);
                const afterNav = content.substring(navEndIndex);
                
                // Insérer l'en-tête unifié
                const unifiedHeader = createUnifiedHeader(config.title, config.subtitle, config.icon);
                content = beforeNav + '\n' + unifiedHeader + '\n' + afterNav;
            }
        }

        // Remplacer les conteneurs principaux par des conteneurs unifiés
        content = content.replace(
            /<div class="main-container"[^>]*>/g,
            '<div class="unified-container">'
        );

        // Remplacer les cartes par des cartes unifiées
        content = content.replace(
            /<div class="([^"]*card[^"]*)">/g,
            '<div class="unified-card">'
        );

        // Remplacer les boutons par des boutons unifiés
        content = content.replace(
            /<button class="([^"]*btn[^"]*)"([^>]*)>/g,
            '<button class="unified-button"$2>'
        );

        // Sauvegarder le fichier modifié
        fs.writeFileSync(filePath, content);
        console.log(`✅ Style unifié appliqué à: ${filename}`);
        return true;
        
    } catch (error) {
        console.error(`❌ Erreur lors du traitement de ${filename}:`, error.message);
        return false;
    }
}

// Appliquer le style à toutes les pages
console.log('🎨 Application du style unifié à toutes les pages...\n');

let successCount = 0;
let totalCount = pages.length;

pages.forEach(page => {
    if (applyUnifiedStyle(page)) {
        successCount++;
    }
});

console.log(`\n📊 Résultat: ${successCount}/${totalCount} pages traitées avec succès`);

if (successCount === totalCount) {
    console.log('🎉 Tous les styles ont été appliqués avec succès !');
    console.log('✨ Toutes les interfaces ont maintenant une cohérence visuelle parfaite !');
} else {
    console.log('⚠️ Certaines pages nécessitent une attention manuelle.');
}

module.exports = { applyUnifiedStyle, createUnifiedHeader, pageConfigs };
