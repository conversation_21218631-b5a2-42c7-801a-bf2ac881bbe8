#!/bin/bash

# Script pour démarrer l'agent Claude et tester sa fonctionnalité

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction pour afficher des messages colorés
print_message() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Vérifier si Node.js est installé
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js n'est pas installé"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    print_success "Node.js détecté: $NODE_VERSION"
}

# Vérifier si Ollama est installé et démarré
check_ollama() {
    if ! command -v ollama &> /dev/null; then
        print_error "Ollama n'est pas installé"
        print_message "Installez Ollama depuis: https://ollama.ai"
        exit 1
    fi
    
    print_success "Ollama détecté"
    
    # Vérifier si Ollama est démarré
    if ! curl -s http://localhost:11434/api/tags &> /dev/null; then
        print_warning "Ollama n'est pas démarré. Démarrage en cours..."
        ollama serve &
        sleep 5
    fi
    
    print_success "Ollama est en cours d'exécution"
}

# Vérifier et installer le modèle Claude
check_claude_model() {
    print_message "Vérification du modèle Claude..."
    
    if ! ollama list | grep -q "incept5/llama3.1-claude"; then
        print_warning "Modèle Claude non trouvé. Installation en cours..."
        print_message "Cela peut prendre plusieurs minutes..."
        
        ollama pull incept5/llama3.1-claude:latest
        
        if [ $? -eq 0 ]; then
            print_success "Modèle Claude installé avec succès"
        else
            print_error "Échec de l'installation du modèle Claude"
            exit 1
        fi
    else
        print_success "Modèle Claude déjà disponible"
    fi
}

# Installer les dépendances npm si nécessaire
install_dependencies() {
    if [ ! -d "node_modules" ]; then
        print_message "Installation des dépendances npm..."
        npm install
        print_success "Dépendances installées"
    else
        print_success "Dépendances déjà installées"
    fi
}

# Vérifier la configuration de l'agent
check_agent_config() {
    print_message "Vérification de la configuration de l'agent..."
    
    if [ ! -f "data/config/agents.json" ]; then
        print_warning "Fichier de configuration des agents manquant"
        
        # Créer le dossier de configuration
        mkdir -p data/config
        
        # Créer la configuration de l'agent Claude
        cat > data/config/agents.json <<EOF
{
  "defaultAgent": "agent_claude",
  "agents": {
    "agent_claude": {
      "id": "agent_claude",
      "name": "Agent Claude (4GB)",
      "type": "ollama",
      "model": "incept5/llama3.1-claude:latest",
      "description": "Agent principal basé sur le modèle Llama 3.1 Claude (4GB). Ce modèle offre des performances similaires à Claude avec une excellente compréhension et génération de texte.",
      "temperature": 0.7,
      "maxTokens": 2000,
      "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
      "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
      "isMainAgent": true,
      "memoryPriority": "high"
    }
  }
}
EOF
        print_success "Configuration de l'agent créée"
    else
        print_success "Configuration de l'agent trouvée"
    fi
}

# Démarrer l'application
start_application() {
    print_message "Démarrage de l'application Louna..."
    
    # Tuer les processus existants sur le port 3004
    if lsof -ti:3004 &> /dev/null; then
        print_warning "Port 3004 occupé. Arrêt des processus existants..."
        kill -9 $(lsof -ti:3004) 2>/dev/null || true
        sleep 2
    fi
    
    # Démarrer l'application en arrière-plan
    npm start &
    APP_PID=$!
    
    print_message "Application démarrée (PID: $APP_PID)"
    print_message "Attente du démarrage complet..."
    
    # Attendre que l'application soit prête
    for i in {1..30}; do
        if curl -s http://localhost:3004 &> /dev/null; then
            print_success "Application prête sur http://localhost:3004"
            return 0
        fi
        sleep 1
    done
    
    print_error "L'application n'a pas pu démarrer dans les temps"
    kill $APP_PID 2>/dev/null || true
    exit 1
}

# Tester l'agent Claude
test_claude_agent() {
    print_message "Lancement des tests de l'agent Claude..."
    sleep 3
    
    node test-claude-agent.js
    TEST_RESULT=$?
    
    if [ $TEST_RESULT -eq 0 ]; then
        print_success "Tous les tests sont passés avec succès !"
        return 0
    else
        print_error "Certains tests ont échoué"
        return 1
    fi
}

# Fonction principale
main() {
    print_header "DÉMARRAGE ET TEST DE L'AGENT CLAUDE 4GB"
    
    # Vérifications préliminaires
    check_nodejs
    check_ollama
    check_claude_model
    install_dependencies
    check_agent_config
    
    # Démarrage de l'application
    start_application
    
    # Tests de l'agent
    if test_claude_agent; then
        print_header "🎉 AGENT CLAUDE OPÉRATIONNEL !"
        print_success "Votre agent Claude 4GB fonctionne parfaitement"
        print_message "Accédez à l'interface: http://localhost:3004"
        print_message "Appuyez sur Ctrl+C pour arrêter l'application"
        
        # Garder l'application en cours d'exécution
        wait
    else
        print_header "❌ PROBLÈMES DÉTECTÉS"
        print_error "L'agent Claude ne fonctionne pas correctement"
        print_message "Vérifiez les logs ci-dessus pour plus de détails"
        
        # Arrêter l'application
        if [ ! -z "$APP_PID" ]; then
            kill $APP_PID 2>/dev/null || true
        fi
        exit 1
    fi
}

# Gestion de l'interruption (Ctrl+C)
trap 'print_message "Arrêt de l'\''application..."; kill $APP_PID 2>/dev/null || true; exit 0' INT

# Lancer le script principal
main "$@"
