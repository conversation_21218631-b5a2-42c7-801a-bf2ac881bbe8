/**
 * TEST DU SYSTÈME DE MONITORING ULTRA-INTELLIGENT
 * 
 * Teste toutes les fonctionnalités du monitoring en temps réel :
 * - Métriques système (CPU, mémoire, GPU)
 * - Détection des tâches spécialisées (vidéo, 3D)
 * - Goulots d'étranglement
 * - Prédictions de besoins futurs
 * - Optimisations automatiques
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
    white: '\x1b[37m',
    reset: '\x1b[0m',
    bold: '\x1b[1m',
    bright: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logTitle(message) {
    log(`\n🔍 ${message}`, 'bright');
    log('=' .repeat(60), 'cyan');
}

async function testAPI(endpoint, method = 'GET', data = null, timeout = 10000) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            timeout
        };
        
        if (data) {
            config.data = data;
            config.headers = { 'Content-Type': 'application/json' };
        }
        
        const startTime = Date.now();
        const response = await axios(config);
        const latency = Date.now() - startTime;
        
        return {
            success: true,
            data: response.data,
            latency,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            latency: 0,
            status: error.response?.status || 0
        };
    }
}

async function testUltraMonitoringMetrics() {
    logTitle('TEST DES MÉTRIQUES ULTRA-MONITORING');
    
    const result = await testAPI('/api/ultra-monitor/metrics');
    
    if (result.success) {
        logSuccess(`Métriques récupérées en ${result.latency}ms`);
        
        const data = result.data.data;
        
        // Métriques système
        log(`\n🖥️  MÉTRIQUES SYSTÈME:`, 'magenta');
        log(`   💻 CPU: ${data.system.cpu.usage.toFixed(1)}%`, 'cyan');
        log(`   🧠 Mémoire: ${data.system.memory.usage.toFixed(1)}%`, 'cyan');
        log(`   🎮 GPU: ${data.system.gpu.usage.toFixed(1)}%`, 'cyan');
        log(`   💾 Disque: ${data.system.disk.usage.toFixed(1)}%`, 'cyan');
        
        // Tâches spécialisées
        log(`\n🎯 TÂCHES SPÉCIALISÉES:`, 'magenta');
        log(`   🎥 Vidéo: ${data.tasks.video.active ? 'ACTIVE' : 'INACTIVE'}`, 
            data.tasks.video.active ? 'green' : 'yellow');
        if (data.tasks.video.active) {
            log(`      📊 FPS: ${data.tasks.video.fps.toFixed(1)}`, 'cyan');
            log(`      ⚡ Latence: ${data.tasks.video.latency.toFixed(1)}ms`, 'cyan');
        }
        
        log(`   🎮 Rendu 3D: ${data.tasks.render3d.active ? 'ACTIVE' : 'INACTIVE'}`, 
            data.tasks.render3d.active ? 'green' : 'yellow');
        if (data.tasks.render3d.active) {
            log(`      📊 FPS: ${data.tasks.render3d.fps.toFixed(1)}`, 'cyan');
            log(`      🔥 Complexité: ${data.tasks.render3d.complexity}`, 'cyan');
        }
        
        log(`   🧠 IA: ${data.tasks.ai.processing ? 'TRAITEMENT' : 'INACTIF'}`, 
            data.tasks.ai.processing ? 'green' : 'yellow');
        
        // Performance globale
        log(`\n📊 PERFORMANCE GLOBALE:`, 'magenta');
        log(`   🎯 Score: ${data.performance.overall.toFixed(1)}%`, 
            data.performance.overall > 70 ? 'green' : 
            data.performance.overall > 50 ? 'yellow' : 'red');
        log(`   🔍 Goulots: ${data.performance.bottlenecks.length}`, 
            data.performance.bottlenecks.length === 0 ? 'green' : 'yellow');
        log(`   🔮 Prédictions: ${data.performance.predictions.length}`, 'cyan');
        
        // Monitoring
        log(`\n⏱️  MONITORING:`, 'magenta');
        log(`   🟢 Actif: ${data.monitoring.active ? 'OUI' : 'NON'}`, 
            data.monitoring.active ? 'green' : 'red');
        log(`   ⏰ Uptime: ${Math.round(data.monitoring.uptime / 1000)}s`, 'cyan');
        log(`   📈 Échantillons: ${data.monitoring.samples}`, 'cyan');
        
        return true;
    } else {
        logError(`Échec récupération métriques: ${result.error}`);
        return false;
    }
}

async function testBottleneckDetection() {
    logTitle('TEST DE DÉTECTION DES GOULOTS D\'ÉTRANGLEMENT');
    
    const result = await testAPI('/api/ultra-monitor/bottlenecks');
    
    if (result.success) {
        logSuccess(`Goulots analysés en ${result.latency}ms`);
        
        const data = result.data.data;
        
        log(`\n🔍 ANALYSE DES GOULOTS:`, 'magenta');
        log(`   📊 Total: ${data.count}`, 'cyan');
        log(`   🔴 Critiques: ${data.critical}`, data.critical > 0 ? 'red' : 'green');
        log(`   🟡 Avertissements: ${data.warnings}`, data.warnings > 0 ? 'yellow' : 'green');
        
        if (data.bottlenecks.length > 0) {
            log(`\n🚨 GOULOTS DÉTECTÉS:`, 'yellow');
            data.bottlenecks.forEach((bottleneck, index) => {
                const severityColor = bottleneck.severity === 'critical' ? 'red' : 
                                    bottleneck.severity === 'warning' ? 'yellow' : 'cyan';
                log(`   ${index + 1}. ${bottleneck.type.toUpperCase()}`, severityColor);
                log(`      🎯 Sévérité: ${bottleneck.severity}`, severityColor);
                log(`      💥 Impact: ${bottleneck.impact}`, 'cyan');
                log(`      💡 Recommandation: ${bottleneck.recommendation}`, 'cyan');
            });
        } else {
            logSuccess('Aucun goulot d\'étranglement détecté !');
        }
        
        return true;
    } else {
        logError(`Échec détection goulots: ${result.error}`);
        return false;
    }
}

async function testPredictions() {
    logTitle('TEST DES PRÉDICTIONS DE BESOINS FUTURS');
    
    const result = await testAPI('/api/ultra-monitor/predictions');
    
    if (result.success) {
        logSuccess(`Prédictions analysées en ${result.latency}ms`);
        
        const data = result.data.data;
        
        log(`\n🔮 PRÉDICTIONS:`, 'magenta');
        log(`   📊 Total: ${data.count}`, 'cyan');
        log(`   🎯 Haute probabilité: ${data.highProbability}`, 'cyan');
        
        if (data.predictions.length > 0) {
            log(`\n🔮 BESOINS FUTURS PRÉDITS:`, 'blue');
            data.predictions.forEach((prediction, index) => {
                const probabilityColor = prediction.probability > 0.8 ? 'green' : 
                                       prediction.probability > 0.6 ? 'yellow' : 'cyan';
                log(`   ${index + 1}. ${prediction.type.toUpperCase()}`, 'cyan');
                log(`      ⏰ Délai: ${prediction.timeframe}`, 'cyan');
                log(`      🎯 Probabilité: ${(prediction.probability * 100).toFixed(1)}%`, probabilityColor);
                log(`      🚀 Action: ${prediction.action}`, 'cyan');
            });
        } else {
            logInfo('Aucun besoin futur prédit pour le moment');
        }
        
        return true;
    } else {
        logError(`Échec prédictions: ${result.error}`);
        return false;
    }
}

async function testSpecializedMetrics() {
    logTitle('TEST DES MÉTRIQUES SPÉCIALISÉES');
    
    const result = await testAPI('/api/ultra-monitor/specialized-metrics');
    
    if (result.success) {
        logSuccess(`Métriques spécialisées récupérées en ${result.latency}ms`);
        
        const data = result.data.data;
        
        // Métriques vidéo
        log(`\n🎥 MÉTRIQUES VIDÉO:`, 'magenta');
        log(`   🟢 Active: ${data.video.active ? 'OUI' : 'NON'}`, 
            data.video.active ? 'green' : 'yellow');
        log(`   📊 FPS: ${data.video.fps.toFixed(1)}`, 'cyan');
        log(`   ⚡ Latence: ${data.video.latency.toFixed(1)}ms`, 'cyan');
        log(`   🎯 Qualité: ${data.video.quality}`, 'cyan');
        log(`   📡 Codec: ${data.video.codec}`, 'cyan');
        
        // Métriques 3D
        log(`\n🎮 MÉTRIQUES RENDU 3D:`, 'magenta');
        log(`   🟢 Actif: ${data.render3d.active ? 'OUI' : 'NON'}`, 
            data.render3d.active ? 'green' : 'yellow');
        log(`   📊 FPS: ${data.render3d.fps.toFixed(1)}`, 'cyan');
        log(`   🔺 Triangles: ${data.render3d.triangles}`, 'cyan');
        log(`   🖼️  Textures: ${data.render3d.textures}`, 'cyan');
        log(`   🎨 Shaders: ${data.render3d.shaders}`, 'cyan');
        log(`   🔥 Complexité: ${data.render3d.complexity}`, 'cyan');
        
        // Métriques mémoire thermique
        log(`\n🧠 MÉMOIRE THERMIQUE:`, 'magenta');
        log(`   🌡️  Température: ${data.memory.thermal.temperature.toFixed(1)}°`, 'cyan');
        log(`   ⚡ Efficacité: ${data.memory.thermal.efficiency.toFixed(1)}%`, 'cyan');
        log(`   🔄 Transferts: ${data.memory.thermal.transfers}`, 'cyan');
        log(`   📦 Compression: ${data.memory.thermal.compressionRatio.toFixed(1)}%`, 'cyan');
        
        // Cache
        log(`\n💾 CACHE:`, 'magenta');
        log(`   🎯 Taux de hit: ${data.memory.cache.hitRate.toFixed(1)}%`, 'cyan');
        log(`   📊 Taille: ${data.memory.cache.size}`, 'cyan');
        log(`   ⚡ Efficacité: ${data.memory.cache.efficiency.toFixed(1)}%`, 'cyan');
        
        // IA
        log(`\n🤖 INTELLIGENCE ARTIFICIELLE:`, 'magenta');
        log(`   🟢 Traitement: ${data.ai.processing ? 'OUI' : 'NON'}`, 
            data.ai.processing ? 'green' : 'yellow');
        log(`   📊 Tokens/sec: ${data.ai.tokensPerSecond.toFixed(1)}`, 'cyan');
        log(`   🔥 Complexité: ${data.ai.complexity}`, 'cyan');
        log(`   📏 Taille modèle: ${data.ai.modelSize}`, 'cyan');
        
        return true;
    } else {
        logError(`Échec métriques spécialisées: ${result.error}`);
        return false;
    }
}

async function testDeepAnalysis() {
    logTitle('TEST DE L\'ANALYSE APPROFONDIE');
    
    logInfo('Déclenchement d\'une analyse approfondie...');
    const result = await testAPI('/api/ultra-monitor/deep-analysis', 'POST');
    
    if (result.success) {
        logSuccess(`Analyse approfondie déclenchée en ${result.latency}ms`);
        
        const data = result.data.data;
        log(`📝 Message: ${data.message}`, 'green');
        log(`⏳ Statut: ${data.status}`, 'cyan');
        
        // Attendre un peu puis vérifier les résultats
        logInfo('Attente des résultats de l\'analyse...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Récupérer les métriques mises à jour
        const metricsResult = await testAPI('/api/ultra-monitor/metrics');
        if (metricsResult.success) {
            const metrics = metricsResult.data.data;
            log(`\n📊 RÉSULTATS DE L'ANALYSE:`, 'magenta');
            log(`   🎯 Performance: ${metrics.performance.overall.toFixed(1)}%`, 'cyan');
            log(`   🔍 Nouveaux goulots: ${metrics.performance.bottlenecks.length}`, 'cyan');
            log(`   🔮 Nouvelles prédictions: ${metrics.performance.predictions.length}`, 'cyan');
        }
        
        return true;
    } else {
        logError(`Échec analyse approfondie: ${result.error}`);
        return false;
    }
}

async function testHealthReport() {
    logTitle('TEST DU RAPPORT DE SANTÉ SYSTÈME');
    
    const result = await testAPI('/api/ultra-monitor/health-report');
    
    if (result.success) {
        logSuccess(`Rapport de santé généré en ${result.latency}ms`);
        
        const data = result.data.data;
        const health = data.health;
        
        log(`\n🏥 RAPPORT DE SANTÉ SYSTÈME:`, 'magenta');
        
        // Santé globale
        log(`\n📊 SANTÉ GLOBALE: ${health.overall.toFixed(1)}%`, 
            health.overall > 70 ? 'green' : health.overall > 50 ? 'yellow' : 'red');
        
        // CPU
        const cpuColor = health.cpu.status === 'healthy' ? 'green' : 
                        health.cpu.status === 'warning' ? 'yellow' : 'red';
        log(`   💻 CPU: ${health.cpu.status.toUpperCase()} (${health.cpu.usage.toFixed(1)}%)`, cpuColor);
        
        // Mémoire
        const memoryColor = health.memory.status === 'healthy' ? 'green' : 
                           health.memory.status === 'warning' ? 'yellow' : 'red';
        log(`   🧠 Mémoire: ${health.memory.status.toUpperCase()} (${health.memory.usage.toFixed(1)}%)`, memoryColor);
        
        // Tâches
        log(`\n🎯 ÉTAT DES TÂCHES:`, 'magenta');
        log(`   🎥 Vidéo: ${health.tasks.video.toUpperCase()}`, 
            health.tasks.video === 'active' ? 'green' : 'yellow');
        log(`   🎮 Rendu 3D: ${health.tasks.render3d.toUpperCase()}`, 
            health.tasks.render3d === 'active' ? 'green' : 'yellow');
        log(`   🤖 IA: ${health.tasks.ai.toUpperCase()}`, 
            health.tasks.ai === 'processing' ? 'green' : 'yellow');
        
        // Statistiques
        log(`\n📈 STATISTIQUES:`, 'magenta');
        log(`   🔍 Goulots: ${health.bottlenecks}`, 
            health.bottlenecks === 0 ? 'green' : 'yellow');
        log(`   🔮 Prédictions: ${health.predictions}`, 'cyan');
        log(`   ⏰ Uptime: ${Math.round(health.uptime / 1000)}s`, 'cyan');
        log(`   📊 Échantillons: ${health.samples}`, 'cyan');
        
        // Recommandations
        if (data.recommendations && data.recommendations.length > 0) {
            log(`\n💡 RECOMMANDATIONS:`, 'yellow');
            data.recommendations.forEach((rec, index) => {
                const priorityColor = rec.priority === 'high' ? 'red' : 
                                    rec.priority === 'medium' ? 'yellow' : 'cyan';
                log(`   ${index + 1}. [${rec.type.toUpperCase()}] ${rec.action}`, priorityColor);
                log(`      💥 Impact: ${rec.impact}`, 'cyan');
            });
        } else {
            logSuccess('Aucune recommandation - Système optimal !');
        }
        
        return true;
    } else {
        logError(`Échec rapport de santé: ${result.error}`);
        return false;
    }
}

async function runUltraMonitoringTests() {
    log('🔍 TESTS DU SYSTÈME DE MONITORING ULTRA-INTELLIGENT', 'bright');
    log('=' .repeat(80), 'cyan');
    log('🎯 Objectif: Vérifier le monitoring en temps réel des performances', 'blue');
    log('⚡ Focus: Métriques, goulots, prédictions, optimisations', 'blue');
    
    const tests = [
        { name: 'Métriques ultra-monitoring', fn: testUltraMonitoringMetrics },
        { name: 'Détection des goulots', fn: testBottleneckDetection },
        { name: 'Prédictions de besoins', fn: testPredictions },
        { name: 'Métriques spécialisées', fn: testSpecializedMetrics },
        { name: 'Analyse approfondie', fn: testDeepAnalysis },
        { name: 'Rapport de santé système', fn: testHealthReport }
    ];
    
    let successCount = 0;
    const results = [];
    
    for (const test of tests) {
        try {
            const startTime = Date.now();
            const success = await test.fn();
            const duration = Date.now() - startTime;
            
            results.push({
                name: test.name,
                success,
                duration
            });
            
            if (success) {
                successCount++;
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 1500));
            
        } catch (error) {
            logError(`Erreur lors du test "${test.name}": ${error.message}`);
            results.push({
                name: test.name,
                success: false,
                duration: 0,
                error: error.message
            });
        }
    }
    
    // Résumé final
    logTitle('RÉSUMÉ DES TESTS DE MONITORING ULTRA-INTELLIGENT');
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
        log(`${status} ${result.name}${duration}`, result.success ? 'green' : 'red');
        
        if (result.error) {
            log(`   └─ Erreur: ${result.error}`, 'red');
        }
    });
    
    const successRate = (successCount / tests.length) * 100;
    log(`\n🎯 RÉSULTAT GLOBAL: ${successCount}/${tests.length} tests réussis (${successRate.toFixed(1)}%)`, 
        successRate >= 85 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
    
    if (successRate >= 85) {
        logSuccess('🎉 SYSTÈME DE MONITORING ULTRA-INTELLIGENT OPÉRATIONNEL !');
        log('✨ Surveillance en temps réel des performances activée', 'green');
        log('🔍 Détection automatique des goulots d\'étranglement', 'green');
        log('🔮 Prédictions de besoins futurs fonctionnelles', 'green');
        log('🎯 Métriques spécialisées pour vidéo/3D/mémoire', 'green');
        log('🏥 Rapports de santé système complets', 'green');
    } else if (successRate >= 70) {
        logWarning('⚠️  Système partiellement opérationnel - Optimisations nécessaires');
    } else {
        logError('❌ Système nécessite des corrections majeures');
    }
    
    log('\n🔗 APIs de monitoring: http://localhost:3000/api/ultra-monitor/*', 'cyan');
    log('🏠 Page d\'accueil: http://localhost:3000/', 'cyan');
}

// Exécuter les tests
if (require.main === module) {
    runUltraMonitoringTests().catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    testUltraMonitoringMetrics,
    testBottleneckDetection,
    testPredictions,
    testSpecializedMetrics,
    testDeepAnalysis,
    testHealthReport,
    runUltraMonitoringTests
};
