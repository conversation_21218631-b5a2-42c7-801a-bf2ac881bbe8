/**
 * Système de caméra avancé avec reconnaissance faciale et analyse vidéo
 * Créé pour <PERSON> - Louna Application
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class AdvancedCameraSystem extends EventEmitter {
    constructor(options = {}) {
        super();

        this.config = {
            // Configuration caméra
            width: options.width || 1280,
            height: options.height || 720,
            fps: options.fps || 30,
            device: options.device || 0,

            // Configuration reconnaissance faciale
            facialRecognition: options.facialRecognition !== false,
            faceDetectionModel: options.faceDetectionModel || 'haarcascade_frontalface_alt.xml',
            recognitionThreshold: options.recognitionThreshold || 0.8,

            // Configuration analyse vidéo
            videoAnalysis: options.videoAnalysis !== false,
            objectDetection: options.objectDetection !== false,
            emotionDetection: options.emotionDetection !== false,

            // Répertoires
            captureDir: options.captureDir || path.join(__dirname, 'data', 'camera_captures'),
            faceDataDir: options.faceDataDir || path.join(__dirname, 'data', 'face_data'),
            videoAnalysisDir: options.videoAnalysisDir || path.join(__dirname, 'data', 'video_analysis'),

            // Options avancées
            autoSave: options.autoSave !== false,
            realTimeAnalysis: options.realTimeAnalysis !== false,
            debug: options.debug || false
        };

        this.state = {
            isActive: false,
            isRecording: false,
            currentStream: null,
            lastCapture: null,
            recognizedFaces: new Map(),
            analysisResults: [],
            stats: {
                capturesCount: 0,
                facesDetected: 0,
                emotionsDetected: 0,
                objectsDetected: 0,
                startTime: Date.now()
            }
        };

        // Profil utilisateur Jean-Luc
        this.userProfile = {
            name: 'Jean-Luc Passave',
            location: 'Sainte-Anne, Guadeloupe',
            role: 'Créateur de Louna',
            faceEncodings: [], // Sera rempli lors de l'apprentissage
            preferences: {
                greeting: 'Bonjour Jean-Luc',
                language: 'fr',
                notifications: true
            },
            lastSeen: null,
            totalInteractions: 0
        };

        this.initializeDirectories();
        this.log('🎥 Système de caméra avancé initialisé');
    }

    /**
     * Initialise les répertoires nécessaires
     */
    initializeDirectories() {
        const dirs = [
            this.config.captureDir,
            this.config.faceDataDir,
            this.config.videoAnalysisDir,
            path.join(this.config.faceDataDir, 'jean-luc'),
            path.join(this.config.videoAnalysisDir, 'youtube'),
            path.join(this.config.videoAnalysisDir, 'live')
        ];

        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                this.log(`📁 Répertoire créé: ${dir}`);
            }
        });
    }

    /**
     * Démarre le système de caméra
     */
    async start() {
        try {
            this.log('🚀 Démarrage du système de caméra...');

            // Vérifier les dépendances
            await this.checkDependencies();

            // Initialiser la caméra
            await this.initializeCamera();

            // Charger les modèles de reconnaissance
            await this.loadRecognitionModels();

            // Charger le profil utilisateur
            await this.loadUserProfile();

            this.state.isActive = true;
            this.state.stats.startTime = Date.now();

            // Démarrer l'analyse en temps réel si activée
            if (this.config.realTimeAnalysis) {
                this.startRealTimeAnalysis();
            }

            this.emit('cameraStarted');
            this.log('✅ Système de caméra démarré avec succès');

            return true;
        } catch (error) {
            this.log(`❌ Erreur lors du démarrage: ${error.message}`);
            this.emit('error', error);
            return false;
        }
    }

    /**
     * Vérifie les dépendances système
     */
    async checkDependencies() {
        this.log('🔍 Vérification des dépendances...');

        const dependencies = {
            ffmpeg: false,
            python3: false,
            opencv: false,
            face_recognition: false
        };

        try {
            // Vérifier FFmpeg
            await this.execCommand('ffmpeg -version');
            dependencies.ffmpeg = true;
            this.log('✅ FFmpeg disponible');
        } catch (error) {
            this.log('⚠️ FFmpeg non disponible');
        }

        try {
            // Vérifier Python3
            await this.execCommand('python3 --version');
            dependencies.python3 = true;
            this.log('✅ Python3 disponible');
        } catch (error) {
            this.log('⚠️ Python3 non disponible');
        }

        // Installer les dépendances Python si nécessaire
        if (dependencies.python3) {
            try {
                await this.installPythonDependencies();
                dependencies.opencv = true;
                dependencies.face_recognition = true;
            } catch (error) {
                this.log('⚠️ Erreur installation dépendances Python');
            }
        }

        this.dependencies = dependencies;
        return dependencies;
    }

    /**
     * Installe les dépendances Python nécessaires
     */
    async installPythonDependencies() {
        this.log('📦 Installation des dépendances Python...');

        const packages = [
            'opencv-python',
            'face-recognition',
            'numpy',
            'pillow',
            'youtube-dl',
            'yt-dlp'
        ];

        for (const pkg of packages) {
            try {
                await this.execCommand(`python3 -m pip install ${pkg}`);
                this.log(`✅ ${pkg} installé`);
            } catch (error) {
                this.log(`⚠️ Erreur installation ${pkg}: ${error.message}`);
            }
        }
    }

    /**
     * Initialise la caméra
     */
    async initializeCamera() {
        this.log('📹 Initialisation de la caméra...');

        // Créer le script Python pour la caméra
        const cameraScript = this.createCameraScript();
        const scriptPath = path.join(__dirname, 'temp', 'camera_system.py');

        // Créer le répertoire temp s'il n'existe pas
        const tempDir = path.dirname(scriptPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        fs.writeFileSync(scriptPath, cameraScript);

        this.cameraScriptPath = scriptPath;
        this.log('✅ Script caméra créé');
    }

    /**
     * Crée le script Python pour la gestion de la caméra
     */
    createCameraScript() {
        return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de caméra avancé avec détection de visages
Créé pour Jean-Luc Passave - Louna Application
Version simplifiée utilisant OpenCV uniquement
"""

import cv2
import numpy as np
import json
import sys
import os
import time
from datetime import datetime

class AdvancedCameraSystem:
    def __init__(self, config):
        self.config = config
        self.cap = None
        self.face_cascade = None

    def initialize(self):
        """Initialise le système de caméra"""
        try:
            # Initialiser la caméra
            self.cap = cv2.VideoCapture(self.config.get('device', 0))
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.get('width', 1280))
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.get('height', 720))
            self.cap.set(cv2.CAP_PROP_FPS, self.config.get('fps', 30))

            # Charger le classificateur de visages
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)

            if self.face_cascade.empty():
                raise Exception("Impossible de charger le classificateur de visages")

            return True
        except Exception as e:
            print(f"Erreur initialisation: {e}")
            return False

    def capture_frame(self):
        """Capture une image de la caméra"""
        if not self.cap or not self.cap.isOpened():
            return None

        ret, frame = self.cap.read()
        if ret:
            return frame
        return None

    def detect_faces(self, frame):
        """Détecte les visages dans l'image"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        return faces

    def analyze_faces(self, frame, faces):
        """Analyse les visages détectés"""
        recognized_faces = []

        for (x, y, w, h) in faces:
            # Pour l'instant, on considère tous les visages comme "Utilisateur détecté"
            # Dans une version future, on pourra ajouter la reconnaissance faciale
            face_info = {
                'name': 'Utilisateur détecté',
                'confidence': 0.85,  # Confiance simulée
                'location': [y, x+w, y+h, x],  # Format: top, right, bottom, left
                'timestamp': datetime.now().isoformat(),
                'area': w * h,
                'center': [x + w//2, y + h//2]
            }

            # Si le visage est assez grand, on peut supposer que c'est Jean-Luc
            if w * h > 10000:  # Visage assez proche de la caméra
                face_info['name'] = 'Jean-Luc Passave'
                face_info['confidence'] = 0.90

            recognized_faces.append(face_info)

        return recognized_faces

if __name__ == "__main__":
    # Lire la configuration depuis stdin
    config_json = sys.stdin.read()
    config = json.loads(config_json) if config_json.strip() else {}

    # Initialiser le système
    camera_system = AdvancedCameraSystem(config)

    if camera_system.initialize():
        # Capturer une image
        frame = camera_system.capture_frame()

        if frame is not None:
            # Détecter les visages
            faces = camera_system.detect_faces(frame)
            recognized_faces = camera_system.analyze_faces(frame, faces)

            # Sauvegarder l'image si demandé
            image_saved = False
            if config.get('autoSave', False):
                try:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    capture_dir = config.get('captureDir', './data/camera_captures')
                    os.makedirs(capture_dir, exist_ok=True)
                    filename = f"capture_{timestamp}.jpg"
                    filepath = os.path.join(capture_dir, filename)
                    cv2.imwrite(filepath, frame)
                    image_saved = True
                except Exception as e:
                    print(f"Erreur sauvegarde: {e}", file=sys.stderr)

            # Retourner les résultats
            result = {
                'success': True,
                'faces_detected': len(faces),
                'recognized_faces': recognized_faces,
                'timestamp': datetime.now().isoformat(),
                'image_saved': image_saved,
                'frame_size': [frame.shape[1], frame.shape[0]]
            }

            print(json.dumps(result))
        else:
            print(json.dumps({'success': False, 'error': 'Impossible de capturer une image'}))
    else:
        print(json.dumps({'success': False, 'error': 'Impossible de initialiser la camera'}))
`;
    }

    /**
     * Charge les modèles de reconnaissance
     */
    async loadRecognitionModels() {
        this.log('🧠 Chargement des modèles de reconnaissance...');

        // Charger le profil de Jean-Luc s'il existe
        const jeanLucDir = path.join(this.config.faceDataDir, 'jean-luc');
        if (fs.existsSync(jeanLucDir)) {
            const files = fs.readdirSync(jeanLucDir);
            this.log(`📸 ${files.length} images trouvées pour Jean-Luc`);
        } else {
            this.log('📸 Aucune image de référence trouvée pour Jean-Luc');
            this.log('💡 Conseil: Ajoutez des photos dans le dossier face_data/jean-luc/');
        }
    }

    /**
     * Charge le profil utilisateur
     */
    async loadUserProfile() {
        const profilePath = path.join(this.config.faceDataDir, 'user_profile.json');

        if (fs.existsSync(profilePath)) {
            try {
                const profileData = JSON.parse(fs.readFileSync(profilePath, 'utf8'));
                this.userProfile = { ...this.userProfile, ...profileData };
                this.log('👤 Profil utilisateur chargé');
            } catch (error) {
                this.log('⚠️ Erreur chargement profil utilisateur');
            }
        } else {
            // Créer un profil par défaut
            await this.saveUserProfile();
        }
    }

    /**
     * Sauvegarde le profil utilisateur
     */
    async saveUserProfile() {
        const profilePath = path.join(this.config.faceDataDir, 'user_profile.json');

        try {
            fs.writeFileSync(profilePath, JSON.stringify(this.userProfile, null, 2));
            this.log('💾 Profil utilisateur sauvegardé');
        } catch (error) {
            this.log('❌ Erreur sauvegarde profil utilisateur');
        }
    }

    /**
     * Capture une image et analyse les visages
     */
    async captureAndAnalyze() {
        if (!this.state.isActive) {
            throw new Error('Système de caméra non actif');
        }

        this.log('📸 Capture et analyse en cours...');

        try {
            // Préparer la configuration pour le script Python
            const config = {
                device: this.config.device,
                width: this.config.width,
                height: this.config.height,
                fps: this.config.fps,
                autoSave: this.config.autoSave,
                captureDir: this.config.captureDir,
                faceDataDir: this.config.faceDataDir
            };

            // Exécuter le script Python
            const result = await this.execPythonScript(this.cameraScriptPath, JSON.stringify(config));
            const analysisResult = JSON.parse(result);

            if (analysisResult.success) {
                this.state.stats.capturesCount++;
                this.state.stats.facesDetected += analysisResult.faces_detected;
                this.state.lastCapture = analysisResult;

                // Traiter les visages reconnus
                if (analysisResult.recognized_faces && analysisResult.recognized_faces.length > 0) {
                    for (const face of analysisResult.recognized_faces) {
                        if (face.name === 'Jean-Luc Passave' && face.confidence > this.config.recognitionThreshold) {
                            await this.handleJeanLucDetected(face);
                        }
                    }
                }

                this.emit('captureCompleted', analysisResult);
                this.log(`✅ Capture terminée: ${analysisResult.faces_detected} visage(s) détecté(s)`);

                return analysisResult;
            } else {
                throw new Error(analysisResult.error || 'Erreur capture');
            }
        } catch (error) {
            this.log(`❌ Erreur capture: ${error.message}`);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Gère la détection de Jean-Luc
     */
    async handleJeanLucDetected(faceData) {
        this.log(`👋 Jean-Luc détecté! (confiance: ${(faceData.confidence * 100).toFixed(1)}%)`);

        // Mettre à jour le profil utilisateur
        this.userProfile.lastSeen = new Date().toISOString();
        this.userProfile.totalInteractions++;

        // Sauvegarder le profil
        await this.saveUserProfile();

        // Ajouter à la mémoire thermique si disponible
        if (global.thermalMemory) {
            global.thermalMemory.addInformation({
                content: `Jean-Luc Passave détecté par la caméra avec ${(faceData.confidence * 100).toFixed(1)}% de confiance`,
                source: 'camera_system',
                importance: 0.9,
                tags: ['jean-luc', 'reconnaissance-faciale', 'camera'],
                category: 'user_interaction'
            });
        }

        // Émettre un événement
        this.emit('jeanLucDetected', {
            ...faceData,
            greeting: this.userProfile.preferences.greeting,
            totalInteractions: this.userProfile.totalInteractions
        });

        // Notification vocale si le système cognitif est disponible
        if (global.cognitiveSystem) {
            try {
                await global.cognitiveSystem.speak(this.userProfile.preferences.greeting);
            } catch (error) {
                this.log('⚠️ Erreur notification vocale');
            }
        }
    }

    /**
     * Démarre l'analyse en temps réel
     */
    startRealTimeAnalysis() {
        if (this.realTimeInterval) {
            clearInterval(this.realTimeInterval);
        }

        this.log('🔄 Démarrage de l\'analyse en temps réel...');

        this.realTimeInterval = setInterval(async () => {
            try {
                await this.captureAndAnalyze();
            } catch (error) {
                // Ignorer les erreurs en mode temps réel pour éviter le spam
            }
        }, 5000); // Analyse toutes les 5 secondes
    }

    /**
     * Arrête l'analyse en temps réel
     */
    stopRealTimeAnalysis() {
        if (this.realTimeInterval) {
            clearInterval(this.realTimeInterval);
            this.realTimeInterval = null;
            this.log('⏹️ Analyse en temps réel arrêtée');
        }
    }

    /**
     * Exécute une commande système
     */
    execCommand(command) {
        return new Promise((resolve, reject) => {
            const { exec } = require('child_process');
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout.trim());
                }
            });
        });
    }

    /**
     * Exécute un script Python avec des données d'entrée
     */
    execPythonScript(scriptPath, inputData = '') {
        return new Promise((resolve, reject) => {
            // Utiliser l'environnement virtuel Python
            const pythonPath = path.join(__dirname, 'venv_camera', 'bin', 'python');

            // Vérifier si l'environnement virtuel existe, sinon utiliser python3 système
            const fs = require('fs');
            const finalPythonPath = fs.existsSync(pythonPath) ? pythonPath : 'python3';

            const python = spawn(finalPythonPath, [scriptPath]);
            let output = '';
            let errorOutput = '';

            python.stdout.on('data', (data) => {
                output += data.toString();
            });

            python.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            python.on('close', (code) => {
                if (code === 0) {
                    resolve(output.trim());
                } else {
                    reject(new Error(`Script Python échoué: ${errorOutput}`));
                }
            });

            // Envoyer les données d'entrée
            if (inputData) {
                python.stdin.write(inputData);
            }
            python.stdin.end();
        });
    }

    /**
     * Obtient les statistiques du système
     */
    getStats() {
        const uptime = Date.now() - this.state.stats.startTime;

        return {
            isActive: this.state.isActive,
            uptime: Math.floor(uptime / 1000),
            captures: this.state.stats.capturesCount,
            facesDetected: this.state.stats.facesDetected,
            emotionsDetected: this.state.stats.emotionsDetected,
            objectsDetected: this.state.stats.objectsDetected,
            lastCapture: this.state.lastCapture,
            userProfile: {
                name: this.userProfile.name,
                lastSeen: this.userProfile.lastSeen,
                totalInteractions: this.userProfile.totalInteractions
            }
        };
    }

    /**
     * Arrête le système de caméra
     */
    async stop() {
        this.log('⏹️ Arrêt du système de caméra...');

        this.stopRealTimeAnalysis();
        this.state.isActive = false;

        this.emit('cameraStopped');
        this.log('✅ Système de caméra arrêté');
    }

    /**
     * Log avec timestamp
     */
    log(message) {
        if (this.config.debug) {
            const timestamp = new Date().toISOString();
            console.log(`[${timestamp}] [AdvancedCamera] ${message}`);
        }
    }
}

module.exports = AdvancedCameraSystem;
