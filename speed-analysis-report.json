{"timestamp": "2025-05-27T18:00:47.733Z", "summary": {"currentSystem": "Ollama-based", "proposedSystem": "Direct API connections", "testDuration": 42161, "totalTests": 20}, "performance": {"current": {"totalTests": 10, "successfulTests": 0, "failedTests": 10, "latencies": [], "errors": ["timeout of 10000ms exceeded", "timeout of 10000ms exceeded", "timeout of 10000ms exceeded", "read ECONNRESET", "connect ECONNREFUSED ::1:3000", "connect ECONNREFUSED ::1:3000", "connect ECONNREFUSED ::1:3000", "connect ECONNREFUSED ::1:3000", "connect ECONNREFUSED ::1:3000", "connect ECONNREFUSED ::1:3000"], "averageLatency": 0, "minLatency": null, "maxLatency": 0, "timeouts": 0, "totalTime": 40141, "successRate": 0}, "direct": {"totalTests": 10, "successfulTests": 1, "failedTests": 9, "latencies": [0], "errors": ["Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')", "Cannot read properties of null (reading 'name')"], "averageLatency": 0, "minLatency": 0, "maxLatency": 0, "cacheHits": 1, "apiUsage": {}, "totalTime": 2020, "successRate": 10, "cacheHitRate": 100, "optimizerStats": {"averageLatency": 0, "fastestResponse": 0, "slowestResponse": 0, "cacheHitRate": 1, "reflectionTime": 0.955078125, "optimizationGains": 100, "totalRequests": 10, "cacheStats": {"instant": 4, "quick": 0, "persistent": 0}, "connectionStats": {"totalRequests": 10, "successfulRequests": 0, "failedRequests": 10, "averageLatency": 0, "apiUsage": {}, "cacheHits": 0, "cacheMisses": 10, "activeAPI": "Ollama Fallback", "availableAPIs": 0, "cacheSize": 0, "cacheHitRate": 0}, "optimizationLevel": "ultra-fast"}}, "comparison": {"latencyImprovement": 0, "latencyImprovementPercent": null, "reliabilityImprovement": 10, "speedGain": 38121, "speedGainPercent": 94.96773872100844}}, "recommendations": [{"priority": "MEDIUM", "type": "OPTIMIZATION", "title": "Optimiser le système de cache", "description": "100.0% de cache hits observés", "implementation": "Étendre le cache prédictif et contextuel"}, {"priority": "HIGH", "type": "GENERAL", "title": "Migration recommandée vers les connexions directes", "description": "Gains significatifs observés en vitesse et fiabilité", "implementation": "Déployer le système d'optimisation de vitesse en production"}], "conclusion": {"recommendation": "NOT_RECOMMENDED", "reason": "Ollama performe mieux actuellement", "action": "Optimiser <PERSON><PERSON><PERSON> ou attendre de meilleures APIs"}}