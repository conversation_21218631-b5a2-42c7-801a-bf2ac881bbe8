/**
 * GESTIONNAIRE DE SAUVEGARDE ET RESTAURATION SYSTÈME
 * Permet de sauvegarder et restaurer la configuration optimale de Louna
 * Créé pour préserver l'état optimal après optimisations de vitesse
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SystemBackupManager {
    constructor() {
        this.backupDir = path.join(__dirname, 'data', 'system_backups');
        this.configBackupDir = path.join(this.backupDir, 'configurations');
        this.codeBackupDir = path.join(this.backupDir, 'code_snapshots');
        
        // Créer les dossiers de sauvegarde
        this.ensureDirectories();
        
        // Configuration des fichiers critiques à sauvegarder
        this.criticalFiles = [
            'server.js',
            'routes/chat-route.js',
            'agent-speed-optimizer.js',
            'direct-agent-connection.js',
            'config/app-config.js',
            'data/config/agents.json',
            'data/config/default-agent.json',
            'intelligent-fallback-system.js',
            'package.json'
        ];
        
        // État optimal actuel (après optimisations)
        this.optimalState = {
            name: 'Configuration Optimale Ultra-Rapide',
            description: 'État optimal après optimisations de vitesse - Réponses en 3-6ms',
            version: '2.1.0-optimized',
            features: [
                'Timeout réduit à 8s (au lieu de 45s)',
                'Mode turbo activé automatiquement',
                'APIs cérébrales corrigées',
                'Système de fallback intelligent',
                'Accélérateurs KYBER opérationnels',
                'Réponses ultra-rapides (3-6ms)'
            ],
            performance: {
                responseTime: '3-6ms',
                timeout: '8s',
                turboMode: true,
                kyberAccelerators: true,
                fallbackSystem: true
            }
        };
        
        console.log('💾 Gestionnaire de sauvegarde système initialisé');
    }

    /**
     * Crée les dossiers nécessaires
     */
    ensureDirectories() {
        [this.backupDir, this.configBackupDir, this.codeBackupDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }

    /**
     * Sauvegarde l'état optimal actuel
     */
    async saveOptimalState() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupName = `optimal_state_${timestamp}`;
            const backupPath = path.join(this.configBackupDir, backupName);
            
            console.log('💾 Sauvegarde de l\'état optimal en cours...');
            
            // Créer le dossier de sauvegarde
            fs.mkdirSync(backupPath, { recursive: true });
            
            // Sauvegarder les fichiers critiques
            const savedFiles = [];
            for (const file of this.criticalFiles) {
                const sourcePath = path.join(__dirname, file);
                if (fs.existsSync(sourcePath)) {
                    const targetPath = path.join(backupPath, file);
                    const targetDir = path.dirname(targetPath);
                    
                    // Créer le dossier cible si nécessaire
                    if (!fs.existsSync(targetDir)) {
                        fs.mkdirSync(targetDir, { recursive: true });
                    }
                    
                    // Copier le fichier
                    fs.copyFileSync(sourcePath, targetPath);
                    savedFiles.push(file);
                    console.log(`✅ Sauvegardé: ${file}`);
                }
            }
            
            // Sauvegarder la configuration système
            const systemConfig = {
                ...this.optimalState,
                timestamp: new Date().toISOString(),
                savedFiles: savedFiles,
                backupPath: backupPath,
                nodeVersion: process.version,
                platform: process.platform
            };
            
            fs.writeFileSync(
                path.join(backupPath, 'system_config.json'),
                JSON.stringify(systemConfig, null, 2)
            );
            
            // Sauvegarder l'état des modules npm
            try {
                const packageLock = path.join(__dirname, 'package-lock.json');
                if (fs.existsSync(packageLock)) {
                    fs.copyFileSync(packageLock, path.join(backupPath, 'package-lock.json'));
                }
            } catch (error) {
                console.warn('⚠️ Impossible de sauvegarder package-lock.json:', error.message);
            }
            
            // Créer un fichier de restauration rapide
            const restoreScript = this.generateRestoreScript(backupName);
            fs.writeFileSync(
                path.join(backupPath, 'restore.js'),
                restoreScript
            );
            
            console.log(`✅ État optimal sauvegardé: ${backupName}`);
            console.log(`📁 Emplacement: ${backupPath}`);
            console.log(`📊 Fichiers sauvegardés: ${savedFiles.length}`);
            
            return {
                success: true,
                backupName: backupName,
                backupPath: backupPath,
                filesCount: savedFiles.length,
                timestamp: systemConfig.timestamp
            };
            
        } catch (error) {
            console.error('❌ Erreur lors de la sauvegarde:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Restaure l'état optimal
     */
    async restoreOptimalState(backupName = null) {
        try {
            console.log('🔄 Restauration de l\'état optimal...');
            
            // Trouver la sauvegarde la plus récente si aucune spécifiée
            if (!backupName) {
                backupName = this.findLatestBackup();
                if (!backupName) {
                    throw new Error('Aucune sauvegarde trouvée');
                }
            }
            
            const backupPath = path.join(this.configBackupDir, backupName);
            if (!fs.existsSync(backupPath)) {
                throw new Error(`Sauvegarde non trouvée: ${backupName}`);
            }
            
            // Charger la configuration de sauvegarde
            const configPath = path.join(backupPath, 'system_config.json');
            if (!fs.existsSync(configPath)) {
                throw new Error('Configuration de sauvegarde manquante');
            }
            
            const backupConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            console.log(`📋 Restauration: ${backupConfig.name}`);
            console.log(`📅 Date: ${backupConfig.timestamp}`);
            
            // Créer une sauvegarde de l'état actuel avant restauration
            console.log('💾 Sauvegarde de sécurité de l\'état actuel...');
            const securityBackup = await this.createSecurityBackup();
            
            // Restaurer les fichiers
            const restoredFiles = [];
            for (const file of backupConfig.savedFiles) {
                const sourcePath = path.join(backupPath, file);
                const targetPath = path.join(__dirname, file);
                
                if (fs.existsSync(sourcePath)) {
                    // Créer le dossier cible si nécessaire
                    const targetDir = path.dirname(targetPath);
                    if (!fs.existsSync(targetDir)) {
                        fs.mkdirSync(targetDir, { recursive: true });
                    }
                    
                    // Restaurer le fichier
                    fs.copyFileSync(sourcePath, targetPath);
                    restoredFiles.push(file);
                    console.log(`✅ Restauré: ${file}`);
                }
            }
            
            console.log(`✅ Restauration terminée: ${restoredFiles.length} fichiers`);
            console.log('🔄 Redémarrage recommandé pour appliquer les changements');
            
            return {
                success: true,
                backupName: backupName,
                restoredFiles: restoredFiles.length,
                securityBackup: securityBackup,
                restartRequired: true
            };
            
        } catch (error) {
            console.error('❌ Erreur lors de la restauration:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Liste les sauvegardes disponibles
     */
    listBackups() {
        try {
            if (!fs.existsSync(this.configBackupDir)) {
                return [];
            }
            
            const backups = fs.readdirSync(this.configBackupDir)
                .filter(name => name.startsWith('optimal_state_'))
                .map(name => {
                    const backupPath = path.join(this.configBackupDir, name);
                    const configPath = path.join(backupPath, 'system_config.json');
                    
                    if (fs.existsSync(configPath)) {
                        try {
                            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                            return {
                                name: name,
                                displayName: config.name,
                                description: config.description,
                                timestamp: config.timestamp,
                                version: config.version,
                                filesCount: config.savedFiles?.length || 0,
                                features: config.features || []
                            };
                        } catch (error) {
                            return {
                                name: name,
                                displayName: 'Sauvegarde corrompue',
                                error: error.message
                            };
                        }
                    }
                    
                    return {
                        name: name,
                        displayName: 'Configuration manquante'
                    };
                })
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
            return backups;
            
        } catch (error) {
            console.error('❌ Erreur lors de la liste des sauvegardes:', error);
            return [];
        }
    }

    /**
     * Trouve la sauvegarde la plus récente
     */
    findLatestBackup() {
        const backups = this.listBackups();
        return backups.length > 0 ? backups[0].name : null;
    }

    /**
     * Crée une sauvegarde de sécurité
     */
    async createSecurityBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `security_backup_${timestamp}`;
        
        // Utiliser la même logique que saveOptimalState mais avec un nom différent
        const result = await this.saveOptimalState();
        if (result.success) {
            // Renommer la sauvegarde
            const oldPath = result.backupPath;
            const newPath = path.join(this.configBackupDir, backupName);
            fs.renameSync(oldPath, newPath);
            
            return backupName;
        }
        
        return null;
    }

    /**
     * Génère un script de restauration
     */
    generateRestoreScript(backupName) {
        return `/**
 * SCRIPT DE RESTAURATION AUTOMATIQUE
 * Sauvegarde: ${backupName}
 * Généré le: ${new Date().toISOString()}
 */

const SystemBackupManager = require('../../../system-backup-manager');

async function restore() {
    console.log('🔄 Démarrage de la restauration automatique...');
    
    const manager = new SystemBackupManager();
    const result = await manager.restoreOptimalState('${backupName}');
    
    if (result.success) {
        console.log('✅ Restauration réussie !');
        console.log('🔄 Veuillez redémarrer l\\'application');
        process.exit(0);
    } else {
        console.error('❌ Échec de la restauration:', result.error);
        process.exit(1);
    }
}

restore().catch(console.error);
`;
    }

    /**
     * Obtient les statistiques du système de sauvegarde
     */
    getStats() {
        const backups = this.listBackups();
        const totalSize = this.calculateTotalSize();
        
        return {
            totalBackups: backups.length,
            latestBackup: backups.length > 0 ? backups[0] : null,
            totalSize: totalSize,
            backupDirectory: this.backupDir,
            criticalFiles: this.criticalFiles.length,
            optimalState: this.optimalState
        };
    }

    /**
     * Calcule la taille totale des sauvegardes
     */
    calculateTotalSize() {
        try {
            let totalSize = 0;
            
            const calculateDirSize = (dirPath) => {
                if (!fs.existsSync(dirPath)) return 0;
                
                const items = fs.readdirSync(dirPath);
                for (const item of items) {
                    const itemPath = path.join(dirPath, item);
                    const stats = fs.statSync(itemPath);
                    
                    if (stats.isDirectory()) {
                        totalSize += calculateDirSize(itemPath);
                    } else {
                        totalSize += stats.size;
                    }
                }
            };
            
            calculateDirSize(this.backupDir);
            
            // Convertir en MB
            return Math.round(totalSize / (1024 * 1024) * 100) / 100;
            
        } catch (error) {
            console.error('❌ Erreur calcul taille:', error);
            return 0;
        }
    }
}

module.exports = SystemBackupManager;
