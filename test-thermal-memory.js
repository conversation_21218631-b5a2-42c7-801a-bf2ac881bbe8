#!/usr/bin/env node

/**
 * Script de test pour vérifier le fonctionnement de la mémoire thermique
 * Teste les 6 zones, les capteurs de température, et la variation selon la température
 */

const ThermalMemory = require('./thermal-memory-complete');
const SystemTemperature = require('./system-temperature');

console.log('🧪 DÉMARRAGE DES TESTS DE LA MÉMOIRE THERMIQUE\n');

async function testThermalMemory() {
    try {
        // 1. Initialiser la mémoire thermique
        console.log('📋 1. INITIALISATION DE LA MÉMOIRE THERMIQUE');
        const thermalMemory = new ThermalMemory();
        await new Promise(resolve => setTimeout(resolve, 1000)); // Attendre l'initialisation
        
        // 2. Tester les capteurs de température
        console.log('\n🌡️ 2. TEST DES CAPTEURS DE TEMPÉRATURE');
        const systemTemp = thermalMemory.systemTemperature;
        const temperatures = systemTemp.getTemperatures();
        
        console.log('   Températures actuelles:');
        console.log(`   - CPU: ${temperatures.cpu.toFixed(1)}°C`);
        console.log(`   - GPU: ${temperatures.gpu.toFixed(1)}°C`);
        console.log(`   - Mémoire: ${temperatures.memory.toFixed(1)}°C`);
        console.log(`   - Batterie: ${temperatures.battery.toFixed(1)}°C`);
        console.log(`   - Moyenne: ${temperatures.average.toFixed(1)}°C`);
        console.log(`   - Normalisée: ${systemTemp.getNormalizedTemperature().toFixed(3)}`);
        
        // 3. Tester les 6 zones de mémoire
        console.log('\n🧠 3. TEST DES 6 ZONES DE MÉMOIRE');
        const stats = thermalMemory.getMemoryStats();
        
        console.log('   Zones de mémoire:');
        console.log(`   - Zone 1 (Instantanée): ${stats.zone1Count} entrées`);
        console.log(`   - Zone 2 (Court terme): ${stats.zone2Count} entrées`);
        console.log(`   - Zone 3 (Travail): ${stats.zone3Count} entrées`);
        console.log(`   - Zone 4 (Moyen terme): ${stats.zone4Count} entrées`);
        console.log(`   - Zone 5 (Long terme): ${stats.zone5Count} entrées`);
        console.log(`   - Zone 6 (Rêves): ${stats.zone6Count} entrées`);
        console.log(`   - Total: ${stats.totalMemories} entrées`);
        
        // 4. Tester les seuils de température
        console.log('\n🔥 4. TEST DES SEUILS DE TEMPÉRATURE');
        const thresholds = stats.temperatureThresholds;
        
        console.log('   Seuils de température par zone:');
        Object.entries(thresholds).forEach(([zone, threshold]) => {
            console.log(`   - ${zone}: ${threshold.toFixed(3)}`);
        });
        
        // 5. Ajouter des entrées de test
        console.log('\n📝 5. AJOUT D\'ENTRÉES DE TEST');
        
        const testEntries = [
            { key: 'test_haute_temp', data: 'Entrée à haute température', importance: 0.9 },
            { key: 'test_moyenne_temp', data: 'Entrée à température moyenne', importance: 0.5 },
            { key: 'test_basse_temp', data: 'Entrée à basse température', importance: 0.1 },
            { key: 'test_conversation', data: 'Conversation avec l\'utilisateur', importance: 0.7 },
            { key: 'test_apprentissage', data: 'Données d\'apprentissage', importance: 0.8 }
        ];
        
        testEntries.forEach((entry, index) => {
            const id = thermalMemory.add(entry.key, entry.data, entry.importance);
            console.log(`   ✅ Ajouté: ${entry.key} (ID: ${id})`);
        });
        
        // 6. Vérifier la répartition après ajout
        console.log('\n📊 6. RÉPARTITION APRÈS AJOUT');
        const newStats = thermalMemory.getMemoryStats();
        
        console.log('   Nouvelles statistiques:');
        console.log(`   - Zone 1: ${newStats.zone1Count} entrées`);
        console.log(`   - Zone 2: ${newStats.zone2Count} entrées`);
        console.log(`   - Zone 3: ${newStats.zone3Count} entrées`);
        console.log(`   - Zone 4: ${newStats.zone4Count} entrées`);
        console.log(`   - Zone 5: ${newStats.zone5Count} entrées`);
        console.log(`   - Zone 6: ${newStats.zone6Count} entrées`);
        console.log(`   - Total: ${newStats.totalMemories} entrées`);
        
        // 7. Tester la variation selon la température
        console.log('\n🌡️ 7. TEST DE VARIATION SELON LA TEMPÉRATURE');
        
        // Simuler différentes températures
        const tempTests = [0.2, 0.5, 0.8];
        
        for (const tempPos of tempTests) {
            console.log(`\n   Test avec température normalisée: ${tempPos}`);
            thermalMemory.setTemperatureCursorPosition(tempPos);
            
            const tempStats = thermalMemory.getMemoryStats();
            const tempThresholds = tempStats.temperatureThresholds;
            
            console.log('   Nouveaux seuils:');
            Object.entries(tempThresholds).forEach(([zone, threshold]) => {
                console.log(`     - ${zone}: ${threshold.toFixed(3)}`);
            });
        }
        
        // 8. Tester l'utilisation des températures réelles
        console.log('\n🔧 8. TEST DES TEMPÉRATURES RÉELLES');
        
        console.log('   Activation des températures réelles...');
        thermalMemory.setUseRealTemperatures(true);
        
        const realTempStats = thermalMemory.getMemoryStats();
        console.log(`   Température système utilisée: ${realTempStats.systemTemperatures.cpu.toFixed(1)}°C`);
        console.log(`   Position curseur: ${realTempStats.temperatureCursor.toFixed(3)}`);
        
        // 9. Tester la recherche dans la mémoire
        console.log('\n🔍 9. TEST DE RECHERCHE DANS LA MÉMOIRE');
        
        const searchResults = thermalMemory.search('test', { limit: 5 });
        console.log(`   Résultats de recherche pour "test": ${searchResults.length} entrées`);
        
        searchResults.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.entry.key} (score: ${result.score.toFixed(3)})`);
        });
        
        // 10. Tester l'analyse des patterns
        console.log('\n📈 10. TEST D\'ANALYSE DES PATTERNS');
        
        const patterns = thermalMemory.analyzePatterns();
        console.log(`   Patterns détectés:`);
        console.log(`   - Catégories: ${Object.keys(patterns.categories).length}`);
        console.log(`   - Mots-clés principaux: ${patterns.topKeywords.length}`);
        console.log(`   - Connexions: ${patterns.connections.length}`);
        console.log(`   - Clusters: ${patterns.clusters.length}`);
        
        // 11. Résumé final
        console.log('\n✅ 11. RÉSUMÉ FINAL');
        
        const finalStats = thermalMemory.getMemoryStats();
        const finalTemps = thermalMemory.getSystemTemperatures();
        
        console.log('   État final du système:');
        console.log(`   - Mémoires totales: ${finalStats.totalMemories}`);
        console.log(`   - Température moyenne: ${finalStats.averageTemperature.toFixed(3)}`);
        console.log(`   - Cycles effectués: ${finalStats.cyclesPerformed}`);
        console.log(`   - Utilise températures réelles: ${finalStats.useRealTemperatures ? 'OUI' : 'NON'}`);
        console.log(`   - Auto-évolution: ${finalStats.autoEvolution.enabled ? 'ACTIVÉE' : 'DÉSACTIVÉE'}`);
        console.log(`   - CPU actuel: ${finalTemps.cpu.toFixed(1)}°C`);
        
        console.log('\n🎉 TESTS TERMINÉS AVEC SUCCÈS !');
        
        return {
            success: true,
            zones: 6,
            realTemperatures: true,
            variableThresholds: true,
            systemIntegration: true
        };
        
    } catch (error) {
        console.error('\n❌ ERREUR LORS DES TESTS:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Exécuter les tests
if (require.main === module) {
    testThermalMemory().then(result => {
        console.log('\n📋 RÉSULTAT FINAL:', result);
        process.exit(result.success ? 0 : 1);
    });
}

module.exports = testThermalMemory;
