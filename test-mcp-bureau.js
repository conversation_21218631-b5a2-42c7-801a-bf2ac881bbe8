/**
 * Script de test pour vérifier l'accès au bureau via MCP
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

const MCP_URL = 'http://localhost:3002';

// Couleurs pour les logs
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testMCPConnection() {
    log('\n🚀 TEST DE CONNEXION MCP', 'cyan');
    log('=' * 50, 'cyan');
    
    try {
        // Test 1: Vérifier le statut du serveur MCP
        log('\n1. Test du statut du serveur MCP...', 'blue');
        
        const statusResponse = await axios.get(`${MCP_URL}/mcp/status`);
        
        if (statusResponse.status === 200) {
            log('✅ Serveur MCP en ligne', 'green');
            log(`   Version: ${statusResponse.data.version}`, 'green');
            log(`   Internet: ${statusResponse.data.capabilities.internet ? 'Activé' : 'Désactivé'}`, 'green');
            log(`   Bureau: ${statusResponse.data.capabilities.desktop ? 'Activé' : 'Désactivé'}`, 'green');
            log(`   Commandes: ${statusResponse.data.capabilities.systemCommands ? 'Activé' : 'Désactivé'}`, 'green');
        } else {
            throw new Error(`Statut HTTP inattendu: ${statusResponse.status}`);
        }
        
    } catch (error) {
        log('❌ Erreur de connexion au serveur MCP:', 'red');
        log(`   ${error.message}`, 'red');
        log('\n💡 Solutions possibles:', 'yellow');
        log('   - Vérifiez que le serveur MCP est démarré sur le port 3002', 'yellow');
        log('   - Exécutez: cd code/deepseek-node-ui && node mcp/mcp-server.js', 'yellow');
        return false;
    }
    
    // Test 2: Vérifier l'accès au bureau
    log('\n2. Test de l\'accès au bureau...', 'blue');
    
    try {
        const desktopResponse = await axios.get(`${MCP_URL}/mcp/desktop/check`);
        
        if (desktopResponse.status === 200 && desktopResponse.data.success) {
            const data = desktopResponse.data;
            log('✅ Accès au bureau vérifié', 'green');
            log(`   Chemin: ${data.desktopPath}`, 'green');
            log(`   Existe: ${data.exists ? 'Oui' : 'Non'}`, 'green');
            log(`   Accessible: ${data.accessible === true ? 'Lecture/Écriture' : data.accessible === 'readonly' ? 'Lecture seule' : 'Non'}`, 'green');
            log(`   Nombre de fichiers: ${data.fileCount}`, 'green');
            log(`   Plateforme: ${data.platform}`, 'green');
            log(`   Utilisateur: ${data.userInfo.username}`, 'green');
            
            if (!data.exists) {
                log('⚠️  Le répertoire bureau n\'existe pas !', 'yellow');
                return false;
            }
            
            if (!data.accessible) {
                log('⚠️  Le bureau n\'est pas accessible !', 'yellow');
                return false;
            }
            
        } else {
            throw new Error(desktopResponse.data.error || 'Erreur inconnue');
        }
        
    } catch (error) {
        log('❌ Erreur lors de la vérification du bureau:', 'red');
        log(`   ${error.message}`, 'red');
        return false;
    }
    
    // Test 3: Lister les fichiers du bureau
    log('\n3. Test de listage des fichiers du bureau...', 'blue');
    
    try {
        const filesResponse = await axios.get(`${MCP_URL}/mcp/desktop/files`);
        
        if (filesResponse.status === 200 && filesResponse.data.success) {
            const files = filesResponse.data.files;
            log(`✅ ${files.length} fichiers/dossiers trouvés sur le bureau`, 'green');
            
            if (files.length > 0) {
                log('   Premiers éléments:', 'green');
                files.slice(0, 5).forEach(file => {
                    const type = file.isDirectory ? '📁' : '📄';
                    const size = file.isDirectory ? 'Dossier' : formatFileSize(file.size);
                    log(`   ${type} ${file.name} (${size})`, 'green');
                });
                
                if (files.length > 5) {
                    log(`   ... et ${files.length - 5} autres éléments`, 'green');
                }
            } else {
                log('   Le bureau est vide', 'yellow');
            }
            
        } else {
            throw new Error(filesResponse.data.error || 'Erreur inconnue');
        }
        
    } catch (error) {
        log('❌ Erreur lors du listage des fichiers:', 'red');
        log(`   ${error.message}`, 'red');
        return false;
    }
    
    // Test 4: Créer un fichier de test
    log('\n4. Test de création de fichier...', 'blue');
    
    try {
        const testFileName = `test-mcp-${Date.now()}.txt`;
        const testContent = `Test MCP - ${new Date().toLocaleString('fr-FR')}\n\nCe fichier a été créé par le système MCP pour tester l'accès au bureau.`;
        
        const createResponse = await axios.post(`${MCP_URL}/mcp/desktop/createFile`, {
            fileName: testFileName,
            content: testContent
        });
        
        if (createResponse.status === 200 && createResponse.data.success) {
            log(`✅ Fichier de test créé: ${testFileName}`, 'green');
            log(`   Chemin: ${createResponse.data.path}`, 'green');
            
            // Vérifier que le fichier existe vraiment
            const filePath = createResponse.data.path;
            if (fs.existsSync(filePath)) {
                log('✅ Fichier vérifié sur le système de fichiers', 'green');
                
                // Lire le contenu pour vérifier
                const actualContent = fs.readFileSync(filePath, 'utf8');
                if (actualContent === testContent) {
                    log('✅ Contenu du fichier vérifié', 'green');
                } else {
                    log('⚠️  Le contenu du fichier ne correspond pas', 'yellow');
                }
                
                // Nettoyer - supprimer le fichier de test
                try {
                    fs.unlinkSync(filePath);
                    log('🧹 Fichier de test supprimé', 'green');
                } catch (cleanupError) {
                    log(`⚠️  Impossible de supprimer le fichier de test: ${cleanupError.message}`, 'yellow');
                }
                
            } else {
                log('❌ Le fichier n\'existe pas sur le système de fichiers', 'red');
                return false;
            }
            
        } else {
            throw new Error(createResponse.data.error || 'Erreur inconnue');
        }
        
    } catch (error) {
        log('❌ Erreur lors de la création du fichier:', 'red');
        log(`   ${error.message}`, 'red');
        return false;
    }
    
    // Test 5: Test de recherche Internet
    log('\n5. Test de recherche Internet...', 'blue');
    
    try {
        const searchResponse = await axios.post(`${MCP_URL}/mcp/internet/search`, {
            query: 'intelligence artificielle',
            limit: 3
        });
        
        if (searchResponse.status === 200 && searchResponse.data.success) {
            log('✅ Recherche Internet fonctionnelle', 'green');
            log(`   Requête: ${searchResponse.data.query}`, 'green');
            
            if (searchResponse.data.results && Object.keys(searchResponse.data.results).length > 0) {
                log('   Résultats trouvés', 'green');
            } else {
                log('   Aucun résultat (normal pour DuckDuckGo API)', 'yellow');
            }
            
        } else {
            throw new Error(searchResponse.data.error || 'Erreur inconnue');
        }
        
    } catch (error) {
        log('❌ Erreur lors de la recherche Internet:', 'red');
        log(`   ${error.message}`, 'red');
        log('   (Ceci peut être normal si l\'API de recherche n\'est pas configurée)', 'yellow');
    }
    
    // Résumé final
    log('\n🎉 TESTS TERMINÉS', 'cyan');
    log('=' * 50, 'cyan');
    log('✅ Le système MCP est fonctionnel !', 'green');
    log('✅ L\'accès au bureau fonctionne correctement', 'green');
    log('✅ Vous pouvez maintenant utiliser l\'interface MCP dans Louna', 'green');
    
    log('\n📍 Pour accéder à l\'interface MCP:', 'blue');
    log('   1. Ouvrez votre application Louna', 'blue');
    log('   2. Cliquez sur "MCP" dans la barre latérale', 'blue');
    log('   3. L\'interface devrait afficher le bureau et permettre les interactions', 'blue');
    
    return true;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Exécuter les tests
if (require.main === module) {
    testMCPConnection()
        .then(success => {
            if (success) {
                log('\n🚀 Tous les tests sont réussis ! Le MCP est prêt à être utilisé.', 'green');
                process.exit(0);
            } else {
                log('\n❌ Certains tests ont échoué. Vérifiez la configuration.', 'red');
                process.exit(1);
            }
        })
        .catch(error => {
            log('\n💥 Erreur fatale lors des tests:', 'red');
            log(error.message, 'red');
            process.exit(1);
        });
}

module.exports = { testMCPConnection };
