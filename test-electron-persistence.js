/**
 * Test du système de persistance mémoire dans l'application Electron
 * Créé par <PERSON> Sainte-Anne, Guadeloupe
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class ElectronPersistenceTest {
    constructor() {
        this.electronProcess = null;
        this.testResults = [];
    }

    /**
     * Lance l'application Electron pour les tests
     */
    async startElectronApp() {
        console.log('🚀 Lancement de l\'application Electron pour tests...');
        
        try {
            // Lancer l'application Electron
            this.electronProcess = spawn('npm', ['run', 'electron'], {
                stdio: 'pipe',
                cwd: process.cwd()
            });

            // Écouter les logs
            this.electronProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('📱 Electron:', output);
                
                // Vérifier les messages de persistance
                if (output.includes('Système de persistance mémoire initialisé')) {
                    this.testResults.push({
                        test: 'Initialisation persistance',
                        status: 'SUCCESS',
                        message: 'Système initialisé avec succès'
                    });
                }
                
                if (output.includes('éléments récupérés de la mémoire')) {
                    this.testResults.push({
                        test: 'Récupération données',
                        status: 'SUCCESS',
                        message: 'Données récupérées avec succès'
                    });
                }
            });

            this.electronProcess.stderr.on('data', (data) => {
                const error = data.toString();
                console.error('❌ Electron Error:', error);
                
                if (error.includes('persistance') || error.includes('persistence')) {
                    this.testResults.push({
                        test: 'Erreur persistance',
                        status: 'ERROR',
                        message: error.trim()
                    });
                }
            });

            // Attendre que l'application démarre
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            console.log('✅ Application Electron démarrée');
            return true;
            
        } catch (error) {
            console.error('❌ Erreur lancement Electron:', error);
            return false;
        }
    }

    /**
     * Teste les fonctionnalités de persistance
     */
    async testPersistenceFeatures() {
        console.log('🧪 Test des fonctionnalités de persistance...');
        
        // Vérifier que les fichiers de persistance sont créés
        const persistenceDir = path.join(process.cwd(), 'data', 'persistence');
        
        try {
            // Attendre que le dossier soit créé
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            if (fs.existsSync(persistenceDir)) {
                console.log('✅ Dossier de persistance créé');
                this.testResults.push({
                    test: 'Création dossier persistance',
                    status: 'SUCCESS',
                    message: 'Dossier créé avec succès'
                });
                
                // Lister les fichiers créés
                const files = fs.readdirSync(persistenceDir);
                console.log('📁 Fichiers de persistance:', files);
                
                if (files.length > 0) {
                    this.testResults.push({
                        test: 'Création fichiers persistance',
                        status: 'SUCCESS',
                        message: `${files.length} fichiers créés`
                    });
                }
            } else {
                console.log('⚠️ Dossier de persistance non trouvé');
                this.testResults.push({
                    test: 'Création dossier persistance',
                    status: 'WARNING',
                    message: 'Dossier non créé'
                });
            }
            
        } catch (error) {
            console.error('❌ Erreur test persistance:', error);
            this.testResults.push({
                test: 'Test persistance',
                status: 'ERROR',
                message: error.message
            });
        }
    }

    /**
     * Teste la fermeture et récupération
     */
    async testShutdownRecovery() {
        console.log('🔄 Test fermeture et récupération...');
        
        try {
            // Fermer l'application
            if (this.electronProcess) {
                console.log('🛑 Fermeture de l\'application...');
                this.electronProcess.kill('SIGTERM');
                
                // Attendre la fermeture
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                this.testResults.push({
                    test: 'Fermeture application',
                    status: 'SUCCESS',
                    message: 'Application fermée proprement'
                });
            }
            
            // Relancer l'application
            console.log('🔄 Relancement de l\'application...');
            await this.startElectronApp();
            
            // Attendre et vérifier la récupération
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            this.testResults.push({
                test: 'Relancement application',
                status: 'SUCCESS',
                message: 'Application relancée avec succès'
            });
            
        } catch (error) {
            console.error('❌ Erreur test fermeture/récupération:', error);
            this.testResults.push({
                test: 'Test fermeture/récupération',
                status: 'ERROR',
                message: error.message
            });
        }
    }

    /**
     * Affiche les résultats des tests
     */
    displayResults() {
        console.log('\n📊 RÉSULTATS DES TESTS DE PERSISTANCE ELECTRON\n');
        console.log('='.repeat(60));
        
        let successCount = 0;
        let errorCount = 0;
        let warningCount = 0;
        
        this.testResults.forEach((result, index) => {
            const icon = result.status === 'SUCCESS' ? '✅' : 
                        result.status === 'ERROR' ? '❌' : '⚠️';
            
            console.log(`${icon} ${result.test}: ${result.message}`);
            
            if (result.status === 'SUCCESS') successCount++;
            else if (result.status === 'ERROR') errorCount++;
            else warningCount++;
        });
        
        console.log('='.repeat(60));
        console.log(`📈 Résumé: ${successCount} succès, ${errorCount} erreurs, ${warningCount} avertissements`);
        
        if (errorCount === 0) {
            console.log('🎉 TOUS LES TESTS SONT PASSÉS !');
            console.log('✅ Le système de persistance mémoire fonctionne dans Electron');
        } else {
            console.log('⚠️ Certains tests ont échoué');
            console.log('🔧 Vérifiez les erreurs ci-dessus');
        }
    }

    /**
     * Nettoie les ressources
     */
    cleanup() {
        if (this.electronProcess) {
            console.log('🧹 Nettoyage des processus...');
            this.electronProcess.kill('SIGKILL');
        }
    }

    /**
     * Lance tous les tests
     */
    async runAllTests() {
        console.log('🧪 DÉMARRAGE DES TESTS DE PERSISTANCE ELECTRON');
        console.log('👤 Créateur: Jean-Luc Passave');
        console.log('📍 Localisation: Sainte-Anne, Guadeloupe');
        console.log('🎯 Objectif: Vérifier l\'intégration de la persistance dans Electron\n');

        try {
            // Test 1: Démarrage de l'application
            const started = await this.startElectronApp();
            if (!started) {
                throw new Error('Impossible de démarrer l\'application Electron');
            }

            // Test 2: Fonctionnalités de persistance
            await this.testPersistenceFeatures();

            // Test 3: Fermeture et récupération
            await this.testShutdownRecovery();

            // Attendre un peu avant d'afficher les résultats
            await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
            console.error('❌ Erreur lors des tests:', error);
            this.testResults.push({
                test: 'Tests généraux',
                status: 'ERROR',
                message: error.message
            });
        } finally {
            // Afficher les résultats
            this.displayResults();
            
            // Nettoyer
            this.cleanup();
        }
    }
}

// Fonction principale
async function main() {
    const tester = new ElectronPersistenceTest();
    
    // Gérer l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt des tests...');
        tester.cleanup();
        process.exit(0);
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 Arrêt des tests...');
        tester.cleanup();
        process.exit(0);
    });
    
    // Lancer les tests
    await tester.runAllTests();
}

// Exporter pour utilisation
module.exports = ElectronPersistenceTest;

// Exécuter si appelé directement
if (require.main === module) {
    main().catch(console.error);
}
