/**
 * Patch pour intégrer les corrections agent dans server.js
 */

const { applyAgentFixes } = require('./apply-agent-fixes');

// Fonction pour appliquer les corrections après l'initialisation
async function applyAgentFixesAfterInit() {
    console.log('🔧 Démarrage de l\'application des corrections agent...');
    
    // Attendre un délai pour s'assurer que tous les systèmes sont initialisés
    setTimeout(async () => {
        try {
            console.log('🚀 Application des corrections agent en cours...');
            
            // Vérifier que l'agent manager est disponible via les routes
            const agentsRoute = require('./routes/agents');
            const agentManager = agentsRoute.getAgentManager ? agentsRoute.getAgentManager() : null;
            
            if (agentManager) {
                // Définir l'agent manager globalement pour les corrections
                global.agentManager = agentManager;
                console.log('✅ Agent manager trouvé et défini globalement');
            }
            
            // Appliquer les corrections
            const success = await applyAgentFixes();
            
            if (success) {
                console.log('🎉 CORRECTIONS AGENT APPLIQUÉES AVEC SUCCÈS !');
                console.log('✅ L\'agent principal Vision Ultra est maintenant:');
                console.log('   📅 Connecté au système 2025');
                console.log('   🌐 Capable de recherches Internet MCP');
                console.log('   ⚡ Optimisé pour de meilleures performances');
                console.log('   🚫 Plus de fallback - Agent principal prioritaire');
            } else {
                console.log('❌ Échec de l\'application des corrections agent');
            }
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'application des corrections agent:', error);
        }
    }, 10000); // 10 secondes après le démarrage
}

// Exporter la fonction
module.exports = { applyAgentFixesAfterInit };
