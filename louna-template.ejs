<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Louna - <%= title %></title>
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  
  <!-- Polices Google -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  
  <!-- CSS unifié de Louna -->
  <link rel="stylesheet" href="/css/louna-unified.css">
  
  <!-- CSS spécifique à la page (optionnel) -->
  <% if (typeof pageStyles !== 'undefined') { %>
    <link rel="stylesheet" href="<%= pageStyles %>">
  <% } %>
</head>
<body>
  <!-- Barre de navigation horizontale -->
  <nav class="top-navbar">
    <div class="logo-container">
      <i class="bi bi-braces-asterisk"></i>
      <span class="logo-text">Louna</span>
    </div>
    
    <div class="nav-links">
      <a href="/louna/chat" class="nav-item <%= activePage === 'chat' ? 'active' : '' %>">
        <i class="bi bi-chat-dots"></i>
        <span>Chat</span>
      </a>
      <a href="/louna/presentation" class="nav-item <%= activePage === 'presentation' ? 'active' : '' %>">
        <i class="bi bi-easel"></i>
        <span>Présentation</span>
      </a>
      <a href="/louna" class="nav-item <%= activePage === 'home' ? 'active' : '' %>">
        <i class="bi bi-house"></i>
        <span>Accueil</span>
      </a>
      <a href="/louna/prompts" class="nav-item <%= activePage === 'prompts' ? 'active' : '' %>">
        <i class="bi bi-lightning"></i>
        <span>Prompts</span>
      </a>
      <a href="/louna/memory" class="nav-item <%= activePage === 'memory' ? 'active' : '' %>">
        <i class="bi bi-brain"></i>
        <span>Mémoire</span>
      </a>
      <a href="/louna/thermal" class="nav-item <%= activePage === 'thermal' ? 'active' : '' %>">
        <i class="bi bi-thermometer-half"></i>
        <span>Thermique</span>
      </a>
    </div>
    
    <div class="nav-right">
      <a href="/louna/cerveau" class="nav-item">
        <i class="bi bi-cpu"></i>
        <span>Cerveau</span>
      </a>
      <a href="/louna/ia" class="nav-item">
        <i class="bi bi-robot"></i>
        <span>IA</span>
      </a>
      <a href="/louna/multimedia" class="nav-item">
        <i class="bi bi-film"></i>
        <span>Multimédia</span>
      </a>
      <a href="/louna/code" class="nav-item">
        <i class="bi bi-code-square"></i>
        <span>Code</span>
      </a>
      <a href="/louna/systeme" class="nav-item">
        <i class="bi bi-gear"></i>
        <span>Système</span>
      </a>
      <a href="/louna/securite" class="nav-item">
        <i class="bi bi-shield-lock"></i>
        <span>Sécurité</span>
      </a>
      <a href="/louna/parametres" class="nav-item">
        <i class="bi bi-sliders"></i>
        <span>Paramètres</span>
      </a>
    </div>
  </nav>
  
  <!-- Conteneur principal -->
  <div class="main-container">
    <!-- En-tête de l'interface -->
    <div class="interface-header">
      <div>
        <h1 class="interface-title">Interface Louna</h1>
        <p class="interface-subtitle">Système cognitif avancé avec mémoire thermique</p>
      </div>
      
      <div class="status-container">
        <span class="status-label">Statut</span>
        <div class="status-indicator"></div>
        <span class="status-text">Actif</span>
        <button class="action-button">Déconnecter</button>
      </div>
    </div>
    
    <!-- Contenu spécifique à la page -->
    <%- body %>
  </div>
  
  <!-- Scripts communs -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <!-- Script pour la navigation uniforme -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Gérer les clics sur les liens de navigation
      document.querySelectorAll('.nav-item').forEach(function(link) {
        link.addEventListener('click', function(e) {
          // Stocker l'URL actuelle dans le stockage local
          localStorage.setItem('lastValidUrl', window.location.href);
          
          // Laisser la navigation se faire normalement
        });
      });
      
      // Gérer les erreurs de navigation
      window.addEventListener('error', function(event) {
        console.error('Erreur détectée:', event.error);
        
        // Vérifier si l'erreur est liée à la navigation
        if (event.error && (
            event.error.message.includes('navigation') || 
            event.error.message.includes('route') || 
            event.error.message.includes('undefined')
          )) {
          console.log('Erreur de navigation détectée, tentative de récupération...');
          
          // Récupérer la dernière URL valide
          const lastValidUrl = localStorage.getItem('lastValidUrl') || '/louna';
          
          // Rediriger vers la dernière URL valide
          window.location.href = lastValidUrl;
        }
      });
    });
  </script>
  
  <!-- Script spécifique à la page (optionnel) -->
  <% if (typeof pageScript !== 'undefined') { %>
    <script src="<%= pageScript %>"></script>
  <% } %>
</body>
</html>
