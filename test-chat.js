// Script de test automatique pour l'interface de chat
console.log('🧪 Démarrage des tests de l'interface de chat...');

// Test 1: Vérifier que tous les éléments existent
function testElements() {
    console.log('📋 Test 1: Vérification des éléments...');
    
    const elements = {
        'chat-messages': document.getElementById('chat-messages'),
        'chat-input': document.getElementById('chat-input'),
        'chat-send-btn': document.getElementById('chat-send-btn'),
        'clear-chat-btn': document.getElementById('clear-chat-btn'),
        'memory-btn': document.getElementById('memory-btn'),
        'voice-btn': document.getElementById('voice-btn'),
        'typing-indicator': document.getElementById('typing-indicator')
    };
    
    let allFound = true;
    for (const [id, element] of Object.entries(elements)) {
        if (element) {
            console.log(`✅ ${id} trouvé`);
        } else {
            console.log(`❌ ${id} manquant`);
            allFound = false;
        }
    }
    
    return allFound;
}

// Test 2: Tester l'envoi de message
function testSendMessage() {
    console.log('📋 Test 2: Test d\'envoi de message...');
    
    const chatInput = document.getElementById('chat-input');
    const chatSendBtn = document.getElementById('chat-send-btn');
    
    if (!chatInput || !chatSendBtn) {
        console.log('❌ Éléments manquants pour le test');
        return false;
    }
    
    // Simuler la saisie d'un message
    chatInput.value = 'Test automatique - Bonjour Louna !';
    
    // Simuler le clic sur le bouton d'envoi
    chatSendBtn.click();
    
    // Vérifier que l'input a été vidé
    setTimeout(() => {
        if (chatInput.value === '') {
            console.log('✅ Message envoyé avec succès');
        } else {
            console.log('❌ Échec de l\'envoi du message');
        }
    }, 100);
    
    return true;
}

// Test 3: Tester la synthèse vocale
function testVoice() {
    console.log('📋 Test 3: Test de la synthèse vocale...');
    
    const voiceBtn = document.getElementById('voice-btn');
    
    if (!voiceBtn) {
        console.log('❌ Bouton vocal manquant');
        return false;
    }
    
    // Simuler le clic sur le bouton vocal
    voiceBtn.click();
    
    // Vérifier que le bouton a la classe active
    setTimeout(() => {
        if (voiceBtn.classList.contains('active')) {
            console.log('✅ Synthèse vocale activée');
        } else {
            console.log('❌ Échec de l\'activation vocale');
        }
    }, 100);
    
    return true;
}

// Test 4: Tester le bouton effacer
function testClear() {
    console.log('📋 Test 4: Test du bouton effacer...');
    
    const clearBtn = document.getElementById('clear-chat-btn');
    
    if (!clearBtn) {
        console.log('❌ Bouton effacer manquant');
        return false;
    }
    
    // Remplacer temporairement confirm pour éviter la popup
    const originalConfirm = window.confirm;
    window.confirm = () => true;
    
    // Simuler le clic
    clearBtn.click();
    
    // Restaurer confirm
    window.confirm = originalConfirm;
    
    console.log('✅ Bouton effacer testé');
    return true;
}

// Test 5: Tester les raccourcis clavier
function testKeyboard() {
    console.log('📋 Test 5: Test des raccourcis clavier...');
    
    const chatInput = document.getElementById('chat-input');
    
    if (!chatInput) {
        console.log('❌ Input manquant');
        return false;
    }
    
    // Simuler la saisie et la touche Entrée
    chatInput.value = 'Test clavier - mémoire thermique';
    
    const enterEvent = new KeyboardEvent('keypress', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13
    });
    
    chatInput.dispatchEvent(enterEvent);
    
    // Vérifier que l'input a été vidé
    setTimeout(() => {
        if (chatInput.value === '') {
            console.log('✅ Raccourci clavier fonctionnel');
        } else {
            console.log('❌ Échec du raccourci clavier');
        }
    }, 100);
    
    return true;
}

// Exécuter tous les tests
function runAllTests() {
    console.log('🚀 Exécution de tous les tests...');
    
    const tests = [
        testElements,
        testSendMessage,
        testVoice,
        testClear,
        testKeyboard
    ];
    
    let passedTests = 0;
    
    tests.forEach((test, index) => {
        setTimeout(() => {
            try {
                if (test()) {
                    passedTests++;
                }
            } catch (error) {
                console.log(`❌ Erreur dans le test ${index + 1}:`, error);
            }
            
            // Afficher le résumé après le dernier test
            if (index === tests.length - 1) {
                setTimeout(() => {
                    console.log(`\n📊 RÉSULTATS DES TESTS:`);
                    console.log(`✅ Tests réussis: ${passedTests}/${tests.length}`);
                    console.log(`❌ Tests échoués: ${tests.length - passedTests}/${tests.length}`);
                    
                    if (passedTests === tests.length) {
                        console.log('🎉 TOUS LES TESTS SONT PASSÉS ! Interface de chat fonctionnelle !');
                    } else {
                        console.log('⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.');
                    }
                }, 2000);
            }
        }, index * 1000);
    });
}

// Attendre que la page soit chargée puis lancer les tests
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAllTests, 2000);
    });
} else {
    setTimeout(runAllTests, 2000);
}
