<!-- Interface thermique -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-thermometer-half card-icon"></i>
    <h2 class="card-title">Gestionnaire Thermique</h2>
  </div>
  
  <div style="padding: 20px;">
    <div class="grid-container">
      <div class="grid-item" style="grid-column: span 2;">
        <div style="margin-bottom: 20px;">
          <h3 style="margin-bottom: 15px;">Visualisation des zones thermiques</h3>
          
          <div style="background-color: rgba(255, 255, 255, 0.05); padding: 20px; border-radius: 10px; position: relative; height: 300px;">
            <!-- Visualisation des zones thermiques -->
            <div style="position: absolute; top: 20px; left: 20px; right: 20px; bottom: 20px; display: flex; align-items: center; justify-content: center;">
              <div style="position: relative; width: 300px; height: 300px;">
                <!-- Zone 1 (Instant) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 60px; height: 60px; background-color: var(--temp-hot); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; z-index: 6;">
                  Zone 1
                </div>
                
                <!-- Zone 2 (Court terme) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 120px; height: 120px; background-color: rgba(255, 159, 67, 0.7); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 5;">
                  <div style="width: 100px; height: 100px; background-color: var(--primary-bg); border-radius: 50%;"></div>
                </div>
                
                <!-- Zone 3 (Moyen) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 180px; height: 180px; background-color: rgba(29, 209, 161, 0.5); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 4;">
                  <div style="width: 160px; height: 160px; background-color: var(--primary-bg); border-radius: 50%;"></div>
                </div>
                
                <!-- Zone 4 (Moyen terme) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 240px; height: 240px; background-color: rgba(84, 160, 255, 0.3); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 3;">
                  <div style="width: 220px; height: 220px; background-color: var(--primary-bg); border-radius: 50%;"></div>
                </div>
                
                <!-- Zone 5 (Long terme) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 280px; height: 280px; background-color: rgba(84, 160, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2;">
                  <div style="width: 260px; height: 260px; background-color: var(--primary-bg); border-radius: 50%;"></div>
                </div>
                
                <!-- Zone 6 (Rêve) -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 300px; height: 300px; background-color: rgba(84, 160, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 1;">
                  <div style="width: 280px; height: 280px; background-color: var(--primary-bg); border-radius: 50%;"></div>
                </div>
                
                <!-- Légende -->
                <div style="position: absolute; top: -30px; left: 320px; font-size: 12px; color: var(--text-secondary);">
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-hot); margin-right: 5px;"></span> Zone 1 (48°C)</div>
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-warm); margin-right: 5px;"></span> Zone 2 (35°C)</div>
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-medium); margin-right: 5px;"></span> Zone 3 (20°C)</div>
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-cool); margin-right: 5px;"></span> Zone 4 (10°C)</div>
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-cool); margin-right: 5px;"></span> Zone 5 (5°C)</div>
                  <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: var(--temp-cool); margin-right: 5px;"></span> Zone 6 (3°C)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div style="margin-bottom: 20px;">
          <h3 style="margin-bottom: 15px;">Configuration des zones thermiques</h3>
          
          <div class="grid-container">
            <div class="grid-item">
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Zone active</label>
                <select style="width: 100%; padding: 10px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
                  <option>Zone 1 (Instant)</option>
                  <option>Zone 2 (Court terme)</option>
                  <option>Zone 3 (Moyen)</option>
                  <option>Zone 4 (Moyen terme)</option>
                  <option>Zone 5 (Long terme)</option>
                  <option>Zone 6 (Rêve)</option>
                </select>
              </div>
              
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Température</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <input type="range" min="0" max="50" value="48" style="flex-grow: 1;">
                  <span>48°C</span>
                </div>
              </div>
              
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Capacité</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <input type="range" min="50" max="500" value="100" style="flex-grow: 1;">
                  <span>100 unités</span>
                </div>
              </div>
            </div>
            
            <div class="grid-item">
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Temps de rétention</label>
                <select style="width: 100%; padding: 10px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
                  <option>5 minutes</option>
                  <option>1 heure</option>
                  <option>1 jour</option>
                  <option>1 semaine</option>
                  <option>1 mois</option>
                  <option>Permanent</option>
                </select>
              </div>
              
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Vitesse de refroidissement</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <input type="range" min="0.1" max="2" step="0.1" value="0.5" style="flex-grow: 1;">
                  <span>0.5°C/min</span>
                </div>
              </div>
              
              <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Seuil de transfert</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <input type="range" min="0" max="50" value="42" style="flex-grow: 1;">
                  <span>42°C</span>
                </div>
              </div>
            </div>
          </div>
          
          <div style="text-align: right; margin-top: 15px;">
            <button class="action-button">
              <i class="bi bi-save"></i> Enregistrer la configuration
            </button>
          </div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="card" style="height: 100%;">
          <div class="card-header">
            <i class="bi bi-activity card-icon"></i>
            <h3 class="card-title">Activité Thermique</h3>
          </div>
          
          <div style="padding: 15px;">
            <div style="margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 5px;">Transferts récents</div>
              
              <div class="activity-item" style="display: flex; margin-bottom: 10px;">
                <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:01:15</div>
                <div style="flex-grow: 1;">
                  <i class="bi bi-arrow-down" style="color: var(--temp-cool);"></i>
                  Zone 2 → Zone 3
                </div>
              </div>
              
              <div class="activity-item" style="display: flex; margin-bottom: 10px;">
                <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:00:45</div>
                <div style="flex-grow: 1;">
                  <i class="bi bi-plus" style="color: var(--temp-hot);"></i>
                  Nouvelle entrée en Zone 1
                </div>
              </div>
              
              <div class="activity-item" style="display: flex; margin-bottom: 10px;">
                <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:00:30</div>
                <div style="flex-grow: 1;">
                  <i class="bi bi-arrow-up" style="color: var(--temp-hot);"></i>
                  Zone 3 → Zone 2
                </div>
              </div>
              
              <div class="activity-item" style="display: flex; margin-bottom: 10px;">
                <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">07:55:20</div>
                <div style="flex-grow: 1;">
                  <i class="bi bi-arrow-down" style="color: var(--temp-cool);"></i>
                  Zone 1 → Zone 2 (x5)
                </div>
              </div>
            </div>
            
            <div style="margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 5px;">Statistiques thermiques</div>
              
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>Transferts par heure</span>
                <span>24</span>
              </div>
              
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>Température moyenne</span>
                <span>18.5°C</span>
              </div>
              
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>Efficacité thermique</span>
                <span>92%</span>
              </div>
            </div>
            
            <div style="margin-top: 20px;">
              <div style="font-weight: bold; margin-bottom: 10px;">État des accélérateurs thermiques</div>
              
              <div class="accelerator-item">
                <span class="accelerator-name">Accélérateur Thermique</span>
                <span class="accelerator-value">x2.7</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 70%; background-color: #54a0ff;"></div>
              </div>
              
              <div class="accelerator-item" style="margin-top: 10px;">
                <span class="accelerator-name">Connecteur Thermique</span>
                <span class="accelerator-value">x2.1</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 60%; background-color: #54a0ff;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Graphique thermique -->
<div class="card" style="margin-top: 20px;">
  <div class="card-header">
    <i class="bi bi-graph-up card-icon"></i>
    <h2 class="card-title">Graphique Thermique</h2>
  </div>
  
  <div style="padding: 20px;">
    <div style="background-color: rgba(255, 255, 255, 0.05); padding: 20px; border-radius: 10px; height: 300px; position: relative;">
      <!-- Axe Y -->
      <div style="position: absolute; top: 20px; bottom: 40px; left: 40px; width: 1px; background-color: var(--text-secondary);"></div>
      <div style="position: absolute; top: 20px; left: 20px; font-size: 12px; color: var(--text-secondary);">50°C</div>
      <div style="position: absolute; top: 50%; transform: translateY(-50%); left: 20px; font-size: 12px; color: var(--text-secondary);">25°C</div>
      <div style="position: absolute; bottom: 40px; left: 20px; font-size: 12px; color: var(--text-secondary);">0°C</div>
      
      <!-- Axe X -->
      <div style="position: absolute; bottom: 40px; left: 40px; right: 20px; height: 1px; background-color: var(--text-secondary);"></div>
      <div style="position: absolute; bottom: 20px; left: 40px; font-size: 12px; color: var(--text-secondary);">08:00</div>
      <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); font-size: 12px; color: var(--text-secondary);">12:00</div>
      <div style="position: absolute; bottom: 20px; right: 20px; font-size: 12px; color: var(--text-secondary);">16:00</div>
      
      <!-- Courbes -->
      <svg style="position: absolute; top: 20px; left: 40px; right: 20px; bottom: 40px; width: calc(100% - 60px); height: calc(100% - 60px);">
        <!-- Zone 1 -->
        <polyline points="0,0 50,5 100,10 150,5 200,15 250,10 300,5 350,0 400,10 450,5 500,0" style="fill: none; stroke: var(--temp-hot); stroke-width: 2;" />
        
        <!-- Zone 2 -->
        <polyline points="0,50 50,45 100,55 150,60 200,50 250,55 300,60 350,50 400,45 450,50 500,55" style="fill: none; stroke: var(--temp-warm); stroke-width: 2;" />
        
        <!-- Zone 3 -->
        <polyline points="0,100 50,110 100,105 150,100 200,110 250,115 300,110 350,105 400,100 450,110 500,105" style="fill: none; stroke: var(--temp-medium); stroke-width: 2;" />
        
        <!-- Zone 4 -->
        <polyline points="0,150 50,155 100,160 150,155 200,150 250,155 300,160 350,165 400,160 450,155 500,150" style="fill: none; stroke: var(--temp-cool); stroke-width: 2;" />
        
        <!-- Zone 5 -->
        <polyline points="0,180 50,185 100,180 150,185 200,190 250,185 300,180 350,185 400,190 450,185 500,180" style="fill: none; stroke: var(--temp-cool); stroke-width: 2;" />
        
        <!-- Zone 6 -->
        <polyline points="0,200 50,200 100,205 150,200 200,200 250,205 300,200 350,200 400,205 450,200 500,200" style="fill: none; stroke: var(--temp-cool); stroke-width: 2;" />
      </svg>
      
      <!-- Légende -->
      <div style="position: absolute; top: 20px; right: 20px; font-size: 12px; color: var(--text-secondary);">
        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 2px; background-color: var(--temp-hot); margin-right: 5px;"></span> Zone 1</div>
        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 2px; background-color: var(--temp-warm); margin-right: 5px;"></span> Zone 2</div>
        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 2px; background-color: var(--temp-medium); margin-right: 5px;"></span> Zone 3</div>
        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 2px; background-color: var(--temp-cool); margin-right: 5px;"></span> Zone 4-6</div>
      </div>
    </div>
  </div>
</div>
