
// Correctif 2025 pour l'agent
if (global.calendar2025) {
    const originalMessage = message;
    const dateContext = global.calendar2025.getFormattedDate();
    
    // Ajouter le contexte 2025 si l'utilisateur demande l'année ou des recherches
    if (/année|date|2025|actualité|recherche|internet/i.test(message)) {
        message = `[CONTEXTE 2025: ${dateContext}] ${message}`;
        console.log("📅 Contexte 2025 ajouté au message");
    }
}

