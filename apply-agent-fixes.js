/**
 * Script d'application des corrections pour l'agent principal
 */

const AgentFixes2025 = require('./agent-fixes-2025');

async function applyAgentFixes() {
    console.log('🚀 Démarrage de l\'application des corrections agent...');
    
    try {
        // Attendre que tous les systèmes soient initialisés
        console.log('⏳ Attente de l\'initialisation des systèmes...');
        
        let attempts = 0;
        const maxAttempts = 20;
        
        while (attempts < maxAttempts) {
            const systemsReady = (
                global.calendar2025 && 
                global.enhancedAgent && 
                global.agentManager
            );
            
            if (systemsReady) {
                console.log('✅ Tous les systèmes sont prêts');
                break;
            }
            
            console.log(`⏳ Tentative ${attempts + 1}/${maxAttempts} - Systèmes en cours d'initialisation...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
            attempts++;
        }
        
        if (attempts >= maxAttempts) {
            console.log('❌ Timeout - Systèmes non initialisés');
            return false;
        }
        
        // Créer l'instance des corrections
        const fixes = new AgentFixes2025();
        
        // Appliquer toutes les corrections
        const success = await fixes.applyAllFixes(global.agentManager);
        
        if (success) {
            console.log('🎉 CORRECTIONS APPLIQUÉES AVEC SUCCÈS !');
            console.log('✅ L\'agent principal Vision Ultra est maintenant:');
            console.log('   📅 Connecté au système 2025');
            console.log('   🌐 Capable de recherches Internet MCP');
            console.log('   ⚡ Optimisé pour de meilleures performances');
            
            // Marquer les corrections comme appliquées
            global.agentFixesApplied = true;
            global.agentFixes2025 = fixes;
            
            return true;
        } else {
            console.log('❌ Échec de l\'application des corrections');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Erreur lors de l\'application des corrections:', error);
        return false;
    }
}

// Exporter la fonction
module.exports = { applyAgentFixes };

// Auto-exécution si appelé directement
if (require.main === module) {
    applyAgentFixes()
        .then(success => {
            if (success) {
                console.log('✅ Script terminé avec succès');
                process.exit(0);
            } else {
                console.log('❌ Script terminé avec des erreurs');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error);
            process.exit(1);
        });
}

// Ajouter les corrections de recherche Internet
const InternetSearchFix = require('./fix-internet-search');

// Modifier la fonction principale pour inclure les corrections Internet
const originalApplyAgentFixes = module.exports.applyAgentFixes;

module.exports.applyAgentFixes = async function() {
    console.log('🚀 Application complète des corrections agent...');
    
    try {
        // 1. Appliquer les corrections principales
        const mainFixesSuccess = await originalApplyAgentFixes();
        
        if (!mainFixesSuccess) {
            console.log('❌ Corrections principales échouées');
            return false;
        }
        
        // 2. Appliquer les corrections de recherche Internet
        console.log('🌐 Application des corrections de recherche Internet...');
        const internetFix = new InternetSearchFix();
        const internetFixesSuccess = await internetFix.applyAllInternetFixes();
        
        if (internetFixesSuccess) {
            console.log('✅ Corrections Internet appliquées');
        } else {
            console.log('⚠️ Corrections Internet partiellement échouées');
        }
        
        // 3. Résumé final
        console.log('🎉 TOUTES LES CORRECTIONS APPLIQUÉES !');
        console.log('✅ Agent principal Vision Ultra maintenant:');
        console.log('   📅 Connecté au système 2025');
        console.log('   🌐 Recherche Internet fonctionnelle');
        console.log('   ⚡ Performances optimisées');
        console.log('   🚫 Fallback désactivé');
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur corrections complètes:', error);
        return false;
    }
};
