/**
 * Script de correction des problèmes de navigation entre les onglets
 * Ce script ajoute un gestionnaire d'erreurs de navigation et corrige les liens dans les templates
 */

const fs = require('fs');
const path = require('path');

// Configuration
console.log('🛠️ Correction des problèmes de navigation entre les onglets...');

// Chemin vers le répertoire de travail
const WORK_DIR = process.cwd();
const VIEWS_DIR = path.join(WORK_DIR, 'views');
const PUBLIC_JS_DIR = path.join(WORK_DIR, 'public', 'js');

// 1. Créer un script de récupération de navigation
console.log('1️⃣ Création du script de récupération de navigation...');

const navigationRecoveryScript = `/**
 * Script de récupération de navigation pour Vision Ultra
 * Ce script détecte les erreurs de navigation et permet de récupérer automatiquement
 */

// Stocker l'URL actuelle dans le stockage local lors du chargement de la page
document.addEventListener('DOMContentLoaded', function() {
  // Ne pas stocker les pages d'erreur
  if (!window.location.pathname.includes('/error')) {
    localStorage.setItem('lastValidUrl', window.location.href);
    console.log('URL valide stockée:', window.location.href);
  }
  
  // Ajouter des gestionnaires d'événements aux liens de navigation
  document.querySelectorAll('a').forEach(function(link) {
    // Ne pas interférer avec les liens externes ou les liens avec des attributs spéciaux
    if (link.getAttribute('target') === '_blank' || link.getAttribute('data-no-recovery')) {
      return;
    }
    
    link.addEventListener('click', function(event) {
      // Stocker l'URL actuelle dans le stockage local
      localStorage.setItem('lastValidUrl', window.location.href);
      console.log('URL valide stockée (clic):', window.location.href);
    });
  });
});

// Fonction pour gérer les erreurs de navigation
window.addEventListener('error', function(event) {
  console.error('Erreur détectée:', event.error);
  
  // Vérifier si l'erreur est liée à la navigation
  if (event.error && (
      event.error.message.includes('navigation') || 
      event.error.message.includes('route') || 
      event.error.message.includes('undefined')
    )) {
    console.log('Erreur de navigation détectée, tentative de récupération...');
    
    // Afficher la boîte de récupération si elle existe
    const recoveryBox = document.getElementById('navigationRecovery');
    if (recoveryBox) {
      recoveryBox.classList.add('show');
    } else {
      // Créer une boîte de récupération si elle n'existe pas
      createRecoveryBox();
    }
    
    // Rediriger vers la page d'accueil après un court délai si l'utilisateur ne fait rien
    setTimeout(function() {
      recoverFromError();
    }, 10000);
  }
});

// Fonction pour créer une boîte de récupération
function createRecoveryBox() {
  const recoveryBox = document.createElement('div');
  recoveryBox.id = 'navigationRecovery';
  recoveryBox.style.position = 'fixed';
  recoveryBox.style.bottom = '20px';
  recoveryBox.style.right = '20px';
  recoveryBox.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
  recoveryBox.style.border = '1px solid rgba(255, 107, 107, 0.3)';
  recoveryBox.style.borderRadius = '10px';
  recoveryBox.style.padding = '15px';
  recoveryBox.style.maxWidth = '300px';
  recoveryBox.style.zIndex = '9999';
  recoveryBox.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
  
  recoveryBox.innerHTML = \`
    <h4 style="margin-top: 0; color: #ff6b6b; font-size: 16px;">
      <i class="bi bi-exclamation-triangle-fill" style="margin-right: 5px;"></i> 
      Problème de navigation
    </h4>
    <p style="margin-bottom: 10px; font-size: 14px;">
      Un problème de navigation a été détecté. Voulez-vous revenir à la page précédente ?
    </p>
    <button onclick="recoverFromError()" style="background-color: #ff6b6b; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer; font-size: 14px;">
      <i class="bi bi-arrow-counterclockwise" style="margin-right: 5px;"></i> Récupérer
    </button>
  \`;
  
  document.body.appendChild(recoveryBox);
}

// Fonction de récupération en cas d'erreur
function recoverFromError() {
  console.log('Tentative de récupération...');
  
  // Récupérer la dernière URL valide
  const lastValidUrl = localStorage.getItem('lastValidUrl') || '/luna';
  
  // Rediriger vers la dernière URL valide
  window.location.href = lastValidUrl;
}

// Fonction pour vérifier si une page est chargée correctement
function checkPageLoaded() {
  // Vérifier si les éléments essentiels sont présents
  const sidebar = document.querySelector('.sidebar');
  const mainContent = document.querySelector('.main-content');
  
  if (!sidebar || !mainContent) {
    console.error('Page mal chargée, éléments essentiels manquants');
    recoverFromError();
    return false;
  }
  
  return true;
}

// Vérifier la page après le chargement complet
window.addEventListener('load', function() {
  setTimeout(function() {
    checkPageLoaded();
  }, 1000);
});
`;

// Créer le répertoire public/js s'il n'existe pas
if (!fs.existsSync(PUBLIC_JS_DIR)) {
  fs.mkdirSync(PUBLIC_JS_DIR, { recursive: true });
}

// Écrire le script de récupération de navigation
const navigationRecoveryScriptPath = path.join(PUBLIC_JS_DIR, 'navigation-recovery.js');
fs.writeFileSync(navigationRecoveryScriptPath, navigationRecoveryScript);
console.log(`✅ Script de récupération de navigation créé: ${navigationRecoveryScriptPath}`);

// 2. Ajouter le script de récupération à tous les templates
console.log('2️⃣ Ajout du script de récupération à tous les templates...');

// Trouver tous les fichiers EJS dans le répertoire views
const ejsFiles = fs.readdirSync(VIEWS_DIR)
  .filter(file => file.endsWith('.ejs'))
  .map(file => path.join(VIEWS_DIR, file));

// Ajouter le script de récupération à chaque fichier EJS
let templatesUpdated = 0;
ejsFiles.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Vérifier si le script est déjà inclus
    if (!content.includes('navigation-recovery.js')) {
      // Ajouter le script avant la fermeture de la balise body
      content = content.replace('</body>', '  <script src="/js/navigation-recovery.js"></script>\n</body>');
      
      // Écrire le contenu modifié
      fs.writeFileSync(filePath, content);
      templatesUpdated++;
      console.log(`✅ Script de récupération ajouté à: ${path.basename(filePath)}`);
    }
  } catch (error) {
    console.error(`❌ Erreur lors de la modification de ${path.basename(filePath)}:`, error.message);
  }
});

console.log(`✅ Script de récupération ajouté à ${templatesUpdated} templates`);

// 3. Créer un middleware de récupération de navigation
console.log('3️⃣ Création d\'un middleware de récupération de navigation...');

const navigationMiddleware = `/**
 * Middleware de récupération de navigation pour Vision Ultra
 * Ce middleware intercepte les erreurs de navigation et redirige vers une page de récupération
 */

// Middleware de récupération de navigation
function navigationRecoveryMiddleware(req, res, next) {
  // Sauvegarder la méthode render originale
  const originalRender = res.render;
  
  // Remplacer la méthode render par une version qui gère les erreurs
  res.render = function(view, options, callback) {
    try {
      // Appeler la méthode render originale
      originalRender.call(this, view, options, (err, html) => {
        if (err) {
          console.error(\`Erreur lors du rendu de la vue \${view}:\`, err);
          
          // Essayer de rendre la page d'erreur
          try {
            originalRender.call(this, 'error', {
              status: 500,
              message: 'Erreur lors du rendu de la page',
              error: { stack: err.stack }
            }, (renderErr, errorHtml) => {
              if (renderErr) {
                // Si la page d'erreur ne peut pas être rendue, envoyer une réponse HTML simple
                console.error('Erreur lors du rendu de la page d\\'erreur:', renderErr);
                res.status(500).send(\`
                  <html>
                    <head>
                      <title>Vision Ultra - Erreur</title>
                      <style>
                        body { font-family: Arial, sans-serif; background-color: #1a1a2e; color: #fff; text-align: center; padding: 50px; }
                        h1 { color: #ff6b6b; }
                        a { color: #4a00e0; text-decoration: none; }
                        a:hover { text-decoration: underline; }
                        .container { max-width: 600px; margin: 0 auto; background-color: rgba(30, 30, 46, 0.7); padding: 30px; border-radius: 10px; }
                        .btn { display: inline-block; background-color: #4a00e0; color: #fff; padding: 10px 20px; border-radius: 5px; margin-top: 20px; }
                      </style>
                      <script>
                        // Stocker l'URL précédente
                        const previousUrl = document.referrer || '/luna';
                        
                        // Fonction pour revenir à la page précédente
                        function goBack() {
                          window.location.href = previousUrl;
                        }
                      </script>
                    </head>
                    <body>
                      <div class="container">
                        <h1>Vision Ultra - Erreur</h1>
                        <p>Une erreur inattendue s'est produite.</p>
                        <button onclick="goBack()" class="btn">Retour à la page précédente</button>
                        <a href="/luna" class="btn">Retour à l'accueil</a>
                      </div>
                    </body>
                  </html>
                \`);
              } else {
                // Envoyer la page d'erreur
                res.status(500).send(errorHtml);
              }
            });
          } catch (criticalError) {
            // En cas d'erreur critique, envoyer une réponse HTML simple
            console.error('Erreur critique lors de la récupération:', criticalError);
            res.status(500).send(\`
              <html>
                <head>
                  <title>Vision Ultra - Erreur Critique</title>
                  <style>
                    body { font-family: Arial, sans-serif; background-color: #1a1a2e; color: #fff; text-align: center; padding: 50px; }
                    h1 { color: #ff6b6b; }
                    a { color: #4a00e0; text-decoration: none; }
                    a:hover { text-decoration: underline; }
                    .container { max-width: 600px; margin: 0 auto; background-color: rgba(30, 30, 46, 0.7); padding: 30px; border-radius: 10px; }
                    .btn { display: inline-block; background-color: #4a00e0; color: #fff; padding: 10px 20px; border-radius: 5px; margin-top: 20px; }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <h1>Vision Ultra - Erreur Critique</h1>
                    <p>Une erreur critique s'est produite.</p>
                    <a href="/luna" class="btn">Retour à l'accueil</a>
                  </div>
                </body>
              </html>
            \`);
          }
        } else if (callback) {
          // Si un callback est fourni, l'appeler avec le HTML généré
          callback(null, html);
        } else {
          // Sinon, envoyer le HTML généré
          res.send(html);
        }
      });
    } catch (error) {
      console.error('Erreur lors de l\\'appel à res.render:', error);
      next(error);
    }
  };
  
  next();
}

module.exports = navigationRecoveryMiddleware;
`;

// Créer le répertoire middleware s'il n'existe pas
const MIDDLEWARE_DIR = path.join(WORK_DIR, 'middleware');
if (!fs.existsSync(MIDDLEWARE_DIR)) {
  fs.mkdirSync(MIDDLEWARE_DIR, { recursive: true });
}

// Écrire le middleware de récupération de navigation
const navigationMiddlewarePath = path.join(MIDDLEWARE_DIR, 'navigation-recovery.js');
fs.writeFileSync(navigationMiddlewarePath, navigationMiddleware);
console.log(`✅ Middleware de récupération de navigation créé: ${navigationMiddlewarePath}`);

// 4. Créer un script d'application du middleware
console.log('4️⃣ Création d\'un script d\'application du middleware...');

const applyMiddlewareScript = `/**
 * Script d'application du middleware de récupération de navigation
 * Ce script doit être exécuté pour appliquer le middleware à l'application
 */

// Importer le middleware de récupération de navigation
const navigationRecoveryMiddleware = require('./middleware/navigation-recovery');

// Appliquer le middleware à l'application
console.log('Application du middleware de récupération de navigation...');
app.use(navigationRecoveryMiddleware);
console.log('✅ Middleware de récupération de navigation appliqué avec succès');
`;

// Écrire le script d'application du middleware
const applyMiddlewareScriptPath = path.join(WORK_DIR, 'apply-navigation-recovery.js');
fs.writeFileSync(applyMiddlewareScriptPath, applyMiddlewareScript);
console.log(`✅ Script d'application du middleware créé: ${applyMiddlewareScriptPath}`);

console.log('\n✅ Correction des problèmes de navigation entre les onglets terminée !');
console.log('\nPour appliquer les corrections, suivez ces étapes :');
console.log('1. Arrêtez le serveur actuel');
console.log('2. Ajoutez le middleware dans votre fichier server.js');
console.log('   - Importez le middleware: const navigationRecoveryMiddleware = require(\'./middleware/navigation-recovery\');');
console.log('   - Appliquez le middleware: app.use(navigationRecoveryMiddleware);');
console.log('3. Redémarrez le serveur');
console.log('\nLes corrections devraient maintenant être appliquées et la navigation entre les onglets devrait fonctionner correctement.');
