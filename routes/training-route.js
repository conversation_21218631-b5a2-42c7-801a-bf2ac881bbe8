/**
 * Routes pour la formation des agents
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const AgentTrainer = require('../agent-trainer');

// Créer une instance du formateur d'agents
const agentTrainer = new AgentTrainer({
    dataPath: path.join(__dirname, '..', 'data', 'training'),
    debug: true
});

// Variable pour stocker les références à la mémoire thermique et au gestionnaire d'agents
let thermalMemory = null;
let agentManager = null;

/**
 * Initialise le formateur d'agents avec la mémoire thermique et le gestionnaire d'agents
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} manager - Instance du gestionnaire d'agents
 */
function initializeTrainer(memory, manager) {
    thermalMemory = memory;
    agentManager = manager;

    // Définir la mémoire thermique et le gestionnaire d'agents
    agentTrainer.setThermalMemory(memory);
    agentTrainer.setAgentManager(manager);

    console.log('Formateur d\'agents initialisé avec la mémoire thermique et le gestionnaire d\'agents');
}

/**
 * GET /api/training/datasets
 * Récupère la liste des ensembles de données disponibles
 */
router.get('/datasets', async (req, res) => {
    try {
        const datasets = await agentTrainer.getDatasets();

        res.json({
            success: true,
            datasets
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des ensembles de données:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/training/datasets/:id
 * Récupère un ensemble de données par son ID
 */
router.get('/datasets/:id', async (req, res) => {
    try {
        const datasetId = req.params.id;
        const dataset = await agentTrainer.getDataset(datasetId);

        if (!dataset) {
            return res.status(404).json({
                success: false,
                error: `Ensemble de données ${datasetId} non trouvé`
            });
        }

        res.json({
            success: true,
            dataset
        });
    } catch (error) {
        console.error(`Erreur lors de la récupération de l'ensemble de données ${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/training/datasets
 * Crée un nouvel ensemble de données
 */
router.post('/datasets', async (req, res) => {
    try {
        const dataset = req.body;

        // Vérifier les données requises
        if (!dataset.name || !dataset.data) {
            return res.status(400).json({
                success: false,
                error: 'Le nom et les données de l\'ensemble de données sont requis'
            });
        }

        // Créer l'ensemble de données
        const createdDataset = await agentTrainer.createDataset(dataset);

        res.status(201).json({
            success: true,
            dataset: createdDataset
        });
    } catch (error) {
        console.error('Erreur lors de la création de l\'ensemble de données:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/training/history
 * Récupère l'historique des formations
 */
router.get('/history', (req, res) => {
    try {
        const history = agentTrainer.getTrainingHistory();

        res.json({
            success: true,
            history
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des formations:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/training/state
 * Récupère l'état actuel de la formation
 */
router.get('/state', (req, res) => {
    try {
        const state = agentTrainer.getTrainingState();

        res.json({
            success: true,
            state
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'état de la formation:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/training/train
 * Lance une formation d'agent
 */
router.post('/train', async (req, res) => {
    try {
        const { agentId, trainingAgentId, datasetId, options } = req.body;

        // Vérifier les données requises
        if (!agentId || !datasetId) {
            return res.status(400).json({
                success: false,
                error: 'L\'ID de l\'agent et l\'ID de l\'ensemble de données sont requis'
            });
        }

        // Vérifier si l'agent de formation est spécifié
        const actualTrainingAgentId = trainingAgentId || 'agent_training';

        // Vérifier si une formation est déjà en cours
        const state = agentTrainer.getTrainingState();
        if (state.isTraining) {
            return res.status(400).json({
                success: false,
                error: 'Une formation est déjà en cours'
            });
        }

        // Vérifier si l'agent de formation existe
        if (agentManager && !agentManager.getAgent(actualTrainingAgentId)) {
            return res.status(400).json({
                success: false,
                error: `L'agent de formation ${actualTrainingAgentId} n'existe pas`
            });
        }

        // Lancer la formation de manière asynchrone
        res.json({
            success: true,
            message: 'Formation lancée',
            agentId,
            trainingAgentId: actualTrainingAgentId,
            datasetId
        });

        // Préparer les options de formation
        const trainingOptions = {
            ...options,
            trainingAgentId: actualTrainingAgentId
        };

        // Exécuter la formation en arrière-plan
        agentTrainer.trainAgent(agentId, datasetId, trainingOptions)
            .then(results => {
                console.log(`Formation terminée pour l'agent ${agentId} avec l'agent de formation ${actualTrainingAgentId} et l'ensemble de données ${datasetId}`);
            })
            .catch(error => {
                console.error(`Erreur lors de la formation de l'agent ${agentId}:`, error);
            });
    } catch (error) {
        console.error('Erreur lors du lancement de la formation:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/training/cancel
 * Annule la formation en cours
 */
router.post('/cancel', (req, res) => {
    try {
        // Vérifier si une formation est en cours
        const state = agentTrainer.getTrainingState();
        if (!state.isTraining) {
            return res.status(400).json({
                success: false,
                error: 'Aucune formation en cours'
            });
        }

        // Annuler la formation
        agentTrainer.cancelTraining();

        res.json({
            success: true,
            message: 'Formation annulée'
        });
    } catch (error) {
        console.error('Erreur lors de l\'annulation de la formation:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/training/agents
 * Récupère la liste des agents disponibles pour la formation
 */
router.get('/agents', (req, res) => {
    try {
        if (!agentManager) {
            return res.status(500).json({
                success: false,
                error: 'Gestionnaire d\'agents non initialisé'
            });
        }

        const agents = agentManager.agents.agents || {};

        res.json({
            success: true,
            agents
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des agents:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le routeur et la fonction d'initialisation
module.exports = function(memory, manager) {
    // Initialiser le formateur d'agents si la mémoire thermique et le gestionnaire d'agents sont fournis
    if (memory && manager) {
        initializeTrainer(memory, manager);
    }

    return router;
};
