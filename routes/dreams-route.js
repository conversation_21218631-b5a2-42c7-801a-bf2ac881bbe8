/**
 * Routes pour les rêves de l'agent <PERSON><PERSON>
 */

const express = require('express');
const router = express.Router();

module.exports = function(thermalMemory, kyberAccelerators) {
    /**
     * Récupérer tous les rêves
     * GET /api/thermal/memory/dreams
     */
    router.get('/dreams', (req, res) => {
        try {
            // Récupérer toutes les entrées de la mémoire thermique
            const allEntries = thermalMemory.getAllEntries();
            
            // Filtrer les entrées de type "dream"
            const dreams = allEntries.filter(entry => 
                entry.tags && entry.tags.includes('dream')
            ).map(entry => ({
                id: entry.id,
                title: entry.key,
                content: entry.data,
                timestamp: entry.timestamp,
                temperature: entry.temperature,
                tags: entry.tags
            }));
            
            res.json({
                success: true,
                dreams
            });
        } catch (error) {
            console.error('Erreur lors de la récupération des rêves:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    /**
     * Récupérer un rêve par son ID
     * GET /api/thermal/memory/dreams/:id
     */
    router.get('/dreams/:id', (req, res) => {
        try {
            const { id } = req.params;
            
            // Récupérer l'entrée de la mémoire thermique
            const entry = thermalMemory.getEntryById(id);
            
            if (!entry || !entry.tags || !entry.tags.includes('dream')) {
                return res.status(404).json({
                    success: false,
                    error: 'Rêve non trouvé'
                });
            }
            
            const dream = {
                id: entry.id,
                title: entry.key,
                content: entry.data,
                timestamp: entry.timestamp,
                temperature: entry.temperature,
                tags: entry.tags
            };
            
            res.json({
                success: true,
                dream
            });
        } catch (error) {
            console.error('Erreur lors de la récupération du rêve:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    /**
     * Générer un nouveau rêve
     * POST /api/thermal/memory/dreams/generate
     */
    router.post('/dreams/generate', async (req, res) => {
        try {
            // Récupérer les paramètres de génération
            const { temperature, tags } = req.body;
            
            // Générer un rêve
            const dream = await thermalMemory.generateDream(temperature, tags);
            
            if (!dream) {
                return res.status(500).json({
                    success: false,
                    error: 'Erreur lors de la génération du rêve'
                });
            }
            
            res.json({
                success: true,
                dream: {
                    id: dream.id,
                    title: dream.key,
                    content: dream.content,
                    timestamp: dream.timestamp,
                    temperature: dream.temperature,
                    tags: dream.tags
                }
            });
        } catch (error) {
            console.error('Erreur lors de la génération du rêve:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    /**
     * Supprimer un rêve
     * DELETE /api/thermal/memory/dreams/:id
     */
    router.delete('/dreams/:id', (req, res) => {
        try {
            const { id } = req.params;
            
            // Récupérer l'entrée de la mémoire thermique
            const entry = thermalMemory.getEntryById(id);
            
            if (!entry || !entry.tags || !entry.tags.includes('dream')) {
                return res.status(404).json({
                    success: false,
                    error: 'Rêve non trouvé'
                });
            }
            
            // Supprimer l'entrée
            const success = thermalMemory.removeEntry(id);
            
            if (!success) {
                return res.status(500).json({
                    success: false,
                    error: 'Erreur lors de la suppression du rêve'
                });
            }
            
            res.json({
                success: true,
                message: 'Rêve supprimé avec succès'
            });
        } catch (error) {
            console.error('Erreur lors de la suppression du rêve:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    return router;
};
