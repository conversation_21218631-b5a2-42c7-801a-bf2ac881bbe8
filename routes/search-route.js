/**
 * Routes pour la recherche dans la mémoire thermique
 */

const express = require('express');
const router = express.Router();

module.exports = function(thermalMemory) {
    /**
     * Recherche dans la mémoire thermique
     * GET /api/search?query=<terme>&limit=<nombre>
     */
    router.get('/', (req, res) => {
        try {
            const { query, limit = 10 } = req.query;
            
            if (!query) {
                return res.status(400).json({
                    success: false,
                    error: 'Le paramètre query est requis'
                });
            }
            
            // Récupérer toutes les entrées
            const allEntries = thermalMemory.getAllEntries();
            
            // Filtrer les entrées qui correspondent à la requête
            const filteredEntries = allEntries.filter(entry => {
                // Rechercher dans la clé
                if (entry.key.toLowerCase().includes(query.toLowerCase())) {
                    return true;
                }
                
                // Rechercher dans les données
                if (typeof entry.data === 'string' && 
                    entry.data.toLowerCase().includes(query.toLowerCase())) {
                    return true;
                }
                
                // Rechercher dans les données JSON
                if (typeof entry.data === 'object' && entry.data !== null) {
                    const dataString = JSON.stringify(entry.data).toLowerCase();
                    return dataString.includes(query.toLowerCase());
                }
                
                // Rechercher dans la catégorie
                if (entry.category && 
                    entry.category.toLowerCase().includes(query.toLowerCase())) {
                    return true;
                }
                
                return false;
            });
            
            // Trier par température (plus chaud en premier)
            const sortedEntries = filteredEntries.sort((a, b) => b.temperature - a.temperature);
            
            // Limiter le nombre de résultats
            const limitedEntries = sortedEntries.slice(0, parseInt(limit));
            
            res.json({
                success: true,
                query,
                count: limitedEntries.length,
                total: filteredEntries.length,
                results: limitedEntries
            });
        } catch (error) {
            console.error('Erreur lors de la recherche:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    return router;
};
