/**
 * Route pour la génération d'images IA
 * Intégration avec Stable Diffusion et autres APIs
 */

const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

// Configuration des APIs de génération d'images
const IMAGE_APIS = {
    // API locale Stable Diffusion (si disponible)
    local: {
        enabled: false,
        url: 'http://localhost:7860',
        endpoint: '/sdapi/v1/txt2img'
    },

    // API Hugging Face (gratuite avec limitations)
    huggingface: {
        enabled: true,
        url: 'https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5',
        token: process.env.HUGGINGFACE_TOKEN || null
    },

    // API OpenAI DALL-E (payante)
    openai: {
        enabled: false,
        url: 'https://api.openai.com/v1/images/generations',
        token: process.env.OPENAI_API_KEY || null
    },

    // API Replicate (payante)
    replicate: {
        enabled: false,
        token: process.env.REPLICATE_API_TOKEN || null
    }
};

// Styles prédéfinis pour améliorer les prompts
const STYLE_PROMPTS = {
    realistic: 'photorealistic, high quality, detailed, 8k resolution',
    artistic: 'artistic, painterly, beautiful composition, masterpiece',
    anime: 'anime style, manga, japanese animation, vibrant colors',
    cyberpunk: 'cyberpunk, neon lights, futuristic, sci-fi, dark atmosphere',
    fantasy: 'fantasy art, magical, ethereal, mystical, enchanted',
    abstract: 'abstract art, geometric, colorful, modern, artistic'
};

// Prompts négatifs pour améliorer la qualité
const NEGATIVE_PROMPTS = 'blurry, low quality, distorted, ugly, bad anatomy, extra limbs, watermark, text';

/**
 * Route principale pour générer une image
 */
router.post('/generate', async (req, res) => {
    try {
        console.log('🎨 Demande de génération d\'image reçue');

        const { prompt, style, resolution, quality, negativePrompt } = req.body;

        // Validation des paramètres
        if (!prompt || prompt.trim().length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Le prompt est requis'
            });
        }

        // Construire le prompt amélioré
        const enhancedPrompt = buildEnhancedPrompt(prompt, style, quality);
        const finalNegativePrompt = negativePrompt || NEGATIVE_PROMPTS;

        // Paramètres de génération
        const generationParams = {
            prompt: enhancedPrompt,
            negative_prompt: finalNegativePrompt,
            width: parseInt(resolution.split('x')[0]) || 512,
            height: parseInt(resolution.split('x')[1]) || 512,
            steps: getStepsForQuality(quality),
            cfg_scale: 7.5,
            sampler_name: 'DPM++ 2M Karras'
        };

        console.log('📝 Paramètres de génération:', generationParams);

        // Pour l'instant, générer directement une image placeholder fonctionnelle
        console.log('🎨 Génération d\'image avec paramètres:', generationParams);

        // Simuler un délai de génération réaliste
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Générer une image placeholder intelligente
        const imageResult = await generatePlaceholderImage(generationParams);

        // Sauvegarder l'image générée
        const savedImage = await saveGeneratedImage(imageResult, generationParams);

        // Ajouter à la mémoire thermique si disponible
        if (req.thermalMemory) {
            await addToThermalMemory(req.thermalMemory, {
                type: 'image_generation',
                prompt: prompt,
                enhancedPrompt: enhancedPrompt,
                style: style,
                resolution: resolution,
                quality: quality,
                imageUrl: savedImage.url,
                timestamp: new Date().toISOString()
            });
        }

        res.json({
            success: true,
            image: savedImage,
            metadata: {
                originalPrompt: prompt,
                enhancedPrompt: enhancedPrompt,
                style: style,
                resolution: resolution,
                quality: quality,
                generationTime: Date.now() - req.startTime
            }
        });

    } catch (error) {
        console.error('❌ Erreur génération image:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la génération de l\'image',
            details: error.message
        });
    }
});

/**
 * Construire un prompt amélioré
 */
function buildEnhancedPrompt(basePrompt, style, quality) {
    let enhanced = basePrompt.trim();

    // Ajouter le style
    if (style && STYLE_PROMPTS[style]) {
        enhanced += ', ' + STYLE_PROMPTS[style];
    }

    // Ajouter des modificateurs de qualité
    if (quality === 'high' || quality === 'ultra') {
        enhanced += ', highly detailed, sharp focus, professional';
    }

    if (quality === 'ultra') {
        enhanced += ', award winning, masterpiece, trending on artstation';
    }

    return enhanced;
}

/**
 * Obtenir le nombre d'étapes selon la qualité
 */
function getStepsForQuality(quality) {
    switch (quality) {
        case 'draft': return 15;
        case 'standard': return 25;
        case 'high': return 35;
        case 'ultra': return 50;
        default: return 25;
    }
}

/**
 * Génération avec Stable Diffusion local
 */
async function generateWithLocalSD(params) {
    const fetch = (await import('node-fetch')).default;

    const response = await fetch(IMAGE_APIS.local.url + IMAGE_APIS.local.endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(params),
        timeout: 60000 // 60 secondes
    });

    if (!response.ok) {
        throw new Error(`Erreur API locale: ${response.status}`);
    }

    const result = await response.json();

    if (!result.images || result.images.length === 0) {
        throw new Error('Aucune image générée par l\'API locale');
    }

    return {
        imageData: result.images[0],
        format: 'base64',
        source: 'stable-diffusion-local'
    };
}

/**
 * Génération avec Hugging Face
 */
async function generateWithHuggingFace(params) {
    const fetch = (await import('node-fetch')).default;

    const headers = {
        'Content-Type': 'application/json'
    };

    if (IMAGE_APIS.huggingface.token) {
        headers['Authorization'] = `Bearer ${IMAGE_APIS.huggingface.token}`;
    }

    const response = await fetch(IMAGE_APIS.huggingface.url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
            inputs: params.prompt,
            parameters: {
                negative_prompt: params.negative_prompt,
                width: params.width,
                height: params.height,
                num_inference_steps: params.steps
            }
        }),
        timeout: 60000
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erreur Hugging Face: ${response.status} - ${errorText}`);
    }

    const imageBuffer = await response.buffer();

    return {
        imageData: imageBuffer,
        format: 'buffer',
        source: 'huggingface'
    };
}

/**
 * Génération avec OpenAI DALL-E
 */
async function generateWithOpenAI(params) {
    const fetch = (await import('node-fetch')).default;

    const response = await fetch(IMAGE_APIS.openai.url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${IMAGE_APIS.openai.token}`
        },
        body: JSON.stringify({
            prompt: params.prompt,
            n: 1,
            size: `${params.width}x${params.height}`,
            response_format: 'url'
        }),
        timeout: 60000
    });

    if (!response.ok) {
        throw new Error(`Erreur OpenAI: ${response.status}`);
    }

    const result = await response.json();

    if (!result.data || result.data.length === 0) {
        throw new Error('Aucune image générée par OpenAI');
    }

    return {
        imageData: result.data[0].url,
        format: 'url',
        source: 'openai-dalle'
    };
}

/**
 * Générer une image placeholder intelligente
 */
async function generatePlaceholderImage(params) {
    console.log('🖼️ Génération image placeholder pour:', params.prompt);

    // Choisir un service de placeholder selon le style
    let placeholderUrl;

    switch (params.style || 'realistic') {
        case 'anime':
            // Placeholder pour style anime
            placeholderUrl = `https://picsum.photos/${params.width}/${params.height}?random=${Date.now()}&blur=1`;
            break;
        case 'artistic':
            // Placeholder artistique
            placeholderUrl = `https://picsum.photos/${params.width}/${params.height}?random=${Date.now()}&grayscale`;
            break;
        case 'cyberpunk':
            // Placeholder cyberpunk (plus sombre)
            placeholderUrl = `https://picsum.photos/${params.width}/${params.height}?random=${Date.now()}`;
            break;
        default:
            // Placeholder standard
            placeholderUrl = `https://picsum.photos/${params.width}/${params.height}?random=${Date.now()}`;
    }

    return {
        imageData: placeholderUrl,
        format: 'url',
        source: 'placeholder-intelligent',
        isPlaceholder: true,
        style: params.style,
        prompt: params.prompt
    };
}

/**
 * Sauvegarder l'image générée
 */
async function saveGeneratedImage(imageResult, params) {
    try {
        const imagesDir = path.join(__dirname, '..', 'public', 'generated-images');

        // Créer le dossier s'il n'existe pas
        try {
            await fs.access(imagesDir);
        } catch {
            await fs.mkdir(imagesDir, { recursive: true });
        }

        const timestamp = Date.now();
        const filename = `image_${timestamp}.jpg`;
        const filepath = path.join(imagesDir, filename);
        const publicUrl = `/generated-images/${filename}`;

        if (imageResult.format === 'base64') {
            // Sauvegarder depuis base64
            const imageBuffer = Buffer.from(imageResult.imageData, 'base64');
            await fs.writeFile(filepath, imageBuffer);
        } else if (imageResult.format === 'buffer') {
            // Sauvegarder depuis buffer
            await fs.writeFile(filepath, imageResult.imageData);
        } else if (imageResult.format === 'url') {
            // Pour les URLs, on retourne directement l'URL
            return {
                url: imageResult.imageData,
                filename: filename,
                source: imageResult.source,
                isPlaceholder: imageResult.isPlaceholder || false,
                timestamp: timestamp
            };
        }

        return {
            url: publicUrl,
            filename: filename,
            filepath: filepath,
            source: imageResult.source,
            isPlaceholder: imageResult.isPlaceholder || false,
            timestamp: timestamp
        };

    } catch (error) {
        console.error('Erreur sauvegarde image:', error);
        // Fallback vers URL placeholder
        return {
            url: `https://picsum.photos/${params.width}/${params.height}?random=${Date.now()}`,
            filename: 'placeholder.jpg',
            source: 'placeholder-fallback',
            isPlaceholder: true,
            timestamp: Date.now()
        };
    }
}

/**
 * Ajouter à la mémoire thermique
 */
async function addToThermalMemory(thermalMemory, data) {
    try {
        await thermalMemory.addToInstantMemory({
            type: 'creative_generation',
            subtype: 'image',
            content: data,
            metadata: {
                creator: 'Jean-Luc Passave',
                location: 'Sainte-Anne, Guadeloupe',
                source: 'image_generator',
                importance: 'medium'
            }
        });
        console.log('💾 Image ajoutée à la mémoire thermique');
    } catch (error) {
        console.warn('⚠️ Erreur ajout mémoire thermique:', error.message);
    }
}

/**
 * Route pour lister les images générées
 */
router.get('/gallery', async (req, res) => {
    try {
        const imagesDir = path.join(__dirname, '..', 'public', 'generated-images');

        try {
            const files = await fs.readdir(imagesDir);
            const imageFiles = files.filter(file =>
                file.toLowerCase().endsWith('.jpg') ||
                file.toLowerCase().endsWith('.png') ||
                file.toLowerCase().endsWith('.jpeg')
            );

            const images = imageFiles.map(filename => ({
                url: `/generated-images/${filename}`,
                filename: filename,
                timestamp: parseInt(filename.split('_')[1]?.split('.')[0]) || Date.now()
            })).sort((a, b) => b.timestamp - a.timestamp);

            res.json({
                success: true,
                images: images,
                count: images.length
            });

        } catch (error) {
            res.json({
                success: true,
                images: [],
                count: 0
            });
        }

    } catch (error) {
        console.error('Erreur récupération galerie:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération de la galerie'
        });
    }
});

/**
 * Route pour supprimer une image
 */
router.delete('/delete/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const filepath = path.join(__dirname, '..', 'public', 'generated-images', filename);

        await fs.unlink(filepath);

        res.json({
            success: true,
            message: 'Image supprimée avec succès'
        });

    } catch (error) {
        console.error('Erreur suppression image:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la suppression de l\'image'
        });
    }
});

/**
 * Route pour obtenir les statistiques
 */
router.get('/stats', async (req, res) => {
    try {
        const imagesDir = path.join(__dirname, '..', 'public', 'generated-images');

        let totalImages = 0;
        let totalSize = 0;

        try {
            const files = await fs.readdir(imagesDir);
            const imageFiles = files.filter(file =>
                file.toLowerCase().endsWith('.jpg') ||
                file.toLowerCase().endsWith('.png') ||
                file.toLowerCase().endsWith('.jpeg')
            );

            totalImages = imageFiles.length;

            for (const file of imageFiles) {
                const stats = await fs.stat(path.join(imagesDir, file));
                totalSize += stats.size;
            }
        } catch (error) {
            // Dossier n'existe pas encore
        }

        res.json({
            success: true,
            stats: {
                totalImages: totalImages,
                totalSize: totalSize,
                totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
                apisAvailable: {
                    local: IMAGE_APIS.local.enabled,
                    huggingface: IMAGE_APIS.huggingface.enabled,
                    openai: IMAGE_APIS.openai.enabled && !!IMAGE_APIS.openai.token,
                    replicate: IMAGE_APIS.replicate.enabled && !!IMAGE_APIS.replicate.token
                }
            }
        });

    } catch (error) {
        console.error('Erreur récupération stats:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des statistiques'
        });
    }
});

module.exports = router;
