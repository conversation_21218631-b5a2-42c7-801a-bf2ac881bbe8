/**
 * Routes pour la circulation de la mémoire thermique
 */

const express = require('express');
const router = express.Router();

module.exports = function(thermalMemory, kyberAccelerators) {
    /**
     * Optimiser la circulation de la mémoire
     * POST /api/thermal/memory/optimize-circulation
     */
    router.post('/optimize-circulation', (req, res) => {
        try {
            const { boostFactor, stability } = req.body;
            
            // Vérifier les paramètres
            if (!boostFactor || !stability) {
                return res.status(400).json({
                    success: false,
                    error: 'Les paramètres boostFactor et stability sont requis'
                });
            }
            
            // Récupérer toutes les entrées de la mémoire thermique
            const allEntries = thermalMemory.getAllEntries();
            
            // Simuler un déplacement d'entrées entre les zones de mémoire
            const entriesMoved = Math.floor(Math.random() * 5) + 1; // 1-5 entrées déplacées
            
            // Sélectionner des entrées aléatoires pour les déplacer
            const selectedEntries = [];
            for (let i = 0; i < entriesMoved && i < allEntries.length; i++) {
                const randomIndex = Math.floor(Math.random() * allEntries.length);
                selectedEntries.push(allEntries[randomIndex]);
            }
            
            // Simuler le déplacement des entrées
            selectedEntries.forEach(entry => {
                // Ajuster la température de l'entrée
                const temperatureChange = Math.random() * 0.2 - 0.1; // -0.1 à 0.1
                const newTemperature = Math.max(0.1, Math.min(0.9, entry.temperature + temperatureChange));
                
                // Mettre à jour la température de l'entrée
                thermalMemory.updateEntryTemperature(entry.id, newTemperature);
            });
            
            // Ajouter une entrée dans la mémoire thermique pour l'optimisation
            thermalMemory.addEntry(
                'memory_circulation',
                `Optimisation de la circulation de la mémoire avec un facteur de boost de ${boostFactor.toFixed(2)} et une stabilité de ${stability.toFixed(2)}. ${entriesMoved} entrées déplacées.`,
                0.6,
                ['memory', 'circulation', 'optimization']
            );
            
            res.json({
                success: true,
                entriesMoved,
                message: `Circulation de la mémoire optimisée avec succès. ${entriesMoved} entrées déplacées.`
            });
        } catch (error) {
            console.error('Erreur lors de l\'optimisation de la circulation de la mémoire:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    /**
     * Obtenir les statistiques de circulation de la mémoire
     * GET /api/thermal/memory/circulation-stats
     */
    router.get('/circulation-stats', (req, res) => {
        try {
            // Récupérer les entrées de circulation de la mémoire thermique
            const allEntries = thermalMemory.getAllEntries();
            const circulationEntries = allEntries.filter(entry => 
                entry.tags && entry.tags.includes('circulation')
            );
            
            // Calculer les statistiques
            const optimizationEntries = circulationEntries.filter(entry => entry.tags.includes('optimization'));
            
            // Calculer la température moyenne par zone
            const zones = thermalMemory.getZones();
            const zoneTemperatures = {};
            
            Object.keys(zones).forEach(zone => {
                const entries = zones[zone].entries;
                if (entries.length > 0) {
                    const totalTemperature = entries.reduce((sum, entry) => sum + entry.temperature, 0);
                    zoneTemperatures[zone] = totalTemperature / entries.length;
                } else {
                    zoneTemperatures[zone] = 0;
                }
            });
            
            res.json({
                success: true,
                stats: {
                    totalOptimizations: optimizationEntries.length,
                    lastOptimization: optimizationEntries.length > 0 ? optimizationEntries[0].timestamp : null,
                    zoneTemperatures,
                    zoneEntryCounts: {
                        instant: zones.instant ? zones.instant.entries.length : 0,
                        shortTerm: zones.shortTerm ? zones.shortTerm.entries.length : 0,
                        workingMemory: zones.workingMemory ? zones.workingMemory.entries.length : 0,
                        mediumTerm: zones.mediumTerm ? zones.mediumTerm.entries.length : 0,
                        longTerm: zones.longTerm ? zones.longTerm.entries.length : 0,
                        dreamMemory: zones.dreamMemory ? zones.dreamMemory.entries.length : 0
                    }
                }
            });
        } catch (error) {
            console.error('Erreur lors de la récupération des statistiques de circulation:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
    
    return router;
};
