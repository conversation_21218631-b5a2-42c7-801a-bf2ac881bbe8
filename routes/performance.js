/**
 * Routes pour les performances des agents
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, '..', 'data');
const PERFORMANCE_DIR = path.join(DATA_DIR, 'performance');
const PERFORMANCE_HISTORY_FILE = path.join(PERFORMANCE_DIR, 'performance_history.json');
const METRICS_FILE = path.join(PERFORMANCE_DIR, 'metrics.json');

// Variables pour stocker les références à la mémoire thermique et au gestionnaire d'agents
let thermalMemory = null;
let agentManager = null;

/**
 * Initialise le module de performance avec la mémoire thermique et le gestionnaire d'agents
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} manager - Instance du gestionnaire d'agents
 */
function initializePerformanceModule(memory, manager) {
    thermalMemory = memory;
    agentManager = manager;

    // Créer le dossier de performance s'il n'existe pas
    if (!fs.existsSync(PERFORMANCE_DIR)) {
        fs.mkdirSync(PERFORMANCE_DIR, { recursive: true });
    }

    // Créer le fichier d'historique s'il n'existe pas
    if (!fs.existsSync(PERFORMANCE_HISTORY_FILE)) {
        fs.writeFileSync(PERFORMANCE_HISTORY_FILE, JSON.stringify({ history: {} }, null, 2));
    }

    // Créer le fichier de métriques s'il n'existe pas
    if (!fs.existsSync(METRICS_FILE)) {
        fs.writeFileSync(METRICS_FILE, JSON.stringify({ metrics: {} }, null, 2));
    }

    console.log('Module de performance initialisé');
}

/**
 * Charge l'historique des performances
 * @returns {Promise<Object>} - Historique des performances
 */
async function loadPerformanceHistory() {
    try {
        if (await existsAsync(PERFORMANCE_HISTORY_FILE)) {
            const data = await readFileAsync(PERFORMANCE_HISTORY_FILE, 'utf8');
            const history = JSON.parse(data);
            return history.history || {};
        }
        return {};
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des performances:', error);
        return {};
    }
}

/**
 * Sauvegarde l'historique des performances
 * @param {Object} history - Historique des performances
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function savePerformanceHistory(history) {
    try {
        await writeFileAsync(PERFORMANCE_HISTORY_FILE, JSON.stringify({ history }, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'historique des performances:', error);
        return false;
    }
}

/**
 * Charge les métriques de performance
 * @returns {Promise<Object>} - Métriques de performance
 */
async function loadMetrics() {
    try {
        if (await existsAsync(METRICS_FILE)) {
            const data = await readFileAsync(METRICS_FILE, 'utf8');
            const metrics = JSON.parse(data);
            return metrics.metrics || {};
        }
        return {};
    } catch (error) {
        console.error('Erreur lors du chargement des métriques de performance:', error);
        return {};
    }
}

/**
 * Sauvegarde les métriques de performance
 * @param {Object} metrics - Métriques de performance
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveMetrics(metrics) {
    try {
        await writeFileAsync(METRICS_FILE, JSON.stringify({ metrics }, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde des métriques de performance:', error);
        return false;
    }
}

/**
 * Ajoute une entrée à l'historique des performances
 * @param {string} agentId - ID de l'agent
 * @param {number} performance - Score de performance
 * @param {Object} metrics - Métriques de performance
 * @returns {Promise<Object>} - Entrée ajoutée à l'historique
 */
async function addPerformanceEntry(agentId, performance, metrics = {}) {
    try {
        const history = await loadPerformanceHistory();
        
        // Initialiser l'historique de l'agent si nécessaire
        if (!history[agentId]) {
            history[agentId] = [];
        }
        
        // Créer une entrée d'historique
        const historyEntry = {
            date: new Date().toISOString(),
            performance,
            metrics
        };
        
        // Ajouter l'entrée à l'historique
        history[agentId].push(historyEntry);
        
        // Limiter la taille de l'historique à 100 entrées
        if (history[agentId].length > 100) {
            history[agentId] = history[agentId].slice(-100);
        }
        
        // Sauvegarder l'historique
        await savePerformanceHistory(history);
        
        // Mettre à jour les métriques globales
        await updateAgentMetrics(agentId, metrics);
        
        return historyEntry;
    } catch (error) {
        console.error('Erreur lors de l\'ajout à l\'historique des performances:', error);
        return null;
    }
}

/**
 * Met à jour les métriques globales d'un agent
 * @param {string} agentId - ID de l'agent
 * @param {Object} newMetrics - Nouvelles métriques
 * @returns {Promise<Object>} - Métriques mises à jour
 */
async function updateAgentMetrics(agentId, newMetrics) {
    try {
        const allMetrics = await loadMetrics();
        
        // Initialiser les métriques de l'agent si nécessaire
        if (!allMetrics[agentId]) {
            allMetrics[agentId] = {
                accuracy: 0,
                recall: 0,
                precision: 0,
                f1Score: 0,
                responseTime: 0,
                updatedAt: new Date().toISOString()
            };
        }
        
        // Mettre à jour les métriques avec une moyenne pondérée
        const currentMetrics = allMetrics[agentId];
        const weight = 0.3; // Poids des nouvelles métriques (30%)
        
        // Mettre à jour chaque métrique
        for (const key in newMetrics) {
            if (key in currentMetrics && typeof newMetrics[key] === 'number') {
                currentMetrics[key] = (currentMetrics[key] * (1 - weight)) + (newMetrics[key] * weight);
            }
        }
        
        // Mettre à jour la date de mise à jour
        currentMetrics.updatedAt = new Date().toISOString();
        
        // Sauvegarder les métriques
        await saveMetrics(allMetrics);
        
        return currentMetrics;
    } catch (error) {
        console.error('Erreur lors de la mise à jour des métriques de l\'agent:', error);
        return null;
    }
}

/**
 * Calcule la performance actuelle d'un agent
 * @param {string} agentId - ID de l'agent
 * @returns {Promise<number>} - Score de performance
 */
async function calculateAgentPerformance(agentId) {
    try {
        const history = await loadPerformanceHistory();
        
        // Si l'agent n'a pas d'historique, retourner une valeur par défaut
        if (!history[agentId] || history[agentId].length === 0) {
            return 0.7; // Valeur par défaut
        }
        
        // Calculer la moyenne des 5 dernières performances
        const recentEntries = history[agentId].slice(-5);
        const sum = recentEntries.reduce((total, entry) => total + entry.performance, 0);
        return sum / recentEntries.length;
    } catch (error) {
        console.error('Erreur lors du calcul de la performance de l\'agent:', error);
        return 0.7; // Valeur par défaut en cas d'erreur
    }
}

/**
 * GET /api/performance/history
 * Récupère l'historique des performances d'un agent
 */
router.get('/history', async (req, res) => {
    try {
        const { agentId } = req.query;
        
        if (!agentId) {
            return res.status(400).json({
                success: false,
                error: 'Le paramètre agentId est requis'
            });
        }
        
        const history = await loadPerformanceHistory();
        const agentHistory = history[agentId] || [];
        const metrics = await loadMetrics();
        const agentMetrics = metrics[agentId] || {};
        const currentPerformance = await calculateAgentPerformance(agentId);
        
        res.json({
            success: true,
            agentId,
            history: agentHistory,
            metrics: agentMetrics,
            currentPerformance
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des performances:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/performance/record
 * Enregistre une nouvelle performance pour un agent
 */
router.post('/record', async (req, res) => {
    try {
        const { agentId, performance, metrics } = req.body;
        
        if (!agentId || performance === undefined) {
            return res.status(400).json({
                success: false,
                error: 'Les paramètres agentId et performance sont requis'
            });
        }
        
        // Valider la performance
        const performanceValue = parseFloat(performance);
        if (isNaN(performanceValue) || performanceValue < 0 || performanceValue > 1) {
            return res.status(400).json({
                success: false,
                error: 'La performance doit être un nombre entre 0 et 1'
            });
        }
        
        // Ajouter l'entrée à l'historique
        const historyEntry = await addPerformanceEntry(agentId, performanceValue, metrics || {});
        
        // Calculer la performance actuelle
        const currentPerformance = await calculateAgentPerformance(agentId);
        
        res.json({
            success: true,
            agentId,
            historyEntry,
            currentPerformance
        });
    } catch (error) {
        console.error('Erreur lors de l\'enregistrement de la performance:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/performance/stats
 * Récupère les statistiques de performance de tous les agents
 */
router.get('/stats', async (req, res) => {
    try {
        const metrics = await loadMetrics();
        const history = await loadPerformanceHistory();
        
        // Calculer les performances actuelles pour chaque agent
        const stats = {};
        
        for (const agentId in metrics) {
            const currentPerformance = await calculateAgentPerformance(agentId);
            
            stats[agentId] = {
                ...metrics[agentId],
                performance: currentPerformance,
                historyLength: (history[agentId] || []).length
            };
        }
        
        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques de performance:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le routeur et la fonction d'initialisation
module.exports = function(memory, manager) {
    // Initialiser le module de performance si la mémoire thermique et le gestionnaire d'agents sont fournis
    if (memory && manager) {
        initializePerformanceModule(memory, manager);
    }
    
    return router;
};
