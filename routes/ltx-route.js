/**
 * Routes pour le module LTX (Traitement vidéo)
 */

const express = require('express');
const router = express.Router();

module.exports = function(thermalMemory, kyberAccelerators) {
    /**
     * Optimiser le traitement LTX
     * POST /api/thermal/ltx/optimize
     */
    router.post('/optimize', (req, res) => {
        try {
            const { boostFactor, stability } = req.body;

            // Vérifier les paramètres
            if (!boostFactor || !stability) {
                return res.status(400).json({
                    success: false,
                    error: 'Les paramètres boostFactor et stability sont requis'
                });
            }

            // Simuler une optimisation du traitement LTX
            const processingSpeed = Math.min(1.0, Math.random() * 0.3 + 0.7);
            const detectionAccuracy = Math.min(1.0, Math.random() * 0.2 + 0.8);

            // Ajouter une entrée dans la mémoire thermique
            thermalMemory.add(
                'ltx_optimization',
                `Optimisation du traitement LTX avec un facteur de boost de ${boostFactor.toFixed(2)} et une stabilité de ${stability.toFixed(2)}. Vitesse de traitement: ${(processingSpeed * 100).toFixed(1)}%, Précision de détection: ${(detectionAccuracy * 100).toFixed(1)}%`,
                0.6,
                'ltx'
            );

            res.json({
                success: true,
                processingSpeed,
                detectionAccuracy,
                message: 'Traitement LTX optimisé avec succès'
            });
        } catch (error) {
            console.error('Erreur lors de l\'optimisation du traitement LTX:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    /**
     * Enregistrer une détection d'objet
     * POST /api/thermal/ltx/detection
     */
    router.post('/detection', (req, res) => {
        try {
            const { objects, timestamp } = req.body;

            // Vérifier les paramètres
            if (!objects || !Array.isArray(objects)) {
                return res.status(400).json({
                    success: false,
                    error: 'Le paramètre objects est requis et doit être un tableau'
                });
            }

            // Filtrer les objets avec une confiance suffisante
            const validObjects = objects.filter(obj => obj.confidence > 0.7);

            if (validObjects.length === 0) {
                return res.json({
                    success: true,
                    message: 'Aucun objet avec une confiance suffisante n\'a été détecté'
                });
            }

            // Ajouter une entrée dans la mémoire thermique pour chaque objet
            validObjects.forEach(obj => {
                thermalMemory.add(
                    'ltx_detection',
                    `Détection de ${obj.type} avec une confiance de ${(obj.confidence * 100).toFixed(1)}%`,
                    0.7,
                    'ltx'
                );
            });

            res.json({
                success: true,
                detectedObjects: validObjects.length,
                message: `${validObjects.length} objets détectés et enregistrés dans la mémoire thermique`
            });
        } catch (error) {
            console.error('Erreur lors de l\'enregistrement des détections:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    /**
     * Obtenir les statistiques LTX
     * GET /api/thermal/ltx/stats
     */
    router.get('/stats', (req, res) => {
        try {
            // Récupérer les entrées LTX de la mémoire thermique
            const allEntries = thermalMemory.getAllEntries();
            const ltxEntries = allEntries.filter(entry =>
                entry.tags && entry.tags.includes('ltx')
            );

            // Calculer les statistiques
            const detectionEntries = ltxEntries.filter(entry => entry.tags.includes('detection'));
            const optimizationEntries = ltxEntries.filter(entry => entry.tags.includes('optimization'));

            // Obtenir les types d'objets détectés
            const detectedObjectTypes = new Set();
            detectionEntries.forEach(entry => {
                entry.tags.forEach(tag => {
                    if (tag !== 'ltx' && tag !== 'detection') {
                        detectedObjectTypes.add(tag);
                    }
                });
            });

            res.json({
                success: true,
                stats: {
                    totalEntries: ltxEntries.length,
                    detections: detectionEntries.length,
                    optimizations: optimizationEntries.length,
                    detectedObjectTypes: Array.from(detectedObjectTypes),
                    lastDetection: detectionEntries.length > 0 ? detectionEntries[0].timestamp : null,
                    lastOptimization: optimizationEntries.length > 0 ? optimizationEntries[0].timestamp : null
                }
            });
        } catch (error) {
            console.error('Erreur lors de la récupération des statistiques LTX:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    return router;
};
