/**
 * Routes pour les alertes de performance
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, '..', 'data');
const ALERTS_DIR = path.join(DATA_DIR, 'alerts');
const ALERTS_FILE = path.join(ALERTS_DIR, 'alerts.json');
const ALERTS_HISTORY_FILE = path.join(ALERTS_DIR, 'alerts_history.json');
const ALERTS_CONFIG_FILE = path.join(DATA_DIR, 'config', 'performance-alerts.json');

// Variables pour stocker les références à la mémoire thermique et au gestionnaire d'agents
let thermalMemory = null;
let agentManager = null;

/**
 * Initialise le module d'alertes avec la mémoire thermique et le gestionnaire d'agents
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} manager - Instance du gestionnaire d'agents
 */
function initializeAlertsModule(memory, manager) {
    thermalMemory = memory;
    agentManager = manager;

    // Créer le dossier d'alertes s'il n'existe pas
    if (!fs.existsSync(ALERTS_DIR)) {
        fs.mkdirSync(ALERTS_DIR, { recursive: true });
    }

    // Créer le fichier d'alertes s'il n'existe pas
    if (!fs.existsSync(ALERTS_FILE)) {
        fs.writeFileSync(ALERTS_FILE, JSON.stringify({
            activeAlerts: [],
            dismissedAlerts: [],
            lastCheck: new Date().toISOString()
        }, null, 2));
    }

    // Créer le fichier d'historique s'il n'existe pas
    if (!fs.existsSync(ALERTS_HISTORY_FILE)) {
        fs.writeFileSync(ALERTS_HISTORY_FILE, JSON.stringify({ history: [] }, null, 2));
    }

    console.log('Module d\'alertes initialisé');
}

/**
 * Charge la configuration des alertes
 * @returns {Promise<Object>} - Configuration des alertes
 */
async function loadAlertsConfig() {
    try {
        if (await existsAsync(ALERTS_CONFIG_FILE)) {
            const data = await readFileAsync(ALERTS_CONFIG_FILE, 'utf8');
            return JSON.parse(data);
        }
        return {
            thresholds: {},
            notifications: {
                enableSounds: true,
                enableBrowserNotifications: true,
                dismissDuration: 86400000
            },
            monitoring: {
                checkInterval: 300000,
                enableAutoCheck: true
            }
        };
    } catch (error) {
        console.error('Erreur lors du chargement de la configuration des alertes:', error);
        return {
            thresholds: {},
            notifications: {
                enableSounds: true,
                enableBrowserNotifications: true,
                dismissDuration: 86400000
            },
            monitoring: {
                checkInterval: 300000,
                enableAutoCheck: true
            }
        };
    }
}

/**
 * Sauvegarde la configuration des alertes
 * @param {Object} config - Configuration des alertes
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveAlertsConfig(config) {
    try {
        await writeFileAsync(ALERTS_CONFIG_FILE, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de la configuration des alertes:', error);
        return false;
    }
}

/**
 * Charge l'état des alertes
 * @returns {Promise<Object>} - État des alertes
 */
async function loadAlertsState() {
    try {
        if (await existsAsync(ALERTS_FILE)) {
            const data = await readFileAsync(ALERTS_FILE, 'utf8');
            return JSON.parse(data);
        }
        return {
            activeAlerts: [],
            dismissedAlerts: [],
            lastCheck: new Date().toISOString()
        };
    } catch (error) {
        console.error('Erreur lors du chargement de l\'état des alertes:', error);
        return {
            activeAlerts: [],
            dismissedAlerts: [],
            lastCheck: new Date().toISOString()
        };
    }
}

/**
 * Sauvegarde l'état des alertes
 * @param {Object} state - État des alertes
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveAlertsState(state) {
    try {
        await writeFileAsync(ALERTS_FILE, JSON.stringify(state, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'état des alertes:', error);
        return false;
    }
}

/**
 * Charge l'historique des alertes
 * @returns {Promise<Array>} - Historique des alertes
 */
async function loadAlertsHistory() {
    try {
        if (await existsAsync(ALERTS_HISTORY_FILE)) {
            const data = await readFileAsync(ALERTS_HISTORY_FILE, 'utf8');
            const history = JSON.parse(data);
            return history.history || [];
        }
        return [];
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des alertes:', error);
        return [];
    }
}

/**
 * Sauvegarde l'historique des alertes
 * @param {Array} history - Historique des alertes
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveAlertsHistory(history) {
    try {
        await writeFileAsync(ALERTS_HISTORY_FILE, JSON.stringify({ history }, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'historique des alertes:', error);
        return false;
    }
}

/**
 * Crée une alerte
 * @param {string} category - Catégorie de l'alerte
 * @param {string} id - Identifiant unique de l'alerte
 * @param {string} severity - Sévérité de l'alerte (critical, warning, info)
 * @param {string} title - Titre de l'alerte
 * @param {string} message - Message détaillé de l'alerte
 * @returns {Promise<Object>} - Alerte créée
 */
async function createAlert(category, id, severity, title, message) {
    try {
        const alertsState = await loadAlertsState();
        
        // Vérifier si l'alerte existe déjà et n'a pas été ignorée
        const existingActiveAlert = alertsState.activeAlerts.find(alert => alert.id === id);
        if (existingActiveAlert) {
            // Mettre à jour l'alerte existante
            existingActiveAlert.count++;
            existingActiveAlert.lastOccurrence = new Date().toISOString();
            await saveAlertsState(alertsState);
            return existingActiveAlert;
        }
        
        // Vérifier si l'alerte a été ignorée récemment
        const existingDismissedAlert = alertsState.dismissedAlerts.find(alert => alert.id === id);
        if (existingDismissedAlert) {
            // Si l'alerte a été ignorée il y a moins de 24 heures, ne pas la recréer
            const config = await loadAlertsConfig();
            const dismissDuration = config.notifications.dismissDuration || 86400000;
            const timeSinceDismissal = new Date() - new Date(existingDismissedAlert.dismissedAt);
            if (timeSinceDismissal < dismissDuration) {
                return null;
            }
            
            // Supprimer l'alerte de la liste des alertes ignorées
            alertsState.dismissedAlerts = alertsState.dismissedAlerts.filter(alert => alert.id !== id);
        }
        
        // Créer une nouvelle alerte
        const alert = {
            id,
            category,
            severity,
            title,
            message,
            createdAt: new Date().toISOString(),
            lastOccurrence: new Date().toISOString(),
            count: 1,
            read: false
        };
        
        // Ajouter l'alerte à la liste des alertes actives
        alertsState.activeAlerts.push(alert);
        
        // Mettre à jour la date de dernière vérification
        alertsState.lastCheck = new Date().toISOString();
        
        // Sauvegarder l'état des alertes
        await saveAlertsState(alertsState);
        
        // Ajouter l'alerte à l'historique
        const alertsHistory = await loadAlertsHistory();
        alertsHistory.push({
            ...alert,
            timestamp: new Date().toISOString()
        });
        
        // Limiter la taille de l'historique
        if (alertsHistory.length > 100) {
            alertsHistory.splice(0, alertsHistory.length - 100);
        }
        
        // Sauvegarder l'historique
        await saveAlertsHistory(alertsHistory);
        
        return alert;
    } catch (error) {
        console.error('Erreur lors de la création de l\'alerte:', error);
        return null;
    }
}

/**
 * Ignore une alerte
 * @param {string} alertId - Identifiant de l'alerte
 * @returns {Promise<boolean>} - True si l'alerte a été ignorée
 */
async function dismissAlert(alertId) {
    try {
        const alertsState = await loadAlertsState();
        
        // Trouver l'alerte
        const alertIndex = alertsState.activeAlerts.findIndex(alert => alert.id === alertId);
        if (alertIndex === -1) {
            return false;
        }
        
        // Récupérer l'alerte
        const alert = alertsState.activeAlerts[alertIndex];
        
        // Supprimer l'alerte de la liste des alertes actives
        alertsState.activeAlerts.splice(alertIndex, 1);
        
        // Ajouter l'alerte à la liste des alertes ignorées
        alertsState.dismissedAlerts.push({
            id: alert.id,
            dismissedAt: new Date().toISOString()
        });
        
        // Sauvegarder l'état des alertes
        await saveAlertsState(alertsState);
        
        return true;
    } catch (error) {
        console.error('Erreur lors de l\'ignorance de l\'alerte:', error);
        return false;
    }
}

/**
 * Marque une alerte comme lue
 * @param {string} alertId - Identifiant de l'alerte
 * @returns {Promise<boolean>} - True si l'alerte a été marquée comme lue
 */
async function markAlertAsRead(alertId) {
    try {
        const alertsState = await loadAlertsState();
        
        // Trouver l'alerte
        const alert = alertsState.activeAlerts.find(alert => alert.id === alertId);
        if (!alert) {
            return false;
        }
        
        // Marquer l'alerte comme lue
        alert.read = true;
        
        // Sauvegarder l'état des alertes
        await saveAlertsState(alertsState);
        
        return true;
    } catch (error) {
        console.error('Erreur lors du marquage de l\'alerte comme lue:', error);
        return false;
    }
}

/**
 * GET /api/alerts
 * Récupère les alertes actives
 */
router.get('/', async (req, res) => {
    try {
        const alertsState = await loadAlertsState();
        
        res.json({
            success: true,
            alerts: alertsState.activeAlerts,
            lastCheck: alertsState.lastCheck
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des alertes:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/alerts/history
 * Récupère l'historique des alertes
 */
router.get('/history', async (req, res) => {
    try {
        const alertsHistory = await loadAlertsHistory();
        
        res.json({
            success: true,
            history: alertsHistory
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des alertes:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/alerts/dismissed
 * Récupère les alertes ignorées
 */
router.get('/dismissed', async (req, res) => {
    try {
        const alertsState = await loadAlertsState();
        
        res.json({
            success: true,
            alerts: alertsState.dismissedAlerts
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des alertes ignorées:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/alerts/create
 * Crée une nouvelle alerte
 */
router.post('/create', async (req, res) => {
    try {
        const { category, id, severity, title, message } = req.body;
        
        if (!category || !id || !severity || !title || !message) {
            return res.status(400).json({
                success: false,
                error: 'Les paramètres category, id, severity, title et message sont requis'
            });
        }
        
        const alert = await createAlert(category, id, severity, title, message);
        
        res.json({
            success: true,
            alert
        });
    } catch (error) {
        console.error('Erreur lors de la création de l\'alerte:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/alerts/dismiss/:id
 * Ignore une alerte
 */
router.post('/dismiss/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await dismissAlert(id);
        
        if (!success) {
            return res.status(404).json({
                success: false,
                error: 'Alerte non trouvée'
            });
        }
        
        res.json({
            success: true,
            message: 'Alerte ignorée avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de l\'ignorance de l\'alerte:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/alerts/read/:id
 * Marque une alerte comme lue
 */
router.post('/read/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await markAlertAsRead(id);
        
        if (!success) {
            return res.status(404).json({
                success: false,
                error: 'Alerte non trouvée'
            });
        }
        
        res.json({
            success: true,
            message: 'Alerte marquée comme lue avec succès'
        });
    } catch (error) {
        console.error('Erreur lors du marquage de l\'alerte comme lue:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/alerts/config
 * Récupère la configuration des alertes
 */
router.get('/config', async (req, res) => {
    try {
        const config = await loadAlertsConfig();
        
        res.json({
            success: true,
            config
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de la configuration des alertes:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/alerts/config
 * Met à jour la configuration des alertes
 */
router.post('/config', async (req, res) => {
    try {
        const { config } = req.body;
        
        if (!config) {
            return res.status(400).json({
                success: false,
                error: 'Le paramètre config est requis'
            });
        }
        
        const success = await saveAlertsConfig(config);
        
        if (!success) {
            return res.status(500).json({
                success: false,
                error: 'Erreur lors de la sauvegarde de la configuration'
            });
        }
        
        res.json({
            success: true,
            message: 'Configuration mise à jour avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour de la configuration des alertes:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le routeur et la fonction d'initialisation
module.exports = function(memory, manager) {
    // Initialiser le module d'alertes si la mémoire thermique et le gestionnaire d'agents sont fournis
    if (memory && manager) {
        initializeAlertsModule(memory, manager);
    }
    
    return router;
};
