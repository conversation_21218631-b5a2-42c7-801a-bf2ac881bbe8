/**
 * Routes pour la synchronisation des agents
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, '..', 'data');
const SYNC_DIR = path.join(DATA_DIR, 'sync');
const SYNC_HISTORY_FILE = path.join(SYNC_DIR, 'sync_history.json');

// Variables pour stocker les références à la mémoire thermique et au gestionnaire d'agents
let thermalMemory = null;
let agentManager = null;

/**
 * Initialise le module de synchronisation avec la mémoire thermique et le gestionnaire d'agents
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} manager - Instance du gestionnaire d'agents
 */
function initializeSyncModule(memory, manager) {
    thermalMemory = memory;
    agentManager = manager;

    // Créer le dossier de synchronisation s'il n'existe pas
    if (!fs.existsSync(SYNC_DIR)) {
        fs.mkdirSync(SYNC_DIR, { recursive: true });
    }

    // Créer le fichier d'historique s'il n'existe pas
    if (!fs.existsSync(SYNC_HISTORY_FILE)) {
        fs.writeFileSync(SYNC_HISTORY_FILE, JSON.stringify({ history: [] }, null, 2));
    }

    console.log('Module de synchronisation initialisé');
}

/**
 * Charge l'historique des synchronisations
 * @returns {Promise<Array>} - Historique des synchronisations
 */
async function loadSyncHistory() {
    try {
        if (await existsAsync(SYNC_HISTORY_FILE)) {
            const data = await readFileAsync(SYNC_HISTORY_FILE, 'utf8');
            const history = JSON.parse(data);
            return history.history || [];
        }
        return [];
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des synchronisations:', error);
        return [];
    }
}

/**
 * Sauvegarde l'historique des synchronisations
 * @param {Array} history - Historique des synchronisations
 * @returns {Promise<boolean>} - True si la sauvegarde a réussi
 */
async function saveSyncHistory(history) {
    try {
        await writeFileAsync(SYNC_HISTORY_FILE, JSON.stringify({ history }, null, 2));
        return true;
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'historique des synchronisations:', error);
        return false;
    }
}

/**
 * Ajoute une entrée à l'historique des synchronisations
 * @param {Object} syncResult - Résultat de la synchronisation
 * @returns {Promise<Object>} - Entrée ajoutée à l'historique
 */
async function addToSyncHistory(syncResult) {
    try {
        const history = await loadSyncHistory();
        
        // Créer une entrée d'historique
        const historyEntry = {
            id: history.length + 1,
            date: new Date().toISOString(),
            entriesSynced: syncResult.entriesSynced,
            failedEntries: syncResult.failedEntries,
            totalEntries: syncResult.totalEntries,
            mainToTraining: syncResult.mainToTraining,
            trainingToMain: syncResult.trainingToMain,
            duration: syncResult.duration,
            performanceImpact: syncResult.performanceImpact || 0
        };
        
        // Ajouter l'entrée à l'historique
        history.push(historyEntry);
        
        // Sauvegarder l'historique
        await saveSyncHistory(history);
        
        return historyEntry;
    } catch (error) {
        console.error('Erreur lors de l\'ajout à l\'historique des synchronisations:', error);
        return null;
    }
}

/**
 * GET /api/sync/history
 * Récupère l'historique des synchronisations
 */
router.get('/history', async (req, res) => {
    try {
        const history = await loadSyncHistory();
        
        res.json({
            success: true,
            history
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des synchronisations:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/sync/perform
 * Effectue une synchronisation entre les agents
 */
router.post('/perform', async (req, res) => {
    try {
        const { threshold = 0.5, bidirectional = true } = req.body;
        
        // Vérifier si la mémoire thermique et le gestionnaire d'agents sont disponibles
        if (!thermalMemory || !agentManager) {
            return res.status(500).json({
                success: false,
                error: 'Mémoire thermique ou gestionnaire d\'agents non disponible'
            });
        }
        
        // Récupérer les entrées de mémoire de l'agent principal (Claude)
        const mainAgentEntries = thermalMemory.getAllEntries().filter(entry => {
            if (entry.metadata && entry.metadata.agentId) {
                return entry.metadata.agentId === 'agent_claude';
            }
            return true; // Par défaut, considérer les entrées sans agentId comme appartenant à l'agent principal
        });
        
        // Récupérer les entrées de mémoire de l'agent de formation
        const trainingAgentEntries = thermalMemory.getAllEntries().filter(entry => {
            if (entry.metadata && entry.metadata.agentId) {
                return entry.metadata.agentId === 'agent_training';
            }
            return false;
        });
        
        // Identifier les entrées à synchroniser
        const entriesToSync = [];
        
        // Synchroniser de l'agent principal vers l'agent de formation
        for (const entry of mainAgentEntries) {
            // Vérifier si l'entrée existe déjà dans la mémoire de l'agent de formation
            const existsInTrainingAgent = trainingAgentEntries.some(e => e.key === entry.key);
            
            // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
            if (!existsInTrainingAgent && entry.importance >= threshold) {
                entriesToSync.push({
                    source: 'agent_claude',
                    target: 'agent_training',
                    entry
                });
            }
        }
        
        // Synchroniser de l'agent de formation vers l'agent principal si la synchronisation est bidirectionnelle
        if (bidirectional) {
            for (const entry of trainingAgentEntries) {
                // Vérifier si l'entrée existe déjà dans la mémoire de l'agent principal
                const existsInMainAgent = mainAgentEntries.some(e => e.key === entry.key);
                
                // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
                if (!existsInMainAgent && entry.importance >= threshold) {
                    entriesToSync.push({
                        source: 'agent_training',
                        target: 'agent_claude',
                        entry
                    });
                }
            }
        }
        
        // Synchroniser les entrées
        const startTime = Date.now();
        let syncedCount = 0;
        let failedCount = 0;
        let mainToTrainingCount = 0;
        let trainingToMainCount = 0;
        
        for (const syncItem of entriesToSync) {
            const { source, target, entry } = syncItem;
            
            try {
                // Créer une copie de l'entrée pour l'agent cible
                const entryCopy = { ...entry };
                delete entryCopy.id; // Supprimer l'ID pour en générer un nouveau
                
                // Ajouter des métadonnées de synchronisation
                entryCopy.metadata = entryCopy.metadata || {};
                entryCopy.metadata.syncedFrom = source;
                entryCopy.metadata.syncedAt = new Date().toISOString();
                entryCopy.metadata.agentId = target;
                
                // Ajouter l'entrée à la mémoire thermique
                const entryId = thermalMemory.add(
                    entryCopy.key,
                    entryCopy.data,
                    entryCopy.importance,
                    entryCopy.category,
                    entryCopy.metadata
                );
                
                if (entryId) {
                    syncedCount++;
                    
                    // Mettre à jour les compteurs
                    if (source === 'agent_claude') {
                        mainToTrainingCount++;
                    } else {
                        trainingToMainCount++;
                    }
                } else {
                    failedCount++;
                }
            } catch (error) {
                console.error(`Erreur lors de la synchronisation de l'entrée ${entry.id}:`, error);
                failedCount++;
            }
        }
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000; // en secondes
        
        // Calculer l'impact sur les performances (simulé pour l'instant)
        const performanceImpact = Math.random() * 0.2 - 0.05; // Entre -0.05 et 0.15
        
        // Créer le résultat de la synchronisation
        const syncResult = {
            entriesSynced: syncedCount,
            failedEntries: failedCount,
            totalEntries: entriesToSync.length,
            mainToTraining: mainToTrainingCount,
            trainingToMain: trainingToMainCount,
            duration,
            performanceImpact
        };
        
        // Ajouter le résultat à l'historique
        const historyEntry = await addToSyncHistory(syncResult);
        
        res.json({
            success: true,
            result: syncResult,
            historyEntry
        });
    } catch (error) {
        console.error('Erreur lors de la synchronisation:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le routeur et la fonction d'initialisation
module.exports = function(memory, manager) {
    // Initialiser le module de synchronisation si la mémoire thermique et le gestionnaire d'agents sont fournis
    if (memory && manager) {
        initializeSyncModule(memory, manager);
    }
    
    return router;
};
