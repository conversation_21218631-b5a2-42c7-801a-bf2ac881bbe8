/**
 * Routes API pour la formation des agents
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Dossiers de données
const DATA_DIR = path.join(__dirname, '..', 'data');
const CONFIG_DIR = path.join(DATA_DIR, 'config');
const TRAINING_DIR = path.join(DATA_DIR, 'training');
const DATASETS_DIR = path.join(TRAINING_DIR, 'datasets');
const RESULTS_DIR = path.join(TRAINING_DIR, 'results');
const HISTORY_DIR = path.join(TRAINING_DIR, 'history');

// Créer les dossiers s'ils n'existent pas
if (!fs.existsSync(TRAINING_DIR)) fs.mkdirSync(TRAINING_DIR, { recursive: true });
if (!fs.existsSync(DATASETS_DIR)) fs.mkdirSync(DATASETS_DIR, { recursive: true });
if (!fs.existsSync(RESULTS_DIR)) fs.mkdirSync(RESULTS_DIR, { recursive: true });
if (!fs.existsSync(HISTORY_DIR)) fs.mkdirSync(HISTORY_DIR, { recursive: true });

// État de la formation en cours
let trainingState = {
    isTraining: false,
    progress: 0,
    currentAgent: null,
    currentDataset: null,
    startTime: null,
    endTime: null,
    results: null,
    error: null
};

/**
 * Récupère la liste des agents disponibles pour la formation
 */
router.get('/agents', (req, res) => {
    try {
        // Lire le fichier agents.json
        const agentsPath = path.join(CONFIG_DIR, 'agents.json');
        
        if (!fs.existsSync(agentsPath)) {
            return res.json({
                success: false,
                error: 'Le fichier de configuration des agents n\'existe pas'
            });
        }
        
        const agentsData = JSON.parse(fs.readFileSync(agentsPath, 'utf8'));
        
        res.json({
            success: true,
            agents: agentsData.agents
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des agents:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Récupère la liste des ensembles de données disponibles
 */
router.get('/datasets', (req, res) => {
    try {
        // Lire les fichiers d'ensembles de données
        const datasets = [];
        
        if (fs.existsSync(DATASETS_DIR)) {
            const files = fs.readdirSync(DATASETS_DIR);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const datasetPath = path.join(DATASETS_DIR, file);
                    const datasetData = JSON.parse(fs.readFileSync(datasetPath, 'utf8'));
                    datasets.push(datasetData);
                }
            }
        }
        
        res.json({
            success: true,
            datasets
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des ensembles de données:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Récupère l'historique des formations
 */
router.get('/history', (req, res) => {
    try {
        // Lire les fichiers d'historique
        const history = [];
        
        if (fs.existsSync(HISTORY_DIR)) {
            const files = fs.readdirSync(HISTORY_DIR);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const historyPath = path.join(HISTORY_DIR, file);
                    const historyData = JSON.parse(fs.readFileSync(historyPath, 'utf8'));
                    history.push(historyData);
                }
            }
        }
        
        res.json({
            success: true,
            history
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des formations:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Récupère l'état de la formation en cours
 */
router.get('/state', (req, res) => {
    try {
        res.json({
            success: true,
            state: trainingState
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'état de la formation:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Lance une formation
 */
router.post('/train', async (req, res) => {
    try {
        // Vérifier si une formation est déjà en cours
        if (trainingState.isTraining) {
            return res.json({
                success: false,
                error: 'Une formation est déjà en cours'
            });
        }
        
        const { agentId, datasetId, options } = req.body;
        
        // Vérifier les paramètres
        if (!agentId || !datasetId) {
            return res.json({
                success: false,
                error: 'Les paramètres agentId et datasetId sont requis'
            });
        }
        
        // Récupérer l'agent
        const agentsPath = path.join(CONFIG_DIR, 'agents.json');
        const agentsData = JSON.parse(fs.readFileSync(agentsPath, 'utf8'));
        const agent = agentsData.agents[agentId];
        
        if (!agent) {
            return res.json({
                success: false,
                error: `L'agent ${agentId} n'existe pas`
            });
        }
        
        // Récupérer l'ensemble de données
        const datasetPath = path.join(DATASETS_DIR, `${datasetId}.json`);
        
        if (!fs.existsSync(datasetPath)) {
            return res.json({
                success: false,
                error: `L'ensemble de données ${datasetId} n'existe pas`
            });
        }
        
        const dataset = JSON.parse(fs.readFileSync(datasetPath, 'utf8'));
        
        // Mettre à jour l'état de la formation
        trainingState = {
            isTraining: true,
            progress: 0,
            currentAgent: agent,
            currentDataset: dataset,
            startTime: Date.now(),
            endTime: null,
            results: null,
            error: null
        };
        
        // Lancer la formation en arrière-plan
        startTraining(agent, dataset, options)
            .then(results => {
                // Mettre à jour l'état de la formation
                trainingState.isTraining = false;
                trainingState.progress = 100;
                trainingState.endTime = Date.now();
                trainingState.results = results;
                
                // Sauvegarder les résultats
                saveTrainingResults(results);
                
                // Sauvegarder l'historique
                saveTrainingHistory({
                    id: results.id,
                    agentId,
                    datasetId,
                    options,
                    startTime: trainingState.startTime,
                    endTime: trainingState.endTime,
                    duration: trainingState.endTime - trainingState.startTime,
                    samplesProcessed: results.samplesProcessed,
                    accuracy: results.accuracy,
                    loss: results.loss,
                    memoryEntries: results.memoryEntryIds ? results.memoryEntryIds.length : 0
                });
            })
            .catch(error => {
                console.error('Erreur lors de la formation:', error);
                
                // Mettre à jour l'état de la formation
                trainingState.isTraining = false;
                trainingState.progress = 0;
                trainingState.endTime = Date.now();
                trainingState.error = error.message;
            });
        
        res.json({
            success: true,
            message: 'Formation lancée avec succès'
        });
    } catch (error) {
        console.error('Erreur lors du lancement de la formation:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Annule la formation en cours
 */
router.post('/cancel', (req, res) => {
    try {
        // Vérifier si une formation est en cours
        if (!trainingState.isTraining) {
            return res.json({
                success: false,
                error: 'Aucune formation en cours'
            });
        }
        
        // Mettre à jour l'état de la formation
        trainingState.isTraining = false;
        trainingState.endTime = Date.now();
        trainingState.error = 'Formation annulée par l\'utilisateur';
        
        res.json({
            success: true,
            message: 'Formation annulée avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de l\'annulation de la formation:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Récupère les résultats d'une formation
 */
router.get('/results/:id', (req, res) => {
    try {
        const { id } = req.params;
        
        // Vérifier si les résultats existent
        const resultsPath = path.join(RESULTS_DIR, `${id}.json`);
        
        if (!fs.existsSync(resultsPath)) {
            return res.json({
                success: false,
                error: `Les résultats de la formation ${id} n'existent pas`
            });
        }
        
        // Lire les résultats
        const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
        
        res.json({
            success: true,
            results
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des résultats de formation:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Récupère les résultats de la dernière formation
 */
router.get('/results/latest', (req, res) => {
    try {
        // Lire les fichiers d'historique
        const history = [];
        
        if (fs.existsSync(HISTORY_DIR)) {
            const files = fs.readdirSync(HISTORY_DIR);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const historyPath = path.join(HISTORY_DIR, file);
                    const historyData = JSON.parse(fs.readFileSync(historyPath, 'utf8'));
                    history.push(historyData);
                }
            }
        }
        
        // Trier l'historique par date (plus récent en premier)
        history.sort((a, b) => b.startTime - a.startTime);
        
        // Récupérer la dernière formation
        if (history.length === 0) {
            return res.json({
                success: false,
                error: 'Aucune formation disponible'
            });
        }
        
        const latestTraining = history[0];
        
        // Récupérer les résultats
        const resultsPath = path.join(RESULTS_DIR, `${latestTraining.id}.json`);
        
        if (!fs.existsSync(resultsPath)) {
            return res.json({
                success: false,
                error: `Les résultats de la formation ${latestTraining.id} n'existent pas`
            });
        }
        
        // Lire les résultats
        const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
        
        res.json({
            success: true,
            results
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des résultats de la dernière formation:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Lance la formation
 * @param {Object} agent - Agent à former
 * @param {Object} dataset - Ensemble de données
 * @param {Object} options - Options de formation
 * @returns {Promise<Object>} - Résultats de la formation
 */
async function startTraining(agent, dataset, options) {
    return new Promise((resolve, reject) => {
        try {
            // Générer un ID pour la formation
            const trainingId = `training_${Date.now()}`;
            
            // Simuler la formation
            const totalSamples = dataset.data.length * (options.epochs || 1);
            let samplesProcessed = 0;
            let accuracy = 0;
            let loss = 1.0;
            const progressHistory = [];
            const memoryEntryIds = [];
            
            // Simuler le traitement des échantillons
            const processInterval = setInterval(() => {
                // Vérifier si la formation a été annulée
                if (!trainingState.isTraining) {
                    clearInterval(processInterval);
                    reject(new Error('Formation annulée'));
                    return;
                }
                
                // Traiter un lot d'échantillons
                const batchSize = options.batchSize || 10;
                const remainingSamples = totalSamples - samplesProcessed;
                const samplesToProcess = Math.min(batchSize, remainingSamples);
                
                if (samplesToProcess <= 0) {
                    // Formation terminée
                    clearInterval(processInterval);
                    
                    // Résultats finaux
                    const results = {
                        id: trainingId,
                        agentId: agent.id,
                        agent,
                        datasetId: dataset.id,
                        dataset,
                        options,
                        startTime: trainingState.startTime,
                        endTime: Date.now(),
                        duration: Date.now() - trainingState.startTime,
                        samplesProcessed,
                        accuracy,
                        loss,
                        progressHistory,
                        memoryEntryIds
                    };
                    
                    resolve(results);
                    return;
                }
                
                // Mettre à jour les compteurs
                samplesProcessed += samplesToProcess;
                
                // Mettre à jour la précision et la perte (simulation)
                accuracy = Math.min(0.95, accuracy + (Math.random() * 0.05));
                loss = Math.max(0.05, loss - (Math.random() * 0.05));
                
                // Ajouter une entrée à l'historique de progression
                progressHistory.push({
                    samplesProcessed,
                    accuracy,
                    loss
                });
                
                // Créer une entrée de mémoire (simulation)
                if (options.saveToMemory && Math.random() > 0.7) {
                    const memoryEntryId = `memory_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
                    memoryEntryIds.push(memoryEntryId);
                }
                
                // Mettre à jour la progression
                trainingState.progress = (samplesProcessed / totalSamples) * 100;
            }, 500);
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * Sauvegarde les résultats de la formation
 * @param {Object} results - Résultats de la formation
 */
function saveTrainingResults(results) {
    try {
        const resultsPath = path.join(RESULTS_DIR, `${results.id}.json`);
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    } catch (error) {
        console.error('Erreur lors de la sauvegarde des résultats de formation:', error);
    }
}

/**
 * Sauvegarde l'historique de la formation
 * @param {Object} historyEntry - Entrée d'historique
 */
function saveTrainingHistory(historyEntry) {
    try {
        const historyPath = path.join(HISTORY_DIR, `${historyEntry.id}.json`);
        fs.writeFileSync(historyPath, JSON.stringify(historyEntry, null, 2));
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'historique de formation:', error);
    }
}

module.exports = router;
