/**
 * Script de test complet pour la mémoire thermique
 * Teste toutes les nouvelles fonctionnalités avancées
 */

const ThermalMemory = require('./thermal-memory-complete');

class ThermalMemoryTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  /**
   * Exécute un test et enregistre le résultat
   */
  runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      const result = testFunction();
      if (result) {
        this.testResults.passed++;
        this.testResults.details.push({
          name: testName,
          status: 'PASSED',
          message: 'Test réussi'
        });
        console.log(`✅ ${testName}`);
      } else {
        this.testResults.failed++;
        this.testResults.details.push({
          name: testName,
          status: 'FAILED',
          message: 'Test échoué - résultat false'
        });
        console.log(`❌ ${testName}`);
      }
    } catch (error) {
      this.testResults.failed++;
      this.testResults.details.push({
        name: testName,
        status: 'ERROR',
        message: error.message
      });
      console.log(`❌ ${testName} - Erreur: ${error.message}`);
    }
  }

  /**
   * Test d'initialisation de base
   */
  testBasicInitialization() {
    const memory = new ThermalMemory();
    
    return memory && 
           memory.instantMemory && 
           memory.shortTerm && 
           memory.workingMemory && 
           memory.mediumTerm && 
           memory.longTerm && 
           memory.dreamMemory &&
           memory.stats &&
           memory.config;
  }

  /**
   * Test d'ajout d'entrées
   */
  testAddEntries() {
    const memory = new ThermalMemory();
    
    const id1 = memory.add('test1', 'Données de test 1', 0.8, 'test');
    const id2 = memory.add('test2', 'Données de test 2', 0.5, 'test');
    const id3 = memory.add('test3', 'Données de test 3', 0.2, 'test');
    
    return id1 && id2 && id3 && memory.stats.totalEntries === 3;
  }

  /**
   * Test de récupération d'entrées
   */
  testGetEntries() {
    const memory = new ThermalMemory();
    
    const id = memory.add('test', 'Données de test', 0.7, 'test');
    const entry = memory.get(id);
    
    return entry && 
           entry.key === 'test' && 
           entry.data === 'Données de test' &&
           entry.category === 'test';
  }

  /**
   * Test de recherche sémantique
   */
  testSemanticSearch() {
    const memory = new ThermalMemory();
    
    memory.add('javascript', 'Langage de programmation web', 0.8, 'programming');
    memory.add('python', 'Langage de programmation polyvalent', 0.7, 'programming');
    memory.add('cuisine', 'Art de préparer les aliments', 0.5, 'lifestyle');
    
    const results = memory.search('programmation', { limit: 5 });
    
    return results.length === 2 && 
           results.every(result => result.entry.category === 'programming');
  }

  /**
   * Test d'analyse des patterns
   */
  testPatternAnalysis() {
    const memory = new ThermalMemory();
    
    // Ajouter plusieurs entrées avec des patterns
    memory.add('web1', 'Développement web avec JavaScript', 0.8, 'web');
    memory.add('web2', 'CSS et HTML pour le web', 0.7, 'web');
    memory.add('ai1', 'Intelligence artificielle et machine learning', 0.9, 'ai');
    memory.add('ai2', 'Réseaux de neurones artificiels', 0.8, 'ai');
    
    const patterns = memory.analyzePatterns();
    
    return patterns && 
           patterns.totalEntries === 4 &&
           patterns.categories &&
           patterns.topKeywords &&
           patterns.insights;
  }

  /**
   * Test d'optimisation de la mémoire
   */
  testMemoryOptimization() {
    const memory = new ThermalMemory();
    
    // Ajouter des entrées avec différentes températures
    for (let i = 0; i < 10; i++) {
      memory.add(`entry${i}`, `Données ${i}`, Math.random(), 'test');
    }
    
    const initialStats = { ...memory.stats };
    const optimizationResults = memory.optimizeMemory();
    
    return optimizationResults && 
           optimizationResults.duration >= 0 &&
           optimizationResults.initialStats &&
           optimizationResults.finalStats;
  }

  /**
   * Test du mode sommeil
   */
  testSleepMode() {
    const memory = new ThermalMemory();
    
    // Tester l'activation du mode sommeil
    const sleepEnabled = memory.setSleepModeEnabled(true);
    const sleepStatus = memory.getSleepModeStatus();
    
    // Tester la consolidation forcée
    memory.add('test1', 'Données similaires', 0.5, 'test');
    memory.add('test2', 'Données similaires aussi', 0.5, 'test');
    
    memory.forceMemoryConsolidation();
    
    return sleepEnabled && 
           sleepStatus.enabled &&
           memory.sleepMode.consolidationCount >= 0;
  }

  /**
   * Test des statistiques détaillées
   */
  testDetailedStats() {
    const memory = new ThermalMemory();
    
    // Ajouter quelques entrées
    memory.add('stat1', 'Test statistiques', 0.8, 'stats');
    memory.add('stat2', 'Test métriques', 0.6, 'stats');
    
    const detailedStats = memory.getDetailedStats();
    
    return detailedStats &&
           detailedStats.zones &&
           detailedStats.performance &&
           detailedStats.patterns &&
           detailedStats.sleepMode;
  }

  /**
   * Test d'export/import
   */
  testExportImport() {
    const memory1 = new ThermalMemory();
    
    // Ajouter des données
    memory1.add('export1', 'Données à exporter', 0.7, 'export');
    memory1.add('export2', 'Autres données', 0.5, 'export');
    
    // Exporter
    const exportedData = memory1.exportMemory();
    
    // Créer une nouvelle instance et importer
    const memory2 = new ThermalMemory();
    const importSuccess = memory2.importMemory(exportedData);
    
    return importSuccess &&
           memory2.stats.totalEntries === memory1.stats.totalEntries &&
           exportedData.version === '2.0.0';
  }

  /**
   * Test de génération de rêves
   */
  testDreamGeneration() {
    const memory = new ThermalMemory();
    
    // Ajouter des entrées pour la génération de rêves
    memory.add('dream1', 'Voyage dans l\'espace', 0.3, 'imagination');
    memory.add('dream2', 'Océan profond et mystérieux', 0.2, 'imagination');
    memory.add('dream3', 'Forêt enchantée', 0.4, 'imagination');
    
    const dream = memory.generateDream();
    
    return dream &&
           dream.id &&
           dream.key &&
           dream.data &&
           dream.category === 'dream';
  }

  /**
   * Test de gestion des connexions
   */
  testConnectionManagement() {
    const memory = new ThermalMemory();
    
    // Ajouter des entrées liées
    const id1 = memory.add('conn1', 'JavaScript est un langage', 0.7, 'programming');
    const id2 = memory.add('conn2', 'Langage de programmation web', 0.6, 'programming');
    
    // Forcer la consolidation pour créer des connexions
    memory.forceMemoryConsolidation();
    
    const entry1 = memory.get(id1);
    const entry2 = memory.get(id2);
    
    // Vérifier que des connexions ont été créées ou que le système fonctionne
    return entry1 && entry2 && memory.stats.totalEntries === 2;
  }

  /**
   * Exécute tous les tests
   */
  runAllTests() {
    console.log('🧪 TESTS COMPLETS DE LA MÉMOIRE THERMIQUE');
    console.log('=' * 50);

    this.runTest('Initialisation de base', () => this.testBasicInitialization());
    this.runTest('Ajout d\'entrées', () => this.testAddEntries());
    this.runTest('Récupération d\'entrées', () => this.testGetEntries());
    this.runTest('Recherche sémantique', () => this.testSemanticSearch());
    this.runTest('Analyse des patterns', () => this.testPatternAnalysis());
    this.runTest('Optimisation mémoire', () => this.testMemoryOptimization());
    this.runTest('Mode sommeil', () => this.testSleepMode());
    this.runTest('Statistiques détaillées', () => this.testDetailedStats());
    this.runTest('Export/Import', () => this.testExportImport());
    this.runTest('Génération de rêves', () => this.testDreamGeneration());
    this.runTest('Gestion des connexions', () => this.testConnectionManagement());

    this.displayResults();
  }

  /**
   * Affiche les résultats des tests
   */
  displayResults() {
    console.log('\n' + '=' * 50);
    console.log('📊 RÉSULTATS DES TESTS');
    console.log('=' * 50);

    console.log(`✅ Tests réussis: ${this.testResults.passed}`);
    console.log(`❌ Tests échoués: ${this.testResults.failed}`);
    console.log(`📈 Total: ${this.testResults.total}`);

    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    console.log(`🎯 Taux de réussite: ${successRate}%`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ TESTS ÉCHOUÉS:');
      this.testResults.details
        .filter(test => test.status !== 'PASSED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.message}`);
        });
    }

    console.log('\n' + '=' * 50);
    if (this.testResults.failed === 0) {
      console.log('🎉 TOUS LES TESTS SONT RÉUSSIS !');
      console.log('✨ La mémoire thermique est complètement fonctionnelle.');
    } else {
      console.log('⚠️  CERTAINS TESTS ONT ÉCHOUÉ');
      console.log('🔧 Veuillez vérifier les fonctionnalités concernées.');
    }
    console.log('=' * 50);
  }
}

// Exécuter les tests
const tester = new ThermalMemoryTester();
tester.runAllTests();
