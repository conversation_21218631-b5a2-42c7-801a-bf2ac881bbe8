{"summary": {"totalTests": 14, "successfulTests": 11, "successRate": 79, "duration": 51}, "results": [{"test": "PORT_DETECTION", "success": true, "data": "Port 3001 détecté", "timestamp": "2025-05-28T03:08:56.050Z"}, {"test": "CONNECTIVITY", "success": true, "data": "Application accessible", "timestamp": "2025-05-28T03:08:56.053Z"}, {"test": "API_/api/brain/status", "success": true, "data": {"success": true, "brain": {"isActive": true, "state": {"isAwake": true, "sleepCycles": 0, "dreamState": false, "learningMode": true, "consolidationActive": false, "neuroplasticityLevel": 1}, "qiSystem": {"currentQI": 148.18332954954613, "maxQI": 200, "qiGrowthRate": 0.01, "learningBonus": 0.4818332954954613, "experiencePoints": 18.332954954613285, "cognitiveLevel": "<PERSON><PERSON><PERSON>"}, "neuronMonitoring": {"totalNeurons": 71, "activeNeurons": 36, "neuronGrowthRate": 0.05, "neuronEfficiency": 0.8495419997562698, "neuronHealth": 0.6084507042253521, "neuronRegenerationRate": 0.02}, "emotionalState": {"happiness": 0, "curiosity": 0.6676079717550942, "confidence": -5.738768092861435, "energy": 1, "focus": 0.7033015448556225, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748401726910, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18332954954613, "neuronActivity": 0.49603477033034304}, "emotionalHistory": [{"timestamp": 1748401154726, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401169725, "mood": "curious", "dominantEmotion": "energy", "qi": 148.01479826946513, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401184724, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01479826946513, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401199725, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01479826946513, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401214726, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02959653893026, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401229727, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02959653893026, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401244728, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0443948083954, "neuronActivity": 0.4798269465121861}, {"timestamp": 1748401259891, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05919307786053, "neuronActivity": 0.4880281329603858}, {"timestamp": 1748401274892, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05919307786053, "neuronActivity": 0.4880281329603858}, {"timestamp": 1748401289891, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07407335919012, "neuronActivity": 0.4880281329603858}, {"timestamp": 1748401304893, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07407335919012, "neuronActivity": 0.4880281329603858}, {"timestamp": 1748401319892, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08897409163097, "neuronActivity": 0.4900732440851171}, {"timestamp": 1748401334894, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08897409163097, "neuronActivity": 0.4900732440851171}, {"timestamp": 1748401349893, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10387482407182, "neuronActivity": 0.4900732440851171}, {"timestamp": 1748401364895, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10387482407182, "neuronActivity": 0.4900732440851171}, {"timestamp": 1748401379894, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1188178100338, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401394896, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1188178100338, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401410363, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13376079599576, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401425368, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13376079599576, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401440369, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13870378195773, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401455370, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13870378195773, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401470371, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1436467679197, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401485372, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1436467679197, "neuronActivity": 0.49429859619779315}, {"timestamp": 1748401500620, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.148607115623, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401515627, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.148607115623, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401530630, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1535674633263, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401545632, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1535674633263, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401560739, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15852781102961, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401575740, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15852781102961, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401591168, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16348815873292, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401606169, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16348815873292, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401621502, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16844850643622, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401636503, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16844850643622, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401651503, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17340885413952, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401666503, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17340885413952, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401681909, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17836920184283, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401696909, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.17836920184283, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401711910, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18332954954613, "neuronActivity": 0.49603477033034304}, {"timestamp": 1748401726910, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18332954954613, "neuronActivity": 0.49603477033034304}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:07:46 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:08:01 PM", "mood": "curious"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "11:08:16 PM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "11:08:31 PM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "11:08:46 PM", "mood": "creative"}], "circadianRhythm": 0.866838002449457, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "networks": {"sensory": 15, "working": 12, "longTerm": 20, "emotional": 10, "executive": 8, "creative": 6}, "connections": 198, "lastUpdate": 1748401736054}}, "timestamp": "2025-05-28T03:08:56.055Z"}, {"test": "API_/api/thermal/memory/stats", "success": true, "data": {"success": true, "stats": {"totalMemories": 79, "zone1Count": 79, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.8640567896822039, "cyclesPerformed": 128, "lastCycleTime": "5/27/2025, 11:08:48 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 44.8193359375, "gpu": 49.30126953125001, "memory": 35.85546875, "average": 40.33740234375, "normalized": 0.24698893229166666}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.9985198771374093, "accessOptimization": 0.9194462669091331, "overallPerformance": 0.5753898432139627}, "parameters": {"temperatureCursorSensitivity": 0.10939289395675643, "memoryDecayRate": 0.99, "importanceFactor": 1.182050479620089, "accessFactor": 1.05, "decayFactor": 0.99}}}}, "timestamp": "2025-05-28T03:08:56.056Z"}, {"test": "API_/api/kyber/status", "success": true, "data": [{"name": "KYBER-Alpha", "type": "<PERSON><PERSON><PERSON><PERSON>", "boost": 2.1, "active": true, "efficiency": 87}, {"name": "KYBER-Beta", "type": "Mémoire Thermique", "boost": 1.8, "active": true, "efficiency": 92}, {"name": "KYBER-Gamma", "type": "Réponse Cognitive", "boost": 3.2, "active": false, "efficiency": 72}, {"name": "KYBER-Delta", "type": "Optimisation MCP", "boost": 1.5, "active": true, "efficiency": 83}, {"name": "KYBER-Epsilon", "type": "Accélération Quantique", "boost": 4, "active": false, "efficiency": 54}], "timestamp": "2025-05-28T03:08:56.057Z"}, {"test": "API_/api/qi/current", "success": true, "data": {"success": true, "qi": {"qi": 203, "baseQI": 203, "maxQI": 250, "trend": "stable", "lastEvaluation": 1748401139911, "totalEvaluations": 1, "isReal": true, "isSimulation": false}, "message": "QI RÉEL récupéré avec succès", "isReal": true, "isSimulation": false, "timestamp": "2025-05-28T03:08:56.058Z"}, "timestamp": "2025-05-28T03:08:56.058Z"}, {"test": "API_/api/security/status", "success": true, "data": {"success": true, "status": {"antivirus": {"active": true, "lastScan": null, "threatsDetected": 0}, "vpn": {"connected": true, "server": {"name": "Secure-FR-1", "ip": "*************", "location": "France", "status": "active"}}, "firewall": {"active": true, "rules": 4, "blockedIPs": 2}, "protection": {"realTime": true, "protectedFiles": 0, "encryptedData": 0}}}, "timestamp": "2025-05-28T03:08:56.059Z"}, {"test": "AGENT_STATUS", "success": true, "data": "Agent <PERSON><PERSON><PERSON><PERSON>", "timestamp": "2025-05-28T03:08:56.060Z"}, {"test": "REASONING_Mathématiques simples", "success": false, "data": "timeout of 15000ms exceeded", "timestamp": "2025-05-28T03:09:11.063Z"}, {"test": "REASONING_Logique déductive", "success": false, "data": "timeout of 15000ms exceeded", "timestamp": "2025-05-28T03:09:28.068Z"}, {"test": "REASONING_Créativité", "success": false, "data": "timeout of 15000ms exceeded", "timestamp": "2025-05-28T03:09:45.071Z"}, {"test": "THERMAL_MEMORY", "success": true, "data": {"success": true, "stats": {"totalMemories": 95, "zone1Count": 95, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.8611518582475279, "cyclesPerformed": 147, "lastCycleTime": "5/27/2025, 11:09:39 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 41.76025390625, "gpu": 45.93627929687501, "memory": 33.408203125, "average": 37.584228515625, "normalized": 0.19600423177083334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.9858198045442502, "accessOptimization": 0.8618432086646036, "overallPerformance": 0.5542989039626561}, "parameters": {"temperatureCursorSensitivity": 0.11048955771867289, "memoryDecayRate": 0.99, "importanceFactor": 1.2935278165250437, "accessFactor": 1.05, "decayFactor": 0.99}}}}, "timestamp": "2025-05-28T03:09:47.084Z"}, {"test": "EVOLUTION_SYSTEM", "success": true, "data": {"success": true, "qi": {"qi": 203, "baseQI": 203, "maxQI": 250, "trend": "stable", "lastEvaluation": 1748401139911, "totalEvaluations": 1, "isReal": true, "isSimulation": false}, "message": "QI RÉEL récupéré avec succès", "isReal": true, "isSimulation": false, "timestamp": "2025-05-28T03:09:47.085Z"}, "timestamp": "2025-05-28T03:09:47.086Z"}, {"test": "PERFORMANCE_SYSTEM", "success": true, "data": [{"name": "KYBER-Alpha", "type": "<PERSON><PERSON><PERSON><PERSON>", "boost": 2.1, "active": true, "efficiency": 87}, {"name": "KYBER-Beta", "type": "Mémoire Thermique", "boost": 1.8, "active": true, "efficiency": 92}, {"name": "KYBER-Gamma", "type": "Réponse Cognitive", "boost": 3.2, "active": true, "efficiency": 91}, {"name": "KYBER-Delta", "type": "Optimisation MCP", "boost": 1.5, "active": false, "efficiency": 67}, {"name": "KYBER-Epsilon", "type": "Accélération Quantique", "boost": 4, "active": false, "efficiency": 80}], "timestamp": "2025-05-28T03:09:47.089Z"}]}