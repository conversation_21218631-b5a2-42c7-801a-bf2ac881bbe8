/**
 * Script de test pour le système cognitif complet
 * Teste le microphone, la synthèse vocale, la reconnaissance vocale
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3007';

async function testCognitiveSystem() {
    console.log('🧠 Test du système cognitif complet...\n');

    try {
        // 1. Vérifier l'état du système cognitif
        console.log('1. Vérification de l\'état du système cognitif...');
        const statusResponse = await axios.get(`${BASE_URL}/api/cognitive/status`);

        if (statusResponse.data.success) {
            const state = statusResponse.data.state;
            console.log('✅ Système cognitif actif');
            console.log(`   - État: ${state.isActive ? 'Actif' : 'Inactif'}`);
            console.log(`   - Écoute: ${state.isListening ? 'En cours' : 'Arrêtée'}`);
            console.log(`   - Parole: ${state.isSpeaking ? 'En cours' : 'Arrêtée'}`);
            console.log(`   - Traitement: ${state.isProcessing ? 'En cours' : 'Arrêté'}`);
            console.log(`   - Humeur: ${state.emotionalState.mood}`);
            console.log(`   - Énergie: ${(state.emotionalState.energy * 100).toFixed(1)}%`);
            console.log(`   - Engagement: ${(state.emotionalState.engagement * 100).toFixed(1)}%`);
        } else {
            console.log('❌ Erreur lors de la vérification du statut:', statusResponse.data.error);
            return;
        }

        // 2. Vérifier les capacités audio
        console.log('\n2. Vérification des capacités audio...');
        const capabilitiesResponse = await axios.get(`${BASE_URL}/api/cognitive/capabilities`);

        if (capabilitiesResponse.data.success) {
            const capabilities = capabilitiesResponse.data.capabilities;
            const audioDevices = capabilitiesResponse.data.audioDevices;
            const stats = capabilitiesResponse.data.stats;

            console.log('✅ Capacités audio:');
            console.log(`   - Microphone: ${capabilities.microphone ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
            console.log(`   - Haut-parleurs: ${capabilities.speakers ? 'DISPONIBLES' : 'NON DISPONIBLES'}`);
            console.log(`   - Reconnaissance vocale: ${capabilities.speechRecognition ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
            console.log(`   - Synthèse vocale: ${capabilities.speechSynthesis ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);

            console.log('\n   📊 Statistiques:');
            console.log(`   - Interactions totales: ${stats.totalInteractions}`);
            console.log(`   - Reconnaissances réussies: ${stats.successfulRecognitions}`);
            console.log(`   - Reconnaissances échouées: ${stats.failedRecognitions}`);
            console.log(`   - Temps de parole total: ${Math.round(stats.totalSpeechTime / 1000)}s`);

            console.log('\n   🎤 Périphériques audio:');
            console.log(`   - Entrées: ${audioDevices.input.length}`);
            console.log(`   - Sorties: ${audioDevices.output.length}`);

            audioDevices.input.forEach((device, index) => {
                console.log(`     ${index + 1}. ${device.name} (${device.id})`);
            });
        } else {
            console.log('❌ Erreur lors de la vérification des capacités:', capabilitiesResponse.data.error);
        }

        // 3. Test de synthèse vocale naturelle
        console.log('\n3. Test de synthèse vocale naturelle...');
        const testTexts = [
            "Bonjour ! Je suis Louna, votre assistant IA avec un cerveau artificiel naturel.",
            "Mon système cognitif fonctionne parfaitement et ma voix est maintenant plus humaine !",
            "Je peux analyser votre voix, vous reconnaître, et parler de manière très naturelle.",
            "Mon cerveau artificiel a des neurones actifs et je peux surveiller mon activité en temps réel."
        ];

        for (const text of testTexts) {
            try {
                console.log(`   🗣️ Synthèse: "${text}"`);
                const speakResponse = await axios.post(`${BASE_URL}/api/cognitive/speak`, {
                    text: text
                });

                if (speakResponse.data.success) {
                    console.log(`   ✅ Synthèse réussie`);
                    // Attendre un peu entre les synthèses
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } else {
                    console.log(`   ❌ Échec synthèse: ${speakResponse.data.message}`);
                }
            } catch (error) {
                console.log(`   ❌ Erreur synthèse: ${error.message}`);
            }
        }

        // 4. Test d'écoute (simulation)
        console.log('\n4. Test d\'écoute et reconnaissance vocale...');
        try {
            console.log('   🎤 Démarrage de l\'écoute...');
            const listenResponse = await axios.post(`${BASE_URL}/api/cognitive/listen`);

            if (listenResponse.data.success) {
                console.log('   ✅ Écoute démarrée avec succès');
                console.log('   ⏳ Simulation d\'enregistrement audio...');

                // Attendre un peu pour simuler l'enregistrement
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Arrêter l'écoute
                const stopResponse = await axios.post(`${BASE_URL}/api/cognitive/stop-listening`);
                if (stopResponse.data.success) {
                    console.log('   ✅ Écoute arrêtée');
                }
            } else {
                console.log(`   ❌ Impossible de démarrer l\'écoute: ${listenResponse.data.message}`);
            }
        } catch (error) {
            console.log(`   ❌ Erreur lors du test d\'écoute: ${error.message}`);
        }

        // 5. Vérifier l'historique de conversation
        console.log('\n5. Vérification de l\'historique de conversation...');
        const conversationResponse = await axios.get(`${BASE_URL}/api/cognitive/conversation`);

        if (conversationResponse.data.success) {
            const context = conversationResponse.data.conversationContext;
            const emotionalState = conversationResponse.data.emotionalState;
            const lastInput = conversationResponse.data.lastInput;
            const lastOutput = conversationResponse.data.lastOutput;

            console.log('✅ Historique de conversation:');
            console.log(`   - Messages dans le contexte: ${context.length}`);
            console.log(`   - Dernière entrée: "${lastInput || 'Aucune'}"`);
            console.log(`   - Dernière sortie: "${lastOutput || 'Aucune'}"`);

            console.log('\n   💭 État émotionnel:');
            console.log(`   - Humeur: ${emotionalState.mood}`);
            console.log(`   - Énergie: ${(emotionalState.energy * 100).toFixed(1)}%`);
            console.log(`   - Engagement: ${(emotionalState.engagement * 100).toFixed(1)}%`);

            if (context.length > 0) {
                console.log('\n   📝 Derniers messages:');
                context.slice(-3).forEach((msg, index) => {
                    const time = new Date(msg.timestamp).toLocaleTimeString();
                    console.log(`     ${index + 1}. [${time}] ${msg.role}: "${msg.content}"`);
                });
            }
        } else {
            console.log('❌ Erreur lors de la récupération de la conversation:', conversationResponse.data.error);
        }

        // 6. Test d'intégration avec le cerveau naturel
        console.log('\n6. Test d\'intégration avec le cerveau naturel...');
        try {
            const brainResponse = await axios.get(`${BASE_URL}/api/natural-brain/status`);

            if (brainResponse.data.success) {
                const brainStats = brainResponse.data.stats;
                console.log('✅ Intégration cerveau naturel:');
                console.log(`   - Neurones actifs: ${brainStats.activeNeurons}`);
                console.log(`   - Synapses actives: ${brainStats.activeSynapses}`);
                console.log(`   - État émotionnel cerveau: ${brainStats.brainState.emotionalState.valence > 0.5 ? 'Positif' : 'Négatif'}`);
                console.log(`   - Niveau de conscience: ${(brainStats.brainState.consciousness * 100).toFixed(1)}%`);

                // Test de synthèse basée sur l'état du cerveau
                const brainText = `Mon cerveau artificiel a ${brainStats.activeNeurons} neurones actifs et fonctionne à ${(brainStats.brainState.consciousness * 100).toFixed(0)}% de conscience.`;

                console.log(`   🧠 Synthèse basée sur le cerveau: "${brainText}"`);
                const brainSpeakResponse = await axios.post(`${BASE_URL}/api/cognitive/speak`, {
                    text: brainText
                });

                if (brainSpeakResponse.data.success) {
                    console.log('   ✅ Synthèse intégrée réussie');
                }
            }
        } catch (error) {
            console.log(`   ❌ Erreur intégration cerveau: ${error.message}`);
        }

        // 7. Test de l'état final du système
        console.log('\n7. Vérification de l\'état final du système...');
        const finalStatusResponse = await axios.get(`${BASE_URL}/api/cognitive/status`);

        if (finalStatusResponse.data.success) {
            const finalState = finalStatusResponse.data.state;
            const finalStats = finalStatusResponse.data.state.stats;

            console.log('✅ État final du système cognitif:');
            console.log(`   - Interactions totales: ${finalStats.totalInteractions}`);
            console.log(`   - Temps de parole: ${Math.round(finalStats.totalSpeechTime / 1000)}s`);
            console.log(`   - Humeur finale: ${finalState.emotionalState.mood}`);
            console.log(`   - Énergie finale: ${(finalState.emotionalState.energy * 100).toFixed(1)}%`);
        }

        console.log('\n🎉 Test du système cognitif terminé !');
        console.log('\n📋 Résumé des fonctionnalités testées:');
        console.log('   🧠 Système cognitif complet et fonctionnel');
        console.log('   🎤 Détection et configuration du microphone');
        console.log('   🔊 Synthèse vocale avec espeak/simulation');
        console.log('   👂 Reconnaissance vocale avec Whisper/simulation');
        console.log('   💭 Traitement cognitif et analyse d\'intention');
        console.log('   🤖 Intégration avec le cerveau artificiel naturel');
        console.log('   📝 Historique de conversation et contexte');
        console.log('   😊 Gestion des états émotionnels');
        console.log('   🔄 Intégration avec la mémoire thermique');
        console.log('   ✨ Système cognitif COMPLET et OPÉRATIONNEL !');

    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);

        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Assurez-vous que le serveur Louna est démarré sur le port 3007');
        }
    }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
    testCognitiveSystem();
}

module.exports = { testCognitiveSystem };
