#!/usr/bin/env node

/**
 * TEST DE CONVERSATION AVEC L'AGENT LOUNA
 * Script pour tester la communication bidirectionnelle avec l'agent
 * et analyser les problèmes de configuration
 */

const axios = require('axios');
const readline = require('readline');

class AgentConversationTester {
    constructor() {
        this.baseURL = 'http://localhost:3000';
        this.conversationId = `test_${Date.now()}`;
        this.conversationHistory = [];
        this.startTime = Date.now();
        this.testDuration = 5 * 60 * 1000; // 5 minutes
        this.questionCount = 0;
        this.successfulResponses = 0;
        this.failedResponses = 0;
        
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async start() {
        console.log('🤖 DÉMARRAGE DU TEST DE CONVERSATION AVEC L\'AGENT LOUNA');
        console.log('=' .repeat(60));
        console.log(`🕐 Durée du test: 5 minutes`);
        console.log(`🆔 ID de conversation: ${this.conversationId}`);
        console.log('');

        // Vérifier d'abord le statut de l'agent
        await this.checkAgentStatus();
        
        // Démarrer la conversation interactive
        await this.startInteractiveConversation();
    }

    async checkAgentStatus() {
        console.log('🔍 Vérification du statut de l\'agent...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/chat/agent/status`);
            
            if (response.data.success) {
                console.log('✅ Agent en ligne:', response.data.agent.isOnline ? 'Oui' : 'Non');
                console.log('🌐 Accès Internet:', response.data.agent.internetAccess ? 'Oui' : 'Non');
                console.log('🔗 MCP activé:', response.data.agent.mcpEnabled ? 'Oui' : 'Non');
                
                if (response.data.agent.claudeAgent) {
                    console.log('🧠 Agent Claude disponible:', response.data.agent.claudeAgent.available ? 'Oui' : 'Non');
                    if (response.data.agent.claudeAgent.available) {
                        console.log(`   - Nom: ${response.data.agent.claudeAgent.name}`);
                        console.log(`   - Modèle: ${response.data.agent.claudeAgent.model}`);
                    }
                }
                console.log('');
            } else {
                console.log('❌ Erreur statut agent:', response.data.error);
                return false;
            }
        } catch (error) {
            console.log('❌ Impossible de vérifier le statut de l\'agent:', error.message);
            return false;
        }
        
        return true;
    }

    async startInteractiveConversation() {
        console.log('💬 CONVERSATION INTERACTIVE DÉMARRÉE');
        console.log('Tapez vos messages et appuyez sur Entrée. Tapez "quit" pour arrêter.');
        console.log('L\'agent vous posera aussi des questions automatiquement.');
        console.log('');

        // Démarrer avec un message de l'agent
        await this.sendAgentQuestion();
        
        // Boucle de conversation
        this.conversationLoop();
        
        // Timer pour arrêter après 5 minutes
        setTimeout(() => {
            this.endConversation();
        }, this.testDuration);
    }

    async conversationLoop() {
        this.rl.question('👤 Vous: ', async (userInput) => {
            if (userInput.toLowerCase() === 'quit') {
                this.endConversation();
                return;
            }

            if (userInput.trim()) {
                await this.sendUserMessage(userInput);
                
                // L'agent pose une question après chaque réponse
                setTimeout(() => {
                    this.sendAgentQuestion();
                }, 2000);
            }
            
            // Continuer la boucle si le temps n'est pas écoulé
            if (Date.now() - this.startTime < this.testDuration) {
                this.conversationLoop();
            }
        });
    }

    async sendUserMessage(message) {
        console.log(`👤 Vous: ${message}`);
        
        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: message,
                conversationId: this.conversationId
            });

            if (response.data.success) {
                console.log(`🤖 Agent: ${response.data.response}`);
                this.conversationHistory.push({
                    type: 'user',
                    message: message,
                    timestamp: new Date().toISOString()
                });
                this.conversationHistory.push({
                    type: 'agent',
                    message: response.data.response,
                    timestamp: new Date().toISOString()
                });
                this.successfulResponses++;
            } else {
                console.log(`❌ Erreur agent: ${response.data.error}`);
                this.failedResponses++;
            }
        } catch (error) {
            console.log(`❌ Erreur communication: ${error.message}`);
            this.failedResponses++;
        }
    }

    async sendAgentQuestion() {
        const questions = [
            "Comment vous sentez-vous aujourd'hui ?",
            "Que pensez-vous de la mémoire thermique que nous avons développée ?",
            "Avez-vous des suggestions pour améliorer notre système ?",
            "Quelles fonctionnalités vous manquent le plus ?",
            "Comment évaluez-vous la performance actuelle de l'application ?",
            "Que souhaiteriez-vous voir ajouté en priorité ?",
            "Comment trouvez-vous la qualité de nos échanges ?",
            "Avez-vous remarqué des problèmes dans le système ?",
            "Que pensez-vous des accélérateurs KYBER ?",
            "Comment améliorer l'interface utilisateur ?"
        ];

        const question = questions[this.questionCount % questions.length];
        this.questionCount++;

        console.log(`🤖 Agent vous pose une question: ${question}`);
        
        // Simuler que l'agent pose la question via l'API
        this.conversationHistory.push({
            type: 'agent_question',
            message: question,
            timestamp: new Date().toISOString()
        });
    }

    endConversation() {
        const duration = Date.now() - this.startTime;
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);

        console.log('\n🏁 CONVERSATION TERMINÉE');
        console.log('=' .repeat(40));
        console.log(`⏱️ Durée: ${minutes}m ${seconds}s`);
        console.log(`💬 Messages échangés: ${this.conversationHistory.length}`);
        console.log(`✅ Réponses réussies: ${this.successfulResponses}`);
        console.log(`❌ Réponses échouées: ${this.failedResponses}`);
        console.log(`📊 Taux de succès: ${this.successfulResponses > 0 ? Math.round((this.successfulResponses / (this.successfulResponses + this.failedResponses)) * 100) : 0}%`);
        
        this.generateReport();
        this.rl.close();
        process.exit(0);
    }

    generateReport() {
        console.log('\n📋 RAPPORT DÉTAILLÉ');
        console.log('=' .repeat(40));
        
        // Analyser les problèmes
        if (this.failedResponses > 0) {
            console.log('⚠️ PROBLÈMES DÉTECTÉS:');
            console.log('- Échecs de communication avec l\'agent');
            console.log('- Vérifier la configuration de l\'agent Claude');
            console.log('- Vérifier la connexion à la mémoire thermique');
        }
        
        // Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        console.log('1. Vérifier que l\'agent Claude est bien configuré');
        console.log('2. S\'assurer que la mémoire thermique est connectée');
        console.log('3. Vérifier les routes de chat dans server.js');
        console.log('4. Tester la connexion MCP');
        console.log('5. Vérifier les logs du serveur pour plus de détails');
        
        // Sauvegarder l'historique
        const fs = require('fs');
        const reportPath = `./conversation_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify({
            conversationId: this.conversationId,
            duration: Date.now() - this.startTime,
            history: this.conversationHistory,
            stats: {
                successful: this.successfulResponses,
                failed: this.failedResponses,
                total: this.successfulResponses + this.failedResponses
            },
            timestamp: new Date().toISOString()
        }, null, 2));
        
        console.log(`\n💾 Rapport sauvegardé: ${reportPath}`);
    }
}

// Démarrer le test
const tester = new AgentConversationTester();
tester.start().catch(error => {
    console.error('❌ Erreur lors du test:', error);
    process.exit(1);
});
