/* 
 * Louna Unified CSS
 * Ce fichier CSS reproduit exactement l'interface montrée dans la capture d'écran
 */

:root {
  /* Couleurs principales */
  --primary-bg: #1e1e2f; /* Fond principal sombre */
  --header-bg: #c8a2c8; /* Barre de navigation rose/violet clair */
  --card-bg: #262640; /* Fond des cartes légèrement plus clair que le fond principal */
  --accent-color: #ff6b6b; /* Couleur d'accent pour les boutons et éléments importants */
  --text-color: #ffffff; /* Couleur du texte principal */
  --text-secondary: #a0a0a0; /* Couleur du texte secondaire */
  --border-color: #3a3a5c; /* Couleur des bordures */
  
  /* Températures des zones */
  --temp-hot: #ff6b6b; /* Rouge pour les zones chaudes */
  --temp-warm: #ff9f43; /* Orange pour les zones tièdes */
  --temp-medium: #1dd1a1; /* Vert pour les zones moyennes */
  --temp-cool: #54a0ff; /* Bleu pour les zones froides */
  
  /* Dimensions */
  --header-height: 60px;
  --sidebar-width: 0px; /* Pas de sidebar latérale dans cette interface */
}

/* Reset et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  background-color: var(--primary-bg);
  color: var(--text-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Barre de navigation horizontale */
.top-navbar {
  height: var(--header-height);
  background-color: var(--header-bg);
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.logo-container {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.logo-container i {
  font-size: 24px;
  margin-right: 10px;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.nav-links {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;
  text-decoration: none;
  color: #333;
  font-size: 12px;
  position: relative;
}

.nav-item i {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--accent-color);
}

.nav-right {
  display: flex;
  align-items: center;
}

.nav-right .nav-item {
  margin-left: 15px;
  margin-right: 0;
}

/* Conteneur principal */
.main-container {
  margin-top: var(--header-height);
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* En-tête de l'interface */
.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
}

.interface-title {
  font-size: 24px;
  font-weight: bold;
}

.interface-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 5px;
}

.status-container {
  display: flex;
  align-items: center;
}

.status-label {
  margin-right: 10px;
  font-size: 14px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1dd1a1;
  margin-right: 5px;
}

.status-text {
  font-size: 14px;
  margin-right: 15px;
}

.action-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
}

/* Cartes */
.card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-icon {
  font-size: 20px;
  margin-right: 10px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
}

.card-badge {
  background-color: #ff9f43;
  color: white;
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 10px;
  margin-left: 10px;
}

/* Grilles et colonnes */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.grid-item {
  min-width: 0;
}

/* Barres de progression */
.progress-bar {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-fill {
  height: 100%;
  background-color: var(--temp-medium);
  border-radius: 4px;
}

.progress-fill.hot {
  background-color: var(--temp-hot);
}

.progress-fill.warm {
  background-color: var(--temp-warm);
}

.progress-fill.cool {
  background-color: var(--temp-cool);
}

/* Zones thermiques */
.thermal-zone {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.zone-name {
  font-size: 14px;
}

.zone-temp {
  font-size: 14px;
  font-weight: bold;
}

.zone-temp.hot {
  color: var(--temp-hot);
}

.zone-temp.warm {
  color: var(--temp-warm);
}

.zone-temp.medium {
  color: var(--temp-medium);
}

.zone-temp.cool {
  color: var(--temp-cool);
}

/* Statistiques */
.stats-container {
  display: flex;
  flex-direction: column;
}

.stat-item {
  margin-bottom: 15px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.stat-badge {
  display: inline-block;
  background-color: rgba(29, 209, 161, 0.2);
  color: #1dd1a1;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 5px;
  margin-top: 5px;
}

/* Accélérateurs */
.accelerator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.accelerator-name {
  font-size: 14px;
}

.accelerator-value {
  font-size: 14px;
  background-color: rgba(84, 160, 255, 0.2);
  color: #54a0ff;
  padding: 2px 8px;
  border-radius: 5px;
}

/* Responsive */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
  }
  
  .nav-item span {
    display: none;
  }
  
  .nav-item i {
    font-size: 20px;
    margin-bottom: 0;
  }
}
