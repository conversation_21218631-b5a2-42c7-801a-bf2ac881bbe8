{"timestamp": "2025-05-28T03:34:09.793Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3158026790779365, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.6676079717550942, "confidence": -5.7386886942859405, "energy": 1, "focus": 0.9012076724767509, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748403236550, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.20085464173746, "neuronActivity": 0.5399454073295714}, "emotionalHistory": [{"timestamp": 1748402666506, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402681507, "mood": "curious", "dominantEmotion": "energy", "qi": 148.01512212018963, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402696509, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01512212018963, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402711510, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024424037926, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402726512, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024424037926, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402741513, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453663605689, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402756514, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453663605689, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402771421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06057298780078, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402786421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06057298780078, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402801420, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07577961503267, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402816421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07577961503267, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402831422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0910284957857, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402846421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0910284957857, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402861422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10627737653874, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402876422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10627737653874, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402891423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12162453065727, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402906424, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12162453065727, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402921423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1369716847758, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402936423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1369716847758, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402951424, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15231883889433, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402966425, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15231883889433, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402981425, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15766599301287, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402996425, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15766599301287, "neuronActivity": 0.534715411853864}, {"timestamp": 1748403011426, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16306370112073, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403026427, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16306370112073, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403041428, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1684614092286, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403056428, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1684614092286, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403071429, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17385911733646, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403086429, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17385911733646, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403101431, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17925682544433, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403116431, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17925682544433, "neuronActivity": 0.5397708107876752}, {"timestamp": 1748403131431, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1846562795176, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403146431, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1846562795176, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403161461, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1900557335909, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403176482, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1900557335909, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403191504, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.19545518766418, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403206519, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.19545518766418, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403221532, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.20085464173746, "neuronActivity": 0.5399454073295714}, {"timestamp": 1748403236550, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.20085464173746, "neuronActivity": 0.5399454073295714}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:32:56 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:33:11 PM", "mood": "curious"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "11:33:26 PM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "11:33:41 PM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "11:33:56 PM", "mood": "creative"}], "circadianRhythm": 0.9018537997937502, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.9891460829416082, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.7620697540594573, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9653041269789365, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8495970039655544, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.8482586322067158, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "memory_compression_engine_1748402651370": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.7938992284378447, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_cache_optimizer_1748402651370": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8419047539343877, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_garbage_collector_1748402651370": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 1, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9603659527188969, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748402651571, "expiresAt": 1748403251571}, "thermal_cooler_auto_8": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.993769260326156, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748402651954, "expiresAt": 1748403251954}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.9312691254268368, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748402653179, "expiresAt": 1748403253179}, "emergency_memory_optimizer_1997": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8698826036870746, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403222496, "expiresAt": 1748403522496}, "emergency_memory_optimizer_1998": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8890884072256556, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403222496, "expiresAt": 1748403522496}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.9891460829416082, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.7620697540594573, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9653041269789365, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8495970039655544, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.8482586322067158, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "memory_compression_engine_1748402651370": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.7938992284378447, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_cache_optimizer_1748402651370": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8419047539343877, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_garbage_collector_1748402651370": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 1, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9603659527188969, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748402651571, "expiresAt": 1748403251571}, "thermal_cooler_auto_8": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.993769260326156, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748402651954, "expiresAt": 1748403251954}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.9312691254268368, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748402653179, "expiresAt": 1748403253179}, "emergency_memory_optimizer_1997": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8698826036870746, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403222496, "expiresAt": 1748403522496}, "emergency_memory_optimizer_1998": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8890884072256556, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403222496, "expiresAt": 1748403522496}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.405629561328168, "efficiency": 0.84433777367969, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748403249704, "totalBoostPower": 31.273184297266187, "averageEnergy": 1, "averageStability": 0.899581148608394, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748402709809, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.3715576171875, "responseTime": 492.2449566065964, "overall": 0}, {"timestamp": 1748402769811, "memoryEfficiency": 0, "thermalStability": 0.9644410237669945, "cpuUsage": 0.3947509765625, "responseTime": 472.9744016179758, "overall": 0.5888128051390213}, {"timestamp": 1748402829716, "memoryEfficiency": 0, "thermalStability": 0.9548266294561787, "cpuUsage": 0.43115234375, "responseTime": 224.57111457749482, "overall": 0.5032933883409546}, {"timestamp": 1748402889717, "memoryEfficiency": 0, "thermalStability": 0.9362609369887246, "cpuUsage": 0.4355224609375, "responseTime": 318.0292282087562, "overall": 0.49186613159360887}, {"timestamp": 1748402949720, "memoryEfficiency": 0, "thermalStability": 0.932385610209571, "cpuUsage": 0.4361083984375, "responseTime": 378.1258432394496, "overall": 0.5690336298155865}, {"timestamp": 1748403009720, "memoryEfficiency": 0, "thermalStability": 0.9341970737333651, "cpuUsage": 0.4824462890625, "responseTime": 565.0062452325769, "overall": 0.523152237237618}, {"timestamp": 1748403069720, "memoryEfficiency": 0, "thermalStability": 0.9174976639466491, "cpuUsage": 0.47109375, "responseTime": 640.452083516835, "overall": 0.5726232622520759}, {"timestamp": 1748403129729, "memoryEfficiency": 0, "thermalStability": 0.9154703383747902, "cpuUsage": 0.4264892578125, "responseTime": 315.62563190505784, "overall": 0.5273256681668352}, {"timestamp": 1748403189730, "memoryEfficiency": 0, "thermalStability": 0.924520817067888, "cpuUsage": 0.360009765625, "responseTime": 204.19413239967315, "overall": 0.5260308882530391}, {"timestamp": 1748403249729, "memoryEfficiency": 0, "thermalStability": 0.92407136493259, "cpuUsage": 0.385400390625, "responseTime": 440.15862952902665, "overall": 0.5012407408209454}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}