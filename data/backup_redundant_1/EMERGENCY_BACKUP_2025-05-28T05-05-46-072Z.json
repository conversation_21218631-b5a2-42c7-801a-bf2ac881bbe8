{"timestamp": "2025-05-28T05:05:46.073Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.0295043003236726, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.4068696530428717, "energy": 1, "focus": 0.8978479822723198, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748408731374, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1353905646712, "neuronActivity": 0.5183302534098974}, "emotionalHistory": [{"timestamp": 1748408461023, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408476023, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01496275322245, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408491025, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01496275322245, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408506025, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299255064449, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408521025, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299255064449, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408536025, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04488825966735, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408551026, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04488825966735, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408566367, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0598510128898, "neuronActivity": 0.4962753222438369}, {"timestamp": 1748408581369, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0598510128898, "neuronActivity": 0.5047260264691891}, {"timestamp": 1748408596368, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07489827315447, "neuronActivity": 0.5047260264691891}, {"timestamp": 1748408611369, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07489827315447, "neuronActivity": 0.5047260264691891}, {"timestamp": 1748408626370, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0899877869403, "neuronActivity": 0.5089513785818651}, {"timestamp": 1748408641372, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0899877869403, "neuronActivity": 0.5089513785818651}, {"timestamp": 1748408656372, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10507730072612, "neuronActivity": 0.5089513785818651}, {"timestamp": 1748408671373, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10507730072612, "neuronActivity": 0.5089513785818651}, {"timestamp": 1748408686373, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12020726213711, "neuronActivity": 0.512996141098886}, {"timestamp": 1748408701374, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12020726213711, "neuronActivity": 0.5183302534098974}, {"timestamp": 1748408716374, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1353905646712, "neuronActivity": 0.5183302534098974}, {"timestamp": 1748408731374, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1353905646712, "neuronActivity": 0.5183302534098974}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:04:31 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:04:46 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:05:01 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:05:16 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:05:31 AM", "mood": "curious"}], "circadianRhythm": 0.9859409204506837, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8465292764418837, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9047296274967489, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8246099608460423, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8000308371453329, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408447775, "expiresAt": 1748409047775}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9839944982454282, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748408447775, "expiresAt": 1748409047775}, "memory_compression_engine_1748408447776": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9577209693680578, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "memory_cache_optimizer_1748408447776": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8514280162904914, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "memory_garbage_collector_1748408447776": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9738823706069462, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9661151286880753, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748408450086, "expiresAt": 1748409050086}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9505934102112454, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748408451309, "expiresAt": 1748409051309}, "emergency_memory_optimizer_128": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8144839647386078, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408483942, "expiresAt": 1748408783942}, "emergency_memory_optimizer_129": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9221650380670112, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408483942, "expiresAt": 1748408783942}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8465292764418837, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9047296274967489, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8246099608460423, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8000308371453329, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408447775, "expiresAt": 1748409047775}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9839944982454282, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748408447775, "expiresAt": 1748409047775}, "memory_compression_engine_1748408447776": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9577209693680578, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "memory_cache_optimizer_1748408447776": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8514280162904914, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "memory_garbage_collector_1748408447776": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9738823706069462, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748408447776, "expiresAt": 1748409347776}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9661151286880753, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748408450086, "expiresAt": 1748409050086}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9505934102112454, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748408451309, "expiresAt": 1748409051309}, "emergency_memory_optimizer_128": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8144839647386078, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408483942, "expiresAt": 1748408783942}, "emergency_memory_optimizer_129": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9221650380670112, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748408483942, "expiresAt": 1748408783942}}, "totalAccelerators": 12, "activeAccelerators": 12, "averageBoostFactor": 2.559435066927083, "efficiency": 0.8535661040156249, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748408745946, "totalBoostPower": 30.713220803124997, "averageEnergy": 1, "averageStability": 0.8996902581788225, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748408505957, "memoryEfficiency": 0, "thermalStability": 0.8863463883598646, "cpuUsage": 0.42685546875, "responseTime": 273.468591854802, "overall": 0.5642547526804871}, {"timestamp": 1748408566364, "memoryEfficiency": 0, "thermalStability": 0.9938081694973839, "cpuUsage": 0.45380859375, "responseTime": 200.69268423248303, "overall": 0.5579473359978502}, {"timestamp": 1748408626365, "memoryEfficiency": 0, "thermalStability": 0.9220690812087722, "cpuUsage": 0.5955322265625, "responseTime": 228.1092935933094, "overall": 0.5189798423906551}, {"timestamp": 1748408686365, "memoryEfficiency": 0, "thermalStability": 0.9966132437603341, "cpuUsage": 0.5494384765625, "responseTime": 499.620888218049, "overall": 0.5507984568478653}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}