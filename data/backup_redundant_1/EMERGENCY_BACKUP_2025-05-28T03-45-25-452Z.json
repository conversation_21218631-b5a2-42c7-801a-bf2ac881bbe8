{"timestamp": "2025-05-28T03:45:25.452Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3687184951826172, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.406869411278101, "energy": 1, "focus": 0.8771953472668711, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748403910473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13544300050327, "neuronActivity": 0.5155129659580032}, "emotionalHistory": [{"timestamp": 1748403640457, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403655457, "mood": "curious", "dominantEmotion": "energy", "qi": 148.01496886291434, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403670456, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01496886291434, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403685456, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299377258287, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403700456, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299377258287, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403715457, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04490658874303, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403730457, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04490658874303, "neuronActivity": 0.4968862914341244}, {"timestamp": 1748403745470, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05994904234626, "neuronActivity": 0.5042453603242006}, {"timestamp": 1748403760471, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05994904234626, "neuronActivity": 0.5042453603242006}, {"timestamp": 1748403775472, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0749914959495, "neuronActivity": 0.5042453603242006}, {"timestamp": 1748403790473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0749914959495, "neuronActivity": 0.5042453603242006}, {"timestamp": 1748403805472, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0900621185668, "neuronActivity": 0.5070622617326511}, {"timestamp": 1748403820474, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0900621185668, "neuronActivity": 0.5070622617326511}, {"timestamp": 1748403835473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10513274118412, "neuronActivity": 0.5070622617326511}, {"timestamp": 1748403850473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10513274118412, "neuronActivity": 0.5070622617326511}, {"timestamp": 1748403865472, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1202878708437, "neuronActivity": 0.5155129659580032}, {"timestamp": 1748403880473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1202878708437, "neuronActivity": 0.5155129659580032}, {"timestamp": 1748403895473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13544300050327, "neuronActivity": 0.5155129659580032}, {"timestamp": 1748403910473, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13544300050327, "neuronActivity": 0.5155129659580032}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:44:10 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:44:25 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:44:40 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:44:55 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:45:10 PM", "mood": "curious"}], "circadianRhythm": 0.9159462517142363, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9035414670656496, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8581577523720034, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9190896187809349, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9090656077970178, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403625756, "expiresAt": 1748404225756}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9256936517759019, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748403625757, "expiresAt": 1748404225757}, "memory_compression_engine_1748403625758": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.1290124999999995, "stability": 0.8951851178546774, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "memory_cache_optimizer_1748403625758": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4774075000000004, "stability": 0.8611609239378496, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "memory_garbage_collector_1748403625758": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.31450625, "stability": 0.9494213354099348, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "response_accelerator_auto_6": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.9463715263249642, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748403628017, "expiresAt": 1748404228017}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.977666104965144, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748403629431, "expiresAt": 1748404229431}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9035414670656496, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8581577523720034, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9190896187809349, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9090656077970178, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403625756, "expiresAt": 1748404225756}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9256936517759019, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748403625757, "expiresAt": 1748404225757}, "memory_compression_engine_1748403625758": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.1290124999999995, "stability": 0.8951851178546774, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "memory_cache_optimizer_1748403625758": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4774075000000004, "stability": 0.8611609239378496, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "memory_garbage_collector_1748403625758": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.31450625, "stability": 0.9494213354099348, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403625758, "expiresAt": 1748404525758}, "response_accelerator_auto_6": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.9463715263249642, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748403628017, "expiresAt": 1748404228017}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.977666104965144, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748403629431, "expiresAt": 1748404229431}}, "totalAccelerators": 10, "activeAccelerators": 10, "averageBoostFactor": 2.4309806437500003, "efficiency": 0.845858838625, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748403865459, "totalBoostPower": 24.309806437500004, "averageEnergy": 1, "averageStability": 0.9145353106284076, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748403685453, "memoryEfficiency": 0, "thermalStability": 0.9669667879740397, "cpuUsage": 0.4127685546875, "responseTime": 440.0684110237736, "overall": 0.5487338708932186}, {"timestamp": 1748403745467, "memoryEfficiency": 0, "thermalStability": 0.9931391802099016, "cpuUsage": 0.4919189453125, "responseTime": 371.6999344924095, "overall": 0.5566883873378023}, {"timestamp": 1748403805468, "memoryEfficiency": 0, "thermalStability": 0.9888430434796546, "cpuUsage": 0.4101806640625, "responseTime": 541.3170303307111, "overall": 0.5297721479806361}, {"timestamp": 1748403865468, "memoryEfficiency": 0, "thermalStability": 0.9851519510563876, "cpuUsage": 0.41171875, "responseTime": 636.1066751390617, "overall": 0.529990271819156}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}