{"timestamp": "2025-05-28T05:34:23.741Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 100, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10723211319284398, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7, "maxMemorySize": 50}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.6676079717550942, "confidence": -5.206447257491696, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748410448247, "mood": "creative", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, "emotionalHistory": [{"timestamp": 1748409877042, "mood": "curious", "dominantEmotion": "energy", "qi": 203, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409892042, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409907043, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409922043, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409937044, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409952044, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409967052, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409982527, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409997528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410012528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01567751695293, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410027529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01567751695293, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410042529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410057528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410072529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410087530, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410102531, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01576202399517, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410117532, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01576202399517, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410132533, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01580867891437, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410147535, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01580867891437, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410162548, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410177547, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410192548, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410207558, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410222575, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410237589, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410252606, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410267632, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410282697, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410297698, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410312698, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410327699, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410342699, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410357724, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410372725, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410387726, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410402729, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410417769, "mood": "creative", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410433209, "mood": "creative", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410448247, "mood": "creative", "dominantEmotion": "creativity", "qi": 225.00580867891438, "neuronActivity": 0.5808678914374824}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:33:07 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:33:22 AM", "mood": "curious"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "1:33:37 AM", "mood": "creative"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "1:33:53 AM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "1:34:08 AM", "mood": "creative"}], "circadianRhythm": 0.9968197846704097, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.988970699322863, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9379377428923986, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.9242660301234296, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 1, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "memory_compression_engine_1748409863437": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.9121619097021885, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_cache_optimizer_1748409863437": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8201921090221843, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_garbage_collector_1748409863437": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.8847477327641379, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9116512195498935, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "neural_stimulator_auto_6": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.902144040826321, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "cpu_booster_auto_396": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.130249409724609, "stability": 0.9507662813497423, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748409972426, "expiresAt": 1748410572426}, "emergency_memory_optimizer_1116": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9147104074558395, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410183859, "expiresAt": 1748410483859}, "emergency_memory_optimizer_1117": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9109215645273151, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410183859, "expiresAt": 1748410483859}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.988970699322863, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9379377428923986, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.9242660301234296, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 1, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "memory_compression_engine_1748409863437": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.9121619097021885, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_cache_optimizer_1748409863437": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8201921090221843, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_garbage_collector_1748409863437": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.8847477327641379, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9116512195498935, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "neural_stimulator_auto_6": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.902144040826321, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "cpu_booster_auto_396": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.130249409724609, "stability": 0.9507662813497423, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748409972426, "expiresAt": 1748410572426}, "emergency_memory_optimizer_1116": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9147104074558395, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410183859, "expiresAt": 1748410483859}, "emergency_memory_optimizer_1117": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9109215645273151, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410183859, "expiresAt": 1748410483859}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.3725167163630214, "efficiency": 0.8423510029817812, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748410461930, "totalBoostPower": 30.84271731271928, "averageEnergy": 1, "averageStability": 0.927574595195101, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748409921963, "memoryEfficiency": 0, "thermalStability": 0.8970570074187385, "cpuUsage": 0.47548828125, "responseTime": 647.0616629870804, "overall": 0.4961118888009902}, {"timestamp": 1748409982518, "memoryEfficiency": 0, "thermalStability": 0.993707583554917, "cpuUsage": 0.70107421875, "responseTime": 617.82635883846, "overall": 0.5696116415036709}, {"timestamp": 1748410042518, "memoryEfficiency": 0, "thermalStability": 0.8641401084346904, "cpuUsage": 0.4923828125, "responseTime": 241.1734337192452, "overall": 0.5260279738135483}, {"timestamp": 1748410102520, "memoryEfficiency": 0, "thermalStability": 0.8721655420545074, "cpuUsage": 0.5096435546875, "responseTime": 439.77272573785245, "overall": 0.5153731021530712}, {"timestamp": 1748410162521, "memoryEfficiency": 0, "thermalStability": 0.977197479415271, "cpuUsage": 0.489111328125, "responseTime": 604.4735354355962, "overall": 0.5648973533543413}, {"timestamp": 1748410222498, "memoryEfficiency": 0, "thermalStability": 0.9803696366233958, "cpuUsage": 0.4803955078125, "responseTime": 474.9027536809202, "overall": 0.5127969740676065}, {"timestamp": 1748410282497, "memoryEfficiency": 0, "thermalStability": 0.9831635001632902, "cpuUsage": 0.581396484375, "responseTime": 699.6606315259446, "overall": 0.5570501203357584}, {"timestamp": 1748410342498, "memoryEfficiency": 0, "thermalStability": 0.9804217441214456, "cpuUsage": 0.5759765625, "responseTime": 302.0631205649672, "overall": 0.579030971787018}, {"timestamp": 1748410402499, "memoryEfficiency": 0, "thermalStability": 0.9769139947576655, "cpuUsage": 0.5323974609375, "responseTime": 200.89605419099826, "overall": 0.5280830301098954}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}