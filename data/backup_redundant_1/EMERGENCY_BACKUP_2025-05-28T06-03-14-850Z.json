{"timestamp": "2025-05-28T06:03:14.851Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 100, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.171810711355827, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10407070439254373, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7, "maxMemorySize": 50}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.0726350531875934, "energy": 1, "focus": 0.8826336840382396, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748412180954, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, "emotionalHistory": [{"timestamp": 1748411909817, "mood": "curious", "dominantEmotion": "energy", "qi": 203, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748411924817, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748411939972, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748411954972, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748411970146, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748411985147, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748412000146, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4926112627500947}, {"timestamp": 1748412015282, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4985495011124842}, {"timestamp": 1748412030300, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0149261126275, "neuronActivity": 0.4985495011124842}, {"timestamp": 1748412045411, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01498549501113, "neuronActivity": 0.4985495011124842}, {"timestamp": 1748412060411, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01498549501113, "neuronActivity": 0.4985495011124842}, {"timestamp": 1748412075411, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412090412, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412105413, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412120414, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412135742, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412150742, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412165743, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}, {"timestamp": 1748412180954, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499603069772, "neuronActivity": 0.49960306977094987}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:02:00 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:02:15 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:02:30 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:02:45 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:03:00 AM", "mood": "curious"}], "circadianRhythm": 0.9999567085264464, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8907400985663716, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8436332612358354, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8182986396351806, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411896310, "expiresAt": 1748412496310}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8807476424033176, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748411896310, "expiresAt": 1748412496310}, "memory_compression_engine_1748411896310": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9814457904984952, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896310, "expiresAt": 1748412796310}, "memory_cache_optimizer_1748411896311": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8664201568535039, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896311, "expiresAt": 1748412796311}, "memory_garbage_collector_1748411896311": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9281632461657792, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896311, "expiresAt": 1748412796311}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9403439144875158, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748411898920, "expiresAt": 1748412498920}, "neural_stimulator_auto_10": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8196121567251681, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748411900080, "expiresAt": 1748412500080}, "emergency_memory_optimizer_48": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8697568831064661, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411913726, "expiresAt": 1748412213726}, "emergency_memory_optimizer_49": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9528800682104733, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411913726, "expiresAt": 1748412213726}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8907400985663716, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8436332612358354, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8182986396351806, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411896310, "expiresAt": 1748412496310}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8807476424033176, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748411896310, "expiresAt": 1748412496310}, "memory_compression_engine_1748411896310": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9814457904984952, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896310, "expiresAt": 1748412796310}, "memory_cache_optimizer_1748411896311": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8664201568535039, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896311, "expiresAt": 1748412796311}, "memory_garbage_collector_1748411896311": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9281632461657792, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411896311, "expiresAt": 1748412796311}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9403439144875158, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748411898920, "expiresAt": 1748412498920}, "neural_stimulator_auto_10": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8196121567251681, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748411900080, "expiresAt": 1748412500080}, "emergency_memory_optimizer_48": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8697568831064661, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411913726, "expiresAt": 1748412213726}, "emergency_memory_optimizer_49": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9528800682104733, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411913726, "expiresAt": 1748412213726}}, "totalAccelerators": 12, "activeAccelerators": 12, "averageBoostFactor": 2.559435066927083, "efficiency": 0.8535661040156249, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748412194750, "totalBoostPower": 30.713220803124997, "averageEnergy": 1, "averageStability": 0.8993368214906755, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748411954761, "memoryEfficiency": 0, "thermalStability": 0.9414799728327328, "cpuUsage": 0.4620849609375, "responseTime": 409.9764569756568, "overall": 0.5679970734814569}, {"timestamp": 1748412015255, "memoryEfficiency": 0, "thermalStability": 0.9948693549053537, "cpuUsage": 0.4399169921875, "responseTime": 364.72951122756126, "overall": 0.576454601056622}, {"timestamp": 1748412075256, "memoryEfficiency": 0, "thermalStability": 0.9975428013751905, "cpuUsage": 0.4700927734375, "responseTime": 676.8907886247822, "overall": 0.5451138348219224}, {"timestamp": 1748412135715, "memoryEfficiency": 0, "thermalStability": 0.9964119743969705, "cpuUsage": 0.481689453125, "responseTime": 303.54023514194745, "overall": 0.5561643822974307}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}