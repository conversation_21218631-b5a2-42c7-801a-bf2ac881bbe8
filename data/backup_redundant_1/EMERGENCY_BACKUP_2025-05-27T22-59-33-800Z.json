{"timestamp": "2025-05-27T22:59:33.800Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1433549477511944, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.98, "confidence": -3.5602233381257333, "energy": 1, "focus": 0.86960021389466, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748386773776, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13965096697237, "neuronActivity": 0.5031528639368517}, "emotionalHistory": [{"timestamp": 1748386488764, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386503764, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01488258695554, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386518765, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01488258695554, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386533766, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02976517391107, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386548767, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02976517391107, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386563768, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0446477608666, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386578768, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0446477608666, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386593770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596127958422, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386608771, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596127958422, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386623770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07457783081782, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386638770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07457783081782, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386653770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08956710593606, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386668772, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08956710593606, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386683772, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1045563810543, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386698773, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1045563810543, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386713774, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11958790969365, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386728774, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11958790969365, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386743775, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.134619438333, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386758775, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.134619438333, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386773776, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13965096697237, "neuronActivity": 0.5031528639368517}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:58:33 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:58:48 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:59:03 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:59:18 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:59:33 PM", "mood": "curious"}], "circadianRhythm": 0.3696696725826318, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9251381937808848, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8822205367329723, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9035465673890686, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8351479977294841, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9041620561318248, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "memory_compression_engine_1748386475356": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8539839387173874, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_cache_optimizer_1748386475356": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9203690225695704, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_garbage_collector_1748386475356": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 1, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8347607935210318, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748386476563, "expiresAt": 1748387076563}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9599876871496714, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748386479231, "expiresAt": 1748387079231}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9251381937808848, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8822205367329723, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9035465673890686, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8351479977294841, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9041620561318248, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "memory_compression_engine_1748386475356": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8539839387173874, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_cache_optimizer_1748386475356": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9203690225695704, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_garbage_collector_1748386475356": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 1, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8347607935210318, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748386476563, "expiresAt": 1748387076563}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9599876871496714, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748386479231, "expiresAt": 1748387079231}}, "totalAccelerators": 10, "activeAccelerators": 10, "averageBoostFactor": 2.3534803740625003, "efficiency": 0.84120882244375, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748386773679, "totalBoostPower": 23.534803740625, "averageEnergy": 1, "averageStability": 0.9019316793721897, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748386533709, "memoryEfficiency": 0, "thermalStability": 0.9467075440618727, "cpuUsage": 0.453173828125, "responseTime": 383.81401315084054, "overall": 0.5730819763864375}, {"timestamp": 1748386593719, "memoryEfficiency": 0, "thermalStability": 0.9985499405198627, "cpuUsage": 0.4138916015625, "responseTime": 467.9054897311932, "overall": 0.5386825498127343}, {"timestamp": 1748386653719, "memoryEfficiency": 0, "thermalStability": 0.9953356590535906, "cpuUsage": 0.409375, "responseTime": 639.4282247381846, "overall": 0.5233978979013905}, {"timestamp": 1748386713721, "memoryEfficiency": 0, "thermalStability": 0.9910762175917626, "cpuUsage": 0.4203369140625, "responseTime": 298.47189855851536, "overall": 0.5299768597432113}, {"timestamp": 1748386773722, "memoryEfficiency": 0, "thermalStability": 0.9896249077386327, "cpuUsage": 0.4096923828125, "responseTime": 331.1766418177012, "overall": 0.5397517569482395}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}