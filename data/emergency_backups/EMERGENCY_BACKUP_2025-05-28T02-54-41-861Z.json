{"timestamp": "2025-05-28T02:54:41.862Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.95, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.5331446453973203, "curiosity": 1, "confidence": 0.32639999999999997, "energy": 1, "focus": 0.7241989907674271, "creativity": 0.7775739361919609, "stress": 0.22836, "fatigue": 0.18000000000000002, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748400881844, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.48079596306970873}, "emotionalHistory": [{"timestamp": 1748400881844, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.48079596306970873}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:54:41 PM", "mood": "curious"}], "circadianRhythm": 0.8452793430635459, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9079655132421649, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9232361750894876, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.987275366953252, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748400867181, "expiresAt": 1748401467181}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748400867181, "expiresAt": 1748401467181}, "memory_compression_engine_1748400867182": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "memory_cache_optimizer_1748400867182": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "memory_garbage_collector_1748400867182": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 3, "stability": 0.9, "energy": 1000, "enabled": true, "type": "response_accelerator", "createdAt": 1748400867283, "expiresAt": 1748401467283}, "neural_stimulator_auto_8": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "neural_stimulator", "createdAt": 1748400867785, "expiresAt": 1748401467785}, "thermal_cooler_auto_18": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748400868891, "expiresAt": 1748401468891}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9079655132421649, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9232361750894876, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.987275366953252, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748400867181, "expiresAt": 1748401467181}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748400867181, "expiresAt": 1748401467181}, "memory_compression_engine_1748400867182": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "memory_cache_optimizer_1748400867182": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "memory_garbage_collector_1748400867182": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748400867182, "expiresAt": 1748401767182}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 3, "stability": 0.9, "energy": 1000, "enabled": true, "type": "response_accelerator", "createdAt": 1748400867283, "expiresAt": 1748401467283}, "neural_stimulator_auto_8": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "neural_stimulator", "createdAt": 1748400867785, "expiresAt": 1748401467785}, "thermal_cooler_auto_18": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748400868891, "expiresAt": 1748401468891}}, "totalAccelerators": 11, "activeAccelerators": 11, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748400866840, "totalBoostPower": 28.23, "averageEnergy": 727.5454545454545, "averageStability": 0.9107706413895369, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}