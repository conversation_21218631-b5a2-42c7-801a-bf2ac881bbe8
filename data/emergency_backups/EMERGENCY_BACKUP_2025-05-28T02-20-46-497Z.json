{"timestamp": "2025-05-28T02:20:46.498Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3203478206680457, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.406863210845583, "energy": 1, "focus": 0.9432443109484282, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748398832319, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13778804141958, "neuronActivity": 0.5370593757197915}, "emotionalHistory": [{"timestamp": 1748398561468, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398577262, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01525402118887, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398592264, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01525402118887, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398607267, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03050804237773, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398622267, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03050804237773, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398637270, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0457620635666, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398652276, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0457620635666, "neuronActivity": 0.5254021188879092}, {"timestamp": 1748398667279, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0610761184998, "neuronActivity": 0.5314054933213844}, {"timestamp": 1748398682285, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0610761184998, "neuronActivity": 0.5314054933213844}, {"timestamp": 1748398697285, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07639017343303, "neuronActivity": 0.5314054933213844}, {"timestamp": 1748398712293, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07639017343303, "neuronActivity": 0.5314054933213844}, {"timestamp": 1748398727293, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0917185136691, "neuronActivity": 0.5328340236071154}, {"timestamp": 1748398742296, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0917185136691, "neuronActivity": 0.5328340236071154}, {"timestamp": 1748398757299, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10704685390516, "neuronActivity": 0.5328340236071154}, {"timestamp": 1748398772299, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10704685390516, "neuronActivity": 0.5328340236071154}, {"timestamp": 1748398787298, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12241744766237, "neuronActivity": 0.5370593757197915}, {"timestamp": 1748398802303, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12241744766237, "neuronActivity": 0.5370593757197915}, {"timestamp": 1748398817307, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13778804141958, "neuronActivity": 0.5370593757197915}, {"timestamp": 1748398832319, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13778804141958, "neuronActivity": 0.5370593757197915}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:19:32 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:19:47 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:20:02 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:20:17 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:20:32 PM", "mood": "curious"}], "circadianRhythm": 0.7877500533555498, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9407460979516886, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8419223260971472, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8926406395477898, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9177334949914979, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398548179, "expiresAt": 1748399148179}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.862770953017165, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748398548179, "expiresAt": 1748399148179}, "memory_compression_engine_1748398548180": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8923771480403399, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "memory_cache_optimizer_1748398548180": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9759345101655268, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "memory_garbage_collector_1748398548180": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8934913007883487, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9095764632740707, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748398548484, "expiresAt": 1748399148484}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9647169405549857, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748398548534, "expiresAt": 1748399148534}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8988223182495249, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748398549901, "expiresAt": 1748399149901}, "emergency_memory_optimizer_388": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.9358385028852542, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398658329, "expiresAt": 1748398958329}, "emergency_memory_optimizer_389": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.8736236477049452, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398658329, "expiresAt": 1748398958329}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9407460979516886, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8419223260971472, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8926406395477898, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9177334949914979, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398548179, "expiresAt": 1748399148179}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.862770953017165, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748398548179, "expiresAt": 1748399148179}, "memory_compression_engine_1748398548180": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8923771480403399, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "memory_cache_optimizer_1748398548180": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9759345101655268, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "memory_garbage_collector_1748398548180": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8934913007883487, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398548180, "expiresAt": 1748399448180}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9095764632740707, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748398548484, "expiresAt": 1748399148484}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9647169405549857, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748398548534, "expiresAt": 1748399148534}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8988223182495249, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748398549901, "expiresAt": 1748399149901}, "emergency_memory_optimizer_388": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.9358385028852542, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398658329, "expiresAt": 1748398958329}, "emergency_memory_optimizer_389": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.8736236477049452, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398658329, "expiresAt": 1748398958329}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.5114601266826924, "efficiency": 0.8506876076009615, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748398846367, "totalBoostPower": 32.648981646875, "averageEnergy": 1, "averageStability": 0.9077072571744835, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748398606379, "memoryEfficiency": 0, "thermalStability": 0.8829542305734422, "cpuUsage": 0.49501953125, "responseTime": 268.77647440440126, "overall": 0.5325777517276962}, {"timestamp": 1748398666381, "memoryEfficiency": 0, "thermalStability": 0.9973641377770238, "cpuUsage": 0.4215576171875, "responseTime": 486.70467520272626, "overall": 0.5196956247580579}, {"timestamp": 1748398726382, "memoryEfficiency": 0, "thermalStability": 0.9655438739806413, "cpuUsage": 0.4142578125, "responseTime": 523.388197576221, "overall": 0.5535041569949212}, {"timestamp": 1748398786466, "memoryEfficiency": 0, "thermalStability": 0.9989910650998354, "cpuUsage": 0.416455078125, "responseTime": 583.7990165612189, "overall": 0.5124581616769359}, {"timestamp": 1748398846466, "memoryEfficiency": 0, "thermalStability": 0.9978318620473147, "cpuUsage": 0.4259765625, "responseTime": 645.530121037646, "overall": 0.5116041924379191}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}