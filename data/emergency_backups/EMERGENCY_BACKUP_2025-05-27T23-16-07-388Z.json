{"timestamp": "2025-05-27T23:16:07.388Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2908602711583665, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1030299973486183, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.98, "confidence": -3.5602293383661583, "energy": 1, "focus": 0.8088489322006774, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748387767367, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13964887735412, "neuronActivity": 0.5125822706348276}, "emotionalHistory": [{"timestamp": 1748387482352, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387497352, "mood": "curious", "dominantEmotion": "creativity", "qi": 148, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387512352, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01483877801033, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387527353, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01483877801033, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387542355, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02967755602066, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387557356, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.044516334031, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387572357, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.044516334031, "neuronActivity": 0.4838778010333481}, {"timestamp": 1748387587357, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05943961908358, "neuronActivity": 0.49232850525870037}, {"timestamp": 1748387602358, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05943961908358, "neuronActivity": 0.49232850525870037}, {"timestamp": 1748387617358, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07436290413617, "neuronActivity": 0.49232850525870037}, {"timestamp": 1748387632359, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07436290413617, "neuronActivity": 0.49232850525870037}, {"timestamp": 1748387647358, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0893171566856, "neuronActivity": 0.4954252549438632}, {"timestamp": 1748387662361, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0893171566856, "neuronActivity": 0.4954252549438632}, {"timestamp": 1748387677361, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10427140923505, "neuronActivity": 0.4954252549438632}, {"timestamp": 1748387692361, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10427140923505, "neuronActivity": 0.5065648762174496}, {"timestamp": 1748387707362, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1193972319414, "neuronActivity": 0.5125822706348276}, {"timestamp": 1748387722363, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1193972319414, "neuronActivity": 0.5125822706348276}, {"timestamp": 1748387737364, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13452305464776, "neuronActivity": 0.5125822706348276}, {"timestamp": 1748387752366, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13452305464776, "neuronActivity": 0.5125822706348276}, {"timestamp": 1748387767367, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13964887735412, "neuronActivity": 0.5125822706348276}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "7:15:07 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "7:15:22 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "7:15:37 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:15:52 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:16:07 PM", "mood": "curious"}], "circadianRhythm": 0.40485846611262766, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8861303990397272, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9182503541882451, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.926471046542066, "energy": 1, "enabled": true, "type": "connection"}, "cpu_booster_auto_1": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.2737809374999998, "stability": 0.9116736329780349, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "memory_optimizer_auto_2": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.7839154077793038, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "qi_enhancer_auto_3": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8704144286969148, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "memory_compression_engine_1748387468609": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8908540821189053, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "memory_cache_optimizer_1748387468609": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.820779125471717, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "memory_garbage_collector_1748387468609": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.7938221675633989, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8122103200249547, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748387468609, "expiresAt": 1748388068609}, "neural_stimulator_auto_12": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9870929433203771, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748387471363, "expiresAt": 1748388071363}, "emergency_memory_optimizer_120": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9248684706823269, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387500489, "expiresAt": 1748387800489}, "emergency_memory_optimizer_121": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8848609614346525, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387500489, "expiresAt": 1748387800489}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8861303990397272, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9182503541882451, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.926471046542066, "energy": 1, "enabled": true, "type": "connection"}, "cpu_booster_auto_1": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.2737809374999998, "stability": 0.9116736329780349, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "memory_optimizer_auto_2": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.7839154077793038, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "qi_enhancer_auto_3": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8704144286969148, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748387468608, "expiresAt": 1748388068608}, "memory_compression_engine_1748387468609": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8908540821189053, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "memory_cache_optimizer_1748387468609": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.820779125471717, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "memory_garbage_collector_1748387468609": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.7938221675633989, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748387468609, "expiresAt": 1748388368609}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8122103200249547, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748387468609, "expiresAt": 1748388068609}, "neural_stimulator_auto_12": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9870929433203771, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748387471363, "expiresAt": 1748388071363}, "emergency_memory_optimizer_120": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9248684706823269, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387500489, "expiresAt": 1748387800489}, "emergency_memory_optimizer_121": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8848609614346525, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748387500489, "expiresAt": 1748387800489}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.537461672355769, "efficiency": 0.852247700341346, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748387767287, "totalBoostPower": 32.987001740625, "averageEnergy": 1, "averageStability": 0.8777956415262019, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748387527298, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.8314453125, "responseTime": 480.33690230299766, "overall": 0}, {"timestamp": 1748387587300, "memoryEfficiency": 0, "thermalStability": 0.5580991125723223, "cpuUsage": 0.936181640625, "responseTime": 498.72707703374004, "overall": 0.37833527440888937}, {"timestamp": 1748387647301, "memoryEfficiency": 0, "thermalStability": 0.9989560335874558, "cpuUsage": 1.082470703125, "responseTime": 660.8837731271333, "overall": 0.5826552265612827}, {"timestamp": 1748387707302, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 1.121337890625, "responseTime": 223.52559289836486, "overall": 0.5651681604901633}, {"timestamp": 1748387767303, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 0.6793212890625, "responseTime": 660.2023882737865, "overall": 0.5148472708946108}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}