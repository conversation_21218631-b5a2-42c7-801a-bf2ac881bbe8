/**
 * SCRIPT DE RESTAURATION AUTOMATIQUE
 * Sauvegarde: optimal_state_2025-05-28T04-58-20-269Z
 * Généré le: 2025-05-28T04:58:20.346Z
 */

const SystemBackupManager = require('../../../system-backup-manager');

async function restore() {
    console.log('🔄 Démarrage de la restauration automatique...');
    
    const manager = new SystemBackupManager();
    const result = await manager.restoreOptimalState('optimal_state_2025-05-28T04-58-20-269Z');
    
    if (result.success) {
        console.log('✅ Restauration réussie !');
        console.log('🔄 Veuillez redémarrer l\'application');
        process.exit(0);
    } else {
        console.error('❌ Échec de la restauration:', result.error);
        process.exit(1);
    }
}

restore().catch(console.error);
