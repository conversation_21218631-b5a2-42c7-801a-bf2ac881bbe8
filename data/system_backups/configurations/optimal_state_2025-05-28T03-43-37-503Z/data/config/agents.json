{"defaultAgent": "agent_claude", "agents": {"agent_claude": {"id": "agent_claude", "name": "Agent <PERSON> (4GB)", "type": "ollama", "model": "incept5/llama3.1-claude:latest", "description": "Agent principal basé sur le modèle Llama 3.1 Claude (4GB). Ce modèle offre des performances similaires à Claude avec une excellente compréhension et génération de texte.", "temperature": 0.7, "maxTokens": 2000, "createdAt": "2025-05-24T12:00:00.000Z", "updatedAt": "2025-05-24T12:00:00.000Z", "isMainAgent": true, "memoryPriority": "high"}, "agent_deepseek_r1": {"id": "agent_deepseek_r1", "name": "DeepSeek R1 (7B)", "type": "ollama", "model": "deepseek-r1:7b", "description": "Agent secondaire basé sur le modèle DeepSeek R1 (7B). Ce modèle offre un bon équilibre entre performances et ressources nécessaires.", "temperature": 0.7, "maxTokens": 1000, "createdAt": "2025-05-20T12:00:00.000Z", "updatedAt": "2025-05-24T12:00:00.000Z", "isMainAgent": false, "memoryPriority": "medium"}, "agent_training": {"id": "agent_training", "name": "Agent de Formation (1-2GB)", "type": "ollama", "model": "llama3:8b", "description": "Agent de formation basé sur le modèle Llama 3 (8B). Cet agent est optimisé pour l'apprentissage et la formation de la mémoire thermique. Il utilise moins de ressources (1-2GB) pour des opérations rapides.", "temperature": 0.5, "maxTokens": 2000, "createdAt": "2025-05-20T12:00:00.000Z", "updatedAt": "2025-05-24T12:00:00.000Z", "isTrainingAgent": true, "memoryPriority": "low"}}}