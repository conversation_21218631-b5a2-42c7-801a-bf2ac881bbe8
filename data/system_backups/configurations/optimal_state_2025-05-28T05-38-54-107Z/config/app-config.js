/**
 * Configuration centralisée de l'application Louna
 * Toutes les configurations système, API et fonctionnalités
 */

const path = require('path');
const os = require('os');

// Configuration de base de l'application
const APP_CONFIG = {
  // Informations de l'application
  app: {
    name: '<PERSON><PERSON>',
    version: '2.0.0',
    description: 'Intelligence Artificielle Évolutive avec Mémoire Thermique',
    author: 'Louna AI Team',
    environment: process.env.NODE_ENV || 'development'
  },

  // Configuration du serveur
  server: {
    port: process.env.PORT || 3005,
    host: process.env.HOST || 'localhost',
    cors: {
      enabled: true,
      origins: ['http://localhost:3005', 'http://127.0.0.1:3005']
    },
    ssl: {
      enabled: false,
      keyPath: process.env.SSL_KEY_PATH,
      certPath: process.env.SSL_CERT_PATH
    }
  },

  // Configuration de la mémoire thermique
  thermalMemory: {
    enabled: true,
    dataPath: path.join(__dirname, '..', 'data', 'memory'),
    maxEntries: 100000,
    memoryCycleInterval: 30, // secondes - ACCÉLÉRÉ 10x (était 300)
    temperatureThresholds: {
      instant: 0.95,
      shortTerm: 0.85,
      working: 0.7,
      mediumTerm: 0.5,
      longTerm: 0.3,
      dream: 0.1
    },
    autoOptimization: true,
    compressionEnabled: true,
    // NOUVEAUX PARAMÈTRES DE BOOST
    fastLearningMode: true,
    acceleratedEvolution: true,
    learningBoostFactor: 5.0,
    // SYSTÈME DE PERSISTANCE MÉMOIRE (v2.1.0)
    persistenceEnabled: true,
    persistenceFrequency: 5000, // 5 secondes
    emergencyBackupEnabled: true,
    autoRecoveryEnabled: true
  },

  // Configuration du cerveau artificiel
  artificialBrain: {
    enabled: true,
    maxNeurons: 5000, // AUGMENTÉ 5x (était 1000)
    neuronGrowthRate: 0.5, // ACCÉLÉRÉ 5x (était 0.1)
    synapticPlasticity: 0.95, // MAXIMISÉ (était 0.8)
    learningRate: 0.25, // ACCÉLÉRÉ 5x (était 0.05)
    memoryConsolidationInterval: 60, // ACCÉLÉRÉ 10x (était 600)
    sleepCycleInterval: 300, // ACCÉLÉRÉ 12x (était 3600)
    emotionalProcessing: true,
    creativityLevel: 0.95, // MAXIMISÉ (était 0.7)
    // NOUVEAUX PARAMÈTRES DE BOOST
    rapidLearningMode: true,
    neuralAcceleration: true,
    adaptivePlasticity: true,
    enhancedCreativity: true
  },

  // Configuration des accélérateurs Kyber
  kyberAccelerators: {
    enabled: true,
    maxAccelerators: 25, // AUGMENTÉ 2.5x (était 10)
    energyRegenerationRate: 0.5, // ACCÉLÉRÉ 5x (était 0.1)
    stabilityThreshold: 0.95, // MAXIMISÉ (était 0.8)
    updateInterval: 10, // ACCÉLÉRÉ 6x (était 60)
    autoOptimization: true,
    quantumEffects: true,
    // NOUVEAUX PARAMÈTRES DE BOOST
    hyperAcceleration: true,
    quantumBoost: true,
    neuralSynchronization: true,
    learningAmplification: 10.0
  },

  // Configuration de sécurité
  security: {
    enabled: true,
    antivirus: {
      enabled: true,
      scanInterval: 1800, // secondes
      quarantinePath: path.join(__dirname, '..', 'quarantine'),
      realTimeProtection: true
    },
    vpn: {
      enabled: true,
      autoConnect: true,
      preferredLocation: 'France',
      killSwitch: true
    },
    firewall: {
      enabled: true,
      defaultPolicy: 'deny',
      allowedPorts: [3005, 8080, 8081, 8082],
      logBlocked: true
    },
    encryption: {
      algorithm: 'aes-256-gcm',
      keyRotationInterval: 86400 // secondes
    }
  },

  // Configuration Ollama
  ollama: {
    enabled: true,
    url: process.env.OLLAMA_URL || 'http://localhost:11434',
    defaultModel: 'incept5/llama3.1-claude:latest',
    models: {
      main: 'incept5/llama3.1-claude:latest',
      training: 'llama3.2:1b',
      creative: 'incept5/llama3.1-claude:latest',
      secondary: 'deepseek-r1:7b'
    },
    maxTokens: 2048,
    temperature: 0.7,
    useMemory: true,
    memoryLimit: 10,
    autoInstall: true
  },

  // Configuration de génération multimédia
  mediaGeneration: {
    enabled: true,
    outputDir: path.join(__dirname, '..', 'generated'),
    apis: {
      image: {
        endpoint: process.env.IMAGE_API_ENDPOINT || 'http://localhost:7860/api/v1/txt2img',
        apiKey: process.env.IMAGE_API_KEY,
        defaultResolution: '1024x1024',
        maxResolution: '2048x2048'
      },
      video: {
        endpoint: process.env.VIDEO_API_ENDPOINT || 'http://localhost:8080/api/v1/video/generate',
        apiKey: process.env.VIDEO_API_KEY,
        defaultDuration: 5,
        maxDuration: 30,
        defaultFps: 24
      },
      music: {
        endpoint: process.env.MUSIC_API_ENDPOINT || 'http://localhost:8081/api/v1/music/generate',
        apiKey: process.env.MUSIC_API_KEY,
        defaultDuration: 30,
        maxDuration: 300,
        defaultGenre: 'ambient'
      },
      code: {
        endpoint: process.env.CODE_API_ENDPOINT || 'http://localhost:8082/api/v1/code/generate',
        apiKey: process.env.CODE_API_KEY,
        defaultLanguage: 'javascript',
        includeTests: true
      }
    },
    qualitySettings: {
      image: 'high',
      video: 'medium',
      music: 'high',
      code: 'production'
    }
  },

  // Configuration du monitoring
  monitoring: {
    enabled: true,
    updateInterval: 1000, // ACCÉLÉRÉ 5x (était 5000) - mise à jour chaque seconde
    historyLength: 5000, // AUGMENTÉ 5x (était 1000)
    metrics: {
      performance: true,
      memory: true,
      cpu: true,
      network: true,
      userExperience: true,
      // NOUVEAUX MÉTRIQUES DE BOOST
      learningRate: true,
      neuralActivity: true,
      synapticGrowth: true,
      knowledgeAbsorption: true
    },
    alerts: {
      enabled: true,
      thresholds: {
        cpuUsage: 95, // AUGMENTÉ pour permettre plus d'utilisation
        memoryUsage: 90, // AUGMENTÉ pour permettre plus d'utilisation
        responseTime: 2000, // RÉDUIT pour plus de réactivité
        errorRate: 0.02 // RÉDUIT pour plus de qualité
      }
    },
    // NOUVEAUX PARAMÈTRES DE BOOST
    realTimeOptimization: true,
    adaptiveMonitoring: true,
    learningMetrics: true
  },

  // Configuration de l'interface utilisateur
  ui: {
    theme: 'dark',
    colorScheme: {
      primary: '#c8a2c8',
      secondary: '#9d4edd',
      accent: '#e0aaff',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#ffffff',
      textSecondary: '#cccccc'
    },
    animations: {
      enabled: true,
      duration: 300,
      easing: 'ease-in-out'
    },
    accessibility: {
      highContrast: false,
      largeText: false,
      reducedMotion: false
    }
  },

  // Configuration des agents
  agents: {
    maxAgents: 5,
    defaultAgent: {
      name: 'Agent Claude (4GB)',
      model: 'incept5/llama3.1-claude:latest',
      personality: 'helpful',
      memory: true,
      learning: true,
      isMainAgent: true,
      memoryPriority: 'high'
    },
    trainingAgent: {
      name: 'Trainer',
      model: 'llama3.2:1b',
      specialized: true,
      autoQuestions: true
    }
  },

  // Configuration des logs
  logging: {
    enabled: true,
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: true,
      path: path.join(__dirname, '..', 'logs'),
      maxSize: '10MB',
      maxFiles: 5
    },
    console: {
      enabled: true,
      colorize: true,
      timestamp: true
    }
  },

  // Configuration des chemins
  paths: {
    root: path.join(__dirname, '..'),
    public: path.join(__dirname, '..', 'public'),
    data: path.join(__dirname, '..', 'data'),
    logs: path.join(__dirname, '..', 'logs'),
    temp: path.join(__dirname, '..', 'temp'),
    uploads: path.join(__dirname, '..', 'uploads'),
    downloads: path.join(__dirname, '..', 'downloads')
  },

  // Configuration système
  system: {
    platform: os.platform(),
    arch: os.arch(),
    cpus: os.cpus().length,
    memory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
    autoOptimization: true,
    performanceMode: 'balanced', // 'performance', 'balanced', 'power-saving'
    diagnostics: {
      enabled: true,
      interval: 300000, // 5 minutes
      autoFix: true
    }
  }
};

/**
 * Obtient une valeur de configuration par chemin
 * @param {string} path - Chemin vers la configuration (ex: 'server.port')
 * @param {*} defaultValue - Valeur par défaut si non trouvée
 * @returns {*} - Valeur de configuration
 */
function getConfig(path, defaultValue = null) {
  const keys = path.split('.');
  let current = APP_CONFIG;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return defaultValue;
    }
  }

  return current;
}

/**
 * Définit une valeur de configuration par chemin
 * @param {string} path - Chemin vers la configuration
 * @param {*} value - Nouvelle valeur
 */
function setConfig(path, value) {
  const keys = path.split('.');
  let current = APP_CONFIG;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
}

/**
 * Valide la configuration
 * @returns {Object} - Résultat de validation
 */
function validateConfig() {
  const errors = [];
  const warnings = [];

  // Vérifications critiques
  if (!APP_CONFIG.server.port || APP_CONFIG.server.port < 1 || APP_CONFIG.server.port > 65535) {
    errors.push('Port serveur invalide');
  }

  if (APP_CONFIG.thermalMemory.maxEntries < 1000) {
    warnings.push('Nombre maximum d\'entrées mémoire faible');
  }

  if (!APP_CONFIG.ollama.url) {
    warnings.push('URL Ollama non configurée');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

module.exports = {
  APP_CONFIG,
  getConfig,
  setConfig,
  validateConfig
};
