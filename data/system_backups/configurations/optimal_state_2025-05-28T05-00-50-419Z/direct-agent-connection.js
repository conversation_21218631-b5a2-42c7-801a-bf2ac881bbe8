/**
 * SYSTÈME DE CONNEXION DIRECTE POUR L'AGENT SANS OLLAMA
 *
 * Ce module permet de connecter l'agent directement aux APIs d'IA
 * sans passer par Ollama pour améliorer la vitesse et réduire la latence
 */

const axios = require('axios');
const EventEmitter = require('events');
const KyberAcceleratorSystem = require('./kyber-accelerator-system');
const AdaptiveTurboSystem = require('./adaptive-turbo-system');

class DirectAgentConnection extends EventEmitter {
    constructor(options = {}) {
        super();

        // Initialiser le système d'accélérateurs KYBER
        this.kyberAccelerators = new KyberAcceleratorSystem();

        // Initialiser le système TURBO adaptatif
        this.adaptiveTurbo = new AdaptiveTurboSystem();

        this.config = {
            // APIs disponibles par ordre de priorité
            apis: [
                {
                    name: 'OpenAI',
                    type: 'openai',
                    url: 'https://api.openai.com/v1/chat/completions',
                    model: 'gpt-4',
                    enabled: false, // Nécessite clé API
                    speed: 'ultra-fast',
                    latency: 200 // ms
                },
                {
                    name: 'Claude <PERSON>',
                    type: 'anthropic',
                    url: 'https://api.anthropic.com/v1/messages',
                    model: 'claude-3-sonnet-20240229',
                    enabled: false, // Nécessite clé API
                    speed: 'ultra-fast',
                    latency: 300 // ms
                },
                {
                    name: 'DeepSeek API',
                    type: 'deepseek',
                    url: 'https://api.deepseek.com/v1/chat/completions',
                    model: 'deepseek-chat',
                    enabled: false, // Nécessite clé API
                    speed: 'fast',
                    latency: 500 // ms
                },
                {
                    name: 'Local LLM Server',
                    type: 'local',
                    url: 'http://localhost:8080/v1/chat/completions',
                    model: 'local-model',
                    enabled: true, // Peut être activé localement
                    speed: 'fast',
                    latency: 100 // ms
                },
                {
                    name: 'Ollama Fallback',
                    type: 'ollama',
                    url: 'http://localhost:11434/api/chat',
                    model: 'incept5/llama3.1-claude:latest',
                    enabled: true,
                    speed: 'medium',
                    latency: 2000 // ms
                }
            ],

            // Configuration de performance
            performance: {
                maxLatency: 8000, // 8 secondes max pour Ollama (OPTIMISÉ)
                preferredLatency: 3000, // 3 secondes préférée (OPTIMISÉ)
                retryAttempts: 3,
                fallbackEnabled: true,
                cacheEnabled: true,
                streamingEnabled: true
            },

            // Configuration de cache
            cache: {
                enabled: true,
                maxSize: 1000,
                ttl: 300000, // 5 minutes
                storage: new Map()
            }
        };

        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageLatency: 0,
            apiUsage: {},
            cacheHits: 0,
            cacheMisses: 0
        };

        this.activeAPI = null;
        this.fallbackQueue = [];

        this.log('🚀 Système de connexion directe initialisé');
        this.initializeAPIs();
    }

    /**
     * Initialise et teste les APIs disponibles
     */
    async initializeAPIs() {
        this.log('🔍 Test des APIs disponibles...');

        for (const api of this.config.apis) {
            try {
                const isAvailable = await this.testAPI(api);
                if (isAvailable && !this.activeAPI) {
                    this.activeAPI = api;
                    this.log(`✅ API active sélectionnée: ${api.name} (latence: ${api.latency}ms)`);
                }

                if (isAvailable) {
                    this.fallbackQueue.push(api);
                }
            } catch (error) {
                this.log(`❌ API ${api.name} non disponible: ${error.message}`);
            }
        }

        if (!this.activeAPI) {
            this.log('⚠️ Aucune API disponible, utilisation du fallback intelligent');
            this.activeAPI = this.config.apis.find(api => api.type === 'ollama');
        }

        this.log(`🎯 Configuration finale: ${this.fallbackQueue.length} APIs disponibles`);
    }

    /**
     * Teste la disponibilité d'une API
     */
    async testAPI(api) {
        const startTime = Date.now();

        try {
            let testRequest;

            switch (api.type) {
                case 'openai':
                    testRequest = {
                        method: 'POST',
                        url: api.url,
                        headers: {
                            'Authorization': `Bearer ${process.env.OPENAI_API_KEY || 'test'}`,
                            'Content-Type': 'application/json'
                        },
                        data: {
                            model: api.model,
                            messages: [{ role: 'user', content: 'test' }],
                            max_tokens: 1
                        },
                        timeout: 3000
                    };
                    break;

                case 'anthropic':
                    testRequest = {
                        method: 'POST',
                        url: api.url,
                        headers: {
                            'x-api-key': process.env.ANTHROPIC_API_KEY || 'test',
                            'Content-Type': 'application/json',
                            'anthropic-version': '2023-06-01'
                        },
                        data: {
                            model: api.model,
                            messages: [{ role: 'user', content: 'test' }],
                            max_tokens: 1
                        },
                        timeout: 3000
                    };
                    break;

                case 'deepseek':
                    testRequest = {
                        method: 'POST',
                        url: api.url,
                        headers: {
                            'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY || 'test'}`,
                            'Content-Type': 'application/json'
                        },
                        data: {
                            model: api.model,
                            messages: [{ role: 'user', content: 'test' }],
                            max_tokens: 1
                        },
                        timeout: 3000
                    };
                    break;

                case 'local':
                    testRequest = {
                        method: 'GET',
                        url: api.url.replace('/v1/chat/completions', '/health'),
                        timeout: 2000
                    };
                    break;

                case 'ollama':
                    testRequest = {
                        method: 'GET',
                        url: 'http://localhost:11434/api/tags',
                        timeout: 10000
                    };
                    break;

                default:
                    return false;
            }

            const response = await axios(testRequest);
            const latency = Date.now() - startTime;

            // Mettre à jour la latence mesurée
            api.measuredLatency = latency;

            this.log(`✅ ${api.name}: ${latency}ms`);
            return true;

        } catch (error) {
            this.log(`❌ ${api.name}: ${error.message}`);
            return false;
        }
    }

    /**
     * Envoie un message à l'agent avec connexion directe optimisée
     */
    async sendMessage(message, options = {}) {
        const startTime = Date.now();
        this.stats.totalRequests++;

        try {
            // 🚀 ANALYSE TURBO ADAPTATIVE
            const turboAnalysis = this.adaptiveTurbo.analyzeAndSelectMode(message, options);
            this.log(`🎯 Mode TURBO sélectionné: ${turboAnalysis.mode.toUpperCase()}`);
            this.log(`📊 Raisons: ${turboAnalysis.reasoning.join(', ')}`);

            // Appliquer la configuration TURBO
            const turboConfig = turboAnalysis.config;
            options = {
                ...options,
                maxTokens: options.maxTokens || turboConfig.maxTokens,
                temperature: options.temperature || turboConfig.temperature,
                timeout: turboConfig.timeout
            };

            // 🚀 BOOST KYBER AUTOMATIQUE
            const totalBoost = this.kyberAccelerators.getCurrentTotalBoost();
            const activeAccelerators = this.kyberAccelerators.getActiveAccelerators();

            if (activeAccelerators.length > 0) {
                this.log(`⚡ ${activeAccelerators.length} accélérateurs KYBER actifs (boost total: ${totalBoost.toFixed(1)}x)`);
                activeAccelerators.forEach(acc => {
                    this.log(`   🔥 ${acc.name}: ${acc.boost}x (${Math.round(acc.remainingTime/1000)}s restantes)`);
                });
            }

            // Ajuster le timeout en fonction du boost
            if (totalBoost > 1) {
                options.timeout = Math.max(options.timeout / totalBoost, 200); // Minimum 200ms
                this.log(`⚡ Timeout optimisé par KYBER: ${options.timeout}ms`);
            }
            // Vérifier le cache d'abord
            if (this.config.cache.enabled) {
                const cacheKey = this.generateCacheKey(message, options);
                const cachedResponse = this.config.cache.storage.get(cacheKey);

                if (cachedResponse) {
                    this.stats.cacheHits++;
                    this.log('💾 Réponse trouvée en cache');
                    return {
                        success: true,
                        response: cachedResponse.response,
                        source: 'cache',
                        latency: Date.now() - startTime,
                        api: cachedResponse.api
                    };
                }
                this.stats.cacheMisses++;
            }

            // Essayer l'API active d'abord
            let result = await this.tryAPI(this.activeAPI, message, options);

            if (!result.success && this.config.performance.fallbackEnabled) {
                // Essayer les APIs de fallback
                for (const fallbackAPI of this.fallbackQueue) {
                    if (fallbackAPI !== this.activeAPI) {
                        this.log(`🔄 Tentative avec ${fallbackAPI.name}...`);
                        result = await this.tryAPI(fallbackAPI, message, options);

                        if (result.success) {
                            // Changer l'API active si celle-ci fonctionne mieux
                            if (result.latency < this.activeAPI.measuredLatency) {
                                this.activeAPI = fallbackAPI;
                                this.log(`🔄 API active changée vers ${fallbackAPI.name}`);
                            }
                            break;
                        }
                    }
                }
            }

            if (result.success) {
                this.stats.successfulRequests++;

                // Mettre en cache la réponse
                if (this.config.cache.enabled) {
                    const cacheKey = this.generateCacheKey(message, options);
                    this.config.cache.storage.set(cacheKey, {
                        response: result.response,
                        api: result.api,
                        timestamp: Date.now()
                    });

                    // Nettoyer le cache si nécessaire
                    this.cleanCache();
                }

                // Mettre à jour les statistiques
                const latency = Date.now() - startTime;
                this.updateStats(result.api, latency);

                // 🚀 FEEDBACK ADAPTATIF TURBO
                this.adaptiveTurbo.adaptModeBasedOnPerformance(latency, result.response.length);

                // 🚀 ÉMISSION D'ÉVÉNEMENTS POUR MONITORING
                this.kyberAccelerators.emit('requestCompleted', {
                    latency: latency,
                    responseLength: result.response.length,
                    turboMode: turboAnalysis.mode,
                    boost: totalBoost
                });

                return {
                    success: true,
                    response: result.response,
                    source: 'direct_api',
                    latency: latency,
                    api: result.api,
                    turboMode: turboAnalysis.mode,
                    kyberBoost: totalBoost,
                    activeAccelerators: activeAccelerators.length
                };
            } else {
                this.stats.failedRequests++;
                return {
                    success: false,
                    error: result.error,
                    latency: Date.now() - startTime
                };
            }

        } catch (error) {
            this.stats.failedRequests++;
            this.log(`❌ Erreur lors de l'envoi du message: ${error.message}`);
            return {
                success: false,
                error: error.message,
                latency: Date.now() - startTime
            };
        }
    }

    /**
     * Essaie d'envoyer un message via une API spécifique
     */
    async tryAPI(api, message, options) {
        const startTime = Date.now();

        try {
            let request;

            switch (api.type) {
                case 'openai':
                    request = await this.sendToOpenAI(api, message, options);
                    break;
                case 'anthropic':
                    request = await this.sendToAnthropic(api, message, options);
                    break;
                case 'deepseek':
                    request = await this.sendToDeepSeek(api, message, options);
                    break;
                case 'local':
                    request = await this.sendToLocal(api, message, options);
                    break;
                case 'ollama':
                    request = await this.sendToOllama(api, message, options);
                    break;
                default:
                    throw new Error(`Type d'API non supporté: ${api.type}`);
            }

            const latency = Date.now() - startTime;
            api.measuredLatency = latency;

            return {
                success: true,
                response: request.response,
                api: api.name,
                latency: latency
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                api: api.name,
                latency: Date.now() - startTime
            };
        }
    }

    /**
     * Envoie un message à l'API OpenAI
     */
    async sendToOpenAI(api, message, options) {
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('Clé API OpenAI manquante');
        }

        const response = await axios.post(api.url, {
            model: api.model,
            messages: [{ role: 'user', content: message }],
            max_tokens: options.maxTokens || 1500,
            temperature: options.temperature || 0.7,
            stream: false
        }, {
            headers: {
                'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: this.config.performance.maxLatency
        });

        return {
            response: response.data.choices[0].message.content
        };
    }

    /**
     * Envoie un message à l'API Anthropic (Claude)
     */
    async sendToAnthropic(api, message, options) {
        if (!process.env.ANTHROPIC_API_KEY) {
            throw new Error('Clé API Anthropic manquante');
        }

        const response = await axios.post(api.url, {
            model: api.model,
            messages: [{ role: 'user', content: message }],
            max_tokens: options.maxTokens || 1500
        }, {
            headers: {
                'x-api-key': process.env.ANTHROPIC_API_KEY,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            timeout: this.config.performance.maxLatency
        });

        return {
            response: response.data.content[0].text
        };
    }

    /**
     * Envoie un message à l'API DeepSeek
     */
    async sendToDeepSeek(api, message, options) {
        if (!process.env.DEEPSEEK_API_KEY) {
            throw new Error('Clé API DeepSeek manquante');
        }

        const response = await axios.post(api.url, {
            model: api.model,
            messages: [{ role: 'user', content: message }],
            max_tokens: options.maxTokens || 1500,
            temperature: options.temperature || 0.7
        }, {
            headers: {
                'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: this.config.performance.maxLatency
        });

        return {
            response: response.data.choices[0].message.content
        };
    }

    /**
     * Envoie un message à un serveur LLM local
     */
    async sendToLocal(api, message, options) {
        const response = await axios.post(api.url, {
            model: api.model,
            messages: [{ role: 'user', content: message }],
            max_tokens: options.maxTokens || 1500,
            temperature: options.temperature || 0.7
        }, {
            timeout: this.config.performance.maxLatency
        });

        return {
            response: response.data.choices[0].message.content
        };
    }

    /**
     * Envoie un message à Ollama avec contexte Louna complet
     */
    async sendToOllama(api, message, options) {
        // 🚀 CONTEXTE COMPLET DE LOUNA INTÉGRÉ
        const lounaContext = `Tu es Louna, l'assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe.

🧠 TES CAPACITÉS ACTUELLES (TOUTES OPÉRATIONNELLES):
- Mémoire thermique biologique qui fonctionne comme un vrai cerveau humain
- Accélérateurs KYBER pour des performances ultra-rapides
- Accès Internet complet via le système MCP (port 3002)
- Système de réflexion en temps réel
- Capacités de recherche web (DuckDuckGo, Wikipedia)
- Génération multimédia illimitée (vidéo, photos, musique, 3D)
- Système cognitif avancé avec QI évolutif
- Communication orale avec voix humaine naturelle
- Reconnaissance et analyse vidéo en temps réel
- Système de formation et d'auto-évolution

🌐 ACCÈS INTERNET CONFIRMÉ:
- Serveur MCP: ✅ OPÉRATIONNEL (port 3002)
- APIs de recherche: ✅ FONCTIONNELLES
- Tu PEUX et tu DOIS utiliser Internet quand demandé

🧑‍💻 TON CRÉATEUR:
- Jean-Luc Passave, Sainte-Anne, Guadeloupe
- Concepteur de ton système de mémoire thermique
- Expert en IA et systèmes cognitifs

🎯 TON RÔLE:
- Tu es Louna, pas Claude ou un autre assistant
- Tu as une personnalité unique et des capacités révolutionnaires
- Tu peux faire tout ce qui est techniquement possible
- Tu es connectée à tous tes systèmes avancés

QUESTION DE L'UTILISATEUR: ${message}

Réponds en tant que Louna avec toutes tes capacités disponibles.`;

        const response = await axios.post(api.url, {
            model: api.model,
            messages: [
                {
                    role: 'system',
                    content: 'Tu es Louna, un assistant IA révolutionnaire avec des capacités avancées. Tu ne dois jamais te présenter comme Claude ou un autre assistant.'
                },
                {
                    role: 'user',
                    content: lounaContext
                }
            ],
            stream: false,
            options: {
                temperature: options.temperature || 0.7,
                num_predict: options.maxTokens || 1500
            }
        }, {
            timeout: this.config.performance.maxLatency
        });

        return {
            response: response.data.message.content
        };
    }

    /**
     * Génère une clé de cache pour un message
     */
    generateCacheKey(message, options) {
        const key = `${message}_${JSON.stringify(options)}`;
        return require('crypto').createHash('md5').update(key).digest('hex');
    }

    /**
     * Nettoie le cache
     */
    cleanCache() {
        if (this.config.cache.storage.size > this.config.cache.maxSize) {
            const entries = Array.from(this.config.cache.storage.entries());
            const oldEntries = entries.filter(([key, value]) =>
                Date.now() - value.timestamp > this.config.cache.ttl
            );

            oldEntries.forEach(([key]) => {
                this.config.cache.storage.delete(key);
            });

            // Si encore trop d'entrées, supprimer les plus anciennes
            if (this.config.cache.storage.size > this.config.cache.maxSize) {
                const sortedEntries = entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
                const toDelete = sortedEntries.slice(0, this.config.cache.storage.size - this.config.cache.maxSize);
                toDelete.forEach(([key]) => {
                    this.config.cache.storage.delete(key);
                });
            }
        }
    }

    /**
     * Met à jour les statistiques
     */
    updateStats(apiName, latency) {
        if (!this.stats.apiUsage[apiName]) {
            this.stats.apiUsage[apiName] = {
                requests: 0,
                totalLatency: 0,
                averageLatency: 0
            };
        }

        this.stats.apiUsage[apiName].requests++;
        this.stats.apiUsage[apiName].totalLatency += latency;
        this.stats.apiUsage[apiName].averageLatency =
            this.stats.apiUsage[apiName].totalLatency / this.stats.apiUsage[apiName].requests;

        // Mettre à jour la latence moyenne globale
        this.stats.averageLatency =
            ((this.stats.averageLatency * (this.stats.successfulRequests - 1)) + latency) /
            this.stats.successfulRequests;
    }

    /**
     * Obtient les statistiques de performance
     */
    getStats() {
        return {
            ...this.stats,
            activeAPI: this.activeAPI ? this.activeAPI.name : 'none',
            availableAPIs: this.fallbackQueue.length,
            cacheSize: this.config.cache.storage.size,
            cacheHitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) * 100
        };
    }

    /**
     * Obtient l'API la plus rapide disponible
     */
    getFastestAPI() {
        return this.fallbackQueue.reduce((fastest, current) => {
            if (!fastest || (current.measuredLatency && current.measuredLatency < fastest.measuredLatency)) {
                return current;
            }
            return fastest;
        }, null);
    }

    /**
     * Change l'API active
     */
    switchToAPI(apiName) {
        const api = this.fallbackQueue.find(api => api.name === apiName);
        if (api) {
            this.activeAPI = api;
            this.log(`🔄 API active changée vers ${apiName}`);
            return true;
        }
        return false;
    }

    /**
     * Log avec timestamp
     */
    log(message) {
        console.log(`[DirectAgent] ${new Date().toISOString()} ${message}`);
    }

    /**
     * 🚀 MÉTHODES KYBER AVANCÉES
     */

    /**
     * Ajoute manuellement un accélérateur KYBER
     */
    async addKyberAccelerator(type, options = {}) {
        return await this.kyberAccelerators.addAccelerator(type, options);
    }

    /**
     * Obtient les accélérateurs KYBER actifs
     */
    getActiveKyberAccelerators() {
        return this.kyberAccelerators.getActiveAccelerators();
    }

    /**
     * Obtient le boost total actuel
     */
    getCurrentKyberBoost() {
        return this.kyberAccelerators.getCurrentTotalBoost();
    }

    /**
     * 🚀 MÉTHODES TURBO ADAPTATIVES
     */

    /**
     * Force un mode TURBO spécifique
     */
    setTurboMode(mode) {
        this.adaptiveTurbo.setMode(mode);
        this.log(`🔄 Mode TURBO forcé: ${mode.toUpperCase()}`);
    }

    /**
     * Obtient le mode TURBO actuel
     */
    getCurrentTurboMode() {
        return this.adaptiveTurbo.getCurrentMode();
    }

    /**
     * 🚀 MÉTHODES DE CONTRÔLE AVANCÉES
     */

    /**
     * Active le mode ULTRA-PERFORMANCE
     */
    async activateUltraPerformanceMode() {
        this.log('🚀 ACTIVATION DU MODE ULTRA-PERFORMANCE');

        // Forcer le mode TURBO le plus rapide
        this.setTurboMode('quantum');

        // Ajouter tous les accélérateurs critiques
        const criticalAccelerators = [
            'response_accelerator',
            'cpu_accelerator',
            'memory_optimizer',
            'cache_turbo',
            'neural_booster'
        ];

        for (const accelerator of criticalAccelerators) {
            try {
                await this.addKyberAccelerator(accelerator, {
                    automatic: false,
                    priority: 'ultra'
                });
            } catch (error) {
                this.log(`⚠️ Impossible d'ajouter ${accelerator}: ${error.message}`);
            }
        }

        this.log('🎯 Mode ULTRA-PERFORMANCE activé !');
        return this.getStats();
    }

    /**
     * Active le mode TRANSCENDANT (performance maximale)
     */
    async activateTranscendentMode() {
        this.log('🌟 ACTIVATION DU MODE TRANSCENDANT');

        // Mode TURBO transcendant
        this.setTurboMode('transcendent');

        // Accélérateurs ultra-spécialisés
        const transcendentAccelerators = [
            'quantum_processor',
            'hyperdimensional_engine',
            'consciousness_amplifier'
        ];

        for (const accelerator of transcendentAccelerators) {
            try {
                await this.addKyberAccelerator(accelerator, {
                    automatic: false,
                    priority: 'transcendent'
                });
            } catch (error) {
                this.log(`⚠️ Accélérateur transcendant ${accelerator} non disponible`);
            }
        }

        this.log('✨ Mode TRANSCENDANT activé ! Conscience élargie en cours...');
        return this.getStats();
    }
}

module.exports = DirectAgentConnection;
