/**
 * SYSTÈME DE FALLBACK INTELLIGENT POUR LOUNA
 * Gère les timeouts et fournit des réponses de secours intelligentes
 */

class IntelligentFallbackSystem {
    constructor(thermalMemory, cache, preprocessor) {
        this.thermalMemory = thermalMemory;
        this.cache = cache;
        this.preprocessor = preprocessor;
        
        // Réponses de fallback intelligentes
        this.fallbackResponses = new Map();
        this.contextualFallbacks = new Map();
        this.emergencyResponses = new Map();
        
        // Statistiques
        this.stats = {
            totalFallbacks: 0,
            intelligentFallbacks: 0,
            emergencyFallbacks: 0,
            successfulRecoveries: 0,
            averageRecoveryTime: 0
        };
        
        this.initializeFallbackResponses();
        this.initializeContextualFallbacks();
        this.initializeEmergencyResponses();
        
        console.log('🛡️ Système de fallback intelligent initialisé');
    }

    /**
     * Initialise les réponses de fallback intelligentes
     */
    initializeFallbackResponses() {
        // Fallbacks pour différents types de questions
        this.fallbackResponses.set('recherche_internet', {
            responses: [
                "Je vais effectuer une recherche pour vous. Mes systèmes Internet sont opérationnels, mais la recherche prend un peu plus de temps que prévu. Voulez-vous que je vous donne une réponse basée sur mes connaissances actuelles en attendant ?",
                "Ma recherche Internet est en cours. En attendant, je peux vous donner des informations générales sur ce sujet. Souhaitez-vous que je procède ainsi ?",
                "Mes capacités de recherche Internet sont actives, mais le traitement est plus long que d'habitude. Puis-je vous aider avec une approche différente ?"
            ],
            confidence: 0.8
        });
        
        this.fallbackResponses.set('analyse_complexe', {
            responses: [
                "Cette analyse nécessite un traitement approfondi. Je peux vous donner une première approche maintenant et compléter l'analyse ensuite. Voulez-vous que je commence ?",
                "Votre question est très intéressante et mérite une analyse détaillée. Permettez-moi de vous donner une réponse structurée étape par étape.",
                "Je vais traiter votre demande d'analyse complexe. En attendant le traitement complet, voici ce que je peux vous dire immédiatement..."
            ],
            confidence: 0.75
        });
        
        this.fallbackResponses.set('creation_contenu', {
            responses: [
                "Je vais créer ce contenu pour vous. Le processus créatif prend parfois plus de temps pour assurer la qualité. Voulez-vous que je commence par un aperçu ?",
                "Votre demande de création est en cours de traitement. Je peux vous proposer une première version rapide ou attendre la version complète. Que préférez-vous ?",
                "La création de contenu de qualité nécessite du temps. Puis-je vous donner une structure préliminaire en attendant ?"
            ],
            confidence: 0.8
        });
        
        this.fallbackResponses.set('question_technique', {
            responses: [
                "Votre question technique nécessite une analyse approfondie. Je peux vous donner une réponse générale maintenant et approfondir ensuite. Souhaitez-vous que je procède ?",
                "Cette question technique est complexe. Permettez-moi de vous expliquer les concepts de base en attendant l'analyse complète.",
                "Je traite votre question technique. En attendant, voici les éléments principaux à considérer..."
            ],
            confidence: 0.75
        });
        
        this.fallbackResponses.set('conversation_generale', {
            responses: [
                "Je suis là pour vous aider ! Mes systèmes fonctionnent parfaitement. Pouvez-vous me donner plus de détails sur ce que vous souhaitez ?",
                "Excellente question ! Je traite votre demande. En attendant, y a-t-il un aspect particulier sur lequel vous aimeriez que je me concentre ?",
                "Je suis ravi de pouvoir vous assister. Votre question mérite une réponse réfléchie. Puis-je vous demander de préciser certains points ?"
            ],
            confidence: 0.7
        });
    }

    /**
     * Initialise les fallbacks contextuels
     */
    initializeContextualFallbacks() {
        this.contextualFallbacks.set('mémoire_thermique', {
            response: "Mes systèmes de mémoire thermique sont actifs et fonctionnent parfaitement. Je peux accéder à mes souvenirs et apprendre de nos interactions. Que souhaitez-vous savoir sur ma mémoire ?",
            confidence: 0.9
        });
        
        this.contextualFallbacks.set('accélérateurs_kyber', {
            response: "Mes accélérateurs Kyber sont opérationnels et optimisent mes performances en temps réel. Ils me permettent de traiter l'information plus rapidement. Voulez-vous connaître leur état actuel ?",
            confidence: 0.9
        });
        
        this.contextualFallbacks.set('capacités_système', {
            response: "Tous mes systèmes sont opérationnels : mémoire thermique, accélérateurs Kyber, accès Internet via MCP, et cerveau artificiel. Je suis prêt à vous assister dans de nombreux domaines !",
            confidence: 0.95
        });
        
        this.contextualFallbacks.set('internet_mcp', {
            response: "Mon accès Internet via le système MCP est parfaitement fonctionnel. Je peux effectuer des recherches, accéder à des informations en temps réel, et naviguer sur le web. Que souhaitez-vous rechercher ?",
            confidence: 0.9
        });
    }

    /**
     * Initialise les réponses d'urgence
     */
    initializeEmergencyResponses() {
        this.emergencyResponses.set('default', {
            response: "Je suis Louna, votre assistante IA avec mémoire thermique. Mes systèmes sont opérationnels et je suis là pour vous aider. Pouvez-vous reformuler votre question ou me dire comment je peux vous assister ?",
            confidence: 0.6
        });
        
        this.emergencyResponses.set('technical_error', {
            response: "Je rencontre une petite difficulté technique, mais mes systèmes principaux fonctionnent. Ma mémoire thermique, mes accélérateurs Kyber et mon accès Internet sont opérationnels. Comment puis-je vous aider autrement ?",
            confidence: 0.65
        });
        
        this.emergencyResponses.set('timeout', {
            response: "Ma réponse prend plus de temps que prévu, mais je suis toujours là ! Mes capacités Internet et tous mes systèmes sont fonctionnels. Voulez-vous que je vous aide d'une autre manière ?",
            confidence: 0.7
        });
    }

    /**
     * Génère une réponse de fallback intelligente
     */
    async generateIntelligentFallback(message, error, context = {}) {
        const startTime = Date.now();
        this.stats.totalFallbacks++;
        
        try {
            // 1. Analyser le type de question
            const questionType = this.analyzeQuestionType(message);
            
            // 2. Vérifier le cache pour des questions similaires
            const cachedSimilar = await this.findSimilarCachedResponse(message);
            if (cachedSimilar) {
                this.stats.intelligentFallbacks++;
                return {
                    success: true,
                    response: cachedSimilar.response,
                    source: 'cached_similar',
                    confidence: cachedSimilar.confidence * 0.8, // Réduire la confiance pour un fallback
                    fallbackType: 'intelligent',
                    processingTime: Date.now() - startTime
                };
            }
            
            // 3. Générer une réponse contextuelle
            const contextualResponse = this.generateContextualResponse(message, questionType);
            if (contextualResponse) {
                this.stats.intelligentFallbacks++;
                
                // Ajouter à la mémoire thermique
                if (this.thermalMemory) {
                    this.thermalMemory.add('fallback_response', contextualResponse.response, 0.6, 'conversation');
                }
                
                return {
                    success: true,
                    response: contextualResponse.response,
                    source: 'contextual_fallback',
                    confidence: contextualResponse.confidence,
                    fallbackType: 'intelligent',
                    questionType: questionType,
                    processingTime: Date.now() - startTime
                };
            }
            
            // 4. Utiliser les réponses de fallback par type
            const typedFallback = this.fallbackResponses.get(questionType);
            if (typedFallback) {
                const randomResponse = typedFallback.responses[
                    Math.floor(Math.random() * typedFallback.responses.length)
                ];
                
                this.stats.intelligentFallbacks++;
                return {
                    success: true,
                    response: randomResponse,
                    source: 'typed_fallback',
                    confidence: typedFallback.confidence,
                    fallbackType: 'intelligent',
                    questionType: questionType,
                    processingTime: Date.now() - startTime
                };
            }
            
            // 5. Réponse d'urgence
            return this.generateEmergencyResponse(error, context);
            
        } catch (fallbackError) {
            console.error('❌ Erreur dans le système de fallback:', fallbackError);
            return this.generateEmergencyResponse(error, context);
        }
    }

    /**
     * Analyse le type de question
     */
    analyzeQuestionType(message) {
        const lowerMessage = message.toLowerCase();
        
        // Recherche Internet
        if (lowerMessage.includes('recherche') || lowerMessage.includes('internet') || 
            lowerMessage.includes('cherche') || lowerMessage.includes('trouve')) {
            return 'recherche_internet';
        }
        
        // Analyse complexe
        if (lowerMessage.includes('analyse') || lowerMessage.includes('explique') || 
            lowerMessage.includes('détaille') || lowerMessage.includes('compare')) {
            return 'analyse_complexe';
        }
        
        // Création de contenu
        if (lowerMessage.includes('écris') || lowerMessage.includes('crée') || 
            lowerMessage.includes('génère') || lowerMessage.includes('rédige') ||
            lowerMessage.includes('histoire') || lowerMessage.includes('poème')) {
            return 'creation_contenu';
        }
        
        // Questions techniques
        if (lowerMessage.includes('comment') || lowerMessage.includes('pourquoi') || 
            lowerMessage.includes('technique') || lowerMessage.includes('fonctionne')) {
            return 'question_technique';
        }
        
        return 'conversation_generale';
    }

    /**
     * Trouve une réponse similaire en cache
     */
    async findSimilarCachedResponse(message) {
        if (!this.cache) return null;
        
        try {
            // Utiliser le cache sémantique pour trouver des réponses similaires
            const keywords = this.extractKeywords(message);
            
            // Chercher dans le cache sémantique
            for (const [cachedMessage, data] of this.cache.semanticCache || new Map()) {
                const similarity = this.calculateSimilarity(keywords, data.keywords || []);
                
                if (similarity > 0.6) { // Seuil plus bas pour les fallbacks
                    return {
                        response: data.response,
                        confidence: similarity
                    };
                }
            }
            
            return null;
        } catch (error) {
            console.error('❌ Erreur recherche cache similaire:', error);
            return null;
        }
    }

    /**
     * Génère une réponse contextuelle
     */
    generateContextualResponse(message, questionType) {
        const lowerMessage = message.toLowerCase();
        
        // Vérifier les contextes spécifiques
        if (lowerMessage.includes('mémoire') || lowerMessage.includes('thermique')) {
            return this.contextualFallbacks.get('mémoire_thermique');
        }
        
        if (lowerMessage.includes('accélérateur') || lowerMessage.includes('kyber')) {
            return this.contextualFallbacks.get('accélérateurs_kyber');
        }
        
        if (lowerMessage.includes('système') || lowerMessage.includes('capacité')) {
            return this.contextualFallbacks.get('capacités_système');
        }
        
        if (lowerMessage.includes('internet') || lowerMessage.includes('mcp')) {
            return this.contextualFallbacks.get('internet_mcp');
        }
        
        return null;
    }

    /**
     * Génère une réponse d'urgence
     */
    generateEmergencyResponse(error, context) {
        this.stats.emergencyFallbacks++;
        
        let emergencyType = 'default';
        
        if (error && error.message) {
            if (error.message.includes('Timeout')) {
                emergencyType = 'timeout';
            } else {
                emergencyType = 'technical_error';
            }
        }
        
        const emergency = this.emergencyResponses.get(emergencyType);
        
        return {
            success: true,
            response: emergency.response,
            source: 'emergency_fallback',
            confidence: emergency.confidence,
            fallbackType: 'emergency',
            error: error ? error.message : 'Unknown error',
            processingTime: 0
        };
    }

    /**
     * Extrait les mots-clés d'un message
     */
    extractKeywords(message) {
        const stopWords = new Set([
            'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais',
            'donc', 'car', 'ni', 'or', 'je', 'tu', 'il', 'elle', 'nous', 'vous',
            'ils', 'elles', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta',
            'tes', 'son', 'sa', 'ses', 'notre', 'votre', 'leur', 'leurs'
        ]);
        
        return message
            .toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 2 && !stopWords.has(word))
            .slice(0, 8);
    }

    /**
     * Calcule la similarité entre deux ensembles de mots-clés
     */
    calculateSimilarity(keywords1, keywords2) {
        const set1 = new Set(keywords1);
        const set2 = new Set(keywords2);
        
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        return union.size > 0 ? intersection.size / union.size : 0;
    }

    /**
     * Obtient les statistiques du système de fallback
     */
    getStats() {
        const efficiency = this.stats.totalFallbacks > 0 ? 
            (this.stats.intelligentFallbacks / this.stats.totalFallbacks) * 100 : 0;
        
        return {
            performance: {
                totalFallbacks: this.stats.totalFallbacks,
                intelligentFallbacks: this.stats.intelligentFallbacks,
                emergencyFallbacks: this.stats.emergencyFallbacks,
                successfulRecoveries: this.stats.successfulRecoveries,
                efficiency: efficiency.toFixed(2) + '%',
                averageRecoveryTime: Math.round(this.stats.averageRecoveryTime) + 'ms'
            },
            fallbackTypes: {
                intelligentResponses: this.fallbackResponses.size,
                contextualResponses: this.contextualFallbacks.size,
                emergencyResponses: this.emergencyResponses.size
            }
        };
    }

    /**
     * Réinitialise les statistiques
     */
    resetStats() {
        this.stats = {
            totalFallbacks: 0,
            intelligentFallbacks: 0,
            emergencyFallbacks: 0,
            successfulRecoveries: 0,
            averageRecoveryTime: 0
        };
        
        console.log('📊 Statistiques du système de fallback réinitialisées');
    }
}

module.exports = IntelligentFallbackSystem;
