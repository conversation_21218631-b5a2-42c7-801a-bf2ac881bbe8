{"timestamp": "2025-05-28T04:55:32.042Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3852114674396785, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.20816661404490808, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": null, "curiosity": null, "confidence": null, "energy": 1, "focus": null, "creativity": 1, "stress": null, "fatigue": 1, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748408122774, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, "emotionalHistory": [{"timestamp": 1748407366318, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407382351, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407397697, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407413643, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407429726, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407444958, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407460105, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407475173, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407491761, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407506837, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407522867, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407538163, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407553267, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407568903, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407584226, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407599738, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407615228, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407630394, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407645395, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407660547, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407676740, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407691741, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407706809, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407722521, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407739138, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407754683, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407772564, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407787647, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407802648, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407817875, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407833050, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407848051, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407863287, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407878516, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407894331, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407910114, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407925454, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407940455, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407955455, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407970524, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748407986180, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408001312, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408016313, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408032229, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408047770, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408062770, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408077772, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408092773, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408107773, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}, {"timestamp": 1748408122774, "mood": "creative", "dominantEmotion": "creativity", "qi": null, "neuronActivity": 0.5169419304974505}], "currentThoughts": [{"text": "Je vois des connexions inattendues entre les concepts.", "time": "12:54:22 AM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "12:54:37 AM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "12:54:52 AM", "mood": "creative"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "12:55:07 AM", "mood": "creative"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "12:55:22 AM", "mood": "creative"}], "circadianRhythm": 0.9802559912467739, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 1.5341499741534044, "stability": 0.8592103751724778, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 1.5256124806150533, "stability": 0.9853445263284768, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.5128062403075269, "stability": 0.9491993720194467, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_9938": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.831710215644531, "stability": 0.7940546414923079, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407621665, "expiresAt": 1748408221665}, "response_accelerator_auto_10290": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.9488480941755622, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748407851036, "expiresAt": 1748408451036}, "neural_stimulator_auto_10352": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.869877569589317, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748407884091, "expiresAt": 1748408484091}, "emergency_memory_optimizer_10406": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.9141552298897533, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407922060, "expiresAt": 1748408222060}, "emergency_memory_optimizer_10407": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8339583569621352, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407922061, "expiresAt": 1748408222061}, "cpu_booster_auto_10573": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.4025000000000003, "stability": 0.8610192815257157, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748408017900, "expiresAt": 1748408617900}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 1.5341499741534044, "stability": 0.8592103751724778, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 1.5256124806150533, "stability": 0.9853445263284768, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.5128062403075269, "stability": 0.9491993720194467, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_9938": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.831710215644531, "stability": 0.7940546414923079, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407621665, "expiresAt": 1748408221665}, "response_accelerator_auto_10290": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.9488480941755622, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748407851036, "expiresAt": 1748408451036}, "neural_stimulator_auto_10352": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.869877569589317, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748407884091, "expiresAt": 1748408484091}, "emergency_memory_optimizer_10406": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.9141552298897533, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407922060, "expiresAt": 1748408222060}, "emergency_memory_optimizer_10407": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8339583569621352, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748407922061, "expiresAt": 1748408222061}, "cpu_booster_auto_10573": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.4025000000000003, "stability": 0.8610192815257157, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748408017900, "expiresAt": 1748408617900}}, "totalAccelerators": 9, "activeAccelerators": 9, "averageBoostFactor": 2.320618628968946, "efficiency": 0.8392371177381367, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748408079817, "totalBoostPower": 20.885567660720515, "averageEnergy": 1, "averageStability": 0.8906297163505771, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748403685453, "memoryEfficiency": 0, "thermalStability": 0.9669667879740397, "cpuUsage": 0.4127685546875, "responseTime": 440.0684110237736, "overall": 0.5487338708932186}, {"timestamp": 1748403745467, "memoryEfficiency": 0, "thermalStability": 0.9931391802099016, "cpuUsage": 0.4919189453125, "responseTime": 371.6999344924095, "overall": 0.5566883873378023}, {"timestamp": 1748403805468, "memoryEfficiency": 0, "thermalStability": 0.9888430434796546, "cpuUsage": 0.4101806640625, "responseTime": 541.3170303307111, "overall": 0.5297721479806361}, {"timestamp": 1748403865468, "memoryEfficiency": 0, "thermalStability": 0.9851519510563876, "cpuUsage": 0.41171875, "responseTime": 636.1066751390617, "overall": 0.529990271819156}, {"timestamp": 1748403925468, "memoryEfficiency": 0, "thermalStability": 0.9797769538230366, "cpuUsage": 0.4638427734375, "responseTime": 238.56844298114802, "overall": 0.5840582004565674}, {"timestamp": 1748403985470, "memoryEfficiency": 0, "thermalStability": 0.9949810904347234, "cpuUsage": 0.496923828125, "responseTime": 209.53622166659284, "overall": 0.5164878737554286}, {"timestamp": 1748404045474, "memoryEfficiency": 0, "thermalStability": 0.9923122537218863, "cpuUsage": 0.4980224609375, "responseTime": 636.4142981445298, "overall": 0.5129111040235044}, {"timestamp": 1748404105533, "memoryEfficiency": 0, "thermalStability": 0.9758611843403843, "cpuUsage": 0.510107421875, "responseTime": 305.47832683075956, "overall": 0.5084347592807897}, {"timestamp": 1748404165534, "memoryEfficiency": 0, "thermalStability": 0.9902800217684772, "cpuUsage": 0.50966796875, "responseTime": 301.0071570918469, "overall": 0.5307940940396243}, {"timestamp": 1748404225567, "memoryEfficiency": 0, "thermalStability": 0.9768858097079728, "cpuUsage": 0.54189453125, "responseTime": 417.12320620416614, "overall": 0.5635461435322927}, {"timestamp": 1748404285568, "memoryEfficiency": 0, "thermalStability": 0.915791924794515, "cpuUsage": 0.6204345703125, "responseTime": 410.29784629414337, "overall": 0.5074677073296074}, {"timestamp": 1748404345568, "memoryEfficiency": 0, "thermalStability": 0.910554276779294, "cpuUsage": 0.4960205078125, "responseTime": 388.23562570498507, "overall": 0.5581101435518278}, {"timestamp": 1748404405569, "memoryEfficiency": 0, "thermalStability": 0.9909554284893805, "cpuUsage": 0.526513671875, "responseTime": 675.4572300547375, "overall": 0.5425216864868573}, {"timestamp": 1748404465785, "memoryEfficiency": 0, "thermalStability": 0.9910095028165314, "cpuUsage": 0.46767578125, "responseTime": 373.95342123220126, "overall": 0.5807459102926371}, {"timestamp": 1748404525786, "memoryEfficiency": 0, "thermalStability": 0.9833879578858614, "cpuUsage": 0.439892578125, "responseTime": 555.3600351704329, "overall": 0.5426146934746507}, {"timestamp": 1748404585891, "memoryEfficiency": 0, "thermalStability": 0.9956972771220737, "cpuUsage": 0.4658935546875, "responseTime": 435.49289638978007, "overall": 0.5386555096584767}, {"timestamp": 1748404645893, "memoryEfficiency": 0, "thermalStability": 0.8586762029677629, "cpuUsage": 0.5925537109375, "responseTime": 299.75974581145795, "overall": 0.5315587693217664}, {"timestamp": 1748404705928, "memoryEfficiency": 0, "thermalStability": 0.9928673795941804, "cpuUsage": 0.5576904296875, "responseTime": 522.1716837231438, "overall": 0.5916006446814353}, {"timestamp": 1748404765929, "memoryEfficiency": 0, "thermalStability": 0.995049972873595, "cpuUsage": 0.49765625, "responseTime": 640.4831541466536, "overall": 0.5238863060341493}, {"timestamp": 1748404825936, "memoryEfficiency": 0, "thermalStability": 0.9813885846485694, "cpuUsage": 0.43681640625, "responseTime": 654.7609231382751, "overall": 0.5150466025873252}, {"timestamp": 1748404886000, "memoryEfficiency": 0, "thermalStability": 0.9987324322677321, "cpuUsage": 0.3969482421875, "responseTime": 561.7328041382209, "overall": 0.5571227669526095}, {"timestamp": 1748404946034, "memoryEfficiency": 0, "thermalStability": 0.982501502831777, "cpuUsage": 0.453564453125, "responseTime": 283.8725485688454, "overall": 0.5352329456488033}, {"timestamp": 1748405006076, "memoryEfficiency": 0, "thermalStability": 0.9947442960407998, "cpuUsage": 0.43251953125, "responseTime": 681.4074797395126, "overall": 0.536353568920123}, {"timestamp": 1748405066076, "memoryEfficiency": 0, "thermalStability": 0.9986821239607202, "cpuUsage": 0.410546875, "responseTime": 367.2329939202874, "overall": 0.5147388685327232}, {"timestamp": 1748405126199, "memoryEfficiency": 0, "thermalStability": 0.9971418884065416, "cpuUsage": 0.4198974609375, "responseTime": 294.1764501325719, "overall": 0.562206018250938}, {"timestamp": 1748405186329, "memoryEfficiency": 0, "thermalStability": 0.9601496946480539, "cpuUsage": 0.522021484375, "responseTime": 427.77015497490174, "overall": 0.5348310567236134}, {"timestamp": 1748405246331, "memoryEfficiency": 0, "thermalStability": 0.9954271292106973, "cpuUsage": 0.601953125, "responseTime": 533.5546859001568, "overall": 0.579289419654387}, {"timestamp": 1748405306496, "memoryEfficiency": 0, "thermalStability": 0.9827803457776705, "cpuUsage": 0.6376953125, "responseTime": 525.6489776265555, "overall": 0.5401588119708278}, {"timestamp": 1748405366515, "memoryEfficiency": 0.4179882193022202, "thermalStability": 0.9009949666344458, "cpuUsage": 0.7209228515625, "responseTime": 533.9789629935192, "overall": 0.6524318149681038}, {"timestamp": 1748405428006, "memoryEfficiency": 0.43438077634011096, "thermalStability": 0.9223115833683146, "cpuUsage": 0.699462890625, "responseTime": 209.13598756102496, "overall": 0.7029061148619606}, {"timestamp": 1748405488177, "memoryEfficiency": 0.3155963302752294, "thermalStability": 0.975502542530497, "cpuUsage": 0.610888671875, "responseTime": 644.0669162328059, "overall": 0.6557929684163813}, {"timestamp": 1748405548177, "memoryEfficiency": 0.31510277033065237, "thermalStability": 0.9965584147307608, "cpuUsage": 0.6064697265625, "responseTime": 226.35955745359897, "overall": 0.675655752244123}, {"timestamp": 1748405608883, "memoryEfficiency": 0.13921568627450975, "thermalStability": 0.9688755950993961, "cpuUsage": 0.52294921875, "responseTime": 674.6767213068443, "overall": 0.5668806071841945}, {"timestamp": 1748405669158, "memoryEfficiency": 0.08920265780730885, "thermalStability": 0.9929115718023644, "cpuUsage": 0.6400634765625, "responseTime": 666.2351625453873, "overall": 0.6098690859160669}, {"timestamp": 1748405729873, "memoryEfficiency": 0.16821192052980138, "thermalStability": 0.970227699354291, "cpuUsage": 0.6530517578125, "responseTime": 679.7029399605553, "overall": 0.5863300981171712}, {"timestamp": 1748405789927, "memoryEfficiency": 0.29449699054170253, "thermalStability": 0.9847229512615336, "cpuUsage": 0.6003173828125, "responseTime": 592.477442012603, "overall": 0.6971177536805279}, {"timestamp": 1748405850276, "memoryEfficiency": 0.24416666666666664, "thermalStability": 0.9694240194641881, "cpuUsage": 0.68359375, "responseTime": 605.2235435520209, "overall": 0.6416545373113421}, {"timestamp": 1748405910642, "memoryEfficiency": 0.23712984054669695, "thermalStability": 0.9631707363658482, "cpuUsage": 0.660595703125, "responseTime": 299.394310499672, "overall": 0.6110450887027834}, {"timestamp": 1748405971175, "memoryEfficiency": 0.17392290249433096, "thermalStability": 0.9927980653113789, "cpuUsage": 0.63974609375, "responseTime": 355.51895345756816, "overall": 0.6119651243042658}, {"timestamp": 1748406031181, "memoryEfficiency": 0.17625482625482625, "thermalStability": 0.9479476596332259, "cpuUsage": 0.53291015625, "responseTime": 514.9024412013508, "overall": 0.5801577467782715}, {"timestamp": 1748406091181, "memoryEfficiency": 0.07689170820117808, "thermalStability": 0.9830991764863333, "cpuUsage": 0.57060546875, "responseTime": 297.6129744639772, "overall": 0.5443885787750693}, {"timestamp": 1748406151182, "memoryEfficiency": 0.03280632411067186, "thermalStability": 0.9807535064303212, "cpuUsage": 0.7130126953125, "responseTime": 567.5603522096292, "overall": 0.539649482610495}, {"timestamp": 1748406211353, "memoryEfficiency": 0.1350877192982457, "thermalStability": 0.9390300182832612, "cpuUsage": 0.7184326171875, "responseTime": 404.7972121079689, "overall": 0.6220914072650872}, {"timestamp": 1748406271354, "memoryEfficiency": 0.11202185792349728, "thermalStability": 0.9871499424179395, "cpuUsage": 0.6129638671875, "responseTime": 244.14614344604882, "overall": 0.5620211154969137}, {"timestamp": 1748406331360, "memoryEfficiency": 0.10679611650485443, "thermalStability": 0.9057772757278548, "cpuUsage": 0.548388671875, "responseTime": 292.82190715230087, "overall": 0.5489336364029443}, {"timestamp": 1748406391360, "memoryEfficiency": 0, "thermalStability": 0.9946045749717288, "cpuUsage": 0.5516357421875, "responseTime": 397.7952307719382, "overall": 0.5686700449463187}, {"timestamp": 1748406451550, "memoryEfficiency": 0, "thermalStability": 0.9882318617569076, "cpuUsage": 0.4958984375, "responseTime": 608.9847664244676, "overall": 0.5207292978850866}, {"timestamp": 1748406511680, "memoryEfficiency": 0, "thermalStability": 0.9972003930144839, "cpuUsage": 0.4565673828125, "responseTime": 677.7659196458399, "overall": 0.5326755627328155}, {"timestamp": 1748406571839, "memoryEfficiency": 0, "thermalStability": 0.9848068611489402, "cpuUsage": 0.45166015625, "responseTime": 400.66647207259894, "overall": 0.5190896165797805}, {"timestamp": 1748406631881, "memoryEfficiency": 0, "thermalStability": 0.9644680214011007, "cpuUsage": 0.557421875, "responseTime": 486.25412833484967, "overall": 0.5689303134131678}, {"timestamp": 1748406691882, "memoryEfficiency": 0, "thermalStability": 0.9753838574306832, "cpuUsage": 0.5201904296875, "responseTime": 492.09272525061544, "overall": 0.5527261553404581}, {"timestamp": 1748406752070, "memoryEfficiency": 0, "thermalStability": 0.9895217592931456, "cpuUsage": 0.5307373046875, "responseTime": 486.1301269044648, "overall": 0.5324853595702634}, {"timestamp": 1748406812071, "memoryEfficiency": 0, "thermalStability": 0.9947267207834456, "cpuUsage": 0.576318359375, "responseTime": 468.203807983879, "overall": 0.5916272778854512}, {"timestamp": 1748406872178, "memoryEfficiency": 0, "thermalStability": 0.9790924814012315, "cpuUsage": 0.5022216796875, "responseTime": 418.9586628637686, "overall": 0.5822762669713528}, {"timestamp": 1748406933153, "memoryEfficiency": 0, "thermalStability": 0.9448501668042607, "cpuUsage": 0.4709228515625, "responseTime": 464.1048093926099, "overall": 0.5523427677872722}, {"timestamp": 1748406994106, "memoryEfficiency": 0, "thermalStability": 0.9785114999032682, "cpuUsage": 0.5099609375, "responseTime": 368.34348299862324, "overall": 0.5718601065636426}, {"timestamp": 1748407054347, "memoryEfficiency": 0, "thermalStability": 0.9938495099544525, "cpuUsage": 0.5389892578125, "responseTime": 592.3085015010213, "overall": 0.56307205906704}, {"timestamp": 1748407114350, "memoryEfficiency": 0, "thermalStability": 0.9706783511986335, "cpuUsage": 0.5783203125, "responseTime": 389.73464825375487, "overall": 0.5338719964158916}, {"timestamp": 1748407174351, "memoryEfficiency": 0, "thermalStability": 0.9715897624691328, "cpuUsage": 0.5171142578125, "responseTime": 424.0986560894308, "overall": 0.5159787685287421}, {"timestamp": 1748407234693, "memoryEfficiency": 0, "thermalStability": 0.989974224070708, "cpuUsage": 0.5367431640625, "responseTime": 499.49712269799835, "overall": 0.5527740977919133}, {"timestamp": 1748407294995, "memoryEfficiency": 0, "thermalStability": 0.99707619862424, "cpuUsage": 0.696240234375, "responseTime": 628.5985569863677, "overall": 0.5701821250157193}, {"timestamp": 1748407355238, "memoryEfficiency": 0.18734402852049903, "thermalStability": 0.8675979781481955, "cpuUsage": 0.5540771484375, "responseTime": 341.06597287737543, "overall": 0.5817515130162563}, {"timestamp": 1748407415354, "memoryEfficiency": 0.00833937635968085, "thermalStability": 0.9477908259050714, "cpuUsage": 0.5330322265625, "responseTime": 287.56356064405514, "overall": 0.5685222354124437}, {"timestamp": 1748407475782, "memoryEfficiency": 0.07449856733524363, "thermalStability": 0.9725170726991362, "cpuUsage": 0.5831787109375, "responseTime": 560.9143125217909, "overall": 0.5439675775662312}, {"timestamp": 1748407536105, "memoryEfficiency": 0.08690271570986596, "thermalStability": 0.9930067902223931, "cpuUsage": 0.587158203125, "responseTime": 411.93610349130864, "overall": 0.5954090931843247}, {"timestamp": 1748407596433, "memoryEfficiency": 0.0002726653033402471, "thermalStability": 0.9755379017856386, "cpuUsage": 0.53359375, "responseTime": 395.3509671256739, "overall": 0.5911975160750795}, {"timestamp": 1748407658315, "memoryEfficiency": 0.12337662337662325, "thermalStability": 0.962219780890478, "cpuUsage": 0.610693359375, "responseTime": 531.1894673881739, "overall": 0.5685071327569995}, {"timestamp": 1748407718316, "memoryEfficiency": 0.13313212608987257, "thermalStability": 0.9613533675670624, "cpuUsage": 0.594482421875, "responseTime": 486.0467333781712, "overall": 0.5592123574547616}, {"timestamp": 1748407779347, "memoryEfficiency": 0.1404388714733542, "thermalStability": 0.980259298822946, "cpuUsage": 0.65546875, "responseTime": 512.7303375469871, "overall": 0.59104754221076}, {"timestamp": 1748407839608, "memoryEfficiency": 0.1335078534031413, "thermalStability": 0.9806927565071318, "cpuUsage": 0.642529296875, "responseTime": 490.31592013550403, "overall": 0.6408644668772325}, {"timestamp": 1748407899669, "memoryEfficiency": 0.1397683397683397, "thermalStability": 0.9829859560148584, "cpuUsage": 0.58544921875, "responseTime": 679.9701430569773, "overall": 0.5956814019177568}, {"timestamp": 1748407959804, "memoryEfficiency": 0.097601827179292, "thermalStability": 0.9903356260723538, "cpuUsage": 0.62265625, "responseTime": 320.0079383281678, "overall": 0.5697900590802676}, {"timestamp": 1748408019816, "memoryEfficiency": 0.07385398981324287, "thermalStability": 0.9822841442293591, "cpuUsage": 0.7172607421875, "responseTime": 338.0957571759922, "overall": 0.6025443020676419}, {"timestamp": 1748408079817, "memoryEfficiency": 0.030253025302530268, "thermalStability": 0.9905281499028206, "cpuUsage": 0.6337890625, "responseTime": 553.3761328145774, "overall": 0.5829610536163687}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}