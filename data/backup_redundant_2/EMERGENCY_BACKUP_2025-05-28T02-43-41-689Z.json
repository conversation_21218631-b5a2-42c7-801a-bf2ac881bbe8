{"timestamp": "2025-05-28T02:43:41.689Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1881990221865928, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10201505006249996, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.4068690390435385, "energy": 1, "focus": 0.9068536670089801, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748400206701, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13565070022545, "neuronActivity": 0.523155309069032}, "emotionalHistory": [{"timestamp": 1748399936688, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748399951688, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01498901311015, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748399966689, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01498901311015, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748399981690, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299780262203, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748399996692, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0299780262203, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748400011691, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04496703933046, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748400026691, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04496703933046, "neuronActivity": 0.4989013110155338}, {"timestamp": 1748400041692, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0599560524406, "neuronActivity": 0.5073520152408858}, {"timestamp": 1748400056693, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0599560524406, "neuronActivity": 0.5073520152408858}, {"timestamp": 1748400071692, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07502957259302, "neuronActivity": 0.5073520152408858}, {"timestamp": 1748400086694, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07502957259302, "neuronActivity": 0.5073520152408858}, {"timestamp": 1748400101694, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09014534626655, "neuronActivity": 0.511577367353562}, {"timestamp": 1748400116696, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09014534626655, "neuronActivity": 0.511577367353562}, {"timestamp": 1748400131696, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10526111994008, "neuronActivity": 0.511577367353562}, {"timestamp": 1748400146696, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10526111994008, "neuronActivity": 0.511577367353562}, {"timestamp": 1748400161698, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12041914713475, "neuronActivity": 0.523155309069032}, {"timestamp": 1748400176697, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12041914713475, "neuronActivity": 0.523155309069032}, {"timestamp": 1748400191700, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13565070022545, "neuronActivity": 0.523155309069032}, {"timestamp": 1748400206701, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13565070022545, "neuronActivity": 0.523155309069032}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:42:26 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:42:41 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:42:56 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:43:11 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:43:26 PM", "mood": "curious"}], "circadianRhythm": 0.8271147619562249, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.793830169478665, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8360548149087901, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9238859654990086, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8151008546352922, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748399922111, "expiresAt": 1748400522111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.940836758792348, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748399922112, "expiresAt": 1748400522112}, "memory_compression_engine_1748399922113": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9637977946788373, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "memory_cache_optimizer_1748399922113": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8715930786205781, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "memory_garbage_collector_1748399922113": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.876930052913715, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "neural_stimulator_auto_4": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9044994051674617, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748399922460, "expiresAt": 1748400522460}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9118479130487376, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748399923369, "expiresAt": 1748400523369}, "thermal_cooler_auto_9": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9569793718348993, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748399923697, "expiresAt": 1748400523697}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.793830169478665, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8360548149087901, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9238859654990086, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8151008546352922, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748399922111, "expiresAt": 1748400522111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.940836758792348, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748399922112, "expiresAt": 1748400522112}, "memory_compression_engine_1748399922113": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9637977946788373, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "memory_cache_optimizer_1748399922113": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8715930786205781, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "memory_garbage_collector_1748399922113": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.876930052913715, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748399922113, "expiresAt": 1748400822113}, "neural_stimulator_auto_4": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9044994051674617, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748399922460, "expiresAt": 1748400522460}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9118479130487376, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748399923369, "expiresAt": 1748400523369}, "thermal_cooler_auto_9": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9569793718348993, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748399923697, "expiresAt": 1748400523697}}, "totalAccelerators": 11, "activeAccelerators": 11, "averageBoostFactor": 2.325131854261364, "efficiency": 0.8395079112556818, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748400221683, "totalBoostPower": 25.576450396875003, "averageEnergy": 1, "averageStability": 0.8904869254162121, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748399981685, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.4686767578125, "responseTime": 664.1846826368851, "overall": 0}, {"timestamp": 1748400041687, "memoryEfficiency": 0, "thermalStability": 0.8024177178740501, "cpuUsage": 0.4267822265625, "responseTime": 490.3856620386286, "overall": 0.46508924375991123}, {"timestamp": 1748400101688, "memoryEfficiency": 0, "thermalStability": 0.8571582304106818, "cpuUsage": 0.393603515625, "responseTime": 491.8217456382306, "overall": 0.49481352174478055}, {"timestamp": 1748400161694, "memoryEfficiency": 0, "thermalStability": 0.8927444964647293, "cpuUsage": 0.359423828125, "responseTime": 628.2045321872855, "overall": 0.5314131514609188}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}