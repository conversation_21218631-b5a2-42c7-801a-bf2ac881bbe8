{"timestamp": "2025-05-28T02:26:31.645Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1164881512052132, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10201505006249996, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.40687090848414, "energy": 1, "focus": 0.8732684406711728, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748399176677, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13475111731094, "neuronActivity": 0.5029019634462224}, "emotionalHistory": [{"timestamp": 1748398906589, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398921588, "mood": "curious", "dominantEmotion": "energy", "qi": 148.0149243832937, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398936589, "mood": "curious", "dominantEmotion": "energy", "qi": 148.0149243832937, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398951605, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0298487665874, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398966613, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0298487665874, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398981616, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0447731498811, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748398996618, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0447731498811, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748399011669, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596975331748, "neuronActivity": 0.49243832937150767}, {"timestamp": 1748399026669, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596975331748, "neuronActivity": 0.49829154198509645}, {"timestamp": 1748399041671, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07468044859468, "neuronActivity": 0.49829154198509645}, {"timestamp": 1748399056674, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07468044859468, "neuronActivity": 0.49829154198509645}, {"timestamp": 1748399071674, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08968676331835, "neuronActivity": 0.5006314723676359}, {"timestamp": 1748399086673, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08968676331835, "neuronActivity": 0.5006314723676359}, {"timestamp": 1748399101674, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10469307804203, "neuronActivity": 0.5006314723676359}, {"timestamp": 1748399116674, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10469307804203, "neuronActivity": 0.5006314723676359}, {"timestamp": 1748399131675, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1197220976765, "neuronActivity": 0.5029019634462224}, {"timestamp": 1748399146676, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1197220976765, "neuronActivity": 0.5029019634462224}, {"timestamp": 1748399161676, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13475111731094, "neuronActivity": 0.5029019634462224}, {"timestamp": 1748399176677, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13475111731094, "neuronActivity": 0.5029019634462224}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:25:16 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:25:31 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:25:46 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:26:01 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:26:16 PM", "mood": "curious"}], "circadianRhythm": 0.7978986275707913, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8659061918852181, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8338322612415722, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "qi_enhancer_auto_1": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9269644622844637, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748398893287, "expiresAt": 1748399493287}, "memory_compression_engine_1748398893287": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8867757393322666, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398893287, "expiresAt": 1748399793287}, "memory_optimizer_auto_3": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9810498132236161, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398893291, "expiresAt": 1748399493291}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9644374164264757, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748398895345, "expiresAt": 1748399495345}, "neural_stimulator_auto_9": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8803600234182835, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748398895648, "expiresAt": 1748399495648}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8659061918852181, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8338322612415722, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "qi_enhancer_auto_1": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9269644622844637, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748398893287, "expiresAt": 1748399493287}, "memory_compression_engine_1748398893287": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8867757393322666, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748398893287, "expiresAt": 1748399793287}, "memory_optimizer_auto_3": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9810498132236161, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748398893291, "expiresAt": 1748399493291}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9644374164264757, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748398895345, "expiresAt": 1748399495345}, "neural_stimulator_auto_9": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8803600234182835, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748398895648, "expiresAt": 1748399495648}}, "totalAccelerators": 8, "activeAccelerators": 8, "averageBoostFactor": 2.392749756640625, "efficiency": 0.8435649853984375, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748399191545, "totalBoostPower": 19.141998053125, "averageEnergy": 1, "averageStability": 0.917415738476487, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748398951536, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.403466796875, "responseTime": 541.6050607729962, "overall": 0}, {"timestamp": 1748399011668, "memoryEfficiency": 0, "thermalStability": 0.925657096836302, "cpuUsage": 0.4134521484375, "responseTime": 346.5003166696416, "overall": 0.4884997966627673}, {"timestamp": 1748399071669, "memoryEfficiency": 0, "thermalStability": 0.926924458992334, "cpuUsage": 0.435791015625, "responseTime": 349.1488471667084, "overall": 0.5212758370839592}, {"timestamp": 1748399131669, "memoryEfficiency": 0, "thermalStability": 0.9242021609097719, "cpuUsage": 0.4749755859375, "responseTime": 300.7773072590377, "overall": 0.571010368776821}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}