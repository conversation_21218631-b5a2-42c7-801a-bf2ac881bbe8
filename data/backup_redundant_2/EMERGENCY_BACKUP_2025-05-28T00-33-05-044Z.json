{"timestamp": "2025-05-28T00:33:05.044Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3233070151344277, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10723211319284398, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.6676079717550942, "confidence": -5.7387482696233665, "energy": 1, "focus": 0.8630446063256272, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748392370337, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1897495924894, "neuronActivity": 0.5320485038784074}, "emotionalHistory": [{"timestamp": 1748391800036, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391815037, "mood": "curious", "dominantEmotion": "energy", "qi": 148.0151038749373, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391830039, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0151038749373, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391845041, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0302077498746, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391860043, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0302077498746, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391875044, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453116248119, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391890046, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453116248119, "neuronActivity": 0.5103874937307118}, {"timestamp": 1748391905145, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453116248119, "neuronActivity": 0.5188381979560639}, {"timestamp": 1748391920148, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06050000679144, "neuronActivity": 0.5188381979560639}, {"timestamp": 1748391935148, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06050000679144, "neuronActivity": 0.5188381979560639}, {"timestamp": 1748391950149, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.075688388771, "neuronActivity": 0.5188381979560639}, {"timestamp": 1748391965149, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.075688388771, "neuronActivity": 0.5216773109112964}, {"timestamp": 1748391980151, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0909051618801, "neuronActivity": 0.5216773109112964}, {"timestamp": 1748391995151, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0909051618801, "neuronActivity": 0.5216773109112964}, {"timestamp": 1748392010153, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1061219349892, "neuronActivity": 0.5216773109112964}, {"timestamp": 1748392025152, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1061219349892, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392040152, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12139402289677, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392055173, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13666611080433, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392070174, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13666611080433, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392085190, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1419381987119, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392100213, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1419381987119, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392115224, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14721028661947, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392130242, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14721028661947, "neuronActivity": 0.5272087907561215}, {"timestamp": 1748392145242, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15252462804816, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392160243, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15252462804816, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392175244, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15783896947684, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392190245, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15783896947684, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392205247, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16315331090553, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392220247, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16315331090553, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392235249, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16846765233421, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392250250, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16846765233421, "neuronActivity": 0.5314341428687974}, {"timestamp": 1748392265252, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.173788137373, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392280253, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.173788137373, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392295253, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1791086224118, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392310253, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1791086224118, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392325287, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1844291074506, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392340291, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1844291074506, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392355308, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1897495924894, "neuronActivity": 0.5320485038784074}, {"timestamp": 1748392370337, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1897495924894, "neuronActivity": 0.5320485038784074}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "8:31:50 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "8:32:05 PM", "mood": "curious"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "8:32:20 PM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "8:32:35 PM", "mood": "creative"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "8:32:50 PM", "mood": "creative"}], "circadianRhythm": 0.5713985727682832, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.457979102781406, "stability": 0.9998189489989828, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.218484327086055, "stability": 0.9061392167571085, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8592421635430274, "stability": 0.926162750361289, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8151247048623045, "stability": 0.8303813050901557, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748391785332, "expiresAt": 1748392385332}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.3193242326419923, "stability": 0.8217349090767733, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748391785333, "expiresAt": 1748392385333}, "memory_compression_engine_1748391785333": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.760498819449218, "stability": 0.9833210979238687, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748391785333, "expiresAt": 1748392685333}, "neural_stimulator_auto_4": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9411745868072268, "stability": 0.9696589020331645, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748391785333, "expiresAt": 1748392385333}, "response_accelerator_auto_13": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.445374114586915, "stability": 0.9219536721376123, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748391790099, "expiresAt": 1748392390099}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.457979102781406, "stability": 0.9998189489989828, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.218484327086055, "stability": 0.9061392167571085, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8592421635430274, "stability": 0.926162750361289, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8151247048623045, "stability": 0.8303813050901557, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748391785332, "expiresAt": 1748392385332}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.3193242326419923, "stability": 0.8217349090767733, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748391785333, "expiresAt": 1748392385333}, "memory_compression_engine_1748391785333": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.760498819449218, "stability": 0.9833210979238687, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748391785333, "expiresAt": 1748392685333}, "neural_stimulator_auto_4": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9411745868072268, "stability": 0.9696589020331645, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748391785333, "expiresAt": 1748392385333}, "response_accelerator_auto_13": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.445374114586915, "stability": 0.9219536721376123, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748391790099, "expiresAt": 1748392390099}}, "totalAccelerators": 8, "activeAccelerators": 8, "averageBoostFactor": 2.227150256469768, "efficiency": 0.833629015388186, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748392325065, "totalBoostPower": 17.817202051758144, "averageEnergy": 1, "averageStability": 0.9198963502973694, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748391845038, "memoryEfficiency": 0, "thermalStability": 0.9296420115563605, "cpuUsage": 0.4022216796875, "responseTime": 324.1583453553484, "overall": 0.49731767289952267}, {"timestamp": 1748391905143, "memoryEfficiency": 0, "thermalStability": 0.9872407242655754, "cpuUsage": 0.4035400390625, "responseTime": 367.57675599201514, "overall": 0.5825848587578333}, {"timestamp": 1748391965145, "memoryEfficiency": 0, "thermalStability": 0.9986575536015961, "cpuUsage": 0.4124755859375, "responseTime": 335.2921905218294, "overall": 0.5958213249915632}, {"timestamp": 1748392025146, "memoryEfficiency": 0, "thermalStability": 0.996893494659, "cpuUsage": 0.400537109375, "responseTime": 224.38169619229268, "overall": 0.5213132091307526}, {"timestamp": 1748392085147, "memoryEfficiency": 0, "thermalStability": 0.9972589618629879, "cpuUsage": 0.382470703125, "responseTime": 557.5646214556591, "overall": 0.5866496723844283}, {"timestamp": 1748392145149, "memoryEfficiency": 0, "thermalStability": 0.9971612420347001, "cpuUsage": 0.377978515625, "responseTime": 614.7120279192069, "overall": 0.5705110958196351}, {"timestamp": 1748392205150, "memoryEfficiency": 0, "thermalStability": 0.9971249085333612, "cpuUsage": 0.380078125, "responseTime": 417.0560437264061, "overall": 0.5307138605381543}, {"timestamp": 1748392265152, "memoryEfficiency": 0, "thermalStability": 0.9977425297101339, "cpuUsage": 0.3744873046875, "responseTime": 502.635814423183, "overall": 0.5989970969705085}, {"timestamp": 1748392325152, "memoryEfficiency": 0, "thermalStability": 0.997858962085512, "cpuUsage": 0.38330078125, "responseTime": 462.76262390534987, "overall": 0.5339538124553516}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}