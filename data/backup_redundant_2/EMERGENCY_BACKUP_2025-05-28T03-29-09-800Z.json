{"timestamp": "2025-05-28T03:29:09.800Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.299398443319547, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10252512531281245, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.406865690520896, "energy": 1, "focus": 0.9410179617034328, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748402936423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1369716847758, "neuronActivity": 0.534715411853864}, "emotionalHistory": [{"timestamp": 1748402666506, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402681507, "mood": "curious", "dominantEmotion": "energy", "qi": 148.01512212018963, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402696509, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01512212018963, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402711510, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024424037926, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402726512, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024424037926, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402741513, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453663605689, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402756514, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0453663605689, "neuronActivity": 0.5122120189642515}, {"timestamp": 1748402771421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06057298780078, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402786421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06057298780078, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402801420, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07577961503267, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402816421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07577961503267, "neuronActivity": 0.5206627231896037}, {"timestamp": 1748402831422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0910284957857, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402846421, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0910284957857, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402861422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10627737653874, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402876422, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10627737653874, "neuronActivity": 0.5248880753022798}, {"timestamp": 1748402891423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12162453065727, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402906424, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12162453065727, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402921423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1369716847758, "neuronActivity": 0.534715411853864}, {"timestamp": 1748402936423, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1369716847758, "neuronActivity": 0.534715411853864}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:27:56 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:28:11 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:28:26 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:28:41 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:28:56 PM", "mood": "curious"}], "circadianRhythm": 0.8952651464420898, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8716962520903838, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7678900164336611, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.930015240458002, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8791408516985187, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9208484771221822, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "memory_compression_engine_1748402651370": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9072447882050849, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_cache_optimizer_1748402651370": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8434251587105536, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_garbage_collector_1748402651370": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9101201834914315, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9103429358042603, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748402651571, "expiresAt": 1748403251571}, "thermal_cooler_auto_8": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9398369511925135, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748402651954, "expiresAt": 1748403251954}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9338411360740797, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748402653179, "expiresAt": 1748403253179}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8716962520903838, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7678900164336611, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.930015240458002, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8791408516985187, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9208484771221822, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748402651369, "expiresAt": 1748403251369}, "memory_compression_engine_1748402651370": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9072447882050849, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_cache_optimizer_1748402651370": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8434251587105536, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "memory_garbage_collector_1748402651370": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9101201834914315, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748402651370, "expiresAt": 1748403551370}, "response_accelerator_auto_5": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9103429358042603, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748402651571, "expiresAt": 1748403251571}, "thermal_cooler_auto_8": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9398369511925135, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748402651954, "expiresAt": 1748403251954}, "neural_stimulator_auto_13": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9338411360740797, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748402653179, "expiresAt": 1748403253179}}, "totalAccelerators": 11, "activeAccelerators": 11, "averageBoostFactor": 2.325131854261364, "efficiency": 0.8395079112556818, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748402949700, "totalBoostPower": 25.576450396875003, "averageEnergy": 1, "averageStability": 0.8922183628436973, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748402709809, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.3715576171875, "responseTime": 492.2449566065964, "overall": 0}, {"timestamp": 1748402769811, "memoryEfficiency": 0, "thermalStability": 0.9644410237669945, "cpuUsage": 0.3947509765625, "responseTime": 472.9744016179758, "overall": 0.5888128051390213}, {"timestamp": 1748402829716, "memoryEfficiency": 0, "thermalStability": 0.9548266294561787, "cpuUsage": 0.43115234375, "responseTime": 224.57111457749482, "overall": 0.5032933883409546}, {"timestamp": 1748402889717, "memoryEfficiency": 0, "thermalStability": 0.9362609369887246, "cpuUsage": 0.4355224609375, "responseTime": 318.0292282087562, "overall": 0.49186613159360887}, {"timestamp": 1748402949720, "memoryEfficiency": 0, "thermalStability": 0.932385610209571, "cpuUsage": 0.4361083984375, "responseTime": 378.1258432394496, "overall": 0.5690336298155865}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}