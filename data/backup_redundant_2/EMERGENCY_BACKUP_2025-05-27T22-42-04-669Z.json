{"timestamp": "2025-05-27T22:42:04.670Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.111242792047883, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.406890526955221, "energy": 1, "focus": 0.906343617962338, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748385709690, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13599990844816, "neuronActivity": 0.5166334128963662}, "emotionalHistory": [{"timestamp": 1748385439634, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385454634, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01505982799452, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385469634, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01505982799452, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385484635, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03011965598904, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385499637, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03011965598904, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385514637, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04517948398356, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385529638, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04517948398356, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385544680, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04517948398356, "neuronActivity": 0.5059827994521742}, {"timestamp": 1748385559678, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0602943807817, "neuronActivity": 0.5114896798140636}, {"timestamp": 1748385574679, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0602943807817, "neuronActivity": 0.5114896798140636}, {"timestamp": 1748385589679, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07540927757984, "neuronActivity": 0.5114896798140636}, {"timestamp": 1748385604680, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07540927757984, "neuronActivity": 0.5128981305182889}, {"timestamp": 1748385619683, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09053825888503, "neuronActivity": 0.5128981305182889}, {"timestamp": 1748385634684, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09053825888503, "neuronActivity": 0.5128981305182889}, {"timestamp": 1748385649715, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10566724019023, "neuronActivity": 0.5128981305182889}, {"timestamp": 1748385664688, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10566724019023, "neuronActivity": 0.5166334128963662}, {"timestamp": 1748385679689, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1208335743192, "neuronActivity": 0.5166334128963662}, {"timestamp": 1748385694690, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1208335743192, "neuronActivity": 0.5166334128963662}, {"timestamp": 1748385709690, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13599990844816, "neuronActivity": 0.5166334128963662}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:40:49 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "6:41:04 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:41:19 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:41:34 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:41:49 PM", "mood": "curious"}], "circadianRhythm": 0.33274323783219883, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.988424388977873, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7495546800062088, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9753398587751642, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9448813752173582, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385426726, "expiresAt": 1748386026726}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9033204300046221, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748385426726, "expiresAt": 1748386026726}, "memory_compression_engine_1748385426727": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8363220480659576, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "memory_cache_optimizer_1748385426727": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8334385767991785, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "memory_garbage_collector_1748385426727": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8885051911449746, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9833336216784121, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748385426737, "expiresAt": 1748386026737}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8873497084277527, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748385429717, "expiresAt": 1748386029717}, "neural_stimulator_auto_23": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9947987649821636, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748385434661, "expiresAt": 1748386034661}, "emergency_memory_optimizer_86": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9817152596493124, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385450928, "expiresAt": 1748385750928}, "emergency_memory_optimizer_87": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9129391375706177, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385450928, "expiresAt": 1748385750928}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.988424388977873, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7495546800062088, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9753398587751642, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9448813752173582, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385426726, "expiresAt": 1748386026726}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9033204300046221, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748385426726, "expiresAt": 1748386026726}, "memory_compression_engine_1748385426727": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.8363220480659576, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "memory_cache_optimizer_1748385426727": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8334385767991785, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "memory_garbage_collector_1748385426727": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8885051911449746, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748385426727, "expiresAt": 1748386326727}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9833336216784121, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748385426737, "expiresAt": 1748386026737}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8873497084277527, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748385429717, "expiresAt": 1748386029717}, "neural_stimulator_auto_23": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9947987649821636, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748385434661, "expiresAt": 1748386034661}, "emergency_memory_optimizer_86": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9817152596493124, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385450928, "expiresAt": 1748385750928}, "emergency_memory_optimizer_87": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9129391375706177, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748385450928, "expiresAt": 1748385750928}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.495796544951923, "efficiency": 0.8497477926971153, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748385724547, "totalBoostPower": 32.445355084375, "averageEnergy": 1, "averageStability": 0.9138402339461228, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748385484559, "memoryEfficiency": 0, "thermalStability": 0.8416752060254415, "cpuUsage": 0.61904296875, "responseTime": 542.6062854780778, "overall": 0.4809776067789439}, {"timestamp": 1748385544665, "memoryEfficiency": 0, "thermalStability": 0.9732964613785346, "cpuUsage": 0.5559814453125, "responseTime": 247.94208372787912, "overall": 0.5535710068000623}, {"timestamp": 1748385604666, "memoryEfficiency": 0, "thermalStability": 0.992096450800697, "cpuUsage": 0.5640869140625, "responseTime": 687.7931056290943, "overall": 0.5203609830969145}, {"timestamp": 1748385664666, "memoryEfficiency": 0, "thermalStability": 0.9901367177565893, "cpuUsage": 0.55087890625, "responseTime": 500.31223070882317, "overall": 0.5670760491422526}, {"timestamp": 1748385724666, "memoryEfficiency": 0, "thermalStability": 0.9868485194527441, "cpuUsage": 0.5125244140625, "responseTime": 372.28235251609846, "overall": 0.5822025834022067}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}