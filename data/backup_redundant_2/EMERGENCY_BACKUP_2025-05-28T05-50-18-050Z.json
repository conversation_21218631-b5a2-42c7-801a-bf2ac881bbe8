{"timestamp": "2025-05-28T05:50:18.050Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 100, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.13233861410003, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7, "maxMemorySize": 50}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.072633485279074, "energy": 1, "focus": 0.9571144424259327, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748411403481, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01543370029134, "neuronActivity": 0.5433700291344817}, "emotionalHistory": [{"timestamp": 1748411133020, "mood": "curious", "dominantEmotion": "energy", "qi": 203, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411148020, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411163021, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411178020, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411193022, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411208022, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411223022, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411238458, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5254580818344695}, {"timestamp": 1748411253458, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01525458081835, "neuronActivity": 0.5339087860598216}, {"timestamp": 1748411268460, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0153390878606, "neuronActivity": 0.5339087860598216}, {"timestamp": 1748411283461, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.0153390878606, "neuronActivity": 0.5339087860598216}, {"timestamp": 1748411298461, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01538134138173, "neuronActivity": 0.5381341381724977}, {"timestamp": 1748411313461, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01538134138173, "neuronActivity": 0.5381341381724977}, {"timestamp": 1748411328461, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01538134138173, "neuronActivity": 0.5381341381724977}, {"timestamp": 1748411343461, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01538134138173, "neuronActivity": 0.5381341381724977}, {"timestamp": 1748411358462, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01541030406702, "neuronActivity": 0.5410304067024405}, {"timestamp": 1748411373462, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01541030406702, "neuronActivity": 0.5433700291344817}, {"timestamp": 1748411388470, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01543370029134, "neuronActivity": 0.5433700291344817}, {"timestamp": 1748411403481, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01543370029134, "neuronActivity": 0.5433700291344817}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:49:03 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:49:18 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:49:33 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:49:48 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:50:03 AM", "mood": "curious"}], "circadianRhythm": 0.9995296158104798, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8919968270697682, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9210244200610993, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8760428777088034, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8161454048577201, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411120092, "expiresAt": 1748411720091}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8350369752982462, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748411120092, "expiresAt": 1748411720092}, "memory_compression_engine_1748411120096": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9709327619354684, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "memory_cache_optimizer_1748411120096": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8622331377031762, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "memory_garbage_collector_1748411120096": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8935706356158063, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "emergency_memory_optimizer_3": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9549249977224421, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411123188, "expiresAt": 1748411423188}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8249947384991069, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411123188, "expiresAt": 1748411423188}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8897308453184627, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748411123203, "expiresAt": 1748411723203}, "response_accelerator_auto_9": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8638267413286499, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748411123344, "expiresAt": 1748411723344}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9070733433845017, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748411125555, "expiresAt": 1748411725555}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8919968270697682, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9210244200610993, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8760428777088034, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8161454048577201, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411120092, "expiresAt": 1748411720091}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8350369752982462, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748411120092, "expiresAt": 1748411720092}, "memory_compression_engine_1748411120096": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9709327619354684, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "memory_cache_optimizer_1748411120096": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8622331377031762, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "memory_garbage_collector_1748411120096": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.8935706356158063, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748411120096, "expiresAt": 1748412020096}, "emergency_memory_optimizer_3": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9549249977224421, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411123188, "expiresAt": 1748411423188}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8249947384991069, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748411123188, "expiresAt": 1748411423188}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8897308453184627, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748411123203, "expiresAt": 1748411723203}, "response_accelerator_auto_9": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8638267413286499, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748411123344, "expiresAt": 1748411723344}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.9070733433845017, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748411125555, "expiresAt": 1748411725555}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.495796544951923, "efficiency": 0.8497477926971153, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748411417833, "totalBoostPower": 32.445355084375, "averageEnergy": 1, "averageStability": 0.8851949005002502, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748411177897, "memoryEfficiency": 0, "thermalStability": 0.8407951732476552, "cpuUsage": 0.5488525390625, "responseTime": 663.9203235983591, "overall": 0.5122174525550786}, {"timestamp": 1748411238452, "memoryEfficiency": 0, "thermalStability": 0.9962862251947323, "cpuUsage": 0.531396484375, "responseTime": 554.4283327883281, "overall": 0.5532114353598306}, {"timestamp": 1748411298453, "memoryEfficiency": 0, "thermalStability": 0.990527518466115, "cpuUsage": 0.5028076171875, "responseTime": 507.89863922348957, "overall": 0.5676611702595532}, {"timestamp": 1748411358453, "memoryEfficiency": 0, "thermalStability": 0.9972426519211796, "cpuUsage": 0.4907958984375, "responseTime": 343.54465566096496, "overall": 0.5474739932310151}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}