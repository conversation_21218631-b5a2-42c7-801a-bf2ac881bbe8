{"id": "basic_training", "name": "Formation de base", "description": "Ensemble de données de formation de base pour les agents", "createdAt": "2023-05-23T12:00:00.000Z", "updatedAt": "2023-05-23T12:00:00.000Z", "samples": 5, "data": [{"id": "sample_1", "input": "Qu'est-ce que la mémoire thermique ?", "expectedOutput": "La mémoire thermique est un système de gestion de la mémoire inspiré du cerveau humain. Elle organise les informations en différentes zones selon leur importance et leur fraîcheur, permettant une circulation fluide des données entre ces zones. Les informations importantes ou récemment utilisées sont maintenues dans les zones chaudes, tandis que les informations moins importantes ou plus anciennes sont progressivement déplacées vers les zones froides. Ce système permet une récupération efficace des informations pertinentes tout en maintenant une grande quantité de connaissances."}, {"id": "sample_2", "input": "Comment fonctionnent les accélérateurs Kyber ?", "expectedOutput": "Les accélérateurs Kyber sont des composants qui optimisent les performances de la mémoire thermique. Ils agissent sur différents aspects du traitement de l'information : l'accélérateur réflexif améliore la capacité d'introspection, l'accélérateur thermique optimise la circulation entre les zones de mémoire, et l'accélérateur connecteur renforce les liens entre les informations connexes. Chaque accélérateur peut être activé ou désactivé, et son facteur de boost peut être ajusté pour obtenir les performances souhaitées. Ensemble, ils permettent d'améliorer significativement l'efficacité du système de mémoire."}, {"id": "sample_3", "input": "Explique le processus de génération de rêves.", "expectedOutput": "La génération de rêves est un processus qui permet de créer de nouvelles connexions entre les informations stockées dans la mémoire thermique. Pendant ce processus, le système sélectionne aléatoirement des entrées de mémoire et les combine de manière créative pour générer de nouvelles idées ou perspectives. Ces nouvelles connexions sont ensuite évaluées et, si elles sont jugées pertinentes, peuvent être ajoutées à la mémoire. Ce processus s'inspire du rôle des rêves dans le cerveau humain, où ils contribuent à la consolidation de la mémoire et à la résolution de problèmes."}, {"id": "sample_4", "input": "Quelle est la différence entre l'agent principal et l'agent de formation ?", "expectedOutput": "L'agent principal (<PERSON>, 4 Go) est l'agent principal du système, responsable de la plupart des interactions avec l'utilisateur et des tâches complexes. Il possède une grande capacité de raisonnement et de génération de contenu. L'agent de formation (Llama 3, 1-2 Go) est spécialisé dans l'apprentissage et l'amélioration de la mémoire thermique. Il est utilisé pour former l'agent principal en générant des réponses qui sont ensuite évaluées et intégrées à la mémoire. L'agent principal bénéficie des connaissances accumulées par l'agent de formation, créant ainsi un système d'apprentissage continu."}, {"id": "sample_5", "input": "Comment la mémoire thermique et les accélérateurs Kyber travaillent-ils ensemble ?", "expectedOutput": "La mémoire thermique et les accélérateurs Kyber forment un système intégré où chaque composant renforce l'autre. La mémoire thermique organise les informations en zones selon leur importance et leur fraîcheur, tandis que les accélérateurs Kyber optimisent les processus de cette mémoire. Par exemple, l'accélérateur thermique améliore la circulation des informations entre les zones, l'accélérateur réflexif renforce la capacité d'introspection, et l'accélérateur connecteur renforce les liens entre les informations connexes. Cette synergie permet d'obtenir un système de mémoire plus efficace, capable de récupérer rapidement les informations pertinentes tout en maintenant une grande quantité de connaissances."}]}