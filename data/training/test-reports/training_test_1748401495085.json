{"timestamp": "2025-05-28T03:04:55.085Z", "duration": 38, "totalTests": 23, "successfulTests": 2, "results": [{"testName": "ENDPOINT_/api/training/state", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.070Z", "duration": 23}, {"testName": "ENDPOINT_/api/training/agents", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.071Z", "duration": 24}, {"testName": "ENDPOINT_/api/training/datasets", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.072Z", "duration": 25}, {"testName": "ENDPOINT_/api/training/history", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.073Z", "duration": 26}, {"testName": "ENDPOINT_/api/agents/list", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.074Z", "duration": 27}, {"testName": "ENDPOINT_/api/features/status", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.074Z", "duration": 27}, {"testName": "TRAINING_STATE", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.075Z", "duration": 28}, {"testName": "GET_AGENTS", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.076Z", "duration": 29}, {"testName": "GET_DATASETS", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.077Z", "duration": 30}, {"testName": "GET_HISTORY", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.078Z", "duration": 31}, {"testName": "MAIN_AGENT", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.079Z", "duration": 32}, {"testName": "DEEPSEEK_AVAILABILITY", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.079Z", "duration": 32}, {"testName": "AGENT_COMMUNICATION", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.080Z", "duration": 33}, {"testName": "CREATE_TEST_DATASET", "success": true, "data": {"id": "test_dataset_1748401495080", "name": "Dataset de Test Formation", "description": "Dataset créé automatiquement pour tester le système de formation", "data": [{"input": "Quelle est la capitale de la France ?", "expectedOutput": "La capitale de la France est Paris."}, {"input": "Comment calculer 2 + 2 ?", "expectedOutput": "2 + 2 = 4"}, {"input": "Qu'est-ce que l'intelligence artificielle ?", "expectedOutput": "L'intelligence artificielle est une technologie qui permet aux machines de simuler l'intelligence humaine."}], "createdAt": "2025-05-28T03:04:55.080Z"}, "timestamp": "2025-05-28T03:04:55.080Z", "duration": 33}, {"testName": "VALIDATE_TEST_DATASET", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.080Z", "duration": 33}, {"testName": "START_TRAINING", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.081Z", "duration": 34}, {"testName": "TRAINING_MONITORING", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.081Z", "duration": 34}, {"testName": "STOP_TRAINING", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.082Z", "duration": 35}, {"testName": "DEEPSEEK_ACTIVATION", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.082Z", "duration": 35}, {"testName": "DEEPSEEK_TRAINING", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.082Z", "duration": 35}, {"testName": "DEEPSEEK_EVALUATION", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.083Z", "duration": 36}, {"testName": "FULL_INTEGRATION", "success": false, "data": "", "timestamp": "2025-05-28T03:04:55.083Z", "duration": 36}, {"testName": "PERFORMANCE", "success": true, "data": {"successfulRequests": 0, "totalRequests": 5, "duration": 2, "avgResponseTime": 0.4}, "timestamp": "2025-05-28T03:04:55.085Z", "duration": 38}]}