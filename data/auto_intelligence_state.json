{"timestamp": "2025-05-28T02:26:31.548Z", "performanceHistory": [{"timestamp": 1748398951536, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.403466796875, "responseTime": 541.6050607729962, "overall": 0}, {"timestamp": 1748399011668, "memoryEfficiency": 0, "thermalStability": 0.925657096836302, "cpuUsage": 0.4134521484375, "responseTime": 346.5003166696416, "overall": 0.4884997966627673}, {"timestamp": 1748399071669, "memoryEfficiency": 0, "thermalStability": 0.926924458992334, "cpuUsage": 0.435791015625, "responseTime": 349.1488471667084, "overall": 0.5212758370839592}, {"timestamp": 1748399131669, "memoryEfficiency": 0, "thermalStability": 0.9242021609097719, "cpuUsage": 0.4749755859375, "responseTime": 300.7773072590377, "overall": 0.571010368776821}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}