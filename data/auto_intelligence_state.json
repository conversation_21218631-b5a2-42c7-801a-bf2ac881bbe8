{"timestamp": "2025-05-28T06:03:14.759Z", "performanceHistory": [{"timestamp": 1748411954761, "memoryEfficiency": 0, "thermalStability": 0.9414799728327328, "cpuUsage": 0.4620849609375, "responseTime": 409.9764569756568, "overall": 0.5679970734814569}, {"timestamp": 1748412015255, "memoryEfficiency": 0, "thermalStability": 0.9948693549053537, "cpuUsage": 0.4399169921875, "responseTime": 364.72951122756126, "overall": 0.576454601056622}, {"timestamp": 1748412075256, "memoryEfficiency": 0, "thermalStability": 0.9975428013751905, "cpuUsage": 0.4700927734375, "responseTime": 676.8907886247822, "overall": 0.5451138348219224}, {"timestamp": 1748412135715, "memoryEfficiency": 0, "thermalStability": 0.9964119743969705, "cpuUsage": 0.481689453125, "responseTime": 303.54023514194745, "overall": 0.5561643822974307}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}