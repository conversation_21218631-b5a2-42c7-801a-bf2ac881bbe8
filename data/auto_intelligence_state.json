{"timestamp": "2025-05-28T02:20:46.378Z", "performanceHistory": [{"timestamp": 1748398606379, "memoryEfficiency": 0, "thermalStability": 0.8829542305734422, "cpuUsage": 0.49501953125, "responseTime": 268.77647440440126, "overall": 0.5325777517276962}, {"timestamp": 1748398666381, "memoryEfficiency": 0, "thermalStability": 0.9973641377770238, "cpuUsage": 0.4215576171875, "responseTime": 486.70467520272626, "overall": 0.5196956247580579}, {"timestamp": 1748398726382, "memoryEfficiency": 0, "thermalStability": 0.9655438739806413, "cpuUsage": 0.4142578125, "responseTime": 523.388197576221, "overall": 0.5535041569949212}, {"timestamp": 1748398786466, "memoryEfficiency": 0, "thermalStability": 0.9989910650998354, "cpuUsage": 0.416455078125, "responseTime": 583.7990165612189, "overall": 0.5124581616769359}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}