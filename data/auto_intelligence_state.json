{"timestamp": "2025-05-28T05:05:45.956Z", "performanceHistory": [{"timestamp": 1748408505957, "memoryEfficiency": 0, "thermalStability": 0.8863463883598646, "cpuUsage": 0.42685546875, "responseTime": 273.468591854802, "overall": 0.5642547526804871}, {"timestamp": 1748408566364, "memoryEfficiency": 0, "thermalStability": 0.9938081694973839, "cpuUsage": 0.45380859375, "responseTime": 200.69268423248303, "overall": 0.5579473359978502}, {"timestamp": 1748408626365, "memoryEfficiency": 0, "thermalStability": 0.9220690812087722, "cpuUsage": 0.5955322265625, "responseTime": 228.1092935933094, "overall": 0.5189798423906551}, {"timestamp": 1748408686365, "memoryEfficiency": 0, "thermalStability": 0.9966132437603341, "cpuUsage": 0.5494384765625, "responseTime": 499.620888218049, "overall": 0.5507984568478653}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}