{"timestamp": "2025-05-28T04:05:01.288Z", "performanceHistory": [{"timestamp": 1748404861288, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.403662109375, "responseTime": 311.76646350088606, "overall": 0}, {"timestamp": 1748404921560, "memoryEfficiency": 0, "thermalStability": 0.9253761602772607, "cpuUsage": 0.46357421875, "responseTime": 425.5092294155726, "overall": 0.5407618863782985}, {"timestamp": 1748404981561, "memoryEfficiency": 0, "thermalStability": 0.8731733987360825, "cpuUsage": 0.433642578125, "responseTime": 522.2930953586942, "overall": 0.48911139690447086}, {"timestamp": 1748405042454, "memoryEfficiency": 0, "thermalStability": 0.8945366508430905, "cpuUsage": 0.423486328125, "responseTime": 329.8675996839065, "overall": 0.4841998214964437}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}