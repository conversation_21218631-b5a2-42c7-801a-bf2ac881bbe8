{"timestamp": "2025-05-28T03:19:00.502Z", "performanceHistory": [{"timestamp": 1748401742154, "memoryEfficiency": 0, "thermalStability": 0.9985198771374093, "cpuUsage": 0.448193359375, "responseTime": 509.1027402565608, "overall": 0.5753898432139627}, {"timestamp": 1748401802155, "memoryEfficiency": 0, "thermalStability": 0.9858198045442502, "cpuUsage": 0.4493408203125, "responseTime": 690.6394212134444, "overall": 0.5542989039626561}, {"timestamp": 1748401862156, "memoryEfficiency": 0, "thermalStability": 0.9863818859474527, "cpuUsage": 0.46484375, "responseTime": 614.158530034563, "overall": 0.5321218563900197}, {"timestamp": 1748401922223, "memoryEfficiency": 0, "thermalStability": 0.9883195211489996, "cpuUsage": 0.4547119140625, "responseTime": 297.8784077832017, "overall": 0.5471408142161086}, {"timestamp": 1748401982413, "memoryEfficiency": 0, "thermalStability": 0.9925803521027168, "cpuUsage": 0.4564697265625, "responseTime": 630.495573061081, "overall": 0.5357815520160953}, {"timestamp": 1748402042902, "memoryEfficiency": 0, "thermalStability": 0.9821132618519995, "cpuUsage": 0.429443359375, "responseTime": 470.00722034346325, "overall": 0.5697675881555256}, {"timestamp": 1748402104527, "memoryEfficiency": 0, "thermalStability": 0.9865250672317214, "cpuUsage": 0.4086181640625, "responseTime": 459.4412307803685, "overall": 0.5126682608944231}, {"timestamp": 1748402166243, "memoryEfficiency": 0, "thermalStability": 0.9416597371300062, "cpuUsage": 0.528759765625, "responseTime": 671.536286342013, "overall": 0.5121111277425408}, {"timestamp": 1748402227547, "memoryEfficiency": 0, "thermalStability": 0.9786015469166968, "cpuUsage": 0.5168701171875, "responseTime": 659.7555634048417, "overall": 0.565150860972176}, {"timestamp": 1748402288420, "memoryEfficiency": 0, "thermalStability": 0.9822349441134267, "cpuUsage": 0.5200439453125, "responseTime": 485.8433056163082, "overall": 0.5873850293086592}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}