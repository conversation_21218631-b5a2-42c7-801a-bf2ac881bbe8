{"timestamp": "2025-05-28T04:45:31.064Z", "performanceHistory": [{"timestamp": 1748406933153, "memoryEfficiency": 0, "thermalStability": 0.9448501668042607, "cpuUsage": 0.4709228515625, "responseTime": 464.1048093926099, "overall": 0.5523427677872722}, {"timestamp": 1748406994106, "memoryEfficiency": 0, "thermalStability": 0.9785114999032682, "cpuUsage": 0.5099609375, "responseTime": 368.34348299862324, "overall": 0.5718601065636426}, {"timestamp": 1748407054347, "memoryEfficiency": 0, "thermalStability": 0.9938495099544525, "cpuUsage": 0.5389892578125, "responseTime": 592.3085015010213, "overall": 0.56307205906704}, {"timestamp": 1748407114350, "memoryEfficiency": 0, "thermalStability": 0.9706783511986335, "cpuUsage": 0.5783203125, "responseTime": 389.73464825375487, "overall": 0.5338719964158916}, {"timestamp": 1748407174351, "memoryEfficiency": 0, "thermalStability": 0.9715897624691328, "cpuUsage": 0.5171142578125, "responseTime": 424.0986560894308, "overall": 0.5159787685287421}, {"timestamp": 1748407234693, "memoryEfficiency": 0, "thermalStability": 0.989974224070708, "cpuUsage": 0.5367431640625, "responseTime": 499.49712269799835, "overall": 0.5527740977919133}, {"timestamp": 1748407294995, "memoryEfficiency": 0, "thermalStability": 0.99707619862424, "cpuUsage": 0.696240234375, "responseTime": 628.5985569863677, "overall": 0.5701821250157193}, {"timestamp": 1748407355238, "memoryEfficiency": 0.18734402852049903, "thermalStability": 0.8675979781481955, "cpuUsage": 0.5540771484375, "responseTime": 341.06597287737543, "overall": 0.5817515130162563}, {"timestamp": 1748407415354, "memoryEfficiency": 0.00833937635968085, "thermalStability": 0.9477908259050714, "cpuUsage": 0.5330322265625, "responseTime": 287.56356064405514, "overall": 0.5685222354124437}, {"timestamp": 1748407475782, "memoryEfficiency": 0.07449856733524363, "thermalStability": 0.9725170726991362, "cpuUsage": 0.5831787109375, "responseTime": 560.9143125217909, "overall": 0.5439675775662312}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}