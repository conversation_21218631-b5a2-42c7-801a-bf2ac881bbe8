{"timestamp": "2025-05-28T02:43:41.683Z", "performanceHistory": [{"timestamp": 1748399981685, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.4686767578125, "responseTime": 664.1846826368851, "overall": 0}, {"timestamp": 1748400041687, "memoryEfficiency": 0, "thermalStability": 0.8024177178740501, "cpuUsage": 0.4267822265625, "responseTime": 490.3856620386286, "overall": 0.46508924375991123}, {"timestamp": 1748400101688, "memoryEfficiency": 0, "thermalStability": 0.8571582304106818, "cpuUsage": 0.393603515625, "responseTime": 491.8217456382306, "overall": 0.49481352174478055}, {"timestamp": 1748400161694, "memoryEfficiency": 0, "thermalStability": 0.8927444964647293, "cpuUsage": 0.359423828125, "responseTime": 628.2045321872855, "overall": 0.5314131514609188}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}