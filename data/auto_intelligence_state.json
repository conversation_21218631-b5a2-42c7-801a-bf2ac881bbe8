{"timestamp": "2025-05-28T03:29:09.712Z", "performanceHistory": [{"timestamp": 1748402709809, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.3715576171875, "responseTime": 492.2449566065964, "overall": 0}, {"timestamp": 1748402769811, "memoryEfficiency": 0, "thermalStability": 0.9644410237669945, "cpuUsage": 0.3947509765625, "responseTime": 472.9744016179758, "overall": 0.5888128051390213}, {"timestamp": 1748402829716, "memoryEfficiency": 0, "thermalStability": 0.9548266294561787, "cpuUsage": 0.43115234375, "responseTime": 224.57111457749482, "overall": 0.5032933883409546}, {"timestamp": 1748402889717, "memoryEfficiency": 0, "thermalStability": 0.9362609369887246, "cpuUsage": 0.4355224609375, "responseTime": 318.0292282087562, "overall": 0.49186613159360887}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}