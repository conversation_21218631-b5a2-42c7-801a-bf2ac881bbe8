{"timestamp": "2025-05-28T05:50:17.895Z", "performanceHistory": [{"timestamp": 1748411177897, "memoryEfficiency": 0, "thermalStability": 0.8407951732476552, "cpuUsage": 0.5488525390625, "responseTime": 663.9203235983591, "overall": 0.5122174525550786}, {"timestamp": 1748411238452, "memoryEfficiency": 0, "thermalStability": 0.9962862251947323, "cpuUsage": 0.531396484375, "responseTime": 554.4283327883281, "overall": 0.5532114353598306}, {"timestamp": 1748411298453, "memoryEfficiency": 0, "thermalStability": 0.990527518466115, "cpuUsage": 0.5028076171875, "responseTime": 507.89863922348957, "overall": 0.5676611702595532}, {"timestamp": 1748411358453, "memoryEfficiency": 0, "thermalStability": 0.9972426519211796, "cpuUsage": 0.4907958984375, "responseTime": 343.54465566096496, "overall": 0.5474739932310151}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}