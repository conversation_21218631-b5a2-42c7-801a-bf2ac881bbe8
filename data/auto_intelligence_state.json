{"timestamp": "2025-05-27T23:21:07.301Z", "performanceHistory": [{"timestamp": 1748387527298, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.8314453125, "responseTime": 480.33690230299766, "overall": 0}, {"timestamp": 1748387587300, "memoryEfficiency": 0, "thermalStability": 0.5580991125723223, "cpuUsage": 0.936181640625, "responseTime": 498.72707703374004, "overall": 0.37833527440888937}, {"timestamp": 1748387647301, "memoryEfficiency": 0, "thermalStability": 0.9989560335874558, "cpuUsage": 1.082470703125, "responseTime": 660.8837731271333, "overall": 0.5826552265612827}, {"timestamp": 1748387707302, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 1.121337890625, "responseTime": 223.52559289836486, "overall": 0.5651681604901633}, {"timestamp": 1748387767303, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 0.6793212890625, "responseTime": 660.2023882737865, "overall": 0.5148472708946108}, {"timestamp": 1748387827305, "memoryEfficiency": 0, "thermalStability": 0.8476873074968656, "cpuUsage": 0.4776611328125, "responseTime": 428.1837832827648, "overall": 0.5442535121604428}, {"timestamp": 1748387887305, "memoryEfficiency": 0, "thermalStability": 0.6646117891288464, "cpuUsage": 0.408203125, "responseTime": 508.6737386644658, "overall": 0.4803173841074905}, {"timestamp": 1748387947308, "memoryEfficiency": 0.04130019120458894, "thermalStability": 0.43520430247816777, "cpuUsage": 0.3993896484375, "responseTime": 528.6064888810603, "overall": 0.40402029801659944}, {"timestamp": 1748388007309, "memoryEfficiency": 0.13260473588342425, "thermalStability": 0.23005831821097278, "cpuUsage": 0.3807861328125, "responseTime": 332.7671526524815, "overall": 0.4184618525094862}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}