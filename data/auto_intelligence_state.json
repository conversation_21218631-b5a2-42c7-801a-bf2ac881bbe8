{"timestamp": "2025-05-28T05:29:21.962Z", "performanceHistory": [{"timestamp": 1748409921963, "memoryEfficiency": 0, "thermalStability": 0.8970570074187385, "cpuUsage": 0.47548828125, "responseTime": 647.0616629870804, "overall": 0.4961118888009902}, {"timestamp": 1748409982518, "memoryEfficiency": 0, "thermalStability": 0.993707583554917, "cpuUsage": 0.70107421875, "responseTime": 617.82635883846, "overall": 0.5696116415036709}, {"timestamp": 1748410042518, "memoryEfficiency": 0, "thermalStability": 0.8641401084346904, "cpuUsage": 0.4923828125, "responseTime": 241.1734337192452, "overall": 0.5260279738135483}, {"timestamp": 1748410102520, "memoryEfficiency": 0, "thermalStability": 0.8721655420545074, "cpuUsage": 0.5096435546875, "responseTime": 439.77272573785245, "overall": 0.5153731021530712}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}