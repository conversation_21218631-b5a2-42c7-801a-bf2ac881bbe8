{"timestamp": "2025-05-28T05:43:49.724Z", "performanceHistory": [{"timestamp": 1748410789727, "memoryEfficiency": 0, "thermalStability": 0.971043240899841, "cpuUsage": 0.46669921875, "responseTime": 519.4414503739324, "overall": 0.5665394698273756}, {"timestamp": 1748410850135, "memoryEfficiency": 0, "thermalStability": 0.9955811048961348, "cpuUsage": 0.478271484375, "responseTime": 252.89195062902786, "overall": 0.5127969832115752}, {"timestamp": 1748410910136, "memoryEfficiency": 0, "thermalStability": 0.9959521709630886, "cpuUsage": 0.5048583984375, "responseTime": 404.8106126379107, "overall": 0.5585040122104042}, {"timestamp": 1748410970259, "memoryEfficiency": 0, "thermalStability": 0.9881277814507484, "cpuUsage": 0.5107421875, "responseTime": 212.7820244433844, "overall": 0.5916645251535925}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}