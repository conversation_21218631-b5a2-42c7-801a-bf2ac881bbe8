{"timestamp": "2025-05-27T23:04:33.715Z", "performanceHistory": [{"timestamp": 1748386533709, "memoryEfficiency": 0, "thermalStability": 0.9467075440618727, "cpuUsage": 0.453173828125, "responseTime": 383.81401315084054, "overall": 0.5730819763864375}, {"timestamp": 1748386593719, "memoryEfficiency": 0, "thermalStability": 0.9985499405198627, "cpuUsage": 0.4138916015625, "responseTime": 467.9054897311932, "overall": 0.5386825498127343}, {"timestamp": 1748386653719, "memoryEfficiency": 0, "thermalStability": 0.9953356590535906, "cpuUsage": 0.409375, "responseTime": 639.4282247381846, "overall": 0.5233978979013905}, {"timestamp": 1748386713721, "memoryEfficiency": 0, "thermalStability": 0.9910762175917626, "cpuUsage": 0.4203369140625, "responseTime": 298.47189855851536, "overall": 0.5299768597432113}, {"timestamp": 1748386773722, "memoryEfficiency": 0, "thermalStability": 0.9896249077386327, "cpuUsage": 0.4096923828125, "responseTime": 331.1766418177012, "overall": 0.5397517569482395}, {"timestamp": 1748386833724, "memoryEfficiency": 0, "thermalStability": 0.9931624225858185, "cpuUsage": 0.400537109375, "responseTime": 480.18919295917345, "overall": 0.5807450498553253}, {"timestamp": 1748386893726, "memoryEfficiency": 0, "thermalStability": 0.9941525452666813, "cpuUsage": 0.424072265625, "responseTime": 667.7193879504995, "overall": 0.5183106695084057}, {"timestamp": 1748386953726, "memoryEfficiency": 0, "thermalStability": 0.9949151921603415, "cpuUsage": 0.7105712890625, "responseTime": 241.70237020287692, "overall": 0.5152105776291802}, {"timestamp": 1748387013727, "memoryEfficiency": 0, "thermalStability": 0.8758210591557953, "cpuUsage": 0.901953125, "responseTime": 574.9408407883823, "overall": 0.5077223466795656}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}