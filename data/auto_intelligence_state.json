{"timestamp": "2025-05-28T01:33:50.429Z", "performanceHistory": [{"timestamp": 1748395790430, "memoryEfficiency": 0, "thermalStability": 0.9726729485723707, "cpuUsage": 0.43388671875, "responseTime": 665.9849850072189, "overall": 0.5258603509516615}, {"timestamp": 1748395850515, "memoryEfficiency": 0, "thermalStability": 0.9928798062933816, "cpuUsage": 0.3777099609375, "responseTime": 347.93284669725426, "overall": 0.5423782590214783}, {"timestamp": 1748395910516, "memoryEfficiency": 0, "thermalStability": 0.9941636349178022, "cpuUsage": 0.4384033203125, "responseTime": 393.30599490475254, "overall": 0.58293961823548}, {"timestamp": 1748395970518, "memoryEfficiency": 0, "thermalStability": 0.9586231574416161, "cpuUsage": 0.4938720703125, "responseTime": 503.34895385176696, "overall": 0.5742168083859811}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}