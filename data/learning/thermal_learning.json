{"temperatureHistory": [{"timestamp": 1748399263401, "temperatures": {"cpu": 0, "gpu": 0, "memory": 0, "battery": 0, "average": 0, "timestamp": 1748399263400, "normalized": 0}, "normalizedTemperature": 0}, {"timestamp": 1748399324035, "temperatures": {"cpu": 40.44921875, "gpu": 44.49414062500001, "memory": 32.359375, "battery": 28.314453124999996, "average": 36.404296875, "timestamp": 1748399323416, "normalized": 0.17415364583333334}, "normalizedTemperature": 0.17415364583333334}], "memoryStats": [{"timestamp": 1748399263401, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "<PERSON><PERSON>", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 0, "gpu": 0, "memory": 0, "average": 0, "normalized": 0}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748399324035, "stats": {"totalMemories": 11, "zone1Count": 11, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.9644444444444444, "cyclesPerformed": 1, "lastCycleTime": "5/27/2025, 10:27:43 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 40.44921875, "gpu": 44.49414062500001, "memory": 32.359375, "average": 36.404296875, "normalized": 0.17415364583333334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}], "parameterHistory": [{"timestamp": 1748399324036, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.9241762691073947, "accessOptimization": 0.9338132599211834, "overallPerformance": 0.5573968587085734}}], "optimizationHistory": [{"timestamp": 1748399324036, "before": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "after": {"temperatureCursorSensitivity": 0.10049999999999999, "memoryDecayRate": 0.96, "importanceFactor": 1.1683260220317604, "accessFactor": 1.05, "decayFactor": 0.9841569274372525}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.9241762691073947, "accessOptimization": 0.9338132599211834, "overallPerformance": 0.5573968587085734}}], "insights": {}, "lastOptimization": 1748399324036, "cyclesSinceOptimization": 0, "version": 1}