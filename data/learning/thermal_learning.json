{"temperatureHistory": [{"timestamp": 1748399921682, "temperatures": {"cpu": 0, "gpu": 0, "memory": 0, "battery": 0, "average": 0, "timestamp": 1748399921681, "normalized": 0}, "normalizedTemperature": 0}, {"timestamp": 1748399981685, "temperatures": {"cpu": 46.86767578125, "gpu": 51.55444335937501, "memory": 37.494140625, "battery": 32.807373046875, "average": 42.180908203125, "timestamp": 1748399976692, "normalized": 0.2811279296875}, "normalizedTemperature": 0.2811279296875}], "memoryStats": [{"timestamp": 1748399921682, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "<PERSON><PERSON>", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 0, "gpu": 0, "memory": 0, "average": 0, "normalized": 0}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748399981685, "stats": {"totalMemories": 10, "zone1Count": 10, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.9644444444444444, "cyclesPerformed": 1, "lastCycleTime": "5/27/2025, 10:38:41 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 46.86767578125, "gpu": 51.55444335937501, "memory": 37.494140625, "average": 42.180908203125, "normalized": 0.2811279296875}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}], "parameterHistory": [{"timestamp": 1748399981685, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.8024177178740501, "accessOptimization": 0.7478797613256541, "overallPerformance": 0.46508924375991123}}], "optimizationHistory": [{"timestamp": 1748399981685, "before": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "after": {"temperatureCursorSensitivity": 0.10049999999999999, "memoryDecayRate": 0.96, "importanceFactor": 1.2140554954423008, "accessFactor": 1.05, "decayFactor": 0.9829636936351657}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.8024177178740501, "accessOptimization": 0.7478797613256541, "overallPerformance": 0.46508924375991123}}], "insights": {}, "lastOptimization": 1748399981685, "cyclesSinceOptimization": 0, "version": 1}