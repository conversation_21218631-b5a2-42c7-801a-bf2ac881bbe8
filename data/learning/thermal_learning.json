{"temperatureHistory": [{"timestamp": 1748398891505, "temperatures": {"cpu": 0, "gpu": 0, "memory": 0, "battery": 0, "average": 0, "timestamp": 1748398891502, "normalized": 0}, "normalizedTemperature": 0}, {"timestamp": 1748398951536, "temperatures": {"cpu": 40.3466796875, "gpu": 44.38134765625001, "memory": 32.27734375, "battery": 28.242675781249996, "average": 36.31201171875, "timestamp": 1748398951531, "normalized": 0.17244466145833334}, "normalizedTemperature": 0.17244466145833334}], "memoryStats": [{"timestamp": 1748398891505, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "<PERSON><PERSON>", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 0, "gpu": 0, "memory": 0, "average": 0, "normalized": 0}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748398951536, "stats": {"totalMemories": 10, "zone1Count": 10, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.968, "cyclesPerformed": 1, "lastCycleTime": "5/27/2025, 10:21:31 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 40.3466796875, "gpu": 44.38134765625001, "memory": 32.27734375, "average": 36.31201171875, "normalized": 0.17244466145833334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}], "parameterHistory": [{"timestamp": 1748398951537, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.925657096836302, "accessOptimization": 0.7026755587062556, "overallPerformance": 0.4884997966627673}}], "optimizationHistory": [{"timestamp": 1748398951537, "before": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "after": {"temperatureCursorSensitivity": 0.10049999999999999, "memoryDecayRate": 0.96, "importanceFactor": 1.176159437385471, "accessFactor": 1.05, "decayFactor": 0.9841714395489958}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.925657096836302, "accessOptimization": 0.7026755587062556, "overallPerformance": 0.4884997966627673}}], "insights": {}, "lastOptimization": 1748398951537, "cyclesSinceOptimization": 0, "version": 1}