{"temperatureHistory": [{"timestamp": 1748400581536, "temperatures": {"cpu": 0, "gpu": 0, "memory": 0, "battery": 0, "average": 0, "timestamp": 1748400581534, "normalized": 0}, "normalizedTemperature": 0}, {"timestamp": 1748400641539, "temperatures": {"cpu": 39.39453125, "gpu": 43.333984375, "memory": 31.515625, "battery": 27.576171875, "average": 35.455078125, "timestamp": 1748400636549, "normalized": 0.15657552083333334}, "normalizedTemperature": 0.15657552083333334}], "memoryStats": [{"timestamp": 1748400581536, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "<PERSON><PERSON>", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 0, "gpu": 0, "memory": 0, "average": 0, "normalized": 0}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748400641539, "stats": {"totalMemories": 10, "zone1Count": 10, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0.9644444444444444, "cyclesPerformed": 1, "lastCycleTime": "5/27/2025, 10:49:41 PM", "temperatureCursor": 0.3, "temperatureThresholds": {"instant": 0.6, "shortTerm": 0.44999999999999996, "working": 0.3, "mediumTerm": 0.15, "longTerm": 0.05}, "systemTemperatures": {"cpu": 39.39453125, "gpu": 43.333984375, "memory": 31.515625, "average": 35.455078125, "normalized": 0.15657552083333334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}], "parameterHistory": [{"timestamp": 1748400641540, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.938710265689426, "accessOptimization": 0.9942768446308572, "overallPerformance": 0.5798961330960849}}], "optimizationHistory": [{"timestamp": 1748400641540, "before": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}, "after": {"temperatureCursorSensitivity": 0.10049999999999999, "memoryDecayRate": 0.96, "importanceFactor": 1.2475008505038412, "accessFactor": 1.05, "decayFactor": 0.9842993606037563}, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0.938710265689426, "accessOptimization": 0.9942768446308572, "overallPerformance": 0.5798961330960849}}], "insights": {}, "lastOptimization": 1748400641540, "cyclesSinceOptimization": 0, "version": 1}