{"timestamp": "2025-05-28T04:05:01.560Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.233009376707928, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10201505006249996, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.406870642684561, "energy": 1, "focus": 0.9760422224534723, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748405087460, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13788762394873, "neuronActivity": 0.5722823925951155}, "emotionalHistory": [{"timestamp": 1748404816506, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404831507, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404846507, "mood": "curious", "dominantEmotion": "energy", "qi": 148.0151215224101, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404861507, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024304482022, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404876507, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.03024304482022, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404891508, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04536456723034, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404906508, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04536456723034, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404921561, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04536456723034, "neuronActivity": 0.5121522410109471}, {"timestamp": 1748404936562, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.06048608964045, "neuronActivity": 0.5206029452362992}, {"timestamp": 1748404951562, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07569211909282, "neuronActivity": 0.5206029452362992}, {"timestamp": 1748404966563, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07569211909282, "neuronActivity": 0.5206029452362992}, {"timestamp": 1748404981563, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09115067331578, "neuronActivity": 0.5458554222967114}, {"timestamp": 1748404996564, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09115067331578, "neuronActivity": 0.5458554222967114}, {"timestamp": 1748405011565, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10660922753874, "neuronActivity": 0.5458554222967114}, {"timestamp": 1748405026566, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10660922753874, "neuronActivity": 0.5458554222967114}, {"timestamp": 1748405042458, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12216480002277, "neuronActivity": 0.5640079526296677}, {"timestamp": 1748405057459, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12216480002277, "neuronActivity": 0.5640079526296677}, {"timestamp": 1748405072459, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13788762394873, "neuronActivity": 0.5722823925951155}, {"timestamp": 1748405087460, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13788762394873, "neuronActivity": 0.5722823925951155}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:03:46 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:04:02 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:04:17 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:04:32 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "12:04:47 AM", "mood": "curious"}], "circadianRhythm": 0.9381438804728193, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9551191258772987, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.9341988017049285, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9382552939276957, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9303922175204226, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404803825, "expiresAt": 1748405403825}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8487631951831394, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748404803825, "expiresAt": 1748405403825}, "memory_compression_engine_1748404803826": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.1290124999999995, "stability": 0.8768797860007717, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "memory_cache_optimizer_1748404803826": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4774075000000004, "stability": 0.8632570281755497, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "memory_garbage_collector_1748404803826": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.31450625, "stability": 0.914470761272443, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.9274784350490897, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748404803837, "expiresAt": 1748405403837}, "response_accelerator_auto_6": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.8568849680884855, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748404803926, "expiresAt": 1748405403926}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.9877329364140257, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748404805448, "expiresAt": 1748405405448}, "emergency_memory_optimizer_69": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.9418614753218748, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404822855, "expiresAt": 1748405122855}, "emergency_memory_optimizer_70": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.8907826334622828, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404822855, "expiresAt": 1748405122855}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9551191258772987, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.9341988017049285, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9382552939276957, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9303922175204226, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404803825, "expiresAt": 1748405403825}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8487631951831394, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748404803825, "expiresAt": 1748405403825}, "memory_compression_engine_1748404803826": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.1290124999999995, "stability": 0.8768797860007717, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "memory_cache_optimizer_1748404803826": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4774075000000004, "stability": 0.8632570281755497, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "memory_garbage_collector_1748404803826": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.31450625, "stability": 0.914470761272443, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748404803826, "expiresAt": 1748405703826}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.9274784350490897, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748404803837, "expiresAt": 1748405403837}, "response_accelerator_auto_6": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.721759375, "stability": 0.8568849680884855, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748404803926, "expiresAt": 1748405403926}, "neural_stimulator_auto_11": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.070154375, "stability": 0.9877329364140257, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748404805448, "expiresAt": 1748405405448}, "emergency_memory_optimizer_69": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.9418614753218748, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404822855, "expiresAt": 1748405122855}, "emergency_memory_optimizer_70": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.536265625, "stability": 0.8907826334622828, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404822855, "expiresAt": 1748405122855}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.5482068894230774, "efficiency": 0.8528924133653846, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748405042454, "totalBoostPower": 33.126689562500005, "averageEnergy": 1, "averageStability": 0.9127751275383083, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748404861288, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.403662109375, "responseTime": 311.76646350088606, "overall": 0}, {"timestamp": 1748404921560, "memoryEfficiency": 0, "thermalStability": 0.9253761602772607, "cpuUsage": 0.46357421875, "responseTime": 425.5092294155726, "overall": 0.5407618863782985}, {"timestamp": 1748404981561, "memoryEfficiency": 0, "thermalStability": 0.8731733987360825, "cpuUsage": 0.433642578125, "responseTime": 522.2930953586942, "overall": 0.48911139690447086}, {"timestamp": 1748405042454, "memoryEfficiency": 0, "thermalStability": 0.8945366508430905, "cpuUsage": 0.423486328125, "responseTime": 329.8675996839065, "overall": 0.4841998214964437}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}