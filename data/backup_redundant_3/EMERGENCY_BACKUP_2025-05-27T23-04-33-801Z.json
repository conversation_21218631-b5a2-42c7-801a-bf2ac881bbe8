{"timestamp": "2025-05-27T23:04:33.801Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.18657858312642, "accessFactor": 1.05, "decayFactor": 0.988105863252841, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10669059102122011, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.6542558123199923, "confidence": -5.822137766324316, "energy": 1, "focus": 0.747685366332295, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748387073784, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1900691545797, "neuronActivity": 0.5042962107547557}, "emotionalHistory": [{"timestamp": 1748386488764, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386503764, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01488258695554, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386518765, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01488258695554, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386533766, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02976517391107, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386548767, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02976517391107, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386563768, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0446477608666, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386578768, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0446477608666, "neuronActivity": 0.4882586955522753}, {"timestamp": 1748386593770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596127958422, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386608771, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0596127958422, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386623770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07457783081782, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386638770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07457783081782, "neuronActivity": 0.4965034975615212}, {"timestamp": 1748386653770, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08956710593606, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386668772, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08956710593606, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386683772, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1045563810543, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386698773, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1045563810543, "neuronActivity": 0.4989275118241756}, {"timestamp": 1748386713774, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11958790969365, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386728774, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11958790969365, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386743775, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.134619438333, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386758775, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.134619438333, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386773776, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13965096697237, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386788776, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13965096697237, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386803776, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14468249561173, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386818777, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14468249561173, "neuronActivity": 0.5031528639368517}, {"timestamp": 1748386833777, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14972545771928, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386848777, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14972545771928, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386863777, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15476841982684, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386878778, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15476841982684, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386893778, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1598113819344, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386908779, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1598113819344, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386923778, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16485434404194, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386938780, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16485434404194, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386953780, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1698973061495, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386968781, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1698973061495, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386983781, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17494026825705, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748386998782, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17494026825705, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748387013782, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1799832303646, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748387028783, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1799832303646, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748387043783, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18502619247215, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748387058784, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18502619247215, "neuronActivity": 0.5042962107547557}, {"timestamp": 1748387073784, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1900691545797, "neuronActivity": 0.5042962107547557}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:03:33 PM", "mood": "curious"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "7:03:48 PM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "7:04:03 PM", "mood": "creative"}, {"text": "Mon imagination génère de nouvelles possibilités.", "time": "7:04:18 PM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "7:04:33 PM", "mood": "creative"}], "circadianRhythm": 0.38023134761974037, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.8729148987314208, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.7995005996018757, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9396023914855485, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8579324693474095, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.8832481506260407, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "memory_compression_engine_1748386475356": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.8161729388841081, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_cache_optimizer_1748386475356": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8801223265260041, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_garbage_collector_1748386475356": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.9894173954098339, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.787465600036035, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748386476563, "expiresAt": 1748387076563}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9807532517036557, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748386479231, "expiresAt": 1748387079231}, "emergency_memory_optimizer_1156": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8169194133104111, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386807423, "expiresAt": 1748387107423}, "emergency_memory_optimizer_1157": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9713726275100072, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386807423, "expiresAt": 1748387107423}, "cpu_booster_auto_1631": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.357375, "stability": 0.9544323461356511, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748386949776, "expiresAt": 1748387549776}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.8729148987314208, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.7995005996018757, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9396023914855485, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8579324693474095, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.8832481506260407, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748386475356, "expiresAt": 1748387075356}, "memory_compression_engine_1748386475356": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.8161729388841081, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_cache_optimizer_1748386475356": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.8801223265260041, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "memory_garbage_collector_1748386475356": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.9894173954098339, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748386475356, "expiresAt": 1748387375356}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.787465600036035, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748386476563, "expiresAt": 1748387076563}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.9807532517036557, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748386479231, "expiresAt": 1748387079231}, "emergency_memory_optimizer_1156": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8169194133104111, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386807423, "expiresAt": 1748387107423}, "emergency_memory_optimizer_1157": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9713726275100072, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748386807423, "expiresAt": 1748387107423}, "cpu_booster_auto_1631": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.357375, "stability": 0.9544323461356511, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748386949776, "expiresAt": 1748387549776}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.371565240561486, "efficiency": 0.8422939144336892, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748387073709, "totalBoostPower": 30.83034812729932, "averageEnergy": 1, "averageStability": 0.8884503391775385, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748386533709, "memoryEfficiency": 0, "thermalStability": 0.9467075440618727, "cpuUsage": 0.453173828125, "responseTime": 383.81401315084054, "overall": 0.5730819763864375}, {"timestamp": 1748386593719, "memoryEfficiency": 0, "thermalStability": 0.9985499405198627, "cpuUsage": 0.4138916015625, "responseTime": 467.9054897311932, "overall": 0.5386825498127343}, {"timestamp": 1748386653719, "memoryEfficiency": 0, "thermalStability": 0.9953356590535906, "cpuUsage": 0.409375, "responseTime": 639.4282247381846, "overall": 0.5233978979013905}, {"timestamp": 1748386713721, "memoryEfficiency": 0, "thermalStability": 0.9910762175917626, "cpuUsage": 0.4203369140625, "responseTime": 298.47189855851536, "overall": 0.5299768597432113}, {"timestamp": 1748386773722, "memoryEfficiency": 0, "thermalStability": 0.9896249077386327, "cpuUsage": 0.4096923828125, "responseTime": 331.1766418177012, "overall": 0.5397517569482395}, {"timestamp": 1748386833724, "memoryEfficiency": 0, "thermalStability": 0.9931624225858185, "cpuUsage": 0.400537109375, "responseTime": 480.18919295917345, "overall": 0.5807450498553253}, {"timestamp": 1748386893726, "memoryEfficiency": 0, "thermalStability": 0.9941525452666813, "cpuUsage": 0.424072265625, "responseTime": 667.7193879504995, "overall": 0.5183106695084057}, {"timestamp": 1748386953726, "memoryEfficiency": 0, "thermalStability": 0.9949151921603415, "cpuUsage": 0.7105712890625, "responseTime": 241.70237020287692, "overall": 0.5152105776291802}, {"timestamp": 1748387013727, "memoryEfficiency": 0, "thermalStability": 0.8758210591557953, "cpuUsage": 0.901953125, "responseTime": 574.9408407883823, "overall": 0.5077223466795656}, {"timestamp": 1748387073728, "memoryEfficiency": 0, "thermalStability": 0.5764018405228853, "cpuUsage": 0.9478515625, "responseTime": 682.5567430013132, "overall": 0.4622822344366896}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}