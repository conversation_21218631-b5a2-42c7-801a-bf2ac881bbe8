{"timestamp": "2025-05-28T05:43:49.845Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 100, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.0749064275142552, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7, "maxMemorySize": 50}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.072635526073645, "energy": 1, "focus": 0.8625850752620644, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748411024919, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499719139844, "neuronActivity": 0.49971913984314226}, "emotionalHistory": [{"timestamp": 1748410744816, "mood": "curious", "dominantEmotion": "energy", "qi": 203, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410759816, "mood": "curious", "dominantEmotion": "energy", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410774817, "mood": "curious", "dominantEmotion": "energy", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410789816, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410804817, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410819818, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410837775, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.474924807623168}, {"timestamp": 1748410852775, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.48337551184852007}, {"timestamp": 1748410867776, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01474924807624, "neuronActivity": 0.48337551184852007}, {"timestamp": 1748410882776, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01483375511847, "neuronActivity": 0.48337551184852007}, {"timestamp": 1748410897776, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01483375511847, "neuronActivity": 0.48337551184852007}, {"timestamp": 1748410912777, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01487600863962, "neuronActivity": 0.4876008639611962}, {"timestamp": 1748410929024, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01487600863962, "neuronActivity": 0.4876008639611962}, {"timestamp": 1748410944038, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01487600863962, "neuronActivity": 0.4876008639611962}, {"timestamp": 1748410959048, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01487600863962, "neuronActivity": 0.4876008639611962}, {"timestamp": 1748410974049, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499719139844, "neuronActivity": 0.49971913984314226}, {"timestamp": 1748410994916, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499719139844, "neuronActivity": 0.49971913984314226}, {"timestamp": 1748411009918, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499719139844, "neuronActivity": 0.49971913984314226}, {"timestamp": 1748411024919, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01499719139844, "neuronActivity": 0.49971913984314226}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:42:39 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:42:54 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:43:14 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:43:29 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:43:44 AM", "mood": "curious"}], "circadianRhythm": 0.9987434734665206, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9486464651723567, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9619690729828403, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8807127655385002, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410731482, "expiresAt": 1748411331482}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9129543374153578, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748410731483, "expiresAt": 1748411331483}, "memory_compression_engine_1748410731483": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9700431182532288, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "memory_cache_optimizer_1748410731483": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.941119019107236, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "memory_garbage_collector_1748410731483": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9239163145455799, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8484561070417036, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748410731483, "expiresAt": 1748411331483}, "neural_stimulator_auto_12": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8477686413007223, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748410735250, "expiresAt": 1748411335250}, "emergency_memory_optimizer_82": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.845391725727004, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410754929, "expiresAt": 1748411054929}, "emergency_memory_optimizer_83": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8413547954607011, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410754929, "expiresAt": 1748411054929}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9486464651723567, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9619690729828403, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8807127655385002, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410731482, "expiresAt": 1748411331482}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9129543374153578, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748410731483, "expiresAt": 1748411331483}, "memory_compression_engine_1748410731483": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9700431182532288, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "memory_cache_optimizer_1748410731483": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.941119019107236, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "memory_garbage_collector_1748410731483": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9239163145455799, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748410731483, "expiresAt": 1748411631483}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8484561070417036, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748410731483, "expiresAt": 1748411331483}, "neural_stimulator_auto_12": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8477686413007223, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748410735250, "expiresAt": 1748411335250}, "emergency_memory_optimizer_82": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.845391725727004, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410754929, "expiresAt": 1748411054929}, "emergency_memory_optimizer_83": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8413547954607011, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748410754929, "expiresAt": 1748411054929}}, "totalAccelerators": 12, "activeAccelerators": 12, "averageBoostFactor": 2.559435066927083, "efficiency": 0.8535661040156249, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748411029679, "totalBoostPower": 30.713220803124997, "averageEnergy": 1, "averageStability": 0.910194363545436, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748410789727, "memoryEfficiency": 0, "thermalStability": 0.971043240899841, "cpuUsage": 0.46669921875, "responseTime": 519.4414503739324, "overall": 0.5665394698273756}, {"timestamp": 1748410850135, "memoryEfficiency": 0, "thermalStability": 0.9955811048961348, "cpuUsage": 0.478271484375, "responseTime": 252.89195062902786, "overall": 0.5127969832115752}, {"timestamp": 1748410910136, "memoryEfficiency": 0, "thermalStability": 0.9959521709630886, "cpuUsage": 0.5048583984375, "responseTime": 404.8106126379107, "overall": 0.5585040122104042}, {"timestamp": 1748410970259, "memoryEfficiency": 0, "thermalStability": 0.9881277814507484, "cpuUsage": 0.5107421875, "responseTime": 212.7820244433844, "overall": 0.5916645251535925}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}