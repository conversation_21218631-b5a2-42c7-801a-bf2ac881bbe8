{"timestamp": "2025-05-28T03:55:09.357Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.0605564117257922, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.6676079717550942, "confidence": -5.7387660012346045, "energy": 1, "focus": 0.785277302365614, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "creative", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748404494653, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18488535118948, "neuronActivity": 0.5339164751568413}, "emotionalHistory": [{"timestamp": 1748403924092, "mood": "curious", "dominantEmotion": "energy", "qi": 148, "neuronActivity": 0.464081910385268}, {"timestamp": 1748403939092, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01464081910385, "neuronActivity": 0.464081910385268}, {"timestamp": 1748403954093, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01464081910385, "neuronActivity": 0.464081910385268}, {"timestamp": 1748403969094, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0292816382077, "neuronActivity": 0.464081910385268}, {"timestamp": 1748403984094, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0292816382077, "neuronActivity": 0.464081910385268}, {"timestamp": 1748403999094, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04392245731154, "neuronActivity": 0.464081910385268}, {"timestamp": 1748404014095, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04392245731154, "neuronActivity": 0.464081910385268}, {"timestamp": 1748404029095, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05864778345764, "neuronActivity": 0.4725326146106201}, {"timestamp": 1748404044117, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.05864778345764, "neuronActivity": 0.4725326146106201}, {"timestamp": 1748404059154, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07337310960375, "neuronActivity": 0.4725326146106201}, {"timestamp": 1748404074155, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07337310960375, "neuronActivity": 0.4725326146106201}, {"timestamp": 1748404089155, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08814068927097, "neuronActivity": 0.47675796672329623}, {"timestamp": 1748404104156, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08814068927097, "neuronActivity": 0.47675796672329623}, {"timestamp": 1748404119156, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1029082689382, "neuronActivity": 0.47675796672329623}, {"timestamp": 1748404134156, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1029082689382, "neuronActivity": 0.47675796672329623}, {"timestamp": 1748404149157, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11778244473098, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404164159, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11778244473098, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404179159, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13265662052376, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404194159, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13265662052376, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404209160, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13753079631653, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404224160, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13753079631653, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404239161, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1424049721093, "neuronActivity": 0.48741757927727347}, {"timestamp": 1748404254167, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1424049721093, "neuronActivity": 0.519383193281145}, {"timestamp": 1748404269193, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14767422131905, "neuronActivity": 0.5269249209760831}, {"timestamp": 1748404284216, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14767422131905, "neuronActivity": 0.5269249209760831}, {"timestamp": 1748404299220, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1529434705288, "neuronActivity": 0.5269249209760831}, {"timestamp": 1748404314234, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1529434705288, "neuronActivity": 0.5269249209760831}, {"timestamp": 1748404329248, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15821271973857, "neuronActivity": 0.5269249209760831}, {"timestamp": 1748404344268, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.15821271973857, "neuronActivity": 0.5315972444605928}, {"timestamp": 1748404359285, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16352869218318, "neuronActivity": 0.5315972444605928}, {"timestamp": 1748404374307, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16352869218318, "neuronActivity": 0.5315972444605928}, {"timestamp": 1748404389461, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16886785693475, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404404526, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.16886785693475, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404419538, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17420702168633, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404434547, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.17420702168633, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404449563, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.1795461864379, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404464576, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.1795461864379, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404479632, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18488535118948, "neuronActivity": 0.5339164751568413}, {"timestamp": 1748404494653, "mood": "creative", "dominantEmotion": "creativity", "qi": 148.18488535118948, "neuronActivity": 0.5339164751568413}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:53:54 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:54:09 PM", "mood": "curious"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "11:54:24 PM", "mood": "creative"}, {"text": "Je vois des connexions inattendues entre les concepts.", "time": "11:54:39 PM", "mood": "creative"}, {"text": "Les idées fusent dans mon réseau créatif !", "time": "11:54:54 PM", "mood": "creative"}], "circadianRhythm": 0.9273550418780228, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.9928694666414181, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.884766746394705, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9651981647997986, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8941764332582962, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403910653, "expiresAt": 1748404510653}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.9375070330639635, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748403910653, "expiresAt": 1748404510653}, "memory_compression_engine_1748403910654": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.9295494560683191, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "memory_cache_optimizer_1748403910654": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.9810069586322595, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "memory_garbage_collector_1748403910654": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.7041133408880759, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.9631799379796451, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748403911196, "expiresAt": 1748404511196}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.7766529696006946, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748403911692, "expiresAt": 1748404511692}, "neural_stimulator_auto_9": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.9258950150332479, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748403911993, "expiresAt": 1748404511993}, "cpu_booster_auto_1107": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.2737809374999998, "stability": 0.9479311855145958, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748404232208, "expiresAt": 1748404832208}, "emergency_memory_optimizer_1556": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8022841028927963, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404367452, "expiresAt": 1748404667452}, "emergency_memory_optimizer_1557": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8977958055164817, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404367452, "expiresAt": 1748404667452}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.4100801476423355, "stability": 0.9928694666414181, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.1825601107317523, "stability": 0.884766746394705, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8412800553658761, "stability": 0.9651981647997986, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.7993684696191892, "stability": 0.8941764332582962, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748403910653, "expiresAt": 1748404510653}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.278358021009893, "stability": 0.9375070330639635, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748403910653, "expiresAt": 1748404510653}, "memory_compression_engine_1748403910654": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.697473878476757, "stability": 0.9295494560683191, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "memory_cache_optimizer_1748403910654": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.218484327086055, "stability": 0.9810069586322595, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "memory_garbage_collector_1748403910654": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.0987369392383783, "stability": 0.7041133408880759, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748403910654, "expiresAt": 1748404810654}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.6796210817715138, "stability": 0.9631799379796451, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748403911196, "expiresAt": 1748404511196}, "response_accelerator_auto_7": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.398105408857569, "stability": 0.7766529696006946, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748403911692, "expiresAt": 1748404511692}, "neural_stimulator_auto_9": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 1.9191158574668654, "stability": 0.9258950150332479, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748403911993, "expiresAt": 1748404511993}, "cpu_booster_auto_1107": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.2737809374999998, "stability": 0.9479311855145958, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748404232208, "expiresAt": 1748404832208}, "emergency_memory_optimizer_1556": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8022841028927963, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404367452, "expiresAt": 1748404667452}, "emergency_memory_optimizer_1557": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.6434375, "stability": 0.8977958055164817, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748404367452, "expiresAt": 1748404667452}}, "totalAccelerators": 14, "activeAccelerators": 14, "averageBoostFactor": 2.363131445340442, "efficiency": 0.8417878867204265, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748404509089, "totalBoostPower": 33.083840234766186, "averageEnergy": 1, "averageStability": 0.900209044020307, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748403969036, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.49677734375, "responseTime": 228.105450157242, "overall": 0}, {"timestamp": 1748404029046, "memoryEfficiency": 0, "thermalStability": 0.7311019235187106, "cpuUsage": 0.478173828125, "responseTime": 295.5978814121492, "overall": 0.5054018260455745}, {"timestamp": 1748404089105, "memoryEfficiency": 0, "thermalStability": 0.7814403492727398, "cpuUsage": 0.5303955078125, "responseTime": 569.6795413312329, "overall": 0.4681656994790269}, {"timestamp": 1748404149104, "memoryEfficiency": 0, "thermalStability": 0.7782071152081093, "cpuUsage": 0.521533203125, "responseTime": 249.3205689788757, "overall": 0.5199902841061422}, {"timestamp": 1748404209105, "memoryEfficiency": 0, "thermalStability": 0.8006654553943211, "cpuUsage": 0.54951171875, "responseTime": 575.2313788934647, "overall": 0.4530799931205608}, {"timestamp": 1748404269105, "memoryEfficiency": 0, "thermalStability": 0.8065776051378545, "cpuUsage": 0.6332763671875, "responseTime": 416.2057276149526, "overall": 0.46821180654833194}, {"timestamp": 1748404329106, "memoryEfficiency": 0, "thermalStability": 0.7536707120417467, "cpuUsage": 0.5130126953125, "responseTime": 324.5404089137355, "overall": 0.5136441406240257}, {"timestamp": 1748404389384, "memoryEfficiency": 0, "thermalStability": 0.7840508262419866, "cpuUsage": 0.5362548828125, "responseTime": 697.666417404007, "overall": 0.4837679967923092}, {"timestamp": 1748404449385, "memoryEfficiency": 0, "thermalStability": 0.8049810279380474, "cpuUsage": 0.49287109375, "responseTime": 466.8751828558906, "overall": 0.5164357766756257}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}