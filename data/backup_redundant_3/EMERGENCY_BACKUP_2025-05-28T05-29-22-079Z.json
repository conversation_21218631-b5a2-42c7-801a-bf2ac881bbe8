{"timestamp": "2025-05-28T05:29:22.079Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 100, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1000433422151434, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7, "maxMemorySize": 50}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.0726320279519297, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748410147535, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01580867891437, "neuronActivity": 0.5808678914374824}, "emotionalHistory": [{"timestamp": 1748409877042, "mood": "curious", "dominantEmotion": "energy", "qi": 203, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409892042, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409907043, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409922043, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409937044, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409952044, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409967052, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409982527, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5593009910670951}, {"timestamp": 1748409997528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01559300991067, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410012528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01567751695293, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410027529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01567751695293, "neuronActivity": 0.5677516952924472}, {"timestamp": 1748410042529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410057528, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410072529, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410087530, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01571977047405, "neuronActivity": 0.5719770474051232}, {"timestamp": 1748410102531, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01576202399517, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410117532, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01576202399517, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410132533, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01580867891437, "neuronActivity": 0.5808678914374824}, {"timestamp": 1748410147535, "mood": "curious", "dominantEmotion": "creativity", "qi": 225.01580867891437, "neuronActivity": 0.5808678914374824}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:28:07 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:28:22 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:28:37 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:28:52 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:29:07 AM", "mood": "curious"}], "circadianRhythm": 0.9954698208146099, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9723687446621033, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9673999723697276, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9197032899563653, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8637755358388691, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9181653521182721, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "memory_compression_engine_1748409863437": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.882367588112872, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_cache_optimizer_1748409863437": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8859099117198491, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_garbage_collector_1748409863437": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9482871855424827, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9383576720144068, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "neural_stimulator_auto_6": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8710940070253071, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "emergency_memory_optimizer_62": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8996995588750306, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409880852, "expiresAt": 1748410180852}, "emergency_memory_optimizer_63": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9064081024025896, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409880852, "expiresAt": 1748410180852}, "cpu_booster_auto_396": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.31450625, "stability": 0.9676710093011082, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748409972426, "expiresAt": 1748410572426}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9723687446621033, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9673999723697276, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9197032899563653, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8637755358388691, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9181653521182721, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748409863436, "expiresAt": 1748410463436}, "memory_compression_engine_1748409863437": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.882367588112872, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_cache_optimizer_1748409863437": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.8859099117198491, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "memory_garbage_collector_1748409863437": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.9482871855424827, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748409863437, "expiresAt": 1748410763437}, "response_accelerator_auto_4": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.9383576720144068, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "neural_stimulator_auto_6": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8710940070253071, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748409863437, "expiresAt": 1748410463437}, "emergency_memory_optimizer_62": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8996995588750306, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409880852, "expiresAt": 1748410180852}, "emergency_memory_optimizer_63": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9064081024025896, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748409880852, "expiresAt": 1748410180852}, "cpu_booster_auto_396": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.31450625, "stability": 0.9676710093011082, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748409972426, "expiresAt": 1748410572426}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.540594388701923, "efficiency": 0.8524356633221153, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748410161950, "totalBoostPower": 33.027727053125, "averageEnergy": 1, "averageStability": 0.9185544561491525, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748409921963, "memoryEfficiency": 0, "thermalStability": 0.8970570074187385, "cpuUsage": 0.47548828125, "responseTime": 647.0616629870804, "overall": 0.4961118888009902}, {"timestamp": 1748409982518, "memoryEfficiency": 0, "thermalStability": 0.993707583554917, "cpuUsage": 0.70107421875, "responseTime": 617.82635883846, "overall": 0.5696116415036709}, {"timestamp": 1748410042518, "memoryEfficiency": 0, "thermalStability": 0.8641401084346904, "cpuUsage": 0.4923828125, "responseTime": 241.1734337192452, "overall": 0.5260279738135483}, {"timestamp": 1748410102520, "memoryEfficiency": 0, "thermalStability": 0.8721655420545074, "cpuUsage": 0.5096435546875, "responseTime": 439.77272573785245, "overall": 0.5153731021530712}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}