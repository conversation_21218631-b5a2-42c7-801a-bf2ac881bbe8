{"snapshots": [{"timestamp": "2025-05-25T23:30:36.268Z", "uptime": 20334, "metrics": {"qi": {"initial": 120, "current": 120.00758455833451, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.038379586093, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 157, "learningBonus": 20, "cyclesCompleted": 1, "optimizationsApplied": 0}, "creativity": {"level": 95.1, "innovations": 1, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.00379227916726, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 15.7, "creativityIndex": 95.1}, "evolutionScore": 55}, {"timestamp": "2025-05-25T23:32:28.741Z", "uptime": 132807, "metrics": {"qi": {"initial": 120, "current": 120.01469124650868, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.07290318757862, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 164, "learningBonus": 20, "cyclesCompleted": 2, "optimizationsApplied": 0}, "creativity": {"level": 95.1, "innovations": 1, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.00734562325434, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 16.400000000000002, "creativityIndex": 95.1}, "evolutionScore": 55}, {"timestamp": "2025-05-25T23:32:38.735Z", "uptime": 142801, "metrics": {"qi": {"initial": 120, "current": 120.02164153359627, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.10593888575247, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 170, "learningBonus": 20, "cyclesCompleted": 3, "optimizationsApplied": 0}, "creativity": {"level": 95.19999999999999, "innovations": 2, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.01082076679813, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 17, "creativityIndex": 95.19999999999999}, "evolutionScore": 55}, {"timestamp": "2025-05-25T23:32:48.733Z", "uptime": 152799, "metrics": {"qi": {"initial": 120, "current": 120.02898962011034, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.14155289710672, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 177, "learningBonus": 20, "cyclesCompleted": 4, "optimizationsApplied": 0}, "creativity": {"level": 95.29999999999998, "innovations": 3, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.01449481005516, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 17.7, "creativityIndex": 95.29999999999998}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:32:58.735Z", "uptime": 162801, "metrics": {"qi": {"initial": 120, "current": 120.03719867366512, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.16917693647966, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 185, "learningBonus": 20, "cyclesCompleted": 5, "optimizationsApplied": 0}, "creativity": {"level": 95.39999999999998, "innovations": 4, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.01859933683256, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 18.5, "creativityIndex": 95.39999999999998}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:08.734Z", "uptime": 172800, "metrics": {"qi": {"initial": 120, "current": 120.04454357266552, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.209276729182, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 192, "learningBonus": 20, "cyclesCompleted": 6, "optimizationsApplied": 0}, "creativity": {"level": 95.49999999999997, "innovations": 5, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.02227178633276, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 19.2, "creativityIndex": 95.49999999999997}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:18.736Z", "uptime": 182802, "metrics": {"qi": {"initial": 120, "current": 120.05223172920466, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.24673144147582, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 199, "learningBonus": 20, "cyclesCompleted": 7, "optimizationsApplied": 0}, "creativity": {"level": 95.59999999999997, "innovations": 6, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.02611586460234, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 19.900000000000002, "creativityIndex": 95.59999999999997}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:28.733Z", "uptime": 192799, "metrics": {"qi": {"initial": 120, "current": 120.05910506837239, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.2865301590118, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 205, "learningBonus": 20, "cyclesCompleted": 8, "optimizationsApplied": 0}, "creativity": {"level": 95.59999999999997, "innovations": 6, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.029552534186195, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 20.5, "creativityIndex": 95.59999999999997}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:38.734Z", "uptime": 202800, "metrics": {"qi": {"initial": 120, "current": 120.06760465471422, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.3282547601341, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 213, "learningBonus": 20, "cyclesCompleted": 9, "optimizationsApplied": 0}, "creativity": {"level": 95.69999999999996, "innovations": 7, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.03380232735711, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 21.3, "creativityIndex": 95.69999999999996}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:48.734Z", "uptime": 212800, "metrics": {"qi": {"initial": 120, "current": 120.07437444729543, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.36457759707244, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 219, "learningBonus": 20, "cyclesCompleted": 10, "optimizationsApplied": 0}, "creativity": {"level": 95.79999999999995, "innovations": 8, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.03718722364771, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 21.9, "creativityIndex": 95.79999999999995}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:33:58.734Z", "uptime": 222800, "metrics": {"qi": {"initial": 120, "current": 120.08168940317807, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.39922413369885, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 226, "learningBonus": 20, "cyclesCompleted": 11, "optimizationsApplied": 0}, "creativity": {"level": 95.79999999999995, "innovations": 8, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.04084470158904, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 22.6, "creativityIndex": 95.79999999999995}, "evolutionScore": 56}, {"timestamp": "2025-05-25T23:34:08.734Z", "uptime": 232800, "metrics": {"qi": {"initial": 120, "current": 120.08903929761853, "growth": 0, "growthRate": 0.01, "maxGrowth": 200}, "neurons": {"initial": 145, "current": 145.44178360665862, "growth": 0, "growthRate": 0.05, "activeRatio": 0.61}, "memory": {"initialEntries": 83, "currentEntries": 83, "growth": 0, "zones": 6, "avgTemperature": 0.58}, "learning": {"experiencePoints": 233, "learningBonus": 20, "cyclesCompleted": 12, "optimizationsApplied": 0}, "creativity": {"level": 95.89999999999995, "innovations": 9, "zone6Activity": 0.7, "creativeOutputs": 0}}, "performance": {"qiEfficiency": 60.044519648809256, "neuronUtilization": 61, "memoryEfficiency": 41.5, "learningRate": 23.3, "creativityIndex": 95.89999999999995}, "evolutionScore": 56}], "startDate": "2025-05-25T23:30:15.934Z", "totalUptime": 0, "evolutionEvents": [{"timestamp": "2025-05-25T23:32:28.752Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "4.46"}], "evolutionScore": 55, "previousScore": 55}, {"timestamp": "2025-05-25T23:32:38.735Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 6, "direction": "croissance", "percentage": "3.66"}], "evolutionScore": 55, "previousScore": 55}, {"timestamp": "2025-05-25T23:32:48.733Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "4.12"}], "evolutionScore": 56, "previousScore": 55}, {"timestamp": "2025-05-25T23:32:58.736Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 8, "direction": "croissance", "percentage": "4.52"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:08.735Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "3.78"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:18.737Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "3.65"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:28.733Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 6, "direction": "croissance", "percentage": "3.02"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:38.734Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 8, "direction": "croissance", "percentage": "3.90"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:48.734Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 6, "direction": "croissance", "percentage": "2.82"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:33:58.734Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "3.20"}], "evolutionScore": 56, "previousScore": 56}, {"timestamp": "2025-05-25T23:34:08.734Z", "type": "evolution_detected", "changes": [{"metric": "experience", "change": 7, "direction": "croissance", "percentage": "3.10"}], "evolutionScore": 56, "previousScore": 56}]}