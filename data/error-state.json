{"stats": {"total": 3, "byType": {"unhandledRejection": 2, "uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 3}, "recentErrors": [{"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T03:29:09.799Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T03:34:09.793Z", "context": {"promise": {}}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write E<PERSON>\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at EnhancedAgentSystem.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/enhanced-agent-system.js:831:17)", "timestamp": "2025-05-28T03:35:04.615Z", "context": {}, "critical": true}], "timestamp": "2025-05-28T03:35:04.615Z"}