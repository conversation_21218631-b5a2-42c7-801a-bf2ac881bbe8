{"stats": {"total": 2, "byType": {"unhandledRejection": 1, "uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 2}, "recentErrors": [{"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T02:26:31.645Z", "context": {"promise": {}}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at SpecializedAcceleratorPool.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/specialized-accelerator-pool.js:601:17)", "timestamp": "2025-05-28T02:27:38.370Z", "context": {}, "critical": true}], "timestamp": "2025-05-28T02:27:38.371Z"}