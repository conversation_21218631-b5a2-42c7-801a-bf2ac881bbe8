{"stats": {"total": 1, "byType": {"uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 1}, "recentErrors": [{"type": "uncaughtException", "message": "write EIO", "stack": "Error: write E<PERSON>\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at QiNeuronMonitor.updateMetrics (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/qi-neuron-monitor.js:224:17)", "timestamp": "2025-05-28T02:38:38.058Z", "context": {}, "critical": true}], "timestamp": "2025-05-28T02:38:38.059Z"}