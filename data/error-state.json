{"stats": {"total": 10, "byType": {"unhandledRejection": 9, "uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 10}, "recentErrors": [{"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:33:50.600Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:38:50.599Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:43:50.630Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:48:50.680Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:53:50.683Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T01:58:51.349Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T02:03:52.758Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T02:08:52.933Z", "context": {"promise": {}}, "critical": true}, {"type": "unhandledRejection", "message": "this.analyzeRealUsagePatterns is not a function", "stack": "TypeError: this.analyzeRealUsagePatterns is not a function\n    at RealThermalMemoryEvolution.performDeepAnalysis (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:521:37)\n    at Timeout._onTimeout (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-thermal-memory-evolution.js:69:18)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-05-28T02:13:54.943Z", "context": {"promise": {}}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at QiNeuronMonitor.updateMetrics (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/qi-neuron-monitor.js:224:17)", "timestamp": "2025-05-28T02:16:10.980Z", "context": {}, "critical": true}], "timestamp": "2025-05-28T02:16:10.981Z"}