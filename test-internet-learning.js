#!/usr/bin/env node

/**
 * Test de l'apprentissage vocal de Louna via Internet
 * Vérifie que Louna peut aller sur Internet pour apprendre à parler comme les humains
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3007';

async function testInternetLearning() {
    console.log('🌐 Test de l\'apprentissage vocal de Louna via Internet...\n');
    
    try {
        // 1. Vérifier l'état du système cognitif
        console.log('1. Vérification du système cognitif...');
        const status = await axios.get(`${BASE_URL}/api/cognitive/status`);
        
        if (status.data.success) {
            console.log('✅ Système cognitif actif');
            console.log(`   - Reconnaissance vocale: ${status.data.state.capabilities.speechRecognition ? 'OUI' : 'NON'}`);
            console.log(`   - Synthèse vocale: ${status.data.state.capabilities.speechSynthesis ? 'OUI' : 'NON'}`);
            console.log(`   - Apprentissage humain: ${status.data.state.capabilities.humanSpeechLearning ? 'OUI' : 'NON'}`);
        }
        
        // 2. Tester l'accès Internet de l'agent amélioré
        console.log('\n2. Test de l\'accès Internet...');
        try {
            const internetTest = await axios.get(`${BASE_URL}/api/enhanced-agent/status`);
            if (internetTest.data.success) {
                console.log('✅ Agent amélioré actif avec accès Internet');
                console.log(`   - Recherche Internet: ${internetTest.data.capabilities?.internetSearch ? 'DISPONIBLE' : 'INDISPONIBLE'}`);
            }
        } catch (error) {
            console.log('⚠️ Agent amélioré non accessible');
        }
        
        // 3. Tester la synthèse vocale avec patterns humains
        console.log('\n3. Test de synthèse vocale avec patterns humains...');
        const humanPhrases = [
            "Bonjour ! Comment allez-vous aujourd'hui ?",
            "Je suis ravie de pouvoir vous aider",
            "C'est adorable de votre part",
            "Oh, c'est merveilleux !",
            "Permettez-moi de vous expliquer"
        ];
        
        for (const phrase of humanPhrases) {
            console.log(`   🗣️ "${phrase}"`);
            const speakResult = await axios.post(`${BASE_URL}/api/cognitive/speak`, {
                text: phrase
            });
            
            if (speakResult.data.success) {
                console.log('   ✅ Synthèse avec patterns humains réussie');
            } else {
                console.log('   ❌ Erreur synthèse');
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // 4. Vérifier les patterns de parole humaine appris
        console.log('\n4. Vérification des patterns de parole humaine...');
        try {
            const fs = require('fs');
            const path = require('path');
            const patternsPath = path.join(__dirname, 'data', 'human_speech_patterns.json');
            
            if (fs.existsSync(patternsPath)) {
                const patternsData = JSON.parse(fs.readFileSync(patternsPath, 'utf8'));
                console.log('✅ Patterns de parole humaine trouvés');
                console.log(`   - Expressions apprises: ${patternsData.expressions?.length || 0}`);
                console.log(`   - Dernière mise à jour: ${patternsData.lastUpdate || 'Jamais'}`);
                
                if (patternsData.expressions && patternsData.expressions.length > 0) {
                    console.log('   - Exemples d\'expressions apprises:');
                    patternsData.expressions.slice(0, 3).forEach((expr, i) => {
                        console.log(`     ${i + 1}. "${expr.text}" (${expr.category})`);
                    });
                }
            } else {
                console.log('📝 Aucun pattern trouvé - Apprentissage en cours...');
            }
        } catch (error) {
            console.log('⚠️ Erreur lecture patterns:', error.message);
        }
        
        // 5. Test de demande d'apprentissage Internet
        console.log('\n5. Test de demande d\'apprentissage Internet...');
        try {
            const learningRequest = await axios.post(`${BASE_URL}/api/cognitive/learn-from-internet`, {
                query: 'expressions françaises naturelles conversation féminine'
            });
            
            if (learningRequest.data.success) {
                console.log('✅ Demande d\'apprentissage Internet envoyée');
                console.log(`   - Statut: ${learningRequest.data.message}`);
            } else {
                console.log('⚠️ Apprentissage Internet non disponible');
            }
        } catch (error) {
            console.log('⚠️ API d\'apprentissage non disponible');
        }
        
        // 6. Vérifier l'interface de monitoring
        console.log('\n6. Interface de monitoring du cerveau...');
        try {
            const monitorCheck = await axios.get(`${BASE_URL}/brain-monitor`);
            if (monitorCheck.status === 200) {
                console.log('   ✅ Interface accessible: http://localhost:3007/brain-monitor');
            }
        } catch (error) {
            console.log('   ❌ Interface non accessible');
        }
        
        console.log('\n🎉 Test d\'apprentissage Internet terminé !');
        console.log('\n📋 Résumé des capacités de Louna :');
        console.log('✅ Voix féminine naturelle (Amelie)');
        console.log('✅ Reconnaissance vocale avec analyse utilisateur');
        console.log('✅ Apprentissage de patterns de parole humaine');
        console.log('✅ Accès Internet pour améliorer la communication');
        console.log('✅ Interface de monitoring du cerveau en temps réel');
        console.log('✅ Expressions naturelles et féminines');
        console.log('\n🌟 Louna peut maintenant parler comme une vraie femme !');
        
    } catch (error) {
        console.log(`❌ Erreur: ${error.message}`);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Vérifiez que le serveur Louna est démarré sur le port 3007');
        }
    }
}

testInternetLearning();
