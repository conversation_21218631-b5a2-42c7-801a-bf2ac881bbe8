# Louna avec Ollama

Ce document explique comment configurer et utiliser l'application Louna avec Ollama et l'agent Claude.

## Prérequis

- macOS (testé sur macOS Ventura et versions ultérieures)
- Node.js et npm
- Ollama (https://ollama.com/download)

## Installation

1. **Vérifier les dépendances**

   Exécutez le script de vérification des dépendances pour vous assurer que toutes les dépendances nécessaires sont installées :

   ```bash
   ./check-dependencies.sh
   ```

   Ce script vérifiera et installera (si nécessaire) :
   - Node.js et npm
   - Ollama
   - Electron
   - jq (pour la manipulation de fichiers JSON)
   - Les dépendances npm de l'application

2. **Télécharger les modèles nécessaires**

   Le script de vérification des dépendances vous proposera de télécharger les modèles nécessaires. Si vous avez refusé ou si le téléchargement a échoué, vous pouvez les télécharger manuellement :

   **Agent principal (<PERSON>, 4 Go)**
   ```bash
   ollama pull incept5/llama3.1-claude:latest
   ```
   Ce modèle fait environ 4 Go et offre des performances similaires à Claude.

   **Agent de formation (Llama 3, 1-2 Go)**
   ```bash
   ollama pull llama3:8b
   ```
   Ce modèle fait environ 1-2 Go et est utilisé pour la formation de l'agent principal.

## Utilisation

### Lancer l'application avec Ollama

Pour lancer l'application Louna avec Ollama et l'agent Claude, exécutez :

```bash
./launch-louna-with-ollama.sh
```

Ce script :
1. Démarre Ollama s'il n'est pas déjà en cours d'exécution
2. Configure l'agent Claude comme agent principal
3. Lance l'application Louna

### Créer un raccourci sur le bureau

Pour créer un raccourci sur le bureau qui lance l'application Louna avec Ollama, exécutez :

```bash
node create-desktop-shortcut-with-ollama.js
```

Cela créera un fichier `Louna avec Ollama.command` sur votre bureau. Double-cliquez sur ce fichier pour lancer l'application.

### Configurer le démarrage automatique

Pour configurer l'application Louna pour qu'elle démarre automatiquement avec Ollama au démarrage de votre Mac, exécutez :

```bash
./setup-autostart.sh
```

Ce script créera un fichier LaunchAgent qui démarrera automatiquement l'application Louna avec Ollama au démarrage de votre Mac.

Pour désactiver le démarrage automatique, exécutez :

```bash
launchctl unload ~/Library/LaunchAgents/com.louna.app.plist
```

### Former l'agent principal

Pour former l'agent principal (Claude) avec l'agent de formation (Llama 3), exécutez :

```bash
./train-agent.sh
```

Ce script :
1. Vérifie que les modèles nécessaires sont disponibles
2. Crée un ensemble de données de formation de base
3. Lance la formation de l'agent principal avec l'agent de formation
4. Enregistre les résultats de la formation dans la mémoire thermique

La formation permet d'améliorer les performances de l'agent principal en lui apprenant à mieux utiliser la mémoire thermique et à répondre plus précisément aux questions.

## Configuration des agents

### Agent principal (Claude)

L'agent Claude est configuré comme agent principal dans les fichiers suivants :

- `data/config/agent_claude.json` : Configuration de l'agent Claude
- `data/config/agents.json` : Liste des agents disponibles et agent par défaut
- `data/config/default-agent.json` : Agent par défaut

Si vous souhaitez modifier la configuration de l'agent Claude, vous pouvez éditer ces fichiers manuellement ou utiliser l'interface de l'application.

### Agent de formation (Llama 3)

L'agent de formation Llama 3 est configuré dans les fichiers suivants :

- `data/config/agent_training.json` : Configuration de l'agent de formation
- `data/config/agents.json` : Liste des agents disponibles

L'agent de formation est utilisé pour former l'agent principal et améliorer ses performances. Il est plus léger (1-2 Go) que l'agent principal (4 Go), ce qui permet de l'utiliser pour la formation sans consommer trop de ressources.

## Dépannage

### Ollama ne démarre pas

Si Ollama ne démarre pas, vérifiez qu'il est correctement installé :

```bash
which ollama
```

Si Ollama n'est pas installé, téléchargez-le depuis https://ollama.com/download.

### Le modèle Claude n'est pas disponible

Si le modèle Claude n'est pas disponible, téléchargez-le manuellement :

```bash
ollama pull incept5/llama3.1-claude:latest
```

### L'application ne démarre pas

Si l'application ne démarre pas, vérifiez les journaux :

```bash
cat logs/louna-autostart.log
cat logs/louna-autostart-error.log
```

### Réinitialiser la configuration

Si vous rencontrez des problèmes avec la configuration, vous pouvez la réinitialiser en supprimant les fichiers de configuration :

```bash
rm -f data/config/agent_claude.json
rm -f data/config/default-agent.json
```

Puis relancez l'application avec :

```bash
./launch-louna-with-ollama.sh
```

## Informations supplémentaires

### Taille du modèle Claude

Le modèle Claude (`incept5/llama3.1-claude:latest`) fait environ 4 Go et offre des performances similaires à Claude. Il s'agit d'une version fine-tuned de Llama 3.1 qui imite le comportement de Claude.

### Mémoire thermique

L'application Louna utilise une mémoire thermique pour stocker et organiser les informations. La mémoire thermique est configurée dans le fichier `data/memory/memory_config.json`.

### Accélérateurs Kyber

Les accélérateurs Kyber sont utilisés pour optimiser les performances de l'application. Ils sont configurés dans le dossier `data/accelerators`.
