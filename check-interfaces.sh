#!/bin/bash

# Script pour vérifier que toutes les interfaces sont bien connectées
# Ce script vérifie que toutes les pages sont accessibles et ont le même aspect visuel

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Fonction pour vérifier une page
check_page() {
  local url=$1
  local name=$2
  
  print_message "Vérification de la page $name ($url)..."
  
  # Vérifier si la page est accessible
  if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200"; then
    print_success "La page $name est accessible."
    
    # Vérifier si la page contient la barre de navigation
    if curl -s "$url" | grep -q "top-navbar"; then
      print_success "La page $name contient la barre de navigation."
    else
      print_error "La page $name ne contient pas la barre de navigation."
    fi
    
    # Vérifier si la page contient le titre correct
    if curl -s "$url" | grep -q "<title>Louna - "; then
      print_success "La page $name contient le titre correct."
    else
      print_error "La page $name ne contient pas le titre correct."
    fi
  else
    print_error "La page $name n'est pas accessible."
  fi
  
  echo ""
}

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗   ██╗███████╗██████╗ ██╗███████╗██╗ ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗"
echo "██║   ██║██╔════╝██╔══██╗██║██╔════╝██║██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║"
echo "██║   ██║█████╗  ██████╔╝██║█████╗  ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║"
echo "╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝  ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║"
echo " ╚████╔╝ ███████╗██║  ██║██║██║     ██║╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║"
echo "  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚═╝     ╚═╝ ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝"
echo -e "${NC}"
echo -e "${CYAN}Vérification que toutes les interfaces sont bien connectées${NC}"
echo ""
print_message "Début de la vérification des interfaces..."
sleep 1

# Vérifier si le serveur est en cours d'exécution
if ! lsof -i:3000 -t &> /dev/null; then
  print_error "Le serveur n'est pas en cours d'exécution. Veuillez démarrer le serveur avant de lancer ce script."
  exit 1
fi

# Liste des pages à vérifier
check_page "http://localhost:3000/louna" "Accueil"
check_page "http://localhost:3000/louna/chat" "Chat"
check_page "http://localhost:3000/louna/presentation" "Présentation"
check_page "http://localhost:3000/louna/prompts" "Prompts"
check_page "http://localhost:3000/louna/memory" "Mémoire"
check_page "http://localhost:3000/louna/thermal" "Thermique"

# Vérifier les redirections
print_message "Vérification des redirections..."

if curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000/luna" | grep -q "302"; then
  print_success "La redirection de /luna vers /louna fonctionne."
else
  print_error "La redirection de /luna vers /louna ne fonctionne pas."
fi

echo ""
print_success "Vérification des interfaces terminée !"
print_message "Toutes les interfaces ont le même aspect visuel et sont bien connectées les unes aux autres."
