/**
 * TEST SIMPLE ET RAPIDE DU SYSTÈME DE CONNEXION DIRECTE
 * 
 * Ce script teste uniquement les fonctionnalités essentielles
 * sans surcharger le système
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

async function testAPI(endpoint, method = 'GET', data = null, timeout = 5000) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            timeout
        };
        
        if (data) {
            config.data = data;
            config.headers = { 'Content-Type': 'application/json' };
        }
        
        const startTime = Date.now();
        const response = await axios(config);
        const latency = Date.now() - startTime;
        
        return {
            success: true,
            data: response.data,
            latency,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            latency: 0,
            status: error.response?.status || 0
        };
    }
}

async function testDirectConnectionStatus() {
    log('\n🔍 TEST DU STATUT DE CONNEXION DIRECTE', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/status');
    
    if (result.success) {
        logSuccess(`Statut récupéré en ${result.latency}ms`);
        
        const data = result.data.data;
        log(`📡 Connexion directe: ${data.directConnection.available ? 'DISPONIBLE' : 'INDISPONIBLE'}`, 
            data.directConnection.available ? 'green' : 'red');
        log(`⚡ Optimiseur de vitesse: ${data.speedOptimizer.available ? 'ACTIF' : 'INACTIF'}`, 
            data.speedOptimizer.available ? 'green' : 'red');
        
        if (data.speedOptimizer.stats) {
            const stats = data.speedOptimizer.stats;
            log(`📈 Latence moyenne: ${stats.averageLatency}ms`, 'cyan');
            log(`🚀 Réponse la plus rapide: ${stats.fastestResponse}ms`, 'cyan');
            log(`📊 Gains d'optimisation: ${stats.optimizationGains}%`, 'cyan');
        }
        
        return true;
    } else {
        logError(`Échec du test de statut: ${result.error}`);
        return false;
    }
}

async function testDirectMessage() {
    log('\n💬 TEST D\'ENVOI DE MESSAGE DIRECT', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const testMessage = "Bonjour ! Test de connexion directe.";
    
    const result = await testAPI('/api/direct-connection/message', 'POST', {
        message: testMessage,
        options: {
            maxTokens: 50,
            temperature: 0.7
        }
    });
    
    if (result.success) {
        logSuccess(`Message traité en ${result.latency}ms`);
        
        const data = result.data.data;
        log(`📤 Message envoyé: "${testMessage}"`, 'cyan');
        log(`📥 Réponse reçue: "${data.response}"`, 'green');
        log(`🔗 Source: ${data.source}`, 'cyan');
        log(`⚡ Latence: ${data.latency}ms`, 'cyan');
        
        return true;
    } else {
        logError(`Échec de l'envoi de message: ${result.error}`);
        return false;
    }
}

async function testTurboMode() {
    log('\n🚀 TEST DU MODE TURBO', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/turbo-mode', 'POST', {
        enabled: true
    });
    
    if (result.success) {
        logSuccess(`Mode TURBO activé en ${result.latency}ms`);
        log(`📝 Message: ${result.data.message}`, 'green');
        return true;
    } else {
        logError(`Échec de l'activation du mode turbo: ${result.error}`);
        return false;
    }
}

async function testPerformanceStats() {
    log('\n📊 TEST DES STATISTIQUES DE PERFORMANCE', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/performance-stats');
    
    if (result.success) {
        logSuccess(`Statistiques récupérées en ${result.latency}ms`);
        
        const data = result.data.data;
        
        if (data.speedOptimizer) {
            const stats = data.speedOptimizer;
            log(`⚡ Latence moyenne: ${stats.averageLatency}ms`, 'green');
            log(`🚀 Réponse la plus rapide: ${stats.fastestResponse}ms`, 'green');
            log(`📈 Gains d'optimisation: ${stats.optimizationGains}%`, 'green');
            log(`🎯 Taux de cache hit: ${stats.cacheHitRate}%`, 'green');
        }
        
        return true;
    } else {
        logError(`Échec de récupération des statistiques: ${result.error}`);
        return false;
    }
}

async function runSimpleTests() {
    log('🚀 TESTS SIMPLES DE CONNEXION DIRECTE', 'bold');
    log('=' .repeat(60), 'cyan');
    log('🎯 Objectif: Vérifier le fonctionnement de base sans surcharge', 'blue');
    
    const tests = [
        { name: 'Statut de connexion directe', fn: testDirectConnectionStatus },
        { name: 'Mode TURBO', fn: testTurboMode },
        { name: 'Statistiques de performance', fn: testPerformanceStats },
        { name: 'Envoi de message direct', fn: testDirectMessage }
    ];
    
    let successCount = 0;
    const results = [];
    
    for (const test of tests) {
        try {
            const startTime = Date.now();
            const success = await test.fn();
            const duration = Date.now() - startTime;
            
            results.push({
                name: test.name,
                success,
                duration
            });
            
            if (success) {
                successCount++;
            }
            
            // Pause courte entre les tests
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            logError(`Erreur lors du test "${test.name}": ${error.message}`);
            results.push({
                name: test.name,
                success: false,
                duration: 0,
                error: error.message
            });
        }
    }
    
    // Résumé final
    log('\n📋 RÉSUMÉ DES TESTS SIMPLES', 'bold');
    log('=' .repeat(60), 'cyan');
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
        log(`${status} ${result.name}${duration}`, result.success ? 'green' : 'red');
        
        if (result.error) {
            log(`   └─ Erreur: ${result.error}`, 'red');
        }
    });
    
    const successRate = (successCount / tests.length) * 100;
    log(`\n📊 RÉSULTAT GLOBAL: ${successCount}/${tests.length} tests réussis (${successRate.toFixed(1)}%)`, 
        successRate >= 75 ? 'green' : successRate >= 50 ? 'yellow' : 'red');
    
    if (successRate >= 75) {
        logSuccess('🎉 Système de connexion directe fonctionnel !');
        log('✨ Le système fonctionne sans Ollama avec de bonnes performances', 'green');
    } else if (successRate >= 50) {
        log('⚠️  Système partiellement fonctionnel', 'yellow');
    } else {
        logError('❌ Système défaillant');
    }
    
    log('\n🔗 Interface de gestion: http://localhost:3000/direct-connection-manager.html', 'cyan');
    log('🏠 Page d\'accueil: http://localhost:3000/', 'cyan');
}

// Exécuter les tests
if (require.main === module) {
    runSimpleTests().catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    testDirectConnectionStatus,
    testDirectMessage,
    testTurboMode,
    testPerformanceStats,
    runSimpleTests
};
