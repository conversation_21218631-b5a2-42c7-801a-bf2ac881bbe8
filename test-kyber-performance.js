#!/usr/bin/env node

/**
 * Test des performances avec accélérateurs Kyber
 */

const KyberAccelerators = require('./kyber-accelerators');
const axios = require('axios');

async function testKyberPerformance() {
    console.log('🚀 Test des performances avec accélérateurs Kyber...');
    
    // Initialiser les accélérateurs
    const kyber = new KyberAccelerators({
        "updateInterval": 10,
        "maxBoostFactor": 8,
        "minBoostFactor": 1.5,
        "defaultBoostFactor": 3,
        "stabilityThreshold": 0.6,
        "adaptationRate": 0.1,
        "energyConsumptionRate": 0.005,
        "energyRegenerationRate": 0.02,
        "reflexiveBoostFactor": 5,
        "reflexiveStability": 0.95,
        "reflexiveEnabled": true,
        "thermalBoostFactor": 4,
        "thermalStability": 0.9,
        "thermalEnabled": true,
        "connectorBoostFactor": 3.5,
        "connectorStability": 0.92,
        "connectorEnabled": true
});
    
    // Simuler l'application de boost
    const baseResponseTime = 5000; // 5 secondes de base
    const boostedTime = kyber.applyBoost('processing', baseResponseTime);
    
    console.log(`⏱️ Temps de base: ${baseResponseTime}ms`);
    console.log(`⚡ Temps avec Kyber: ${Math.round(boostedTime)}ms`);
    console.log(`🚀 Amélioration: ${Math.round((baseResponseTime - boostedTime) / baseResponseTime * 100)}%`);
    
    // Test de chat réel
    console.log('\n💬 Test de chat avec Kyber...');
    const startTime = Date.now();
    
    try {
        const response = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour Louna, teste tes accélérateurs Kyber !',
            history: [],
            useKyberBoost: true
        }, {
            timeout: 10000
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(`✅ Réponse Kyber reçue en ${responseTime}ms`);
        console.log(`📝 Réponse: ${response.data.response}`);
        
        if (responseTime < 1500) {
            console.log('🎉 Performance EXCEPTIONNELLE avec Kyber !');
        } else if (responseTime < 3000) {
            console.log('⚡ Performance EXCELLENTE avec Kyber !');
        } else {
            console.log('👍 Performance BONNE avec Kyber');
        }
        
        // Afficher les stats Kyber
        const stats = kyber.getAcceleratorStats();
        console.log('\n📊 Stats Kyber après utilisation:');
        console.log(`• Boost moyen: ${stats.averageBoost}x`);
        console.log(`• Efficacité: ${stats.efficiency}`);
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

testKyberPerformance();
