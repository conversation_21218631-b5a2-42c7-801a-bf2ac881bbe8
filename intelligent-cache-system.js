/**
 * SYSTÈME DE CACHE INTELLIGENT POUR LOUNA
 * Accélère les réponses en mettant en cache les réponses fréquentes
 */

class IntelligentCacheSystem {
    constructor(maxCacheSize = 1000, ttl = 3600000) { // 1 heure par défaut
        this.cache = new Map();
        this.accessCount = new Map();
        this.lastAccess = new Map();
        this.responseTime = new Map();
        
        this.maxCacheSize = maxCacheSize;
        this.ttl = ttl; // Time to live en millisecondes
        
        this.stats = {
            hits: 0,
            misses: 0,
            totalRequests: 0,
            averageResponseTime: 0,
            cacheEfficiency: 0,
            memoryUsage: 0
        };
        
        // Patterns de questions fréquentes
        this.commonPatterns = new Map();
        this.semanticCache = new Map();
        
        // Nettoyage automatique toutes les 10 minutes
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 600000);
        
        console.log('🧠 Système de cache intelligent initialisé');
    }

    /**
     * <PERSON><PERSON>ère une clé de cache normalisée
     */
    generateCacheKey(message, userId = 'default') {
        // Normaliser le message
        const normalized = message
            .toLowerCase()
            .trim()
            .replace(/[^\w\s]/g, '') // Supprimer la ponctuation
            .replace(/\s+/g, ' '); // Normaliser les espaces
        
        // Créer une clé unique
        return `${userId}:${this.hashString(normalized)}`;
    }

    /**
     * Hash simple pour les chaînes
     */
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convertir en 32bit
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * Vérifie si une réponse est en cache
     */
    async get(message, userId = 'default') {
        const startTime = Date.now();
        const cacheKey = this.generateCacheKey(message, userId);
        
        this.stats.totalRequests++;
        
        // Vérifier le cache direct
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            
            // Vérifier l'expiration
            if (Date.now() - cached.timestamp < this.ttl) {
                // Mettre à jour les statistiques d'accès
                this.accessCount.set(cacheKey, (this.accessCount.get(cacheKey) || 0) + 1);
                this.lastAccess.set(cacheKey, Date.now());
                
                this.stats.hits++;
                this.updateStats();
                
                console.log(`💾 Cache HIT pour: ${message.substring(0, 50)}...`);
                
                return {
                    cached: true,
                    response: cached.response,
                    responseTime: Date.now() - startTime,
                    source: 'direct_cache'
                };
            } else {
                // Supprimer l'entrée expirée
                this.cache.delete(cacheKey);
                this.accessCount.delete(cacheKey);
                this.lastAccess.delete(cacheKey);
            }
        }
        
        // Vérifier le cache sémantique
        const semanticMatch = await this.findSemanticMatch(message);
        if (semanticMatch) {
            this.stats.hits++;
            this.updateStats();
            
            console.log(`🧠 Cache SÉMANTIQUE pour: ${message.substring(0, 50)}...`);
            
            return {
                cached: true,
                response: semanticMatch.response,
                responseTime: Date.now() - startTime,
                source: 'semantic_cache',
                similarity: semanticMatch.similarity
            };
        }
        
        this.stats.misses++;
        this.updateStats();
        
        console.log(`❌ Cache MISS pour: ${message.substring(0, 50)}...`);
        
        return {
            cached: false,
            responseTime: Date.now() - startTime
        };
    }

    /**
     * Met en cache une réponse
     */
    async set(message, response, userId = 'default', metadata = {}) {
        const cacheKey = this.generateCacheKey(message, userId);
        
        // Vérifier la taille du cache
        if (this.cache.size >= this.maxCacheSize) {
            this.evictLeastUsed();
        }
        
        const cacheEntry = {
            message: message,
            response: response,
            timestamp: Date.now(),
            userId: userId,
            metadata: metadata,
            responseTime: metadata.responseTime || 0
        };
        
        this.cache.set(cacheKey, cacheEntry);
        this.accessCount.set(cacheKey, 1);
        this.lastAccess.set(cacheKey, Date.now());
        
        // Ajouter au cache sémantique si c'est une bonne réponse
        if (metadata.quality && metadata.quality > 0.8) {
            this.addToSemanticCache(message, response, metadata);
        }
        
        // Détecter les patterns communs
        this.updateCommonPatterns(message);
        
        console.log(`💾 Réponse mise en cache: ${message.substring(0, 50)}...`);
        
        this.updateStats();
    }

    /**
     * Trouve une correspondance sémantique
     */
    async findSemanticMatch(message) {
        const messageWords = this.extractKeywords(message);
        let bestMatch = null;
        let bestSimilarity = 0;
        
        for (const [cachedMessage, data] of this.semanticCache) {
            const similarity = this.calculateSimilarity(messageWords, data.keywords);
            
            if (similarity > 0.8 && similarity > bestSimilarity) {
                bestMatch = data;
                bestSimilarity = similarity;
            }
        }
        
        if (bestMatch) {
            return {
                response: bestMatch.response,
                similarity: bestSimilarity
            };
        }
        
        return null;
    }

    /**
     * Ajoute au cache sémantique
     */
    addToSemanticCache(message, response, metadata) {
        const keywords = this.extractKeywords(message);
        const semanticKey = this.generateCacheKey(message);
        
        this.semanticCache.set(semanticKey, {
            message: message,
            response: response,
            keywords: keywords,
            timestamp: Date.now(),
            metadata: metadata
        });
        
        // Limiter la taille du cache sémantique
        if (this.semanticCache.size > 500) {
            const oldestKey = Array.from(this.semanticCache.keys())[0];
            this.semanticCache.delete(oldestKey);
        }
    }

    /**
     * Extrait les mots-clés d'un message
     */
    extractKeywords(message) {
        const stopWords = new Set([
            'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais',
            'donc', 'car', 'ni', 'or', 'je', 'tu', 'il', 'elle', 'nous', 'vous',
            'ils', 'elles', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta',
            'tes', 'son', 'sa', 'ses', 'notre', 'votre', 'leur', 'leurs', 'que',
            'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'est',
            'être', 'avoir', 'faire', 'dire', 'aller', 'voir', 'savoir', 'pouvoir',
            'vouloir', 'venir', 'falloir', 'devoir', 'prendre', 'donner', 'mettre'
        ]);
        
        return message
            .toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 2 && !stopWords.has(word))
            .slice(0, 10); // Limiter à 10 mots-clés
    }

    /**
     * Calcule la similarité entre deux ensembles de mots-clés
     */
    calculateSimilarity(keywords1, keywords2) {
        const set1 = new Set(keywords1);
        const set2 = new Set(keywords2);
        
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        return union.size > 0 ? intersection.size / union.size : 0;
    }

    /**
     * Met à jour les patterns communs
     */
    updateCommonPatterns(message) {
        const pattern = this.extractPattern(message);
        if (pattern) {
            const count = this.commonPatterns.get(pattern) || 0;
            this.commonPatterns.set(pattern, count + 1);
            
            // Garder seulement les 100 patterns les plus fréquents
            if (this.commonPatterns.size > 100) {
                const sorted = Array.from(this.commonPatterns.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 100);
                
                this.commonPatterns.clear();
                sorted.forEach(([pattern, count]) => {
                    this.commonPatterns.set(pattern, count);
                });
            }
        }
    }

    /**
     * Extrait un pattern d'un message
     */
    extractPattern(message) {
        const normalized = message.toLowerCase().trim();
        
        // Patterns de questions
        if (normalized.startsWith('comment')) return 'question_comment';
        if (normalized.startsWith('pourquoi')) return 'question_pourquoi';
        if (normalized.startsWith('qu\'est-ce que') || normalized.startsWith('que')) return 'question_definition';
        if (normalized.startsWith('où')) return 'question_lieu';
        if (normalized.startsWith('quand')) return 'question_temps';
        if (normalized.includes('bonjour') || normalized.includes('salut')) return 'salutation';
        if (normalized.includes('merci') || normalized.includes('remercie')) return 'remerciement';
        if (normalized.includes('au revoir') || normalized.includes('bye')) return 'au_revoir';
        
        return null;
    }

    /**
     * Évince l'entrée la moins utilisée
     */
    evictLeastUsed() {
        let leastUsedKey = null;
        let leastUsedCount = Infinity;
        let oldestTime = Date.now();
        
        for (const [key, count] of this.accessCount) {
            const lastAccessTime = this.lastAccess.get(key) || 0;
            
            if (count < leastUsedCount || (count === leastUsedCount && lastAccessTime < oldestTime)) {
                leastUsedKey = key;
                leastUsedCount = count;
                oldestTime = lastAccessTime;
            }
        }
        
        if (leastUsedKey) {
            this.cache.delete(leastUsedKey);
            this.accessCount.delete(leastUsedKey);
            this.lastAccess.delete(leastUsedKey);
            
            console.log(`🗑️ Éviction cache: ${leastUsedKey}`);
        }
    }

    /**
     * Nettoie le cache des entrées expirées
     */
    cleanup() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [key, entry] of this.cache) {
            if (now - entry.timestamp > this.ttl) {
                this.cache.delete(key);
                this.accessCount.delete(key);
                this.lastAccess.delete(key);
                cleanedCount++;
            }
        }
        
        // Nettoyer le cache sémantique
        for (const [key, entry] of this.semanticCache) {
            if (now - entry.timestamp > this.ttl * 2) { // TTL plus long pour le cache sémantique
                this.semanticCache.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 Cache nettoyé: ${cleanedCount} entrées supprimées`);
            this.updateStats();
        }
    }

    /**
     * Met à jour les statistiques
     */
    updateStats() {
        this.stats.cacheEfficiency = this.stats.totalRequests > 0 ? 
            (this.stats.hits / this.stats.totalRequests) * 100 : 0;
        
        this.stats.memoryUsage = this.cache.size + this.semanticCache.size;
        
        // Calculer le temps de réponse moyen
        const responseTimes = Array.from(this.cache.values())
            .map(entry => entry.responseTime || 0)
            .filter(time => time > 0);
        
        this.stats.averageResponseTime = responseTimes.length > 0 ?
            responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
    }

    /**
     * Obtient les statistiques du cache
     */
    getStats() {
        this.updateStats();
        
        return {
            cache: {
                size: this.cache.size,
                maxSize: this.maxCacheSize,
                semanticSize: this.semanticCache.size,
                memoryUsage: this.stats.memoryUsage
            },
            performance: {
                hits: this.stats.hits,
                misses: this.stats.misses,
                totalRequests: this.stats.totalRequests,
                hitRate: this.stats.cacheEfficiency.toFixed(2) + '%',
                averageResponseTime: Math.round(this.stats.averageResponseTime) + 'ms'
            },
            patterns: {
                commonPatterns: Array.from(this.commonPatterns.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 10),
                totalPatterns: this.commonPatterns.size
            }
        };
    }

    /**
     * Vide le cache
     */
    clear() {
        this.cache.clear();
        this.accessCount.clear();
        this.lastAccess.clear();
        this.semanticCache.clear();
        this.commonPatterns.clear();
        
        this.stats = {
            hits: 0,
            misses: 0,
            totalRequests: 0,
            averageResponseTime: 0,
            cacheEfficiency: 0,
            memoryUsage: 0
        };
        
        console.log('🗑️ Cache vidé complètement');
    }

    /**
     * Arrête le système de cache
     */
    stop() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        
        console.log('🛑 Système de cache intelligent arrêté');
    }
}

module.exports = IntelligentCacheSystem;
