#!/bin/bash

echo "🎉 TEST AUTOMATISÉ COMPLET DE TOUTES LES INTERFACES LOUNA"
echo "=========================================================="

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher un succès
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Fonction pour afficher une erreur
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Fonction pour afficher une info
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Fonction pour afficher un warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Vérifier si le serveur est en cours d'exécution
print_info "Vérification du serveur..."
if ! curl -s -o /dev/null -w "%{http_code}" "http://localhost:3004/" | grep -q "200"; then
    print_error "Le serveur n'est pas accessible sur le port 3004"
    exit 1
fi
print_success "Serveur accessible"

echo ""
print_info "Test des interfaces principales..."

# Liste des interfaces à tester
declare -A interfaces=(
    ["index.html"]="Accueil"
    ["presentation.html"]="Présentation"
    ["chat.html"]="Chat"
    ["generation-studio.html"]="Studio de Génération"
    ["futuristic-interface.html"]="Mémoire Thermique"
    ["brain-visualization.html"]="Visualisation 3D"
    ["qi-neuron-monitor.html"]="Monitoring Qi"
    ["kyber-dashboard.html"]="Accélérateurs Kyber"
    ["agent-navigation.html"]="Navigation Agent"
    ["security-dashboard.html"]="Sécurité"
    ["performance.html"]="Performances"
    ["agents.html"]="Gestion Agents"
    ["training.html"]="Formation"
    ["settings.html"]="Paramètres"
)

# Test de chaque interface
success_count=0
total_count=${#interfaces[@]}

for file in "${!interfaces[@]}"; do
    name="${interfaces[$file]}"
    status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3004/$file")
    
    if [ "$status" = "200" ]; then
        print_success "$name ($file)"
        ((success_count++))
    else
        print_error "$name ($file) - Erreur $status"
    fi
done

echo ""
print_info "Résultat des tests d'interfaces: $success_count/$total_count réussies"

echo ""
print_info "Test des API..."

# Test de l'API de sécurité
print_info "Test de l'API de sécurité..."
security_result=$(curl -s http://localhost:3004/api/security/status)
if echo "$security_result" | jq -e '.success' > /dev/null 2>&1; then
    antivirus=$(echo "$security_result" | jq -r '.status.antivirus.active')
    vpn=$(echo "$security_result" | jq -r '.status.vpn.connected')
    firewall=$(echo "$security_result" | jq -r '.status.firewall.active')
    
    if [ "$antivirus" = "true" ] && [ "$vpn" = "true" ] && [ "$firewall" = "true" ]; then
        print_success "API Sécurité - Tous les systèmes actifs"
    else
        print_warning "API Sécurité - Certains systèmes inactifs"
    fi
else
    print_error "API Sécurité - Erreur de réponse"
fi

# Test de l'API de génération
print_info "Test de l'API de génération..."
gen_result=$(curl -s -X POST http://localhost:3004/api/generation/video \
    -H "Content-Type: application/json" \
    -d '{"prompt": "Test automatisé", "duration": 10, "quality": "HD"}')

if echo "$gen_result" | jq -e '.success' > /dev/null 2>&1; then
    video_id=$(echo "$gen_result" | jq -r '.video.id')
    print_success "API Génération - Vidéo générée: $video_id"
else
    print_error "API Génération - Erreur de génération"
fi

# Test de l'API de chat
print_info "Test de l'API de chat..."
chat_result=$(curl -s -X POST http://localhost:3004/api/chat/message \
    -H "Content-Type: application/json" \
    -d '{"message": "Test automatisé des interfaces"}')

if echo "$chat_result" | jq -e '.success' > /dev/null 2>&1; then
    print_success "API Chat - Réponse reçue"
else
    print_error "API Chat - Erreur de réponse"
fi

# Test des statistiques
print_info "Test des statistiques..."
stats_result=$(curl -s http://localhost:3004/api/generation/stats)
if echo "$stats_result" | jq -e '.success' > /dev/null 2>&1; then
    total_gen=$(echo "$stats_result" | jq -r '.stats.totalGenerations')
    video_gen=$(echo "$stats_result" | jq -r '.stats.videoGenerations')
    print_success "API Statistiques - $total_gen générations totales, $video_gen vidéos"
else
    print_error "API Statistiques - Erreur de réponse"
fi

echo ""
print_info "Test de navigation cohérente..."

# Vérifier que les pages contiennent la navigation
nav_test_pages=("index.html" "presentation.html" "generation-studio.html" "security-dashboard.html")
nav_success=0

for page in "${nav_test_pages[@]}"; do
    content=$(curl -s "http://localhost:3004/$page")
    if echo "$content" | grep -q "top-navbar" && echo "$content" | grep -q "nav-links"; then
        print_success "Navigation présente dans $page"
        ((nav_success++))
    else
        print_error "Navigation manquante dans $page"
    fi
done

echo ""
print_info "Résultat des tests de navigation: $nav_success/${#nav_test_pages[@]} réussies"

echo ""
echo "=========================================================="
if [ $success_count -eq $total_count ] && [ $nav_success -eq ${#nav_test_pages[@]} ]; then
    print_success "🎉 TOUS LES TESTS SONT RÉUSSIS !"
    print_success "✅ Toutes les interfaces sont fonctionnelles"
    print_success "✅ Toutes les API répondent correctement"
    print_success "✅ La navigation est cohérente"
    print_success "✅ L'application Louna est 100% opérationnelle !"
else
    print_warning "⚠️  Certains tests ont échoué"
    print_info "Interfaces: $success_count/$total_count"
    print_info "Navigation: $nav_success/${#nav_test_pages[@]}"
fi
echo "=========================================================="
