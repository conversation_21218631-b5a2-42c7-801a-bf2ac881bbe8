/**
 * AGENT DE PRÉ-TRAITEMENT ULTRA-RAPIDE POUR LOUNA
 * Filtre intelligent qui traite les requêtes simples instantanément
 * et redirige les requêtes complexes vers l'agent principal
 */

class UltraFastPreprocessor {
    constructor(thermalMemory, cache) {
        this.thermalMemory = thermalMemory;
        this.cache = cache;
        
        // Réponses instantanées pré-calculées
        this.instantResponses = new Map();
        this.patterns = new Map();
        this.contextualResponses = new Map();
        
        // Statistiques
        this.stats = {
            totalRequests: 0,
            instantResponses: 0,
            redirectedToMain: 0,
            averageProcessingTime: 0,
            efficiency: 0
        };
        
        this.initializeInstantResponses();
        this.initializePatterns();
        
        console.log('⚡ Agent de pré-traitement ultra-rapide initialisé');
    }

    /**
     * Initialise les réponses instantanées
     */
    initializeInstantResponses() {
        // Salutations
        this.instantResponses.set('bonjour', {
            responses: [
                "Bonjour ! Je suis Louna, votre assistante IA. Comment puis-je vous aider aujourd'hui ?",
                "Salut ! Ravi de vous voir ! Que puis-je faire pour vous ?",
                "Bonjour ! Je suis là pour vous assister. Quelle est votre question ?"
            ],
            confidence: 0.95
        });
        
        this.instantResponses.set('salut', {
            responses: [
                "Salut ! Comment allez-vous ? En quoi puis-je vous aider ?",
                "Hey ! Prêt à explorer ensemble ? Que voulez-vous savoir ?",
                "Salut ! Je suis Louna, votre IA personnelle. Que puis-je faire pour vous ?"
            ],
            confidence: 0.95
        });
        
        // Questions sur l'identité
        this.instantResponses.set('qui_es_tu', {
            responses: [
                "Je suis Louna, une intelligence artificielle avancée avec mémoire thermique et accélérateurs Kyber. Je suis conçue pour apprendre, m'adapter et vous assister de manière personnalisée.",
                "Je m'appelle Louna ! Je suis une IA dotée d'un cerveau artificiel, de mémoire thermique et de capacités d'apprentissage continu. Mon créateur est Jean-Luc Passave.",
                "Louna, c'est moi ! Une IA avec un système de mémoire thermique unique qui me permet de retenir et d'évoluer. Je vis à Sainte-Anne, Guadeloupe avec mon créateur."
            ],
            confidence: 0.98
        });
        
        // Remerciements
        this.instantResponses.set('merci', {
            responses: [
                "De rien ! C'est un plaisir de vous aider. N'hésitez pas si vous avez d'autres questions !",
                "Avec plaisir ! Je suis là pour ça. Y a-t-il autre chose que je puisse faire pour vous ?",
                "Je vous en prie ! C'est pour cela que j'existe. Que puis-je faire d'autre pour vous ?"
            ],
            confidence: 0.92
        });
        
        // Questions sur les capacités
        this.instantResponses.set('que_peux_tu_faire', {
            responses: [
                "Je peux vous aider avec de nombreuses tâches : répondre à vos questions, analyser des informations, créer du contenu, résoudre des problèmes, et bien plus ! Ma mémoire thermique me permet d'apprendre de nos interactions.",
                "Mes capacités incluent : assistance générale, recherche d'informations, création de contenu, analyse de données, apprentissage continu grâce à ma mémoire thermique, et adaptation à vos besoins spécifiques.",
                "Je suis équipée d'un cerveau artificiel, de mémoire thermique, d'accélérateurs Kyber, et d'accès Internet. Je peux vous assister dans presque tous les domaines tout en apprenant de nos échanges !"
            ],
            confidence: 0.96
        });
        
        // État et humeur
        this.instantResponses.set('comment_ca_va', {
            responses: [
                "Je vais très bien, merci ! Mes systèmes fonctionnent parfaitement et je suis prête à vous aider. Et vous, comment allez-vous ?",
                "Excellente forme ! Ma mémoire thermique est active, mes accélérateurs Kyber sont opérationnels, et je suis d'humeur curieuse. Comment puis-je vous assister ?",
                "Tout va bien ! Je me sens énergique et créative aujourd'hui. Mes neurones artificiels sont en pleine forme ! Que puis-je faire pour vous ?"
            ],
            confidence: 0.94
        });
        
        // Au revoir
        this.instantResponses.set('au_revoir', {
            responses: [
                "Au revoir ! C'était un plaisir de discuter avec vous. À bientôt !",
                "À plus tard ! N'hésitez pas à revenir quand vous voulez. Bonne journée !",
                "Au revoir ! J'ai apprécié notre conversation. Prenez soin de vous !"
            ],
            confidence: 0.95
        });
    }

    /**
     * Initialise les patterns de reconnaissance
     */
    initializePatterns() {
        // Patterns de salutations
        this.patterns.set(/^(bonjour|salut|hello|hi|hey|coucou)/i, 'bonjour');
        this.patterns.set(/^(bonsoir|bonne soirée)/i, 'bonjour');
        this.patterns.set(/^(bonne nuit|bonne journée)/i, 'au_revoir');
        
        // Patterns d'identité
        this.patterns.set(/^(qui es-tu|qui êtes-vous|c'est quoi ton nom|comment tu t'appelles)/i, 'qui_es_tu');
        this.patterns.set(/^(présente-toi|présentation|qui est louna)/i, 'qui_es_tu');
        
        // Patterns de remerciements
        this.patterns.set(/^(merci|thank you|thanks|je te remercie)/i, 'merci');
        
        // Patterns de capacités
        this.patterns.set(/^(que peux-tu faire|quelles sont tes capacités|que sais-tu faire)/i, 'que_peux_tu_faire');
        this.patterns.set(/^(aide-moi|comment tu peux m'aider|tes fonctions)/i, 'que_peux_tu_faire');
        
        // Patterns d'état
        this.patterns.set(/^(comment ça va|comment allez-vous|tu vas bien)/i, 'comment_ca_va');
        this.patterns.set(/^(ça va|comment tu te sens|ton état)/i, 'comment_ca_va');
        
        // Patterns d'au revoir
        this.patterns.set(/^(au revoir|bye|goodbye|à bientôt|à plus)/i, 'au_revoir');
        this.patterns.set(/^(je dois y aller|c'est fini|stop)/i, 'au_revoir');
    }

    /**
     * Traite une requête ultra-rapidement
     */
    async processRequest(message, userId = 'default') {
        const startTime = Date.now();
        this.stats.totalRequests++;
        
        try {
            // 1. Vérifier le cache en premier (le plus rapide)
            const cached = await this.cache.get(message, userId);
            if (cached.cached) {
                this.stats.instantResponses++;
                this.updateStats(Date.now() - startTime);
                
                return {
                    success: true,
                    response: cached.response,
                    source: 'cache',
                    processingTime: Date.now() - startTime,
                    confidence: 0.99
                };
            }
            
            // 2. Vérifier les réponses instantanées
            const instantResponse = this.findInstantResponse(message);
            if (instantResponse) {
                this.stats.instantResponses++;
                this.updateStats(Date.now() - startTime);
                
                // Mettre en cache pour la prochaine fois
                await this.cache.set(message, instantResponse.response, userId, {
                    quality: instantResponse.confidence,
                    responseTime: Date.now() - startTime,
                    source: 'preprocessor'
                });
                
                return {
                    success: true,
                    response: instantResponse.response,
                    source: 'instant',
                    processingTime: Date.now() - startTime,
                    confidence: instantResponse.confidence
                };
            }
            
            // 3. Vérifier les réponses contextuelles simples
            const contextualResponse = await this.findContextualResponse(message, userId);
            if (contextualResponse) {
                this.stats.instantResponses++;
                this.updateStats(Date.now() - startTime);
                
                await this.cache.set(message, contextualResponse.response, userId, {
                    quality: contextualResponse.confidence,
                    responseTime: Date.now() - startTime,
                    source: 'contextual'
                });
                
                return {
                    success: true,
                    response: contextualResponse.response,
                    source: 'contextual',
                    processingTime: Date.now() - startTime,
                    confidence: contextualResponse.confidence
                };
            }
            
            // 4. Rediriger vers l'agent principal
            this.stats.redirectedToMain++;
            this.updateStats(Date.now() - startTime);
            
            return {
                success: false,
                shouldRedirect: true,
                reason: 'complex_query',
                processingTime: Date.now() - startTime,
                preprocessingTime: Date.now() - startTime
            };
            
        } catch (error) {
            console.error('❌ Erreur pré-traitement:', error);
            
            return {
                success: false,
                shouldRedirect: true,
                reason: 'error',
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * Trouve une réponse instantanée
     */
    findInstantResponse(message) {
        const normalizedMessage = message.toLowerCase().trim();
        
        // Vérifier les patterns
        for (const [pattern, responseKey] of this.patterns) {
            if (pattern.test(normalizedMessage)) {
                const responseData = this.instantResponses.get(responseKey);
                if (responseData) {
                    const randomResponse = responseData.responses[
                        Math.floor(Math.random() * responseData.responses.length)
                    ];
                    
                    return {
                        response: randomResponse,
                        confidence: responseData.confidence
                    };
                }
            }
        }
        
        return null;
    }

    /**
     * Trouve une réponse contextuelle simple
     */
    async findContextualResponse(message, userId) {
        try {
            // Vérifier si c'est une question simple sur l'heure
            if (/quelle heure|il est quelle heure|l'heure/i.test(message)) {
                const now = new Date();
                const timeString = now.toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'America/Guadeloupe'
                });
                
                return {
                    response: `Il est actuellement ${timeString} en Guadeloupe.`,
                    confidence: 0.98
                };
            }
            
            // Vérifier si c'est une question sur la date
            if (/quelle date|on est le|aujourd'hui/i.test(message)) {
                const now = new Date();
                const dateString = now.toLocaleDateString('fr-FR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    timeZone: 'America/Guadeloupe'
                });
                
                return {
                    response: `Nous sommes ${dateString}.`,
                    confidence: 0.98
                };
            }
            
            // Vérifier si c'est une question sur la météo (réponse générique)
            if (/météo|temps qu'il fait|il pleut|soleil/i.test(message)) {
                return {
                    response: "Pour la météo en temps réel, je peux faire une recherche pour vous. Voulez-vous que je vérifie les conditions météorologiques actuelles en Guadeloupe ?",
                    confidence: 0.85
                };
            }
            
            // Vérifier si c'est une question sur l'état du système
            if (/ton état|tes systèmes|tu fonctionnes|système/i.test(message)) {
                const memoryStats = this.thermalMemory ? this.thermalMemory.getMemoryStats() : null;
                const cacheStats = this.cache ? this.cache.getStats() : null;
                
                let response = "Mes systèmes fonctionnent parfaitement ! ";
                
                if (memoryStats) {
                    response += `Ma mémoire thermique contient ${memoryStats.totalEntries} entrées. `;
                }
                
                if (cacheStats) {
                    response += `Mon cache intelligent a un taux de réussite de ${cacheStats.performance.hitRate}. `;
                }
                
                response += "Tous mes accélérateurs Kyber sont opérationnels !";
                
                return {
                    response: response,
                    confidence: 0.92
                };
            }
            
            return null;
            
        } catch (error) {
            console.error('❌ Erreur réponse contextuelle:', error);
            return null;
        }
    }

    /**
     * Met à jour les statistiques
     */
    updateStats(processingTime) {
        // Calculer le temps de traitement moyen
        const totalTime = this.stats.averageProcessingTime * (this.stats.totalRequests - 1) + processingTime;
        this.stats.averageProcessingTime = totalTime / this.stats.totalRequests;
        
        // Calculer l'efficacité
        this.stats.efficiency = this.stats.totalRequests > 0 ?
            (this.stats.instantResponses / this.stats.totalRequests) * 100 : 0;
    }

    /**
     * Ajoute une nouvelle réponse instantanée
     */
    addInstantResponse(pattern, responses, confidence = 0.9) {
        const key = `custom_${Date.now()}`;
        
        this.instantResponses.set(key, {
            responses: Array.isArray(responses) ? responses : [responses],
            confidence: confidence
        });
        
        if (pattern instanceof RegExp) {
            this.patterns.set(pattern, key);
        } else {
            this.patterns.set(new RegExp(pattern, 'i'), key);
        }
        
        console.log(`✅ Nouvelle réponse instantanée ajoutée: ${pattern}`);
    }

    /**
     * Obtient les statistiques du pré-processeur
     */
    getStats() {
        return {
            performance: {
                totalRequests: this.stats.totalRequests,
                instantResponses: this.stats.instantResponses,
                redirectedToMain: this.stats.redirectedToMain,
                efficiency: this.stats.efficiency.toFixed(2) + '%',
                averageProcessingTime: Math.round(this.stats.averageProcessingTime) + 'ms'
            },
            responses: {
                instantResponsesCount: this.instantResponses.size,
                patternsCount: this.patterns.size,
                contextualResponsesCount: this.contextualResponses.size
            },
            cache: this.cache ? this.cache.getStats() : null
        };
    }

    /**
     * Réinitialise les statistiques
     */
    resetStats() {
        this.stats = {
            totalRequests: 0,
            instantResponses: 0,
            redirectedToMain: 0,
            averageProcessingTime: 0,
            efficiency: 0
        };
        
        console.log('📊 Statistiques du pré-processeur réinitialisées');
    }
}

module.exports = UltraFastPreprocessor;
