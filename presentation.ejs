<!-- Interface de présentation -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-easel card-icon"></i>
    <h2 class="card-title">Présentation de Louna</h2>
  </div>
  
  <div style="padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="font-size: 36px; font-weight: bold; margin-bottom: 10px; color: var(--header-bg);">Louna</div>
      <div style="font-size: 18px; color: var(--text-secondary);">Système cognitif avancé avec mémoire thermique</div>
    </div>
    
    <div class="grid-container">
      <div class="grid-item">
        <div class="feature-card" style="background-color: rgba(255, 107, 107, 0.1); padding: 20px; border-radius: 10px; height: 100%;">
          <i class="bi bi-thermometer-half" style="font-size: 36px; color: var(--temp-hot); margin-bottom: 15px;"></i>
          <h3 style="margin-bottom: 10px;">Mémoire Thermique</h3>
          <p>Système de mémoire inspiré du cerveau humain, organisant les informations en zones de différentes "températures" pour une gestion optimale des connaissances.</p>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="feature-card" style="background-color: rgba(29, 209, 161, 0.1); padding: 20px; border-radius: 10px; height: 100%;">
          <i class="bi bi-lightning" style="font-size: 36px; color: #1dd1a1; margin-bottom: 15px;"></i>
          <h3 style="margin-bottom: 10px;">Accélérateurs</h3>
          <p>Modules d'optimisation qui améliorent les performances du système, permettant un traitement plus rapide et plus efficace des informations.</p>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="feature-card" style="background-color: rgba(84, 160, 255, 0.1); padding: 20px; border-radius: 10px; height: 100%;">
          <i class="bi bi-cpu" style="font-size: 36px; color: #54a0ff; margin-bottom: 15px;"></i>
          <h3 style="margin-bottom: 10px;">Cerveau Artificiel</h3>
          <p>Architecture cognitive avancée qui simule les processus de pensée humains, permettant une compréhension contextuelle et une adaptation intelligente.</p>
        </div>
      </div>
    </div>
    
    <div style="margin: 30px 0;">
      <h3 style="margin-bottom: 15px;">Fonctionnalités principales</h3>
      
      <div class="feature-list">
        <div class="feature-item" style="display: flex; align-items: center; margin-bottom: 15px;">
          <i class="bi bi-check-circle-fill" style="color: var(--accent-color); margin-right: 10px; font-size: 20px;"></i>
          <div>
            <div style="font-weight: bold;">Mémoire thermique à 6 zones</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Organisation des informations selon leur importance et leur temporalité</div>
          </div>
        </div>
        
        <div class="feature-item" style="display: flex; align-items: center; margin-bottom: 15px;">
          <i class="bi bi-check-circle-fill" style="color: var(--accent-color); margin-right: 10px; font-size: 20px;"></i>
          <div>
            <div style="font-weight: bold;">Accélérateurs cognitifs</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Amélioration des performances de traitement et d'analyse</div>
          </div>
        </div>
        
        <div class="feature-item" style="display: flex; align-items: center; margin-bottom: 15px;">
          <i class="bi bi-check-circle-fill" style="color: var(--accent-color); margin-right: 10px; font-size: 20px;"></i>
          <div>
            <div style="font-weight: bold;">Interface unifiée</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Navigation fluide entre les différentes fonctionnalités</div>
          </div>
        </div>
        
        <div class="feature-item" style="display: flex; align-items: center; margin-bottom: 15px;">
          <i class="bi bi-check-circle-fill" style="color: var(--accent-color); margin-right: 10px; font-size: 20px;"></i>
          <div>
            <div style="font-weight: bold;">Système de chat avancé</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Communication naturelle avec l'utilisateur</div>
          </div>
        </div>
        
        <div class="feature-item" style="display: flex; align-items: center; margin-bottom: 15px;">
          <i class="bi bi-check-circle-fill" style="color: var(--accent-color); margin-right: 10px; font-size: 20px;"></i>
          <div>
            <div style="font-weight: bold;">Tableau de bord système</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Monitoring en temps réel des performances et de l'état du système</div>
          </div>
        </div>
      </div>
    </div>
    
    <div style="margin: 30px 0;">
      <h3 style="margin-bottom: 15px;">Architecture du système</h3>
      
      <div style="background-color: rgba(255, 255, 255, 0.05); padding: 20px; border-radius: 10px;">
        <div class="architecture-diagram" style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 20px;">
          <div class="architecture-component" style="flex: 1; min-width: 200px; text-align: center; padding: 15px; background-color: rgba(255, 107, 107, 0.1); border-radius: 5px;">
            <div style="font-weight: bold; margin-bottom: 10px;">Interface Utilisateur</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Interaction avec l'utilisateur</div>
          </div>
          
          <div class="architecture-arrow" style="display: flex; align-items: center; justify-content: center; padding: 0 10px;">
            <i class="bi bi-arrow-right"></i>
          </div>
          
          <div class="architecture-component" style="flex: 1; min-width: 200px; text-align: center; padding: 15px; background-color: rgba(29, 209, 161, 0.1); border-radius: 5px;">
            <div style="font-weight: bold; margin-bottom: 10px;">Cerveau Artificiel</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Traitement cognitif</div>
          </div>
          
          <div class="architecture-arrow" style="display: flex; align-items: center; justify-content: center; padding: 0 10px;">
            <i class="bi bi-arrow-right"></i>
          </div>
          
          <div class="architecture-component" style="flex: 1; min-width: 200px; text-align: center; padding: 15px; background-color: rgba(84, 160, 255, 0.1); border-radius: 5px;">
            <div style="font-weight: bold; margin-bottom: 10px;">Mémoire Thermique</div>
            <div style="font-size: 14px; color: var(--text-secondary);">Stockage et gestion des connaissances</div>
          </div>
        </div>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px;">
      <button class="action-button" style="padding: 10px 20px; font-size: 16px;">
        <i class="bi bi-play-fill"></i> Démarrer une conversation
      </button>
    </div>
  </div>
</div>
