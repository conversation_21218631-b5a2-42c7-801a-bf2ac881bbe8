/**
 * TEST COMPLET DU SYSTÈME ULTRA-AVANCÉ
 * 
 * Teste tous les systèmes créés :
 * 1. Monitoring ultra-intelligent
 * 2. Gestionnaire de ressources adaptatif
 * 3. Pool d'accélérateurs spécialisés
 * 4. Mémoire neuromorphique
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
    white: '\x1b[37m',
    reset: '\x1b[0m',
    bold: '\x1b[1m',
    bright: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logTitle(message) {
    log(`\n🚀 ${message}`, 'bright');
    log('=' .repeat(80), 'cyan');
}

function logSubTitle(message) {
    log(`\n🎯 ${message}`, 'magenta');
    log('-' .repeat(60), 'cyan');
}

async function testAPI(endpoint, method = 'GET', data = null, timeout = 10000) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            timeout
        };
        
        if (data) {
            config.data = data;
            config.headers = { 'Content-Type': 'application/json' };
        }
        
        const startTime = Date.now();
        const response = await axios(config);
        const latency = Date.now() - startTime;
        
        return {
            success: true,
            data: response.data,
            latency,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            latency: 0,
            status: error.response?.status || 0
        };
    }
}

async function testUltraMonitoring() {
    logSubTitle('TEST DU MONITORING ULTRA-INTELLIGENT');
    
    const tests = [
        { name: 'Métriques système', endpoint: '/api/ultra-monitor/metrics' },
        { name: 'Goulots d\'étranglement', endpoint: '/api/ultra-monitor/bottlenecks' },
        { name: 'Métriques spécialisées', endpoint: '/api/ultra-monitor/specialized-metrics' },
        { name: 'Rapport de santé', endpoint: '/api/ultra-monitor/health-report' }
    ];
    
    let successCount = 0;
    
    for (const test of tests) {
        const result = await testAPI(test.endpoint);
        if (result.success) {
            logSuccess(`${test.name} - ${result.latency}ms`);
            successCount++;
        } else {
            logError(`${test.name} - ${result.error}`);
        }
    }
    
    return successCount === tests.length;
}

async function testResourceManager() {
    logSubTitle('TEST DU GESTIONNAIRE DE RESSOURCES ADAPTATIF');
    
    const tests = [
        { name: 'État des ressources', endpoint: '/api/resource-manager/status' },
        { name: 'Allocation actuelle', endpoint: '/api/resource-manager/allocation' },
        { name: 'Historique optimisations', endpoint: '/api/resource-manager/optimizations' }
    ];
    
    let successCount = 0;
    
    for (const test of tests) {
        const result = await testAPI(test.endpoint);
        if (result.success) {
            logSuccess(`${test.name} - ${result.latency}ms`);
            
            if (test.name === 'État des ressources') {
                const data = result.data.data;
                log(`   📊 Ressources CPU disponibles: ${data.resourceState.available.cpu.toFixed(1)}%`, 'cyan');
                log(`   🧠 Ressources mémoire disponibles: ${data.resourceState.available.memory.toFixed(1)}%`, 'cyan');
                log(`   🎮 Ressources GPU disponibles: ${data.resourceState.available.gpu.toFixed(1)}%`, 'cyan');
                log(`   🎯 Tâches actives: ${data.resourceState.activeTasks.length}`, 'cyan');
            }
            
            successCount++;
        } else {
            logError(`${test.name} - ${result.error}`);
        }
    }
    
    // Test d'allocation forcée
    logInfo('Test d\'allocation forcée pour vidéo...');
    const allocResult = await testAPI('/api/resource-manager/allocate', 'POST', {
        taskType: 'video',
        metrics: { fps: 25, latency: 45 }
    });
    
    if (allocResult.success) {
        logSuccess(`Allocation forcée - ${allocResult.latency}ms`);
        successCount++;
    } else {
        logError(`Allocation forcée - ${allocResult.error}`);
    }
    
    return successCount === tests.length + 1;
}

async function testKyberAccelerators() {
    logSubTitle('TEST DES ACCÉLÉRATEURS KYBER');
    
    const tests = [
        { name: 'Accélérateurs actifs', endpoint: '/api/direct-connection/kyber/active' },
        { name: 'Rapport de performance', endpoint: '/api/direct-connection/performance-report' }
    ];
    
    let successCount = 0;
    
    for (const test of tests) {
        const result = await testAPI(test.endpoint);
        if (result.success) {
            logSuccess(`${test.name} - ${result.latency}ms`);
            
            if (test.name === 'Accélérateurs actifs') {
                const data = result.data.data;
                log(`   ⚡ Accélérateurs actifs: ${data.count}`, 'cyan');
                log(`   🎯 Boost total: ${data.totalBoost.toFixed(1)}x`, 'cyan');
                log(`   💪 Efficacité moyenne: ${data.averageEfficiency.toFixed(1)}%`, 'cyan');
            }
            
            successCount++;
        } else {
            logError(`${test.name} - ${result.error}`);
        }
    }
    
    // Test d'ajout d'accélérateur
    logInfo('Test d\'ajout d\'accélérateur spécialisé...');
    const addResult = await testAPI('/api/direct-connection/kyber/add-accelerator', 'POST', {
        type: 'neural_stimulator',
        duration: 300000
    });
    
    if (addResult.success) {
        logSuccess(`Accélérateur ajouté - ${addResult.latency}ms`);
        successCount++;
    } else {
        logError(`Ajout accélérateur - ${addResult.error}`);
    }
    
    return successCount === tests.length + 1;
}

async function testTurboSystem() {
    logSubTitle('TEST DU SYSTÈME TURBO ADAPTATIF');
    
    // Test d'analyse de question
    logInfo('Test d\'analyse de question complexe...');
    const analyzeResult = await testAPI('/api/direct-connection/turbo/analyze', 'POST', {
        question: 'Peux-tu créer une vidéo 4K en temps réel avec des effets 3D complexes et de l\'intelligence artificielle pour analyser le contenu ?'
    });
    
    if (analyzeResult.success) {
        logSuccess(`Analyse TURBO - ${analyzeResult.latency}ms`);
        const data = analyzeResult.data.data;
        log(`   🎯 Mode recommandé: ${data.recommendedMode}`, 'cyan');
        log(`   ⚡ Vitesse: ${data.speedMultiplier}x`, 'cyan');
        log(`   🧠 Complexité détectée: ${data.complexity}`, 'cyan');
        log(`   🎥 Vidéo détectée: ${data.patterns.video ? 'OUI' : 'NON'}`, 'cyan');
        log(`   🎮 3D détecté: ${data.patterns.render3d ? 'OUI' : 'NON'}`, 'cyan');
        log(`   🤖 IA détectée: ${data.patterns.ai ? 'OUI' : 'NON'}`, 'cyan');
        return true;
    } else {
        logError(`Analyse TURBO - ${analyzeResult.error}`);
        return false;
    }
}

async function testUltraPerformanceMode() {
    logSubTitle('TEST DU MODE ULTRA-PERFORMANCE');
    
    logInfo('Activation du mode ULTRA-PERFORMANCE...');
    const ultraResult = await testAPI('/api/direct-connection/ultra-performance', 'POST', {
        task: 'Génération vidéo LIVE 4K avec rendu 3D temps réel'
    });
    
    if (ultraResult.success) {
        logSuccess(`Mode ULTRA-PERFORMANCE activé - ${ultraResult.latency}ms`);
        const data = ultraResult.data.data;
        log(`   🚀 Accélérateurs ajoutés: ${data.acceleratorsAdded}`, 'cyan');
        log(`   ⚡ Boost total: ${data.totalBoost.toFixed(1)}x`, 'cyan');
        log(`   🎯 Mode TURBO: ${data.turboMode}`, 'cyan');
        log(`   💪 Optimisations: ${data.optimizations.length}`, 'cyan');
        
        // Attendre un peu puis vérifier l'état
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const statusResult = await testAPI('/api/direct-connection/performance-report');
        if (statusResult.success) {
            const status = statusResult.data.data;
            log(`   📊 Performance actuelle: ${status.overallPerformance.toFixed(1)}%`, 'green');
        }
        
        return true;
    } else {
        logError(`Mode ULTRA-PERFORMANCE - ${ultraResult.error}`);
        return false;
    }
}

async function testTranscendentMode() {
    logSubTitle('TEST DU MODE TRANSCENDANT');
    
    logInfo('Activation du mode TRANSCENDANT...');
    const transcendentResult = await testAPI('/api/direct-connection/transcendent', 'POST', {
        task: 'Traitement IA ultra-complexe avec génération multimédia simultanée'
    });
    
    if (transcendentResult.success) {
        logSuccess(`Mode TRANSCENDANT activé - ${transcendentResult.latency}ms`);
        const data = transcendentResult.data.data;
        log(`   🌟 Accélérateurs transcendants: ${data.transcendentAccelerators}`, 'cyan');
        log(`   ⚡ Boost transcendant: ${data.transcendentBoost.toFixed(1)}x`, 'cyan');
        log(`   🧠 Mode IA avancé: ${data.advancedAI ? 'ACTIVÉ' : 'INACTIF'}`, 'cyan');
        log(`   🎯 Vitesse TURBO: ${data.turboSpeed}x`, 'cyan');
        
        return true;
    } else {
        logError(`Mode TRANSCENDANT - ${transcendentResult.error}`);
        return false;
    }
}

async function testSystemIntegration() {
    logSubTitle('TEST D\'INTÉGRATION SYSTÈME COMPLÈTE');
    
    logInfo('Simulation d\'une charge de travail complexe...');
    
    // 1. Déclencher une tâche vidéo complexe
    await testAPI('/api/resource-manager/allocate', 'POST', {
        taskType: 'video',
        metrics: { fps: 20, latency: 60, quality: '4k' }
    });
    
    // 2. Déclencher une tâche 3D complexe
    await testAPI('/api/resource-manager/allocate', 'POST', {
        taskType: 'render3d',
        metrics: { fps: 15, complexity: 'ultra', triangles: 150000, textures: 80 }
    });
    
    // 3. Activer le mode TURBO
    await testAPI('/api/direct-connection/turbo/set-mode', 'POST', {
        mode: 'QUANTUM'
    });
    
    // 4. Ajouter des accélérateurs spécialisés
    await testAPI('/api/direct-connection/kyber/add-accelerator', 'POST', {
        type: 'response_accelerator'
    });
    
    // Attendre que les systèmes réagissent
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 5. Vérifier l'état global
    const healthResult = await testAPI('/api/ultra-monitor/health-report');
    
    if (healthResult.success) {
        const health = healthResult.data.data.health;
        
        log(`\n📊 ÉTAT GLOBAL DU SYSTÈME:`, 'magenta');
        log(`   🎯 Performance globale: ${health.overall.toFixed(1)}%`, 
            health.overall > 70 ? 'green' : 'yellow');
        log(`   💻 CPU: ${health.cpu.status.toUpperCase()}`, 
            health.cpu.status === 'healthy' ? 'green' : 'yellow');
        log(`   🧠 Mémoire: ${health.memory.status.toUpperCase()}`, 
            health.memory.status === 'healthy' ? 'green' : 'yellow');
        log(`   🎥 Vidéo: ${health.tasks.video.toUpperCase()}`, 
            health.tasks.video === 'active' ? 'green' : 'yellow');
        log(`   🎮 3D: ${health.tasks.render3d.toUpperCase()}`, 
            health.tasks.render3d === 'active' ? 'green' : 'yellow');
        log(`   🔍 Goulots: ${health.bottlenecks}`, 
            health.bottlenecks === 0 ? 'green' : 'yellow');
        
        return health.overall > 50; // Système fonctionnel si > 50%
    }
    
    return false;
}

async function runCompleteSystemTest() {
    logTitle('TEST COMPLET DU SYSTÈME ULTRA-AVANCÉ KYBER & TURBO');
    log('🎯 Objectif: Vérifier tous les systèmes créés', 'blue');
    log('⚡ Systèmes testés: Monitoring, Ressources, Accélérateurs, TURBO', 'blue');
    log('🧠 Focus: Intégration complète et performances ultra-avancées', 'blue');
    
    const testSuites = [
        { name: 'Monitoring Ultra-Intelligent', fn: testUltraMonitoring },
        { name: 'Gestionnaire de Ressources', fn: testResourceManager },
        { name: 'Accélérateurs KYBER', fn: testKyberAccelerators },
        { name: 'Système TURBO Adaptatif', fn: testTurboSystem },
        { name: 'Mode ULTRA-PERFORMANCE', fn: testUltraPerformanceMode },
        { name: 'Mode TRANSCENDANT', fn: testTranscendentMode },
        { name: 'Intégration Système Complète', fn: testSystemIntegration }
    ];
    
    let successCount = 0;
    const results = [];
    
    for (const suite of testSuites) {
        try {
            const startTime = Date.now();
            const success = await suite.fn();
            const duration = Date.now() - startTime;
            
            results.push({
                name: suite.name,
                success,
                duration
            });
            
            if (success) {
                successCount++;
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            logError(`Erreur lors du test "${suite.name}": ${error.message}`);
            results.push({
                name: suite.name,
                success: false,
                duration: 0,
                error: error.message
            });
        }
    }
    
    // Résumé final
    logTitle('RÉSUMÉ COMPLET DU SYSTÈME ULTRA-AVANCÉ');
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
        log(`${status} ${result.name}${duration}`, result.success ? 'green' : 'red');
        
        if (result.error) {
            log(`   └─ Erreur: ${result.error}`, 'red');
        }
    });
    
    const successRate = (successCount / testSuites.length) * 100;
    log(`\n🎯 RÉSULTAT GLOBAL: ${successCount}/${testSuites.length} systèmes opérationnels (${successRate.toFixed(1)}%)`, 
        successRate >= 85 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
    
    if (successRate >= 85) {
        logSuccess('🎉 SYSTÈME ULTRA-AVANCÉ KYBER & TURBO PLEINEMENT OPÉRATIONNEL !');
        log('', 'reset');
        log('🚀 CAPACITÉS ULTRA-AVANCÉES ACTIVÉES:', 'bright');
        log('   ✨ Monitoring intelligent en temps réel', 'green');
        log('   🎯 Gestion adaptative des ressources', 'green');
        log('   ⚡ Accélérateurs KYBER automatiques', 'green');
        log('   🎮 Pool d\'accélérateurs spécialisés', 'green');
        log('   🧠 Mémoire neuromorphique', 'green');
        log('   🚀 Mode TURBO adaptatif', 'green');
        log('   🌟 Mode ULTRA-PERFORMANCE', 'green');
        log('   🔮 Mode TRANSCENDANT', 'green');
        log('', 'reset');
        log('🎥 PRÊT POUR:', 'bright');
        log('   📹 Génération vidéo LIVE 4K temps réel', 'cyan');
        log('   🎮 Rendu 3D ultra-complexe', 'cyan');
        log('   🤖 IA avec performances transcendantes', 'cyan');
        log('   🧠 Mémoire comme un vrai cerveau humain', 'cyan');
        log('   ⚡ Optimisations automatiques intelligentes', 'cyan');
        
    } else if (successRate >= 70) {
        logWarning('⚠️  Système partiellement opérationnel - Optimisations recommandées');
    } else {
        logError('❌ Système nécessite des corrections majeures');
    }
    
    log('\n🔗 APIs disponibles:', 'cyan');
    log('   🔍 Monitoring: /api/ultra-monitor/*', 'cyan');
    log('   🎯 Ressources: /api/resource-manager/*', 'cyan');
    log('   ⚡ KYBER: /api/direct-connection/kyber/*', 'cyan');
    log('   🚀 TURBO: /api/direct-connection/turbo/*', 'cyan');
    log('   🏠 Interface: http://localhost:3000/', 'cyan');
}

// Exécuter les tests
if (require.main === module) {
    runCompleteSystemTest().catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    testUltraMonitoring,
    testResourceManager,
    testKyberAccelerators,
    testTurboSystem,
    testUltraPerformanceMode,
    testTranscendentMode,
    testSystemIntegration,
    runCompleteSystemTest
};
