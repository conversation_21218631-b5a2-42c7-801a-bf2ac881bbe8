/**
 * Test de l'accès Internet pour l'agent Louna
 * Ce script teste si l'agent peut accéder à Internet et effectuer des recherches
 */

const axios = require('axios');

class InternetAccessTester {
    constructor() {
        this.testResults = {
            basicConnectivity: false,
            httpRequests: false,
            webSearch: false,
            apiAccess: false,
            errors: []
        };
    }

    /**
     * Test de connectivité de base
     */
    async testBasicConnectivity() {
        try {
            console.log('🔍 Test de connectivité de base...');
            
            const response = await axios.get('https://httpbin.org/ip', {
                timeout: 10000
            });
            
            if (response.status === 200 && response.data.origin) {
                console.log(`✅ Connectivité OK - IP: ${response.data.origin}`);
                this.testResults.basicConnectivity = true;
                return true;
            }
            
            return false;
        } catch (error) {
            console.log(`❌ Erreur connectivité: ${error.message}`);
            this.testResults.errors.push(`Connectivité: ${error.message}`);
            return false;
        }
    }

    /**
     * Test des requêtes HTTP
     */
    async testHttpRequests() {
        try {
            console.log('🌐 Test des requêtes HTTP...');
            
            const tests = [
                'https://jsonplaceholder.typicode.com/posts/1',
                'https://api.github.com/users/octocat',
                'https://httpbin.org/json'
            ];
            
            let successCount = 0;
            
            for (const url of tests) {
                try {
                    const response = await axios.get(url, { timeout: 10000 });
                    if (response.status === 200) {
                        successCount++;
                        console.log(`✅ ${url} - OK`);
                    }
                } catch (error) {
                    console.log(`❌ ${url} - Erreur: ${error.message}`);
                    this.testResults.errors.push(`HTTP ${url}: ${error.message}`);
                }
            }
            
            if (successCount >= 2) {
                this.testResults.httpRequests = true;
                console.log(`✅ Requêtes HTTP OK (${successCount}/${tests.length})`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.log(`❌ Erreur test HTTP: ${error.message}`);
            this.testResults.errors.push(`HTTP: ${error.message}`);
            return false;
        }
    }

    /**
     * Test de recherche web (simulation)
     */
    async testWebSearch() {
        try {
            console.log('🔍 Test de recherche web...');
            
            // Test avec l'API DuckDuckGo (plus simple que Google)
            const searchQuery = 'artificial intelligence';
            const response = await axios.get(`https://api.duckduckgo.com/?q=${encodeURIComponent(searchQuery)}&format=json&no_html=1&skip_disambig=1`, {
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }
            });
            
            if (response.status === 200) {
                console.log(`✅ Recherche web OK - Requête: "${searchQuery}"`);
                this.testResults.webSearch = true;
                return true;
            }
            
            return false;
        } catch (error) {
            console.log(`❌ Erreur recherche web: ${error.message}`);
            this.testResults.errors.push(`Recherche: ${error.message}`);
            return false;
        }
    }

    /**
     * Test d'accès aux APIs
     */
    async testApiAccess() {
        try {
            console.log('🔌 Test d\'accès aux APIs...');
            
            const apis = [
                {
                    name: 'OpenWeatherMap (sans clé)',
                    url: 'https://api.openweathermap.org/data/2.5/weather?q=Paris'
                },
                {
                    name: 'REST Countries',
                    url: 'https://restcountries.com/v3.1/name/france'
                },
                {
                    name: 'JSONPlaceholder',
                    url: 'https://jsonplaceholder.typicode.com/users'
                }
            ];
            
            let successCount = 0;
            
            for (const api of apis) {
                try {
                    const response = await axios.get(api.url, { timeout: 10000 });
                    if (response.status === 200) {
                        successCount++;
                        console.log(`✅ ${api.name} - OK`);
                    }
                } catch (error) {
                    console.log(`❌ ${api.name} - Erreur: ${error.message}`);
                    this.testResults.errors.push(`API ${api.name}: ${error.message}`);
                }
            }
            
            if (successCount >= 2) {
                this.testResults.apiAccess = true;
                console.log(`✅ Accès APIs OK (${successCount}/${apis.length})`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.log(`❌ Erreur test APIs: ${error.message}`);
            this.testResults.errors.push(`APIs: ${error.message}`);
            return false;
        }
    }

    /**
     * Test de l'agent Louna spécifiquement
     */
    async testLounaAgent() {
        try {
            console.log('🤖 Test de l\'agent Louna...');
            
            // Vérifier si l'agent est accessible
            const agentResponse = await axios.get('http://localhost:3007/api/agent/status', {
                timeout: 5000
            }).catch(() => null);
            
            if (agentResponse && agentResponse.status === 200) {
                console.log('✅ Agent Louna accessible');
                
                // Test de chat avec l'agent
                const chatResponse = await axios.post('http://localhost:3007/api/chat', {
                    message: 'Peux-tu faire une recherche sur Internet pour moi ?',
                    useInternet: true
                }, {
                    timeout: 30000
                }).catch(() => null);
                
                if (chatResponse && chatResponse.status === 200) {
                    console.log('✅ Chat avec agent OK');
                    return true;
                }
            }
            
            console.log('⚠️ Agent Louna non accessible ou non fonctionnel');
            return false;
        } catch (error) {
            console.log(`❌ Erreur test agent: ${error.message}`);
            this.testResults.errors.push(`Agent: ${error.message}`);
            return false;
        }
    }

    /**
     * Exécute tous les tests
     */
    async runAllTests() {
        console.log('🚀 Démarrage des tests d\'accès Internet pour Louna...\n');
        
        const tests = [
            { name: 'Connectivité de base', method: this.testBasicConnectivity },
            { name: 'Requêtes HTTP', method: this.testHttpRequests },
            { name: 'Recherche web', method: this.testWebSearch },
            { name: 'Accès APIs', method: this.testApiAccess },
            { name: 'Agent Louna', method: this.testLounaAgent }
        ];
        
        for (const test of tests) {
            console.log(`\n--- ${test.name} ---`);
            await test.method.call(this);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Pause entre tests
        }
        
        this.displayResults();
    }

    /**
     * Affiche les résultats des tests
     */
    displayResults() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 RÉSULTATS DES TESTS D\'ACCÈS INTERNET');
        console.log('='.repeat(50));
        
        const results = [
            { name: 'Connectivité de base', status: this.testResults.basicConnectivity },
            { name: 'Requêtes HTTP', status: this.testResults.httpRequests },
            { name: 'Recherche web', status: this.testResults.webSearch },
            { name: 'Accès APIs', status: this.testResults.apiAccess }
        ];
        
        results.forEach(result => {
            const icon = result.status ? '✅' : '❌';
            const status = result.status ? 'SUCCÈS' : 'ÉCHEC';
            console.log(`${icon} ${result.name}: ${status}`);
        });
        
        const successCount = results.filter(r => r.status).length;
        const totalTests = results.length;
        const successRate = (successCount / totalTests * 100).toFixed(1);
        
        console.log('\n' + '-'.repeat(50));
        console.log(`📈 Taux de réussite: ${successCount}/${totalTests} (${successRate}%)`);
        
        if (this.testResults.errors.length > 0) {
            console.log('\n🚨 ERREURS DÉTECTÉES:');
            this.testResults.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
        
        console.log('\n💡 RECOMMANDATIONS:');
        if (!this.testResults.basicConnectivity) {
            console.log('- Vérifiez votre connexion Internet');
            console.log('- Vérifiez les paramètres de proxy/firewall');
        }
        if (!this.testResults.httpRequests) {
            console.log('- Vérifiez les certificats SSL');
            console.log('- Vérifiez les paramètres de sécurité');
        }
        if (!this.testResults.webSearch) {
            console.log('- Les moteurs de recherche peuvent bloquer les requêtes automatisées');
            console.log('- Utilisez des APIs de recherche dédiées');
        }
        if (!this.testResults.apiAccess) {
            console.log('- Certaines APIs nécessitent des clés d\'authentification');
            console.log('- Vérifiez les limites de taux des APIs');
        }
        
        console.log('='.repeat(50));
    }
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
    const tester = new InternetAccessTester();
    tester.runAllTests().catch(error => {
        console.error('❌ Erreur lors des tests:', error);
        process.exit(1);
    });
}

module.exports = InternetAccessTester;
