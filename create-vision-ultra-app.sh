#!/bin/bash

# Script pour créer une application Vision Ultra pour macOS
# Ce script crée une application macOS qui lance Vision Ultra

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Vision Ultra]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
SCRIPT_PATH="$APP_DIR/launch-vision-ultra.sh"
APP_NAME="Vision Ultra"
APP_PATH="$HOME/Desktop/$APP_NAME.app"

# Vérifier si le script de lancement existe
if [ ! -f "$SCRIPT_PATH" ]; then
  print_error "Le script de lancement n'existe pas à l'emplacement: $SCRIPT_PATH"
  exit 1
fi

# Créer la structure de l'application
print_message "Création de l'application macOS..."
mkdir -p "$APP_PATH/Contents/MacOS"
mkdir -p "$APP_PATH/Contents/Resources"

# Créer le fichier Info.plist
cat > "$APP_PATH/Contents/Info.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>VisionUltraLauncher</string>
    <key>CFBundleIconFile</key>
    <string>AppIcon</string>
    <key>CFBundleIdentifier</key>
    <string>com.jeanpassave.visionultra</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>Vision Ultra</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2025 Jean Passave. Tous droits réservés.</string>
</dict>
</plist>
EOL

# Créer le script de lancement
cat > "$APP_PATH/Contents/MacOS/VisionUltraLauncher" << EOL
#!/bin/bash

# Lancer l'application Vision Ultra
"$SCRIPT_PATH" &

# Ouvrir le navigateur après un court délai
sleep 3
open "http://localhost:3000/luna/system-dashboard"
EOL

# Rendre le script exécutable
chmod +x "$APP_PATH/Contents/MacOS/VisionUltraLauncher"

# Créer une icône par défaut si aucune n'est disponible
if [ ! -f "$APP_DIR/public/images/vision-ultra-icon.icns" ]; then
  print_message "Création d'une icône par défaut..."
  # Utiliser une icône système par défaut
  cp "/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/GenericApplicationIcon.icns" "$APP_PATH/Contents/Resources/AppIcon.icns"
else
  # Copier l'icône de l'application
  cp "$APP_DIR/public/images/vision-ultra-icon.icns" "$APP_PATH/Contents/Resources/AppIcon.icns"
fi

print_success "Application macOS créée avec succès à l'emplacement: $APP_PATH"
print_message "Vous pouvez maintenant lancer Vision Ultra en double-cliquant sur l'application sur votre bureau."
