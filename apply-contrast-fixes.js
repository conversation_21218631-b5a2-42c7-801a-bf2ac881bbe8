#!/usr/bin/env node

/**
 * Script pour appliquer les corrections de contraste à toutes les pages HTML de l'application Louna
 */

const fs = require('fs');
const path = require('path');

// Liste des fichiers HTML à corriger
const htmlFiles = [
    'public/index.html',
    'public/chat.html',
    'public/presentation.html',
    'public/futuristic-interface.html',
    'public/brain-visualization.html',
    'public/kyber-dashboard.html',
    'public/generation-studio.html',
    'public/agent-navigation.html',
    'public/security-dashboard.html',
    'public/performance.html',
    'public/qi-neuron-monitor.html',
    'public/agents.html',
    'public/training.html',
    'public/memory-fusion.html',
    'public/settings.html',
    'public/ltx-video.html',
    'public/code-editor.html',
    'public/code-extensions.html'
];

// Ligne CSS à ajouter
const contrastFixLine = '    <link rel="stylesheet" href="/css/contrast-fixes.css">';

// Fonction pour vérifier si le fichier contient déjà la ligne
function hasContrastFix(content) {
    return content.includes('contrast-fixes.css');
}

// Fonction pour ajouter la ligne CSS après les autres liens CSS
function addContrastFix(content) {
    // Chercher la dernière ligne de CSS avant Font Awesome ou Google Fonts
    const lines = content.split('\n');
    let insertIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (line.includes('<link rel="stylesheet"') && 
            (line.includes('/css/') || line.includes('css/')) &&
            !line.includes('cdnjs.cloudflare.com') &&
            !line.includes('fonts.googleapis.com')) {
            insertIndex = i;
        }
    }
    
    if (insertIndex !== -1) {
        lines.splice(insertIndex + 1, 0, contrastFixLine);
        return lines.join('\n');
    }
    
    return content;
}

// Fonction principale
function applyContrastFixes() {
    console.log('🎨 Application des corrections de contraste...\n');
    
    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    htmlFiles.forEach(filePath => {
        try {
            if (!fs.existsSync(filePath)) {
                console.log(`⚠️  Fichier non trouvé: ${filePath}`);
                skippedCount++;
                return;
            }
            
            const content = fs.readFileSync(filePath, 'utf8');
            
            if (hasContrastFix(content)) {
                console.log(`✅ Déjà corrigé: ${filePath}`);
                skippedCount++;
                return;
            }
            
            const updatedContent = addContrastFix(content);
            
            if (updatedContent !== content) {
                fs.writeFileSync(filePath, updatedContent, 'utf8');
                console.log(`🔧 Corrigé: ${filePath}`);
                processedCount++;
            } else {
                console.log(`⚠️  Impossible de corriger: ${filePath}`);
                errorCount++;
            }
            
        } catch (error) {
            console.error(`❌ Erreur avec ${filePath}:`, error.message);
            errorCount++;
        }
    });
    
    console.log('\n📊 Résumé:');
    console.log(`✅ Fichiers corrigés: ${processedCount}`);
    console.log(`⏭️  Fichiers ignorés: ${skippedCount}`);
    console.log(`❌ Erreurs: ${errorCount}`);
    console.log(`📁 Total traité: ${htmlFiles.length}`);
    
    if (processedCount > 0) {
        console.log('\n🎉 Corrections de contraste appliquées avec succès !');
        console.log('💡 Tous les textes devraient maintenant être lisibles.');
    }
}

// Exécuter le script
if (require.main === module) {
    applyContrastFixes();
}

module.exports = { applyContrastFixes };
