/**
 * SCRIPT DE TEST POUR LE SYSTÈME DE CONNEXION DIRECTE
 * 
 * Ce script teste toutes les fonctionnalités du système de connexion directe
 * sans Ollama pour vérifier les performances et la fiabilité
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

async function testAPI(endpoint, method = 'GET', data = null) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            timeout: 10000
        };
        
        if (data) {
            config.data = data;
            config.headers = { 'Content-Type': 'application/json' };
        }
        
        const startTime = Date.now();
        const response = await axios(config);
        const latency = Date.now() - startTime;
        
        return {
            success: true,
            data: response.data,
            latency,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            latency: 0,
            status: error.response?.status || 0
        };
    }
}

async function testDirectConnectionStatus() {
    log('\n🔍 TEST DU STATUT DE CONNEXION DIRECTE', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/status');
    
    if (result.success) {
        logSuccess(`Statut récupéré en ${result.latency}ms`);
        
        const data = result.data.data;
        log(`📡 Connexion directe: ${data.directConnection.available ? 'DISPONIBLE' : 'INDISPONIBLE'}`, 
            data.directConnection.available ? 'green' : 'red');
        log(`⚡ Optimiseur de vitesse: ${data.speedOptimizer.available ? 'ACTIF' : 'INACTIF'}`, 
            data.speedOptimizer.available ? 'green' : 'red');
        log(`🎯 API active: ${data.directConnection.activeAPI}`, 'cyan');
        log(`📊 APIs disponibles: ${data.directConnection.availableAPIs}`, 'cyan');
        
        if (data.speedOptimizer.stats) {
            const stats = data.speedOptimizer.stats;
            log(`📈 Latence moyenne: ${stats.averageLatency}ms`, 'magenta');
            log(`🚀 Réponse la plus rapide: ${stats.fastestResponse}ms`, 'magenta');
            log(`📊 Gains d'optimisation: ${stats.optimizationGains}%`, 'magenta');
            log(`🎯 Niveau d'optimisation: ${stats.optimizationLevel}`, 'magenta');
        }
        
        return true;
    } else {
        logError(`Échec du test de statut: ${result.error}`);
        return false;
    }
}

async function testDirectMessage() {
    log('\n💬 TEST D\'ENVOI DE MESSAGE DIRECT', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const testMessage = "Bonjour ! Pouvez-vous me répondre via la connexion directe ?";
    
    const result = await testAPI('/api/direct-connection/message', 'POST', {
        message: testMessage,
        options: {
            maxTokens: 100,
            temperature: 0.7
        }
    });
    
    if (result.success) {
        logSuccess(`Message traité en ${result.latency}ms`);
        
        const data = result.data.data;
        log(`📤 Message envoyé: "${testMessage}"`, 'cyan');
        log(`📥 Réponse reçue: "${data.response}"`, 'green');
        log(`🔗 Source: ${data.source}`, 'magenta');
        log(`⚡ Latence: ${data.latency}ms`, 'magenta');
        log(`🎯 API utilisée: ${data.api}`, 'magenta');
        
        if (data.reflection) {
            log(`💭 Réflexion: ${JSON.stringify(data.reflection)}`, 'yellow');
        }
        
        return true;
    } else {
        logError(`Échec de l'envoi de message: ${result.error}`);
        return false;
    }
}

async function testTurboMode() {
    log('\n🚀 TEST DU MODE TURBO', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/turbo-mode', 'POST', {
        enabled: true
    });
    
    if (result.success) {
        logSuccess(`Mode TURBO activé en ${result.latency}ms`);
        log(`📝 Message: ${result.data.message}`, 'green');
        log(`🎯 Mode: ${result.data.mode}`, 'cyan');
        return true;
    } else {
        logError(`Échec de l'activation du mode turbo: ${result.error}`);
        return false;
    }
}

async function testAPISwitch() {
    log('\n🔄 TEST DE BASCULEMENT D\'API', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const apis = ['OpenAI', 'Claude API', 'DeepSeek API', 'Local LLM Server'];
    let successCount = 0;
    
    for (const apiName of apis) {
        const result = await testAPI('/api/direct-connection/switch-api', 'POST', {
            apiName: apiName
        });
        
        if (result.success) {
            logSuccess(`Basculement vers ${apiName} réussi`);
            successCount++;
        } else {
            logWarning(`Basculement vers ${apiName} échoué: ${result.error}`);
        }
    }
    
    log(`📊 Résultat: ${successCount}/${apis.length} APIs testées avec succès`, 
        successCount === apis.length ? 'green' : 'yellow');
    
    return successCount > 0;
}

async function testSpeedTest() {
    log('\n🧪 TEST DE VITESSE', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/speed-test', 'POST', {
        testMessage: "Test de vitesse de connexion directe",
        iterations: 3
    });
    
    if (result.success) {
        logSuccess(`Test de vitesse terminé en ${result.latency}ms`);
        
        const data = result.data.data;
        const summary = data.summary;
        
        log(`📊 Tests réussis: ${summary.successfulTests}/${summary.totalTests}`, 'green');
        log(`📈 Taux de succès: ${summary.successRate.toFixed(1)}%`, 'cyan');
        log(`⚡ Latence moyenne: ${summary.averageLatency.toFixed(0)}ms`, 'magenta');
        log(`⏱️  Temps total: ${summary.totalTime}ms`, 'magenta');
        
        log('\n📋 Détails des tests:', 'yellow');
        data.results.forEach(test => {
            const status = test.success ? '✅' : '❌';
            log(`  ${status} Test ${test.iteration}: ${test.latency}ms (${test.source || 'N/A'})`, 'cyan');
        });
        
        return true;
    } else {
        logError(`Échec du test de vitesse: ${result.error}`);
        return false;
    }
}

async function testPerformanceStats() {
    log('\n📊 TEST DES STATISTIQUES DE PERFORMANCE', 'bold');
    log('=' .repeat(50), 'cyan');
    
    const result = await testAPI('/api/direct-connection/performance-stats');
    
    if (result.success) {
        logSuccess(`Statistiques récupérées en ${result.latency}ms`);
        
        const data = result.data.data;
        
        if (data.speedOptimizer) {
            const stats = data.speedOptimizer;
            log(`⚡ Latence moyenne: ${stats.averageLatency}ms`, 'green');
            log(`🚀 Réponse la plus rapide: ${stats.fastestResponse}ms`, 'green');
            log(`📈 Gains d'optimisation: ${stats.optimizationGains}%`, 'green');
            log(`🎯 Taux de cache hit: ${stats.cacheHitRate}%`, 'green');
            log(`📊 Niveau d'optimisation: ${stats.optimizationLevel}`, 'green');
        }
        
        if (data.comparison) {
            const comp = data.comparison;
            log(`📊 Baseline Ollama: ${comp.ollamaBaseline}ms`, 'yellow');
            log(`⚡ Moyenne actuelle: ${comp.currentAverage}ms`, 'yellow');
            log(`📈 Amélioration: ${comp.improvement}%`, 'yellow');
        }
        
        return true;
    } else {
        logError(`Échec de récupération des statistiques: ${result.error}`);
        return false;
    }
}

async function runAllTests() {
    log('🚀 DÉMARRAGE DES TESTS DE CONNEXION DIRECTE', 'bold');
    log('=' .repeat(60), 'cyan');
    log('🎯 Objectif: Tester le système de connexion directe sans Ollama', 'blue');
    log('⚡ Focus: Performance, fiabilité et fonctionnalités', 'blue');
    
    const tests = [
        { name: 'Statut de connexion directe', fn: testDirectConnectionStatus },
        { name: 'Envoi de message direct', fn: testDirectMessage },
        { name: 'Mode TURBO', fn: testTurboMode },
        { name: 'Basculement d\'API', fn: testAPISwitch },
        { name: 'Test de vitesse', fn: testSpeedTest },
        { name: 'Statistiques de performance', fn: testPerformanceStats }
    ];
    
    let successCount = 0;
    const results = [];
    
    for (const test of tests) {
        try {
            const startTime = Date.now();
            const success = await test.fn();
            const duration = Date.now() - startTime;
            
            results.push({
                name: test.name,
                success,
                duration
            });
            
            if (success) {
                successCount++;
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            logError(`Erreur lors du test "${test.name}": ${error.message}`);
            results.push({
                name: test.name,
                success: false,
                duration: 0,
                error: error.message
            });
        }
    }
    
    // Résumé final
    log('\n📋 RÉSUMÉ DES TESTS', 'bold');
    log('=' .repeat(60), 'cyan');
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
        log(`${status} ${result.name}${duration}`, result.success ? 'green' : 'red');
        
        if (result.error) {
            log(`   └─ Erreur: ${result.error}`, 'red');
        }
    });
    
    const successRate = (successCount / tests.length) * 100;
    log(`\n📊 RÉSULTAT GLOBAL: ${successCount}/${tests.length} tests réussis (${successRate.toFixed(1)}%)`, 
        successRate >= 80 ? 'green' : successRate >= 50 ? 'yellow' : 'red');
    
    if (successRate >= 80) {
        logSuccess('🎉 Système de connexion directe opérationnel !');
    } else if (successRate >= 50) {
        logWarning('⚠️  Système partiellement fonctionnel - Optimisations nécessaires');
    } else {
        logError('❌ Système défaillant - Intervention requise');
    }
    
    log('\n🔗 Interface de gestion: http://localhost:3000/direct-connection-manager.html', 'cyan');
    log('🏠 Page d\'accueil: http://localhost:3000/', 'cyan');
}

// Exécuter les tests
if (require.main === module) {
    runAllTests().catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    testDirectConnectionStatus,
    testDirectMessage,
    testTurboMode,
    testAPISwitch,
    testSpeedTest,
    testPerformanceStats,
    runAllTests
};
