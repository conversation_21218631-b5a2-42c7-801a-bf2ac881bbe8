#!/bin/bash

# Script de lancement simple pour Louna
# Version simplifiée pour éviter les erreurs

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
NC='\033[0m'

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"

echo -e "${PURPLE}🚀 Lancement de Louna...${NC}"

# Vérifier si le répertoire existe
if [ ! -d "$APP_DIR" ]; then
  echo -e "${RED}❌ Répertoire de l'application non trouvé: $APP_DIR${NC}"
  exit 1
fi

# Aller dans le répertoire de l'application
cd "$APP_DIR" || exit 1

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  echo -e "${RED}❌ Node.js n'est pas installé${NC}"
  exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  echo -e "${RED}❌ npm n'est pas installé${NC}"
  exit 1
fi

# Arrêter les processus existants sur le port 3005
echo -e "${BLUE}🔄 Nettoyage des processus existants...${NC}"
pkill -f "node server.js" 2>/dev/null || true
pkill -f "electron" 2>/dev/null || true
sleep 2

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
  echo -e "${BLUE}📦 Installation des dépendances...${NC}"
  npm install
  if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Erreur lors de l'installation des dépendances${NC}"
    exit 1
  fi
fi

# Lancer l'application Electron directement
echo -e "${GREEN}✅ Lancement de l'application Electron Louna...${NC}"
npm run electron

echo -e "${PURPLE}📱 Application fermée${NC}"
