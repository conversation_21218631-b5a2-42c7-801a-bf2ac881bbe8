/**
 * Script de préchargement pour l'application Electron Louna
 * Ce script s'exécute dans le contexte du processus de rendu avant que la page web ne soit chargée
 * Il expose des fonctionnalités d'Electron au processus de rendu de manière sécurisée
 */

const { contextBridge, ipcRenderer, shell } = require('electron');
const os = require('os');
const path = require('path');

// Exposer des fonctionnalités d'Electron au processus de rendu
contextBridge.exposeInMainWorld('electron', {
  // Informations sur le système
  system: {
    platform: process.platform,
    arch: process.arch,
    version: process.version,
    osVersion: os.release(),
    osName: os.type(),
    hostname: os.hostname(),
    username: os.userInfo().username,
    homedir: os.homedir(),
    tempdir: os.tmpdir(),
    cpuCount: os.cpus().length,
    totalMemory: os.totalmem(),
    freeMemory: os.freemem()
  },

  // Fonctions de notification
  notification: {
    // Afficher une notification
    show: (title, body) => {
      ipcRenderer.send('show-notification', { title, body });
    }
  },

  // Fonctions de navigation
  navigation: {
    // Ouvrir un lien externe dans le navigateur par défaut
    openExternal: (url) => {
      ipcRenderer.send('open-external-link', url);
    }
  },

  // Fonctions de sauvegarde et restauration
  backup: {
    // Sauvegarder la mémoire thermique
    save: () => {
      ipcRenderer.send('backup-memory');
    },

    // Restaurer la mémoire thermique
    restore: () => {
      ipcRenderer.send('restore-memory');
    }
  },

  // Fonctions de dialogue
  dialog: {
    // Ouvrir un dialogue de sélection de fichier
    openFile: async (options) => {
      return await ipcRenderer.invoke('dialog:openFile', options);
    },

    // Ouvrir un dialogue de sélection de dossier
    openDirectory: async (options) => {
      return await ipcRenderer.invoke('dialog:openDirectory', options);
    },

    // Ouvrir un dialogue de sauvegarde de fichier
    saveFile: async (options) => {
      return await ipcRenderer.invoke('dialog:saveFile', options);
    }
  },

  // Fonctions de fichier
  file: {
    // Lire un fichier
    read: async (filePath) => {
      return await ipcRenderer.invoke('file:read', filePath);
    },

    // Écrire dans un fichier
    write: async (filePath, content) => {
      return await ipcRenderer.invoke('file:write', filePath, content);
    },

    // Vérifier si un fichier existe
    exists: async (filePath) => {
      return await ipcRenderer.invoke('file:exists', filePath);
    }
  },

  // Fonctions de démarrage automatique
  autoLaunch: {
    // Activer ou désactiver le démarrage automatique
    set: (enabled, minimized = true) => {
      ipcRenderer.send('set-auto-launch', { enabled, minimized });
    },

    // Vérifier si le démarrage automatique est activé
    get: async () => {
      return await ipcRenderer.invoke('get-auto-launch');
    }
  },

  // Fonctions média natives (caméra, micro)
  media: {
    // Obtenir les périphériques média disponibles
    getDevices: async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        return devices;
      } catch (error) {
        console.error('Erreur lors de l\'énumération des périphériques:', error);
        return [];
      }
    },

    // Obtenir un flux vidéo de la caméra
    getVideoStream: async (constraints = { video: true, audio: false }) => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        return stream;
      } catch (error) {
        console.error('Erreur lors de l\'accès à la caméra:', error);
        throw error;
      }
    },

    // Obtenir un flux audio du microphone
    getAudioStream: async (constraints = { video: false, audio: true }) => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        return stream;
      } catch (error) {
        console.error('Erreur lors de l\'accès au microphone:', error);
        throw error;
      }
    },

    // Obtenir un flux vidéo + audio
    getMediaStream: async (constraints = { video: true, audio: true }) => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        return stream;
      } catch (error) {
        console.error('Erreur lors de l\'accès aux médias:', error);
        throw error;
      }
    },

    // Capturer l'écran
    getDisplayStream: async (constraints = { video: true, audio: true }) => {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia(constraints);
        return stream;
      } catch (error) {
        console.error('Erreur lors de la capture d\'écran:', error);
        throw error;
      }
    },

    // Arrêter un flux média
    stopStream: (stream) => {
      if (stream && stream.getTracks) {
        stream.getTracks().forEach(track => {
          track.stop();
        });
      }
    },

    // Vérifier les permissions média
    checkPermissions: async () => {
      try {
        const cameraPermission = await navigator.permissions.query({ name: 'camera' });
        const microphonePermission = await navigator.permissions.query({ name: 'microphone' });

        return {
          camera: cameraPermission.state,
          microphone: microphonePermission.state
        };
      } catch (error) {
        console.error('Erreur lors de la vérification des permissions:', error);
        return { camera: 'unknown', microphone: 'unknown' };
      }
    }
  },

  // Fonctions de fenêtre
  window: {
    // Minimiser la fenêtre
    minimize: () => {
      ipcRenderer.send('minimize-window');
    },

    // Maximiser/restaurer la fenêtre
    maximize: () => {
      ipcRenderer.send('maximize-window');
    },

    // Afficher le menu contextuel
    showContextMenu: () => {
      ipcRenderer.send('show-context-menu');
    },

    // Obtenir les informations système
    getSystemInfo: async () => {
      return await ipcRenderer.invoke('get-system-info');
    }
  },

  // Fonctions de persistance mémoire
  persistence: {
    // Sauvegarder la mémoire instantanée
    save: async () => {
      return await ipcRenderer.invoke('persistence:save');
    },

    // Obtenir les statistiques de persistance
    getStats: async () => {
      return await ipcRenderer.invoke('persistence:stats');
    },

    // Ajouter des données à la mémoire instantanée
    addData: async (content, options = {}) => {
      return await ipcRenderer.invoke('persistence:add-data', { content, options });
    },

    // Effectuer une sauvegarde d'urgence
    emergencySave: async () => {
      return await ipcRenderer.invoke('persistence:emergency-save');
    },

    // Tester le système de persistance
    test: async () => {
      return await ipcRenderer.invoke('persistence:test');
    }
  }
});

// Ajouter des styles CSS spécifiques à Electron
document.addEventListener('DOMContentLoaded', () => {
  // Ajouter une classe pour identifier l'application Electron
  document.body.classList.add('electron-app');

  // Ajouter une classe pour la plateforme
  document.body.classList.add(`platform-${process.platform}`);

  // Créer un élément de style pour les styles spécifiques à Electron
  const style = document.createElement('style');
  style.textContent = `
    /* Styles spécifiques à l'application Electron */
    .electron-app {
      /* Désactiver la sélection de texte par défaut */
      user-select: none;
    }

    /* Styles spécifiques à la plateforme */
    .platform-darwin .mac-only {
      display: block;
    }

    .platform-win32 .windows-only {
      display: block;
    }

    .platform-linux .linux-only {
      display: block;
    }

    /* Masquer les éléments spécifiques à la plateforme par défaut */
    .mac-only, .windows-only, .linux-only {
      display: none;
    }

    /* Styles pour les éléments d'interface natifs */
    .native-button {
      border-radius: 4px;
      padding: 8px 16px;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: var(--text-primary);
      transition: all 0.2s ease;
    }

    .native-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .native-button:active {
      background-color: rgba(255, 255, 255, 0.3);
    }

    /* Styles pour les barres de titre personnalisées */
    .titlebar {
      -webkit-app-region: drag;
      height: 32px;
      background-color: var(--bg-card);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
    }

    .titlebar-button {
      -webkit-app-region: no-drag;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      cursor: pointer;
    }

    .titlebar-close {
      background-color: #ff5f57;
    }

    .titlebar-minimize {
      background-color: #ffbd2e;
    }

    .titlebar-maximize {
      background-color: #28c940;
    }
  `;

  // Ajouter l'élément de style au document
  document.head.appendChild(style);
});
