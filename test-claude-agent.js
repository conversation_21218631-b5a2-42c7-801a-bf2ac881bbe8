#!/usr/bin/env node

/**
 * Script de test pour vérifier que l'agent Claude 4GB fonctionne correctement
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3004';
const TEST_MESSAGES = [
    'Bonjour, peux-tu me dire qui tu es ?',
    'Quelle est la capitale de la France ?',
    'Explique-moi le concept de mémoire thermique',
    'Comment fonctionne un agent IA ?',
    'Peux-tu me parler de tes capacités ?'
];

console.log('🧪 === TEST DE L\'AGENT CLAUDE 4GB ===\n');

async function testAgentStatus() {
    console.log('1️⃣ Test du statut de l\'agent...');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/agent/status`);
        const data = response.data;
        
        if (data.success) {
            console.log('✅ Statut de l\'agent récupéré avec succès');
            console.log(`   - Agent en ligne: ${data.agent.isOnline ? 'Oui' : 'Non'}`);
            console.log(`   - Accès Internet: ${data.agent.internetAccess ? 'Oui' : 'Non'}`);
            console.log(`   - MCP activé: ${data.agent.mcpEnabled ? 'Oui' : 'Non'}`);
            
            if (data.agent.claudeAgent) {
                console.log(`   - Agent Claude disponible: ${data.agent.claudeAgent.available ? 'Oui' : 'Non'}`);
                if (data.agent.claudeAgent.available) {
                    console.log(`   - Nom: ${data.agent.claudeAgent.name}`);
                    console.log(`   - Modèle: ${data.agent.claudeAgent.model}`);
                }
            }
            
            return data.agent.claudeAgent && data.agent.claudeAgent.available;
        } else {
            console.log('❌ Erreur lors de la récupération du statut:', data.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Erreur de connexion:', error.message);
        return false;
    }
}

async function testChatMessage(message, index) {
    console.log(`\n${index + 2}️⃣ Test du message: "${message}"`);
    
    try {
        const startTime = Date.now();
        
        const response = await axios.post(`${BASE_URL}/api/chat/message`, {
            message: message,
            conversationId: 'test-conversation'
        }, {
            timeout: 30000 // 30 secondes de timeout
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        const data = response.data;
        
        if (data.success) {
            console.log('✅ Réponse reçue avec succès');
            console.log(`   - Temps de réponse: ${responseTime}ms`);
            console.log(`   - Agent: ${data.agent.name} (${data.agent.model})`);
            console.log(`   - Réponse: ${data.response.substring(0, 100)}${data.response.length > 100 ? '...' : ''}`);
            return true;
        } else {
            console.log('❌ Erreur dans la réponse:', data.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Erreur lors de l\'envoi du message:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('   → Le serveur n\'est pas démarré');
        } else if (error.code === 'ECONNRESET') {
            console.log('   → Connexion interrompue par le serveur');
        } else if (error.response) {
            console.log(`   → Erreur HTTP ${error.response.status}: ${error.response.statusText}`);
        }
        return false;
    }
}

async function testReflectionsStream() {
    console.log(`\n${TEST_MESSAGES.length + 2}️⃣ Test du stream de réflexions...`);
    
    try {
        const response = await axios.get(`${BASE_URL}/api/agent/reflections/stream`, {
            timeout: 10000,
            responseType: 'stream'
        });
        
        console.log('✅ Connexion au stream de réflexions établie');
        
        let reflectionCount = 0;
        const maxReflections = 3;
        
        return new Promise((resolve) => {
            response.data.on('data', (chunk) => {
                const lines = chunk.toString().split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            if (data.type !== 'heartbeat') {
                                reflectionCount++;
                                console.log(`   - Réflexion ${reflectionCount}: [${data.type}] ${data.text}`);
                                
                                if (reflectionCount >= maxReflections) {
                                    console.log('✅ Stream de réflexions fonctionne correctement');
                                    resolve(true);
                                    return;
                                }
                            }
                        } catch (e) {
                            // Ignorer les erreurs de parsing
                        }
                    }
                }
            });
            
            setTimeout(() => {
                if (reflectionCount === 0) {
                    console.log('⚠️ Aucune réflexion reçue dans les 10 secondes');
                    resolve(false);
                } else {
                    console.log(`✅ ${reflectionCount} réflexion(s) reçue(s)`);
                    resolve(true);
                }
            }, 10000);
        });
        
    } catch (error) {
        console.log('❌ Erreur lors du test du stream:', error.message);
        return false;
    }
}

async function generateTestReport(results) {
    console.log('\n📊 === RAPPORT DE TEST ===');
    
    const totalTests = results.length;
    const successfulTests = results.filter(r => r.success).length;
    const successRate = (successfulTests / totalTests * 100).toFixed(1);
    
    console.log(`Tests réussis: ${successfulTests}/${totalTests} (${successRate}%)`);
    
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.name}`);
    });
    
    // Sauvegarder le rapport
    const report = {
        timestamp: new Date().toISOString(),
        totalTests: totalTests,
        successfulTests: successfulTests,
        successRate: parseFloat(successRate),
        results: results
    };
    
    const reportPath = path.join(__dirname, 'test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Rapport sauvegardé dans: ${reportPath}`);
    
    if (successRate >= 80) {
        console.log('\n🎉 AGENT CLAUDE FONCTIONNE CORRECTEMENT !');
        return true;
    } else {
        console.log('\n⚠️ PROBLÈMES DÉTECTÉS AVEC L\'AGENT CLAUDE');
        return false;
    }
}

async function runTests() {
    const results = [];
    
    // Test 1: Statut de l'agent
    const statusResult = await testAgentStatus();
    results.push({
        name: 'Statut de l\'agent',
        success: statusResult
    });
    
    if (!statusResult) {
        console.log('\n❌ Agent Claude non disponible - Arrêt des tests');
        await generateTestReport(results);
        process.exit(1);
    }
    
    // Test 2-6: Messages de chat
    for (let i = 0; i < TEST_MESSAGES.length; i++) {
        const messageResult = await testChatMessage(TEST_MESSAGES[i], i);
        results.push({
            name: `Message ${i + 1}: "${TEST_MESSAGES[i].substring(0, 30)}..."`,
            success: messageResult
        });
        
        // Pause entre les messages
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Test 7: Stream de réflexions
    const streamResult = await testReflectionsStream();
    results.push({
        name: 'Stream de réflexions',
        success: streamResult
    });
    
    // Générer le rapport final
    const overallSuccess = await generateTestReport(results);
    
    process.exit(overallSuccess ? 0 : 1);
}

// Lancer les tests
runTests().catch(error => {
    console.error('\n💥 Erreur fatale lors des tests:', error);
    process.exit(1);
});
