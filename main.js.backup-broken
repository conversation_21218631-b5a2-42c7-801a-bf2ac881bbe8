/**
 * Fichier principal pour l'application Electron Louna
 */

const { app, BrowserWindow, ipcMain, dialog, shell, Menu } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const fs = require('fs');

// Garder une référence globale de l'objet window, sinon la fenêtre sera
// fermée automatiquement quand l'objet JavaScript sera garbage collected.
let mainWindow;
let serverProcess;
let serverPort = 3005; // Port synchronisé avec server.js
let serverReady = false;

/**
 * Crée la fenêtre principale de l'application
 */
function createWindow() {
  // Créer la fenêtre du navigateur
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    backgroundColor: '#1a1a2e',
    show: false,
    movable: true, // Permettre le déplacement de la fenêtre
    resizable: true, // Permettre le redimensionnement
    minimizable: true, // Permettre la minimisation
    maximizable: true, // Permettre la maximisation
    closable: true, // Permettre la fermeture
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default', // Style de barre de titre par défaut pour permettre le déplacement
    frame: true, // Afficher le cadre de la fenêtre pour permettre le déplacement
    vibrancy: 'ultra-dark',
    visualEffectState: 'active'
  });

  // Afficher l'écran de chargement
  mainWindow.loadFile('public/loading.html');

  // Démarrer le serveur
  startServer();

  // Ouvrir les DevTools en mode développement
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Émis lorsque la fenêtre est fermée
  mainWindow.on('closed', function () {
    // Dé-référencer l'objet window, généralement vous stockeriez les fenêtres
    // dans un tableau si votre application supporte le multi-fenêtre, c'est le moment
    // où vous devriez supprimer l'élément correspondant.
    mainWindow = null;

    // Arrêter le serveur
    if (serverProcess) {
      serverProcess.kill();
      serverProcess = null;
    }
  });

  // Créer le menu de l'application
  createMenu();
}

/**
 * Démarre le serveur Node.js
 */
function startServer() {
  // Démarrer le serveur Node.js
  serverProcess = spawn('node', ['server.js'], {
    stdio: ['ignore', 'pipe', 'pipe']
  });

  let serverOutput = '';

  // Écouter la sortie standard du serveur
  serverProcess.stdout.on('data', (data) => {
    serverOutput += data.toString();
    console.log(`[Server]: ${data.toString()}`);

    // Vérifier si le serveur est prêt
    if (data.toString().includes('Serveur Louna démarré sur le port') ||
        data.toString().includes('Serveur démarré sur le port')) {
      serverReady = true;
      // Charger l'application une fois que le serveur est prêt
      loadApp();
    }
  });

  // Écouter la sortie d'erreur du serveur
  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error]: ${data.toString()}`);
  });

  // Gérer la fermeture du serveur
  serverProcess.on('close', (code) => {
    console.log(`Le serveur s'est arrêté avec le code ${code}`);
    serverReady = false;
  });

  // Définir un délai maximum pour le démarrage du serveur
  setTimeout(() => {
    if (!serverReady) {
      console.error('Le serveur n\'a pas démarré dans le délai imparti');
      dialog.showErrorBox(
        'Erreur de démarrage',
        'Le serveur n\'a pas démarré correctement. Veuillez redémarrer l\'application.'
      );
    }
  }, 10000);
}

/**
 * Charge l'application une fois que le serveur est prêt
 */
function loadApp() {
  const appUrl = `http://localhost:${serverPort}/chat`;

  // Charger l'URL de l'application
  mainWindow.loadURL(appUrl);

  // Afficher la fenêtre une fois que tout est chargé
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // NAVIGATION INTELLIGENTE - PERMETTRE INTERNET POUR L'AGENT, BLOQUER NAVIGATION ACCIDENTELLE
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log("🔍 Demande d'ouverture:", url);
    
    // Toujours permettre les liens internes Louna
    if (url.includes('localhost:3005') || url.includes('127.0.0.1:3005')) {
      return { action: 'allow' };
    }
    
    // Permettre les ressources nécessaires (APIs, CDN, etc.)
    const allowedDomains = [
      'api.openai.com',
      'api.deepseek.com',
      'cdnjs.cloudflare.com',
      'unpkg.com',
      'googleapis.com',
      'github.com'
    ];
    
    try {
      const urlObj = new URL(url);
      if (allowedDomains.some(domain => urlObj.hostname.includes(domain))) {
        console.log("✅ Domaine autorisé:", urlObj.hostname);
        return { action: 'allow' };
      }
    } catch (e) {}
    
    // Bloquer les autres ouvertures de nouvelles fenêtres
    console.log("🚫 Nouvelle fenêtre bloquée:", url);
    return { action: 'deny' };
  });
  
  // Navigation intelligente - permettre les ressources, bloquer la navigation accidentelle
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    try {
      const parsedUrl = new URL(navigationUrl);
      
      // Toujours permettre la navigation interne
      if (parsedUrl.hostname === 'localhost' || parsedUrl.hostname === '127.0.0.1') {
        return; // Permettre
      }
      
      // Permettre les ressources nécessaires mais pas la navigation complète
      const allowedForResources = [
        'cdnjs.cloudflare.com',
        'unpkg.com',
        'fonts.googleapis.com',
        'api.deepseek.com'
      ];
      
      if (allowedForResources.some(domain => parsedUrl.hostname.includes(domain))) {
        console.log("✅ Ressource autorisée:", parsedUrl.hostname);
        return; // Permettre
      }
      
      // Bloquer la navigation vers des sites externes
      console.log("🚫 Navigation externe bloquée (interface reste dans Louna):", navigationUrl);
      event.preventDefault();
      
    } catch (e) {
      // URL invalide, bloquer par sécurité
      event.preventDefault();
    }
  });    console.log("🚫 Navigation UI externe bloquée:", navigationUrl);
    event.preventDefault();
  });  });  });
}

/**
 * Crée le menu de l'application
 */
function createMenu() {
  const template = [
    {
      label: 'Louna',
      submenu: [
        { role: 'about', label: 'À propos de Louna' },
        { type: 'separator' },
        { role: 'services', label: 'Services' },
        { type: 'separator' },
        { role: 'hide', label: 'Masquer Louna' },
        { role: 'hideOthers', label: 'Masquer les autres' },
        { role: 'unhide', label: 'Tout afficher' },
        { type: 'separator' },
        { role: 'quit', label: 'Quitter Louna' }
      ]
    },
    {
      label: 'Édition',
      submenu: [
        { role: 'undo', label: 'Annuler' },
        { role: 'redo', label: 'Rétablir' },
        { type: 'separator' },
        { role: 'cut', label: 'Couper' },
        { role: 'copy', label: 'Copier' },
        { role: 'paste', label: 'Coller' },
        { role: 'selectAll', label: 'Tout sélectionner' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { role: 'reload', label: 'Actualiser' },
        { role: 'forceReload', label: 'Forcer l\'actualisation' },
        { role: 'toggleDevTools', label: 'Outils de développement' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'Taille réelle' },
        { role: 'zoomIn', label: 'Zoom avant' },
        { role: 'zoomOut', label: 'Zoom arrière' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'Plein écran' }
      ]
    },
    {
      label: 'Navigation',
      submenu: [
        {
          label: '🎯 Hub Central - MAÎTRE',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/chat`);
          }
        },
        {
          label: 'Chat',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/chat`);
          }
        },
        {
          label: 'Mémoire Thermique',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/futuristic-interface.html`);
          }
        },
        {
          label: 'Accélérateurs Kyber',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/kyber-dashboard.html`);
          }
        },
        {
          label: 'Agents',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/agents.html`);
          }
        },
        {
          label: 'Formation',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/training.html`);
          }
        },
        {
          label: 'Cours Ultra-Avancé',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/advanced-course-monitor.html`);
          }
        },
        {
          label: 'Audio/Vidéo',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/audio-video-controls.html`);
          }
        }
      ]
    },
    {
      label: 'Fenêtre',
      submenu: [
        { role: 'minimize', label: 'Réduire' },
        { role: 'zoom', label: 'Zoom' },
        { type: 'separator' },
        { role: 'front', label: 'Tout ramener au premier plan' }
      ]
    },
    {
      role: 'help',
      label: 'Aide',
      submenu: [
        {
          label: 'Documentation',
          click: async () => {
            await console.log("🚫 Lien externe bloqué - restant dans Louna");
          }
        },
        {
          label: 'Signaler un problème',
          click: async () => {
            await console.log("🚫 Lien externe bloqué - restant dans Louna");
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Cette méthode sera appelée quand Electron aura fini
// de s'initialiser et sera prêt à créer des fenêtres de navigateur.
// Certaines APIs peuvent être utilisées uniquement après cet événement.
app.on('ready', createWindow);

// Quitter quand toutes les fenêtres sont fermées.
app.on('window-all-closed', function () {
  // Sur macOS, il est commun pour une application et leur barre de menu
  // de rester active tant que l'utilisateur ne quitte pas explicitement avec Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', function () {
  // Sur macOS, il est commun de re-créer une fenêtre de l'application quand
  // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
  if (mainWindow === null) {
    createWindow();
  }
});

// Dans ce fichier, vous pouvez inclure le reste du code spécifique du
// processus principal de votre application. Vous pouvez également le mettre dans des fichiers séparés
// et les inclure ici.

// Gérer les événements IPC (communication entre le processus principal et le renderer)
ipcMain.on('restart-server', () => {
  if (serverProcess) {
    serverProcess.kill();
    serverProcess = null;
  }
  startServer();
});

ipcMain.on('open-file-dialog', async (event) => {
  const { filePaths } = await dialog.showOpenDialog({
    properties: ['openFile', 'multiSelections']
  });
  event.reply('selected-files', filePaths);
});

// Exporter l'application pour les tests
module.exports = app;
