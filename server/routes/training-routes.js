/**
 * Routes API pour la formation des agents
 */

const express = require('express');
const router = express.Router();
const trainingService = require('../training-service');
const agentService = require('../agent-service');

/**
 * Récupère les agents disponibles pour la formation
 */
router.get('/agents', async (req, res) => {
    try {
        // Récupérer tous les agents
        const agents = await agentService.getAgents();
        
        // Filtrer les agents pour ne garder que ceux qui peuvent être utilisés pour la formation
        const trainingAgents = agents.filter(agent => 
            agent.type === 'ollama' || // Agents Ollama
            agent.type === 'openai'    // Agents OpenAI
        );
        
        res.json({ success: true, agents: trainingAgents });
    } catch (error) {
        console.error('Erreur lors de la récupération des agents pour la formation:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Récupère les ensembles de données disponibles
 */
router.get('/datasets', async (req, res) => {
    try {
        const datasets = await trainingService.getDatasets();
        res.json({ success: true, datasets });
    } catch (error) {
        console.error('Erreur lors de la récupération des ensembles de données:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Récupère un ensemble de données par son ID
 */
router.get('/datasets/:id', async (req, res) => {
    try {
        const datasetId = req.params.id;
        const dataset = await trainingService.getDatasetById(datasetId);
        
        if (!dataset) {
            return res.status(404).json({ success: false, error: `Ensemble de données ${datasetId} non trouvé` });
        }
        
        res.json({ success: true, dataset });
    } catch (error) {
        console.error(`Erreur lors de la récupération de l'ensemble de données ${req.params.id}:`, error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Crée un nouvel ensemble de données
 */
router.post('/datasets', async (req, res) => {
    try {
        const datasetInfo = req.body;
        
        // Vérifier que les informations nécessaires sont présentes
        if (!datasetInfo.name) {
            return res.status(400).json({ success: false, error: 'Le nom de l\'ensemble de données est requis' });
        }
        
        // Créer l'ensemble de données
        const dataset = await trainingService.createDataset(datasetInfo);
        
        res.json({ success: true, dataset });
    } catch (error) {
        console.error('Erreur lors de la création de l\'ensemble de données:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Met à jour un ensemble de données existant
 */
router.put('/datasets/:id', async (req, res) => {
    try {
        const datasetId = req.params.id;
        const datasetInfo = req.body;
        
        // Vérifier que l'ensemble de données existe
        const existingDataset = await trainingService.getDatasetById(datasetId);
        
        if (!existingDataset) {
            return res.status(404).json({ success: false, error: `Ensemble de données ${datasetId} non trouvé` });
        }
        
        // Mettre à jour l'ensemble de données
        datasetInfo.id = datasetId; // S'assurer que l'ID est correct
        const dataset = await trainingService.createDataset(datasetInfo); // Réutiliser la fonction de création
        
        res.json({ success: true, dataset });
    } catch (error) {
        console.error(`Erreur lors de la mise à jour de l'ensemble de données ${req.params.id}:`, error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Supprime un ensemble de données
 */
router.delete('/datasets/:id', async (req, res) => {
    try {
        const datasetId = req.params.id;
        
        // Vérifier que l'ensemble de données existe
        const existingDataset = await trainingService.getDatasetById(datasetId);
        
        if (!existingDataset) {
            return res.status(404).json({ success: false, error: `Ensemble de données ${datasetId} non trouvé` });
        }
        
        // Supprimer l'ensemble de données
        await trainingService.deleteDataset(datasetId);
        
        res.json({ success: true });
    } catch (error) {
        console.error(`Erreur lors de la suppression de l'ensemble de données ${req.params.id}:`, error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Lance une formation
 */
router.post('/train', async (req, res) => {
    try {
        const trainingOptions = req.body;
        
        // Vérifier que les informations nécessaires sont présentes
        if (!trainingOptions.agentId) {
            return res.status(400).json({ success: false, error: 'L\'ID de l\'agent est requis' });
        }
        
        if (!trainingOptions.trainingAgentId) {
            return res.status(400).json({ success: false, error: 'L\'ID de l\'agent de formation est requis' });
        }
        
        if (!trainingOptions.datasetId) {
            return res.status(400).json({ success: false, error: 'L\'ID de l\'ensemble de données est requis' });
        }
        
        // Lancer la formation
        const result = await trainingService.startTraining(trainingOptions);
        
        res.json(result);
    } catch (error) {
        console.error('Erreur lors du lancement de la formation:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Annule la formation en cours
 */
router.post('/cancel', (req, res) => {
    try {
        const result = trainingService.cancelTraining();
        res.json(result);
    } catch (error) {
        console.error('Erreur lors de l\'annulation de la formation:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Récupère l'état de la formation
 */
router.get('/state', (req, res) => {
    try {
        const result = trainingService.getTrainingState();
        res.json(result);
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'état de la formation:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * Récupère l'historique des formations
 */
router.get('/history', (req, res) => {
    try {
        const result = trainingService.getTrainingHistory();
        res.json(result);
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique des formations:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

module.exports = router;
