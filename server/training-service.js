/**
 * Service de formation des agents
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const memoryService = require('./memory-service');
const agentService = require('./agent-service');
const ollama = require('./ollama-service');

// État de la formation
let trainingState = {
    isTraining: false,
    progress: 0,
    currentAgent: null,
    currentDataset: null,
    startTime: null,
    endTime: null,
    results: null,
    error: null
};

// Historique des formations
let trainingHistory = [];

// Chemin vers les données
const DATA_DIR = path.join(__dirname, '../data');
const TRAINING_DIR = path.join(DATA_DIR, 'training');
const DATASETS_DIR = path.join(TRAINING_DIR, 'datasets');
const HISTORY_FILE = path.join(TRAINING_DIR, 'history.json');

/**
 * Initialise le service de formation
 */
async function initialize() {
    try {
        // Créer les répertoires s'ils n'existent pas
        await createDirectories();

        // Charger l'historique des formations
        await loadTrainingHistory();

        console.log('Service de formation initialisé');
    } catch (error) {
        console.error('Erreur lors de l\'initialisation du service de formation:', error);
    }
}

/**
 * Crée les répertoires nécessaires
 */
async function createDirectories() {
    try {
        // Créer le répertoire de formation
        await fs.mkdir(TRAINING_DIR, { recursive: true });

        // Créer le répertoire des ensembles de données
        await fs.mkdir(DATASETS_DIR, { recursive: true });
    } catch (error) {
        console.error('Erreur lors de la création des répertoires:', error);
        throw error;
    }
}

/**
 * Charge l'historique des formations
 */
async function loadTrainingHistory() {
    try {
        // Vérifier si le fichier d'historique existe
        try {
            await fs.access(HISTORY_FILE);
        } catch (error) {
            // Créer un fichier d'historique vide
            await fs.writeFile(HISTORY_FILE, JSON.stringify([]));
            trainingHistory = [];
            return;
        }

        // Charger l'historique
        const historyData = await fs.readFile(HISTORY_FILE, 'utf8');
        trainingHistory = JSON.parse(historyData);
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des formations:', error);
        trainingHistory = [];
    }
}

/**
 * Sauvegarde l'historique des formations
 */
async function saveTrainingHistory() {
    try {
        await fs.writeFile(HISTORY_FILE, JSON.stringify(trainingHistory, null, 2));
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'historique des formations:', error);
    }
}

/**
 * Récupère les ensembles de données disponibles
 */
async function getDatasets() {
    try {
        // Lire le contenu du répertoire des ensembles de données
        const files = await fs.readdir(DATASETS_DIR);
        
        // Filtrer les fichiers JSON
        const jsonFiles = files.filter(file => file.endsWith('.json'));
        
        // Charger les ensembles de données
        const datasets = [];
        
        for (const file of jsonFiles) {
            try {
                const filePath = path.join(DATASETS_DIR, file);
                const fileData = await fs.readFile(filePath, 'utf8');
                const dataset = JSON.parse(fileData);
                
                // Ajouter le nombre d'échantillons
                dataset.samples = dataset.data ? dataset.data.length : 0;
                
                // Ne pas inclure les données complètes pour économiser de la bande passante
                const { data, ...datasetInfo } = dataset;
                
                datasets.push(datasetInfo);
            } catch (error) {
                console.error(`Erreur lors du chargement de l'ensemble de données ${file}:`, error);
            }
        }
        
        return datasets;
    } catch (error) {
        console.error('Erreur lors de la récupération des ensembles de données:', error);
        return [];
    }
}

/**
 * Récupère un ensemble de données par son ID
 */
async function getDatasetById(datasetId) {
    try {
        const filePath = path.join(DATASETS_DIR, `${datasetId}.json`);
        
        // Vérifier si le fichier existe
        try {
            await fs.access(filePath);
        } catch (error) {
            return null;
        }
        
        // Charger l'ensemble de données
        const fileData = await fs.readFile(filePath, 'utf8');
        return JSON.parse(fileData);
    } catch (error) {
        console.error(`Erreur lors de la récupération de l'ensemble de données ${datasetId}:`, error);
        return null;
    }
}

/**
 * Crée un nouvel ensemble de données
 */
async function createDataset(datasetInfo) {
    try {
        // Générer un ID unique si non fourni
        const datasetId = datasetInfo.id || `dataset_${uuidv4()}`;
        
        // Préparer les données de l'ensemble de données
        const dataset = {
            id: datasetId,
            name: datasetInfo.name || 'Nouvel ensemble de données',
            description: datasetInfo.description || '',
            createdAt: datasetInfo.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            data: datasetInfo.data || []
        };
        
        // Sauvegarder l'ensemble de données
        const filePath = path.join(DATASETS_DIR, `${datasetId}.json`);
        await fs.writeFile(filePath, JSON.stringify(dataset, null, 2));
        
        return dataset;
    } catch (error) {
        console.error('Erreur lors de la création de l\'ensemble de données:', error);
        throw error;
    }
}

/**
 * Lance une formation
 */
async function startTraining(trainingOptions) {
    try {
        // Vérifier si une formation est déjà en cours
        if (trainingState.isTraining) {
            throw new Error('Une formation est déjà en cours');
        }
        
        // Récupérer l'agent
        const agent = await agentService.getAgentById(trainingOptions.agentId);
        if (!agent) {
            throw new Error(`Agent ${trainingOptions.agentId} non trouvé`);
        }
        
        // Récupérer l'agent de formation
        const trainingAgent = await agentService.getAgentById(trainingOptions.trainingAgentId);
        if (!trainingAgent) {
            throw new Error(`Agent de formation ${trainingOptions.trainingAgentId} non trouvé`);
        }
        
        // Récupérer l'ensemble de données
        const dataset = await getDatasetById(trainingOptions.datasetId);
        if (!dataset) {
            throw new Error(`Ensemble de données ${trainingOptions.datasetId} non trouvé`);
        }
        
        // Vérifier que l'ensemble de données contient des données
        if (!dataset.data || dataset.data.length === 0) {
            throw new Error('L\'ensemble de données ne contient aucune donnée');
        }
        
        // Mettre à jour l'état de la formation
        trainingState = {
            isTraining: true,
            progress: 0,
            currentAgent: agent,
            currentDataset: dataset,
            startTime: new Date(),
            endTime: null,
            results: {
                agentId: agent.id,
                trainingAgentId: trainingAgent.id,
                datasetId: dataset.id,
                options: trainingOptions.options || {},
                samplesProcessed: 0,
                accuracy: 0,
                loss: 0,
                memoryEntries: 0
            },
            error: null
        };
        
        // Lancer la formation en arrière-plan
        trainInBackground(agent, trainingAgent, dataset, trainingOptions.options || {})
            .catch(error => {
                console.error('Erreur lors de la formation en arrière-plan:', error);
                
                // Mettre à jour l'état de la formation en cas d'erreur
                trainingState.isTraining = false;
                trainingState.endTime = new Date();
                trainingState.error = error.message;
            });
        
        return { success: true };
    } catch (error) {
        console.error('Erreur lors du lancement de la formation:', error);
        return { success: false, error: error.message };
    }
}

/**
 * Effectue la formation en arrière-plan
 */
async function trainInBackground(agent, trainingAgent, dataset, options) {
    try {
        // Options par défaut
        const trainingOptions = {
            epochs: options.epochs || 1,
            batchSize: options.batchSize || 5,
            learningRate: options.learningRate || 0.001,
            useMemory: options.useMemory !== false,
            saveToMemory: options.saveToMemory !== false
        };
        
        // Nombre total d'échantillons à traiter
        const totalSamples = dataset.data.length * trainingOptions.epochs;
        let samplesProcessed = 0;
        
        // Statistiques de formation
        let accuracy = 0;
        let loss = 0;
        let memoryEntries = 0;
        
        // Pour chaque époque
        for (let epoch = 0; epoch < trainingOptions.epochs; epoch++) {
            console.log(`Époque ${epoch + 1}/${trainingOptions.epochs}`);
            
            // Mélanger les données pour cette époque
            const shuffledData = [...dataset.data].sort(() => Math.random() - 0.5);
            
            // Traiter les données par lots
            for (let i = 0; i < shuffledData.length; i += trainingOptions.batchSize) {
                // Vérifier si la formation a été annulée
                if (!trainingState.isTraining) {
                    console.log('Formation annulée');
                    return;
                }
                
                // Extraire le lot actuel
                const batch = shuffledData.slice(i, i + trainingOptions.batchSize);
                
                // Traiter chaque échantillon du lot
                for (const sample of batch) {
                    // Traiter l'échantillon
                    const result = await processSample(agent, trainingAgent, sample, trainingOptions);
                    
                    // Mettre à jour les statistiques
                    accuracy += result.accuracy;
                    loss += result.loss;
                    memoryEntries += result.memoryEntries;
                    
                    // Mettre à jour le nombre d'échantillons traités
                    samplesProcessed++;
                    
                    // Mettre à jour la progression
                    trainingState.progress = (samplesProcessed / totalSamples) * 100;
                    trainingState.results.samplesProcessed = samplesProcessed;
                    trainingState.results.accuracy = accuracy / samplesProcessed;
                    trainingState.results.loss = loss / samplesProcessed;
                    trainingState.results.memoryEntries = memoryEntries;
                }
            }
        }
        
        // Formation terminée
        trainingState.isTraining = false;
        trainingState.progress = 100;
        trainingState.endTime = new Date();
        
        // Calculer la durée de la formation
        const duration = trainingState.endTime - trainingState.startTime;
        
        // Mettre à jour les résultats finaux
        trainingState.results.duration = duration;
        
        // Ajouter l'entrée à l'historique des formations
        const historyEntry = {
            id: uuidv4(),
            agentId: agent.id,
            trainingAgentId: trainingAgent.id,
            datasetId: dataset.id,
            options: trainingOptions,
            startTime: trainingState.startTime,
            endTime: trainingState.endTime,
            duration: duration,
            samplesProcessed: samplesProcessed,
            accuracy: trainingState.results.accuracy,
            loss: trainingState.results.loss,
            memoryEntries: memoryEntries
        };
        
        trainingHistory.push(historyEntry);
        
        // Sauvegarder l'historique des formations
        await saveTrainingHistory();
        
        console.log('Formation terminée avec succès');
    } catch (error) {
        console.error('Erreur lors de la formation en arrière-plan:', error);
        
        // Mettre à jour l'état de la formation en cas d'erreur
        trainingState.isTraining = false;
        trainingState.endTime = new Date();
        trainingState.error = error.message;
    }
}

/**
 * Traite un échantillon de formation
 */
async function processSample(agent, trainingAgent, sample, options) {
    try {
        // Extraire l'entrée et la sortie attendue
        const { input, expectedOutput } = sample;
        
        // Résultats par défaut
        const result = {
            accuracy: 0,
            loss: 0,
            memoryEntries: 0
        };
        
        // Utiliser l'agent de formation pour générer une réponse
        let trainingResponse;
        
        if (trainingAgent.type === 'ollama') {
            // Construire le contexte avec la mémoire si nécessaire
            let context = '';
            
            if (options.useMemory) {
                // Récupérer les entrées de mémoire pertinentes
                const memoryEntries = await memoryService.getRelevantMemoryEntries(input, 5, trainingAgent.id);
                
                // Ajouter les entrées de mémoire au contexte
                if (memoryEntries.length > 0) {
                    context = 'Informations de la mémoire:\n' + memoryEntries.map(entry => `- ${entry.content}`).join('\n') + '\n\n';
                }
            }
            
            // Ajouter l'entrée et la sortie attendue au contexte
            context += `Entrée: ${input}\n\nSortie attendue: ${expectedOutput}\n\nGénère une réponse similaire à la sortie attendue:`;
            
            // Générer une réponse avec Ollama
            trainingResponse = await ollama.generateResponse(trainingAgent.model, context, {
                temperature: trainingAgent.temperature || 0.5,
                max_tokens: trainingAgent.maxTokens || 2000
            });
        } else {
            // Type d'agent non pris en charge
            throw new Error(`Type d'agent de formation non pris en charge: ${trainingAgent.type}`);
        }
        
        // Calculer la précision (similarité entre la réponse générée et la sortie attendue)
        const accuracy = calculateSimilarity(trainingResponse, expectedOutput);
        
        // Calculer la perte (1 - précision)
        const loss = 1 - accuracy;
        
        // Mettre à jour les résultats
        result.accuracy = accuracy;
        result.loss = loss;
        
        // Sauvegarder dans la mémoire si nécessaire
        if (options.saveToMemory) {
            // Créer une entrée de mémoire
            const memoryEntry = {
                content: `Q: ${input}\nR: ${expectedOutput}`,
                source: 'training',
                importance: accuracy, // Plus la précision est élevée, plus l'entrée est importante
                tags: ['formation', 'apprentissage'],
                metadata: {
                    accuracy: accuracy,
                    loss: loss,
                    trainingAgentId: trainingAgent.id,
                    mainAgentId: agent.id
                }
            };
            
            // Ajouter l'entrée à la mémoire de l'agent principal
            await memoryService.addMemoryEntry(memoryEntry, agent.id);
            
            // Incrémenter le compteur d'entrées de mémoire
            result.memoryEntries = 1;
        }
        
        return result;
    } catch (error) {
        console.error('Erreur lors du traitement d\'un échantillon:', error);
        return {
            accuracy: 0,
            loss: 1,
            memoryEntries: 0
        };
    }
}

/**
 * Calcule la similarité entre deux chaînes de caractères
 */
function calculateSimilarity(str1, str2) {
    // Implémentation simple de la similarité cosinus
    // Dans une application réelle, vous pourriez utiliser une bibliothèque plus sophistiquée
    
    // Convertir les chaînes en ensembles de mots
    const words1 = str1.toLowerCase().split(/\W+/).filter(word => word.length > 0);
    const words2 = str2.toLowerCase().split(/\W+/).filter(word => word.length > 0);
    
    // Créer un ensemble de tous les mots uniques
    const uniqueWords = new Set([...words1, ...words2]);
    
    // Créer des vecteurs pour chaque chaîne
    const vector1 = Array.from(uniqueWords).map(word => words1.filter(w => w === word).length);
    const vector2 = Array.from(uniqueWords).map(word => words2.filter(w => w === word).length);
    
    // Calculer le produit scalaire
    let dotProduct = 0;
    for (let i = 0; i < vector1.length; i++) {
        dotProduct += vector1[i] * vector2[i];
    }
    
    // Calculer les normes
    const norm1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
    const norm2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));
    
    // Éviter la division par zéro
    if (norm1 === 0 || norm2 === 0) {
        return 0;
    }
    
    // Calculer la similarité cosinus
    return dotProduct / (norm1 * norm2);
}

/**
 * Annule la formation en cours
 */
function cancelTraining() {
    if (!trainingState.isTraining) {
        return { success: false, error: 'Aucune formation en cours' };
    }
    
    trainingState.isTraining = false;
    trainingState.endTime = new Date();
    
    return { success: true };
}

/**
 * Récupère l'état de la formation
 */
function getTrainingState() {
    return { success: true, state: trainingState };
}

/**
 * Récupère l'historique des formations
 */
function getTrainingHistory() {
    return { success: true, history: trainingHistory };
}

// Exporter les fonctions
module.exports = {
    initialize,
    getDatasets,
    getDatasetById,
    createDataset,
    startTraining,
    cancelTraining,
    getTrainingState,
    getTrainingHistory
};
