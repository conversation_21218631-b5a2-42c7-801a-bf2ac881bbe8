/**
 * API pour la mémoire thermique et les accélérateurs Kyber
 * 
 * Ce module expose des routes API pour interagir avec la mémoire thermique
 * et les accélérateurs Kyber de l'agent Louna.
 */

const express = require('express');
const router = express.Router();
const ThermalMemory = require('./thermal-memory-complete');
const KyberAccelerators = require('./kyber-accelerators');

// Initialiser la mémoire thermique et les accélérateurs
const thermalMemory = new ThermalMemory();
const kyberAccelerators = new KyberAccelerators();

/**
 * Routes pour la mémoire thermique
 */

// Récupérer les statistiques de la mémoire thermique
router.get('/memory/stats', (req, res) => {
  try {
    const stats = thermalMemory.getMemoryStats();
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Récupérer les entrées d'une zone spécifique
router.get('/memory/zone/:zone', (req, res) => {
  try {
    const { zone } = req.params;
    const entries = thermalMemory.getEntriesFromZone(zone);
    
    res.json({
      success: true,
      zone,
      entries
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Ajouter une entrée à la mémoire thermique
router.post('/memory/add', (req, res) => {
  try {
    const { key, data, importance, category } = req.body;
    
    if (!key || !data) {
      return res.status(400).json({
        success: false,
        error: 'Les paramètres key et data sont requis'
      });
    }
    
    const id = thermalMemory.add(key, data, importance, category);
    
    res.json({
      success: true,
      id,
      message: 'Entrée ajoutée avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Récupérer une entrée spécifique
router.get('/memory/entry/:id', (req, res) => {
  try {
    const { id } = req.params;
    const entry = thermalMemory.get(id);
    
    if (!entry) {
      return res.status(404).json({
        success: false,
        error: 'Entrée non trouvée'
      });
    }
    
    res.json({
      success: true,
      entry
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Récupérer les entrées récentes pour un contexte donné
router.get('/memory/context', (req, res) => {
  try {
    const { context, limit } = req.query;
    const entries = thermalMemory.getRecentMemoriesForContext(context, parseInt(limit) || 10);
    
    res.json({
      success: true,
      context,
      entries
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Générer un rêve
router.post('/memory/dream', (req, res) => {
  try {
    const dream = thermalMemory.generateDream();
    
    res.json({
      success: true,
      dream
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Réinitialiser la mémoire
router.post('/memory/reset', (req, res) => {
  try {
    thermalMemory.resetMemory();
    
    res.json({
      success: true,
      message: 'Mémoire réinitialisée avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Routes pour les accélérateurs Kyber
 */

// Récupérer les statistiques des accélérateurs
router.get('/accelerators/stats', (req, res) => {
  try {
    const stats = kyberAccelerators.getAcceleratorStats();
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Activer ou désactiver un accélérateur
router.post('/accelerators/toggle/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { enabled } = req.body;
    
    if (enabled === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Le paramètre enabled est requis'
      });
    }
    
    const success = kyberAccelerators.toggleAccelerator(id, enabled);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Accélérateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: `Accélérateur ${id} ${enabled ? 'activé' : 'désactivé'} avec succès`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Ajuster le facteur de boost d'un accélérateur
router.post('/accelerators/boost/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { boostFactor } = req.body;
    
    if (boostFactor === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Le paramètre boostFactor est requis'
      });
    }
    
    const success = kyberAccelerators.adjustBoostFactor(id, parseFloat(boostFactor));
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Accélérateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: `Facteur de boost de l'accélérateur ${id} ajusté à ${boostFactor} avec succès`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Réinitialiser les accélérateurs
router.post('/accelerators/reset', (req, res) => {
  try {
    kyberAccelerators.resetAccelerators();
    
    res.json({
      success: true,
      message: 'Accélérateurs réinitialisés avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Appliquer un boost à une valeur
router.post('/accelerators/apply', (req, res) => {
  try {
    const { type, baseValue } = req.body;
    
    if (!type || baseValue === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Les paramètres type et baseValue sont requis'
      });
    }
    
    const boostedValue = kyberAccelerators.applyBoost(type, parseFloat(baseValue));
    
    res.json({
      success: true,
      type,
      baseValue: parseFloat(baseValue),
      boostedValue,
      boostFactor: boostedValue / parseFloat(baseValue)
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
