/**
 * INTÉGRATION DU SYSTÈME DE MÉMOIRE BIOLOGIQUE
 * Remplace l'ancien système défaillant par une vraie mémoire humaine
 */

const BiologicalMemorySystem = require('./biological-memory-system');

class MemoryIntegration {
    constructor() {
        // Créer le nouveau système biologique
        this.biologicalMemory = new BiologicalMemorySystem({
            // Configuration optimisée pour Louna
            sensoryBufferCapacity: 10,
            workingMemoryCapacity: 6,
            shortTermCapacity: 30,
            longTermCapacity: 100000,
            
            // Temporalités adaptées
            sensoryDecayTime: 3000,
            workingMemoryDecayTime: 20000,
            shortTermDecayTime: 600000,
            consolidationTime: 1800000,
            
            // Facteurs biologiques renforcés
            emotionalBoost: 3.0,
            repetitionBoost: 2.2,
            associationStrength: 0.8,
            forgettingCurve: 0.88,
            
            // Cycles optimisés
            sleepCycleInterval: 3600000, // 1 heure
            dreamProbability: 0.4,
            consolidationRate: 0.2
        });
        
        // Événements biologiques
        this.setupBiologicalEvents();
        
        // Charger l'état précédent
        this.biologicalMemory.loadBiologicalState();
        
        console.log('🧬 Intégration mémoire biologique initialisée');
        console.log('🔄 Remplacement de l\'ancien système défaillant');
    }
    
    /**
     * Configurer les événements biologiques
     */
    setupBiologicalEvents() {
        this.biologicalMemory.on('memoryAdded', (memory) => {
            console.log(`🧠 Nouvelle mémoire: ${memory.data.substring(0, 30)}... (Zone: ${memory.zone})`);
        });
        
        this.biologicalMemory.on('sleepConsolidationComplete', (data) => {
            console.log(`😴 Consolidation: ${data.consolidated} mémoires transférées vers long terme`);
        });
        
        this.biologicalMemory.on('dreamGenerated', (dream) => {
            console.log(`💭 Rêve: ${dream.content.substring(0, 40)}...`);
        });
        
        this.biologicalMemory.on('associationsActivated', (data) => {
            console.log(`🔗 ${data.activated.length} associations activées`);
        });
        
        this.biologicalMemory.on('mentalFatigue', (data) => {
            console.log(`😴 Fatigue mentale: efficacité réduite à ${(data.efficiency * 100).toFixed(1)}%`);
        });
    }
    
    /**
     * Ajouter une mémoire (interface compatible)
     */
    add(key, data, category = 'general', importance = 0.5, metadata = {}) {
        // Détecter les émotions dans les métadonnées
        let emotion = null;
        if (metadata.emotion) {
            emotion = {
                type: metadata.emotion.type || 'neutral',
                intensity: metadata.emotion.intensity || 0.5,
                valence: metadata.emotion.valence || 0.5
            };
        }
        
        // Ajuster l'importance selon la catégorie
        if (category === 'important' || category === 'critical') {
            importance = Math.max(importance, 0.8);
        }
        
        return this.biologicalMemory.addMemory(data, emotion, importance);
    }
    
    /**
     * Récupérer une mémoire (interface compatible)
     */
    get(query) {
        return this.biologicalMemory.retrieveMemory(query);
    }
    
    /**
     * Rechercher des mémoires
     */
    search(query, limit = 10) {
        const allMemories = this.biologicalMemory.getAllMemories();
        const results = [];
        
        for (const memory of allMemories) {
            const similarity = this.biologicalMemory.calculateSimilarity(query, memory.data);
            if (similarity > 0.2) {
                results.push({
                    memory,
                    similarity,
                    zone: memory.zone
                });
            }
        }
        
        return results
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, limit)
            .map(result => result.memory);
    }
    
    /**
     * Obtenir le statut (interface compatible avec l'ancien système)
     */
    getStatus() {
        const bioStatus = this.biologicalMemory.getBiologicalStatus();
        
        // Format compatible avec l'ancien système
        return {
            globalTemp: bioStatus.biologicalState.emotionalInfluence,
            totalMemories: bioStatus.memoryZones.sensoryBuffer.count + 
                          bioStatus.memoryZones.workingMemory.count +
                          bioStatus.memoryZones.shortTermMemory.count +
                          bioStatus.memoryZones.longTermMemory.count,
            zones: [
                {
                    name: "Sensorielle",
                    temperature: bioStatus.biologicalState.emotionalState.arousal,
                    count: bioStatus.memoryZones.sensoryBuffer.count,
                    active: true,
                    capacity: bioStatus.memoryZones.sensoryBuffer.capacity,
                    usage: bioStatus.memoryZones.sensoryBuffer.usage
                },
                {
                    name: "Travail",
                    temperature: bioStatus.biologicalState.emotionalState.valence,
                    count: bioStatus.memoryZones.workingMemory.count,
                    active: true,
                    capacity: bioStatus.memoryZones.workingMemory.capacity,
                    usage: bioStatus.memoryZones.workingMemory.usage
                },
                {
                    name: "Court Terme",
                    temperature: bioStatus.biologicalState.emotionalInfluence,
                    count: bioStatus.memoryZones.shortTermMemory.count,
                    active: true,
                    capacity: bioStatus.memoryZones.shortTermMemory.capacity,
                    usage: bioStatus.memoryZones.shortTermMemory.usage
                },
                {
                    name: "Long Terme",
                    temperature: 0.3 + (bioStatus.performance.memoryEfficiency * 0.4),
                    count: bioStatus.memoryZones.longTermMemory.count,
                    active: true,
                    capacity: bioStatus.memoryZones.longTermMemory.capacity,
                    usage: bioStatus.memoryZones.longTermMemory.usage
                },
                {
                    name: "Émotionnelle",
                    temperature: bioStatus.biologicalState.emotionalState.valence,
                    count: bioStatus.memoryZones.emotionalMemory.count,
                    active: true
                },
                {
                    name: "Rêves",
                    temperature: bioStatus.biologicalState.sleepDepth,
                    count: bioStatus.memoryZones.dreamMemory.count,
                    active: bioStatus.biologicalState.isAsleep
                }
            ],
            isActive: true,
            lastActivity: new Date().toISOString(),
            
            // Données biologiques supplémentaires
            biologicalData: bioStatus,
            memoryEfficiency: bioStatus.performance.memoryEfficiency,
            consolidationRate: bioStatus.performance.consolidationRate,
            forgettingRate: bioStatus.performance.forgettingRate,
            neuralConnections: bioStatus.neuralConnections.totalConnections,
            isAsleep: bioStatus.biologicalState.isAsleep,
            emotionalState: bioStatus.biologicalState.emotionalState
        };
    }
    
    /**
     * Forcer une consolidation (comme dormir)
     */
    forceConsolidation() {
        console.log('💤 Déclenchement forcé de la consolidation...');
        this.biologicalMemory.performSleepConsolidation();
    }
    
    /**
     * Générer un rêve manuellement
     */
    generateDream() {
        console.log('💭 Génération manuelle d\'un rêve...');
        this.biologicalMemory.generateDream();
    }
    
    /**
     * Simuler une émotion forte (influence la mémoire)
     */
    triggerEmotion(type, intensity = 0.8) {
        const emotions = {
            joy: { valence: 0.9, arousal: 0.8, dominance: 0.7 },
            fear: { valence: 0.1, arousal: 0.9, dominance: 0.2 },
            anger: { valence: 0.2, arousal: 0.8, dominance: 0.8 },
            sadness: { valence: 0.2, arousal: 0.3, dominance: 0.3 },
            surprise: { valence: 0.6, arousal: 0.9, dominance: 0.5 },
            calm: { valence: 0.7, arousal: 0.2, dominance: 0.6 }
        };
        
        if (emotions[type]) {
            Object.keys(emotions[type]).forEach(key => {
                this.biologicalMemory.emotionalState[key] = 
                    emotions[type][key] * intensity + 
                    this.biologicalMemory.emotionalState[key] * (1 - intensity);
            });
            
            console.log(`😊 Émotion ${type} déclenchée (intensité: ${intensity})`);
        }
    }
    
    /**
     * Obtenir les mémoires récentes
     */
    getRecentMemories(limit = 20) {
        return this.biologicalMemory.getRecentMemories(limit);
    }
    
    /**
     * Obtenir les associations d'une mémoire
     */
    getAssociations(memoryId) {
        const connections = this.biologicalMemory.neuralConnections.get(memoryId) || [];
        return connections.map(conn => ({
            targetMemory: this.biologicalMemory.findMemoryById(conn.targetId),
            strength: conn.strength,
            created: conn.created
        })).filter(assoc => assoc.targetMemory);
    }
    
    /**
     * Sauvegarder l'état
     */
    save() {
        this.biologicalMemory.saveBiologicalState();
    }
    
    /**
     * Obtenir les statistiques détaillées
     */
    getDetailedStats() {
        const bioStatus = this.biologicalMemory.getBiologicalStatus();
        
        return {
            memoryZones: bioStatus.memoryZones,
            biologicalState: bioStatus.biologicalState,
            neuralConnections: bioStatus.neuralConnections,
            dailyStats: bioStatus.dailyStats,
            performance: bioStatus.performance,
            
            // Métriques avancées
            cognitiveLoad: this.calculateCognitiveLoad(),
            memoryHealth: this.calculateMemoryHealth(),
            learningRate: this.calculateLearningRate()
        };
    }
    
    /**
     * Calculer la charge cognitive
     */
    calculateCognitiveLoad() {
        const bioStatus = this.biologicalMemory.getBiologicalStatus();
        const workingLoad = bioStatus.memoryZones.workingMemory.usage;
        const sensoryLoad = bioStatus.memoryZones.sensoryBuffer.usage;
        
        return (workingLoad + sensoryLoad) / 2;
    }
    
    /**
     * Calculer la santé de la mémoire
     */
    calculateMemoryHealth() {
        const bioStatus = this.biologicalMemory.getBiologicalStatus();
        const efficiency = bioStatus.performance.memoryEfficiency;
        const consolidation = bioStatus.performance.consolidationRate;
        const forgetting = 1 - bioStatus.performance.forgettingRate;
        
        return (efficiency + consolidation + forgetting) / 3;
    }
    
    /**
     * Calculer le taux d'apprentissage
     */
    calculateLearningRate() {
        const bioStatus = this.biologicalMemory.getBiologicalStatus();
        const associations = bioStatus.performance.associationDensity;
        const consolidation = bioStatus.performance.consolidationRate;
        
        return (associations + consolidation) / 2;
    }
}

module.exports = MemoryIntegration;
