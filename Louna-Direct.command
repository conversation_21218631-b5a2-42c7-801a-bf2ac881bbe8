#!/bin/bash

# Script de lancement direct pour Louna
# Version la plus simple possible

# Couleurs
GREEN='\033[0;32m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
NC='\033[0m'

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"

echo -e "${PURPLE}🚀 Lancement direct de Louna...${NC}"

# Vérifier si le répertoire existe
if [ ! -d "$APP_DIR" ]; then
  echo -e "${RED}❌ Répertoire non trouvé: $APP_DIR${NC}"
  exit 1
fi

# Aller dans le répertoire
cd "$APP_DIR" || exit 1

# Arrêter les processus existants
pkill -f "electron" 2>/dev/null || true
sleep 1

# Lancer directement avec npm
echo -e "${GREEN}✅ Lancement de l'application...${NC}"
npm run electron

echo -e "${PURPLE}📱 Application fermée${NC}"
