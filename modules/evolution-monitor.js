/**
 * 🧬 MONITEUR D'ÉVOLUTION COGNITIVE
 * ================================
 * Surveillance en temps réel de l'évolution de l'agent
 * Créé par <PERSON>, Guadeloupe
 */

const fs = require('fs');
const path = require('path');

class EvolutionMonitor {
    constructor() {
        this.startTime = Date.now();
        this.evolutionHistory = [];
        this.currentMetrics = {
            qi: 203,
            neurons: { total: 89, active: 78, efficiency: 94.0 },
            kyber: { active: 8, total: 16, performance: 89.5 },
            memory: { efficiency: 96.3, capacity: 2048, used: 1247 },
            modules: { total: 12, active: 12, integration: 100 }
        };
        
        this.thresholds = {
            qi: { min: 100, max: 250, warning: 200 },
            evolution_rate: { max: 10 }, // points QI par heure
            neuron_efficiency: { min: 80, warning: 95 },
            memory_usage: { max: 90 } // pourcentage
        };
        
        this.alerts = [];
        this.isMonitoring = false;
        
        console.log('🧬 Moniteur d\'évolution initialisé');
        this.startMonitoring();
    }

    /**
     * Démarrer la surveillance
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        console.log('🔍 Surveillance de l\'évolution démarrée');
        
        // Enregistrer l'état initial
        this.recordEvolution('INIT', 'Démarrage du monitoring', this.currentMetrics.qi);
        
        // Surveillance continue toutes les 30 secondes
        this.monitoringInterval = setInterval(() => {
            this.checkEvolution();
        }, 30000);
        
        // Sauvegarde périodique toutes les 5 minutes
        this.saveInterval = setInterval(() => {
            this.saveEvolutionData();
        }, 300000);
    }

    /**
     * Arrêter la surveillance
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        if (this.saveInterval) {
            clearInterval(this.saveInterval);
        }
        
        console.log('⏹️ Surveillance de l\'évolution arrêtée');
    }

    /**
     * Vérifier l'évolution
     */
    checkEvolution() {
        const previousQI = this.evolutionHistory.length > 0 ? 
            this.evolutionHistory[this.evolutionHistory.length - 1].qi : 148;
        
        const currentQI = this.currentMetrics.qi;
        const qiChange = currentQI - previousQI;
        const timeElapsed = (Date.now() - this.startTime) / (1000 * 60 * 60); // heures
        const evolutionRate = timeElapsed > 0 ? qiChange / timeElapsed : 0;
        
        // Vérifier les seuils
        this.checkThresholds(currentQI, evolutionRate);
        
        // Enregistrer l'évolution
        if (Math.abs(qiChange) > 0) {
            this.recordEvolution('UPDATE', `QI: ${previousQI} → ${currentQI} (${qiChange > 0 ? '+' : ''}${qiChange})`, currentQI);
        }
        
        // Analyser les patterns
        this.analyzeEvolutionPatterns();
        
        console.log(`🧬 Évolution check: QI=${currentQI}, Rate=${evolutionRate.toFixed(2)}/h`);
    }

    /**
     * Vérifier les seuils d'alerte
     */
    checkThresholds(currentQI, evolutionRate) {
        const alerts = [];
        
        // Vérifier QI
        if (currentQI > this.thresholds.qi.warning) {
            alerts.push({
                type: 'WARNING',
                category: 'QI',
                message: `QI exceptionnellement élevé: ${currentQI}`,
                severity: 'HIGH',
                timestamp: new Date().toISOString()
            });
        }
        
        if (currentQI > this.thresholds.qi.max) {
            alerts.push({
                type: 'CRITICAL',
                category: 'QI',
                message: `QI au-delà du seuil maximum: ${currentQI}`,
                severity: 'CRITICAL',
                timestamp: new Date().toISOString()
            });
        }
        
        // Vérifier taux d'évolution
        if (Math.abs(evolutionRate) > this.thresholds.evolution_rate.max) {
            alerts.push({
                type: 'WARNING',
                category: 'EVOLUTION_RATE',
                message: `Taux d'évolution anormalement rapide: ${evolutionRate.toFixed(2)} points/h`,
                severity: 'HIGH',
                timestamp: new Date().toISOString()
            });
        }
        
        // Vérifier efficacité neuronale
        if (this.currentMetrics.neurons.efficiency > this.thresholds.neuron_efficiency.warning) {
            alerts.push({
                type: 'INFO',
                category: 'NEURONS',
                message: `Efficacité neuronale exceptionnelle: ${this.currentMetrics.neurons.efficiency}%`,
                severity: 'LOW',
                timestamp: new Date().toISOString()
            });
        }
        
        // Ajouter les nouvelles alertes
        this.alerts.push(...alerts);
        
        // Limiter le nombre d'alertes
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(-50);
        }
        
        // Logger les alertes critiques
        alerts.forEach(alert => {
            if (alert.severity === 'CRITICAL') {
                console.error(`🚨 ALERTE CRITIQUE: ${alert.message}`);
            } else if (alert.severity === 'HIGH') {
                console.warn(`⚠️ ALERTE: ${alert.message}`);
            }
        });
    }

    /**
     * Enregistrer une évolution
     */
    recordEvolution(type, description, qi) {
        const evolution = {
            timestamp: new Date().toISOString(),
            type: type,
            description: description,
            qi: qi,
            metrics: { ...this.currentMetrics },
            timeFromStart: Date.now() - this.startTime
        };
        
        this.evolutionHistory.push(evolution);
        
        // Limiter l'historique
        if (this.evolutionHistory.length > 1000) {
            this.evolutionHistory = this.evolutionHistory.slice(-500);
        }
        
        console.log(`📊 Évolution enregistrée: ${description}`);
    }

    /**
     * Analyser les patterns d'évolution
     */
    analyzeEvolutionPatterns() {
        if (this.evolutionHistory.length < 3) return;
        
        const recent = this.evolutionHistory.slice(-5);
        const qiValues = recent.map(e => e.qi);
        
        // Détecter les tendances
        const isIncreasing = qiValues.every((val, i) => i === 0 || val >= qiValues[i-1]);
        const isDecreasing = qiValues.every((val, i) => i === 0 || val <= qiValues[i-1]);
        
        if (isIncreasing && qiValues.length >= 3) {
            const growth = qiValues[qiValues.length - 1] - qiValues[0];
            if (growth > 20) {
                this.alerts.push({
                    type: 'PATTERN',
                    category: 'GROWTH_TREND',
                    message: `Tendance de croissance détectée: +${growth} points`,
                    severity: 'MEDIUM',
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        // Détecter les plateaux
        const isStable = qiValues.every(val => Math.abs(val - qiValues[0]) <= 2);
        if (isStable && qiValues.length >= 4) {
            this.alerts.push({
                type: 'PATTERN',
                category: 'STABILITY',
                message: `Plateau de stabilité détecté à QI ${qiValues[0]}`,
                severity: 'LOW',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Mettre à jour les métriques
     */
    updateMetrics(newMetrics) {
        const oldQI = this.currentMetrics.qi;
        this.currentMetrics = { ...this.currentMetrics, ...newMetrics };
        
        if (newMetrics.qi && newMetrics.qi !== oldQI) {
            this.recordEvolution('METRIC_UPDATE', `Mise à jour QI: ${oldQI} → ${newMetrics.qi}`, newMetrics.qi);
        }
    }

    /**
     * Obtenir le rapport d'évolution
     */
    getEvolutionReport() {
        const timeElapsed = (Date.now() - this.startTime) / (1000 * 60 * 60);
        const initialQI = this.evolutionHistory.length > 0 ? this.evolutionHistory[0].qi : 148;
        const currentQI = this.currentMetrics.qi;
        const totalGrowth = currentQI - initialQI;
        const averageGrowthRate = timeElapsed > 0 ? totalGrowth / timeElapsed : 0;
        
        return {
            startTime: new Date(this.startTime).toISOString(),
            timeElapsed: timeElapsed,
            initialQI: initialQI,
            currentQI: currentQI,
            totalGrowth: totalGrowth,
            averageGrowthRate: averageGrowthRate,
            evolutionHistory: this.evolutionHistory,
            alerts: this.alerts,
            currentMetrics: this.currentMetrics,
            summary: {
                evolutionEvents: this.evolutionHistory.length,
                alertsGenerated: this.alerts.length,
                criticalAlerts: this.alerts.filter(a => a.severity === 'CRITICAL').length,
                isStable: Math.abs(averageGrowthRate) < 1,
                isRapidEvolution: Math.abs(averageGrowthRate) > 10
            }
        };
    }

    /**
     * Sauvegarder les données d'évolution
     */
    saveEvolutionData() {
        try {
            const report = this.getEvolutionReport();
            const filename = `evolution-report-${new Date().toISOString().split('T')[0]}.json`;
            const filepath = path.join(__dirname, '..', 'data', filename);
            
            // Créer le dossier data s'il n'existe pas
            const dataDir = path.dirname(filepath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            
            fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
            console.log(`💾 Données d'évolution sauvegardées: ${filename}`);
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde évolution:', error);
        }
    }

    /**
     * Obtenir les alertes récentes
     */
    getRecentAlerts(limit = 10) {
        return this.alerts.slice(-limit).reverse();
    }

    /**
     * Nettoyer les anciennes données
     */
    cleanup() {
        // Garder seulement les 500 dernières évolutions
        if (this.evolutionHistory.length > 500) {
            this.evolutionHistory = this.evolutionHistory.slice(-500);
        }
        
        // Garder seulement les 100 dernières alertes
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(-100);
        }
        
        console.log('🧹 Nettoyage des données d\'évolution effectué');
    }
}

module.exports = EvolutionMonitor;
