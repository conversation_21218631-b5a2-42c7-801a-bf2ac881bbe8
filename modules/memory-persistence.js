/**
 * Système de Persistance Mémoire Thermique
 * Solution au problème de perte de données en mémoire instantanée
 * Créé par Jean<PERSON>, Guadeloupe
 */

class ThermalMemoryPersistence {
    constructor() {
        this.instantMemory = [];
        this.autoSaveInterval = null;
        this.saveFrequency = 5000; // 5 secondes
        this.maxInstantMemorySize = 100;
        this.saveCount = 0;
        this.isInitialized = false;

        console.log('🧠 ThermalMemoryPersistence initialisé');
    }

    /**
     * Initialise le système de persistance
     */
    async initialize() {
        try {
            console.log('🚀 Initialisation du système de persistance mémoire...');

            // Récupérer les données sauvegardées
            await this.recoverSavedData();

            // Démarrer l'auto-sauvegarde
            this.startAutoSave();

            // Configurer la détection d'arrêt
            this.setupShutdownDetection();

            // Initialiser IndexedDB
            await this.initializeIndexedDB();

            this.isInitialized = true;
            console.log('✅ Système de persistance mémoire initialisé avec succès');

            return { success: true, message: 'Système de persistance initialisé' };
        } catch (error) {
            console.error('❌ Erreur initialisation persistance:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Démarre l'auto-sauvegarde continue
     */
    startAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        this.autoSaveInterval = setInterval(() => {
            this.saveInstantMemoryToStorage();
        }, this.saveFrequency);

        console.log(`⏰ Auto-sauvegarde démarrée (${this.saveFrequency}ms)`);
    }

    /**
     * Arrête l'auto-sauvegarde
     */
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
            console.log('⏹️ Auto-sauvegarde arrêtée');
        }
    }

    /**
     * Ajoute des données à la mémoire instantanée
     */
    addToInstantMemory(data, options = {}) {
        const memoryItem = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            data: data,
            critical: options.critical || false,
            type: options.type || 'general',
            source: options.source || 'unknown'
        };

        this.instantMemory.push(memoryItem);

        // Sauvegarde immédiate pour données critiques
        if (options.critical) {
            this.saveInstantMemoryToStorage();
            console.log('💾 Sauvegarde immédiate (données critiques)');
        }

        // Limiter la taille de la mémoire instantanée
        this.limitInstantMemorySize();

        console.log(`📝 Ajouté à la mémoire instantanée: ${memoryItem.type}`);
        return memoryItem.id;
    }

    /**
     * Limite la taille de la mémoire instantanée
     */
    limitInstantMemorySize() {
        if (this.instantMemory.length > this.maxInstantMemorySize) {
            const removed = this.instantMemory.splice(0, this.instantMemory.length - this.maxInstantMemorySize);
            console.log(`🗑️ ${removed.length} anciens éléments supprimés de la mémoire instantanée`);
        }
    }

    /**
     * Sauvegarde la mémoire instantanée
     */
    async saveInstantMemoryToStorage() {
        try {
            const saveData = {
                timestamp: new Date().toISOString(),
                version: '2.1.0',
                memory: this.instantMemory,
                metadata: {
                    saveCount: ++this.saveCount,
                    memorySize: this.instantMemory.length
                }
            };

            if (typeof localStorage !== 'undefined') {
                // Côté client (navigateur)
                localStorage.setItem('louna_thermal_memory', JSON.stringify(saveData));
                await this.saveToIndexedDB(saveData);
            } else {
                // Côté serveur (Node.js) - utiliser le système de fichiers
                const fs = require('fs').promises;
                const path = require('path');

                try {
                    const dataDir = path.join(process.cwd(), 'data', 'persistence');
                    await fs.mkdir(dataDir, { recursive: true });

                    const filePath = path.join(dataDir, 'thermal_memory.json');
                    await fs.writeFile(filePath, JSON.stringify(saveData, null, 2), 'utf8');
                } catch (fsError) {
                    console.warn('⚠️ Erreur sauvegarde fichier:', fsError.message);
                }
            }

            console.log(`💾 Auto-sauvegarde #${this.saveCount} effectuée (${this.instantMemory.length} éléments)`);

            return { success: true, saveCount: this.saveCount };
        } catch (error) {
            console.error('❌ Erreur auto-sauvegarde:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Sauvegarde dans IndexedDB
     */
    async saveToIndexedDB(data) {
        return new Promise((resolve, reject) => {
            if (!('indexedDB' in window)) {
                console.warn('⚠️ IndexedDB non disponible');
                resolve();
                return;
            }

            const request = indexedDB.open('LounaThermalMemory', 1);

            request.onerror = () => reject(request.error);

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['memory'], 'readwrite');
                const store = transaction.objectStore('memory');

                const saveRequest = store.put({
                    id: 'current',
                    ...data
                });

                saveRequest.onsuccess = () => resolve();
                saveRequest.onerror = () => reject(saveRequest.error);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('memory')) {
                    db.createObjectStore('memory', { keyPath: 'id' });
                }
            };
        });
    }

    /**
     * Initialise IndexedDB
     */
    async initializeIndexedDB() {
        return new Promise((resolve, reject) => {
            if (!('indexedDB' in window)) {
                console.warn('⚠️ IndexedDB non disponible, utilisation LocalStorage uniquement');
                resolve();
                return;
            }

            const request = indexedDB.open('LounaThermalMemory', 1);

            request.onerror = () => {
                console.error('❌ Erreur ouverture IndexedDB');
                resolve(); // Continue sans IndexedDB
            };

            request.onsuccess = () => {
                console.log('✅ IndexedDB initialisé');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('memory')) {
                    const store = db.createObjectStore('memory', { keyPath: 'id' });
                    console.log('📦 Store IndexedDB créé');
                }
            };
        });
    }

    /**
     * Configure la détection d'arrêt
     */
    setupShutdownDetection() {
        // Vérifier si on est côté serveur ou client
        if (typeof window !== 'undefined') {
            // Côté client (navigateur)
            window.addEventListener('beforeunload', (event) => {
                this.emergencySave();
                event.returnValue = 'Sauvegarde en cours...';
            });

            window.addEventListener('blur', () => {
                this.saveInstantMemoryToStorage();
            });

            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.saveInstantMemoryToStorage();
                }
            });

            window.addEventListener('error', () => {
                this.emergencySave();
            });
        } else {
            // Côté serveur (Node.js)
            process.on('SIGINT', () => {
                console.log('🚨 Signal SIGINT reçu - Sauvegarde d\'urgence...');
                this.emergencySave();
                process.exit(0);
            });

            process.on('SIGTERM', () => {
                console.log('🚨 Signal SIGTERM reçu - Sauvegarde d\'urgence...');
                this.emergencySave();
                process.exit(0);
            });

            process.on('uncaughtException', (error) => {
                console.log('🚨 Exception non capturée - Sauvegarde d\'urgence...');
                this.emergencySave();
                console.error('Exception:', error);
                process.exit(1);
            });

            process.on('unhandledRejection', (reason, promise) => {
                console.log('🚨 Promesse rejetée - Sauvegarde d\'urgence...');
                this.emergencySave();
                console.error('Promesse rejetée:', reason);
            });
        }

        console.log('🛡️ Détection d\'arrêt configurée');
    }

    /**
     * Sauvegarde d'urgence
     */
    emergencySave() {
        console.log('🚨 Sauvegarde d\'urgence en cours...');

        try {
            const emergencyData = {
                timestamp: new Date().toISOString(),
                type: 'emergency',
                memory: this.instantMemory,
                context: 'emergency_shutdown',
                saveCount: this.saveCount
            };

            if (typeof localStorage !== 'undefined') {
                // Côté client (navigateur)
                localStorage.setItem('louna_emergency_save', JSON.stringify(emergencyData));
            } else {
                // Côté serveur (Node.js) - sauvegarde synchrone
                const fs = require('fs');
                const path = require('path');

                try {
                    const dataDir = path.join(process.cwd(), 'data', 'persistence');
                    if (!fs.existsSync(dataDir)) {
                        fs.mkdirSync(dataDir, { recursive: true });
                    }

                    const emergencyPath = path.join(dataDir, 'emergency_save.json');
                    fs.writeFileSync(emergencyPath, JSON.stringify(emergencyData, null, 2), 'utf8');
                } catch (fsError) {
                    console.error('❌ Erreur sauvegarde d\'urgence fichier:', fsError.message);
                }
            }

            console.log('✅ Sauvegarde d\'urgence réussie');
            return true;
        } catch (error) {
            console.error('❌ Échec sauvegarde d\'urgence:', error);
            return false;
        }
    }

    /**
     * Récupère les données sauvegardées
     */
    async recoverSavedData() {
        try {
            let recoveredCount = 0;

            if (typeof localStorage !== 'undefined') {
                // Côté client (navigateur)
                // Récupération données d'urgence en priorité
                const emergencyData = localStorage.getItem('louna_emergency_save');
                if (emergencyData) {
                    const emergency = JSON.parse(emergencyData);
                    console.log('🚨 Données d\'urgence détectées:', emergency.timestamp);

                    this.instantMemory = emergency.memory || [];
                    recoveredCount += this.instantMemory.length;

                    // Nettoyer la sauvegarde d'urgence
                    localStorage.removeItem('louna_emergency_save');
                }

                // Récupération données normales
                const savedData = localStorage.getItem('louna_thermal_memory');
                if (savedData && !emergencyData) {
                    const data = JSON.parse(savedData);
                    this.instantMemory = data.memory || [];
                    this.saveCount = data.metadata?.saveCount || 0;
                    recoveredCount += this.instantMemory.length;
                }

                // Récupération depuis IndexedDB si disponible
                await this.recoverFromIndexedDB();
            } else {
                // Côté serveur (Node.js) - utiliser le système de fichiers
                const fs = require('fs').promises;
                const path = require('path');

                try {
                    const dataDir = path.join(process.cwd(), 'data', 'persistence');
                    await fs.mkdir(dataDir, { recursive: true });

                    // Récupération données d'urgence
                    const emergencyPath = path.join(dataDir, 'emergency_save.json');
                    try {
                        const emergencyData = await fs.readFile(emergencyPath, 'utf8');
                        const emergency = JSON.parse(emergencyData);
                        console.log('🚨 Données d\'urgence détectées:', emergency.timestamp);

                        this.instantMemory = emergency.memory || [];
                        recoveredCount += this.instantMemory.length;

                        // Nettoyer la sauvegarde d'urgence
                        await fs.unlink(emergencyPath);
                    } catch (err) {
                        // Pas de données d'urgence, c'est normal
                    }

                    // Récupération données normales
                    if (recoveredCount === 0) {
                        const normalPath = path.join(dataDir, 'thermal_memory.json');
                        try {
                            const savedData = await fs.readFile(normalPath, 'utf8');
                            const data = JSON.parse(savedData);
                            this.instantMemory = data.memory || [];
                            this.saveCount = data.metadata?.saveCount || 0;
                            recoveredCount += this.instantMemory.length;
                        } catch (err) {
                            // Pas de données normales, c'est normal pour un premier démarrage
                        }
                    }
                } catch (fsError) {
                    console.warn('⚠️ Erreur accès système de fichiers:', fsError.message);
                }
            }

            console.log(`🔄 ${recoveredCount} éléments récupérés de la mémoire`);
            return { success: true, recovered: recoveredCount };

        } catch (error) {
            console.error('❌ Erreur récupération données:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Récupère depuis IndexedDB
     */
    async recoverFromIndexedDB() {
        return new Promise((resolve) => {
            if (!('indexedDB' in window)) {
                resolve();
                return;
            }

            const request = indexedDB.open('LounaThermalMemory', 1);

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['memory'], 'readonly');
                const store = transaction.objectStore('memory');
                const getRequest = store.get('current');

                getRequest.onsuccess = () => {
                    if (getRequest.result && getRequest.result.memory) {
                        // Fusionner avec les données existantes si plus récentes
                        const indexedData = getRequest.result;
                        if (new Date(indexedData.timestamp) > new Date()) {
                            this.instantMemory = indexedData.memory;
                            console.log('🔄 Données IndexedDB plus récentes utilisées');
                        }
                    }
                    resolve();
                };

                getRequest.onerror = () => resolve();
            };

            request.onerror = () => resolve();
        });
    }

    /**
     * Obtient les statistiques de persistance
     */
    getStats() {
        return {
            instantMemorySize: this.instantMemory.length,
            maxSize: this.maxInstantMemorySize,
            saveCount: this.saveCount,
            saveFrequency: this.saveFrequency,
            isAutoSaveActive: !!this.autoSaveInterval,
            isInitialized: this.isInitialized,
            lastSave: this.instantMemory.length > 0 ?
                     this.instantMemory[this.instantMemory.length - 1].timestamp : null
        };
    }

    /**
     * Nettoie les anciennes données
     */
    cleanup() {
        this.stopAutoSave();
        this.instantMemory = [];
        localStorage.removeItem('louna_thermal_memory');
        localStorage.removeItem('louna_emergency_save');
        console.log('🧹 Nettoyage de la persistance mémoire effectué');
    }
}

// Export pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThermalMemoryPersistence;
} else if (typeof window !== 'undefined') {
    window.ThermalMemoryPersistence = ThermalMemoryPersistence;
}

console.log('📦 Module ThermalMemoryPersistence chargé');
