#!/usr/bin/env python3
"""
Test simple d'OpenCV pour vérifier l'installation
"""

try:
    import cv2
    import numpy as np
    print("✅ OpenCV importé avec succès")
    print(f"   Version OpenCV: {cv2.__version__}")
    
    # Test de création d'une image simple
    img = np.zeros((100, 100, 3), dtype=np.uint8)
    img[:] = (255, 0, 255)  # Rose
    print("✅ Création d'image test réussie")
    
    # Test de détection de visages
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    if face_cascade.empty():
        print("❌ Impossible de charger le classificateur de visages")
    else:
        print("✅ Classificateur de visages chargé")
    
    print("\n🎯 OpenCV fonctionne correctement!")
    
except ImportError as e:
    print(f"❌ Erreur import OpenCV: {e}")
except Exception as e:
    print(f"❌ Erreur OpenCV: {e}")

try:
    import numpy as np
    print(f"✅ NumPy version: {np.__version__}")
except ImportError:
    print("❌ NumPy non disponible")

try:
    import PIL
    print(f"✅ Pillow version: {PIL.__version__}")
except ImportError:
    print("❌ Pillow non disponible")

print("\n🔧 Test terminé!")
