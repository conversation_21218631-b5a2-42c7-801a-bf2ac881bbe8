{"id": "auto_backup_2025-05-28T04-40-30-187Z", "type": "auto_backup", "description": "", "timestamp": "2025-05-28T04:40:30.187Z", "version": "1.0.0", "data": {"directConnection": {"config": {"apis": [{"name": "OpenAI", "type": "openai", "url": "https://api.openai.com/v1/chat/completions", "model": "gpt-4", "enabled": false, "speed": "ultra-fast", "latency": 200}, {"name": "<PERSON>", "type": "anthropic", "url": "https://api.anthropic.com/v1/messages", "model": "claude-3-sonnet-20240229", "enabled": false, "speed": "ultra-fast", "latency": 300}, {"name": "DeepSeek API", "type": "deepseek", "url": "https://api.deepseek.com/v1/chat/completions", "model": "deepseek-chat", "enabled": false, "speed": "fast", "latency": 500}, {"name": "Local LLM Server", "type": "local", "url": "http://localhost:8080/v1/chat/completions", "model": "local-model", "enabled": true, "speed": "fast", "latency": 100}, {"name": "Ollama Fallback", "type": "ollama", "url": "http://localhost:11434/api/chat", "model": "incept5/llama3.1-claude:latest", "enabled": true, "speed": "medium", "latency": 2000, "measuredLatency": 6}], "performance": {"maxLatency": 45000, "preferredLatency": 5000, "retryAttempts": 3, "fallbackEnabled": true, "cacheEnabled": true, "streamingEnabled": true}, "cache": {"enabled": true, "maxSize": 1000, "ttl": 300000, "storage": {}}}, "stats": {"totalRequests": 0, "successfulRequests": 0, "failedRequests": 0, "averageLatency": 0, "apiUsage": {}, "cacheHits": 0, "cacheMisses": 0, "activeAPI": "Ollama Fallback", "availableAPIs": 1, "cacheSize": 0, "cacheHitRate": null}}, "speedOptimizer": {"config": {"speed": {"targetLatency": 200, "maxLatency": 1000, "reflectionSpeed": "ultra-fast", "responseOptimization": true, "parallelProcessing": true, "cacheAggressive": true}, "connections": {"directAPI": true, "ollamaFallback": true, "localLLM": true, "cloudAPIs": false, "streamingEnabled": true}, "reflection": {"enabled": true, "maxThinkingTime": 100, "parallelThoughts": 3, "quickDecisions": true, "contextOptimization": true}, "cache": {"enabled": true, "levels": {"instant": {"size": 100, "ttl": 30000}, "quick": {"size": 500, "ttl": 300000}, "persistent": {"size": 1000, "ttl": 3600000}}, "predictive": true, "contextual": true, "cacheAggressive": true}}, "stats": {"averageLatency": 100, "fastestResponse": 50, "slowestResponse": 500, "cacheHitRate": 30, "reflectionTime": 30, "optimizationGains": 85, "totalRequests": 10, "cacheHits": 3, "emergencyBoosts": 1, "lastEmergencyBoost": 1748403627923, "cacheStats": {"instant": 0, "quick": 0, "persistent": 0}, "optimizationLevel": "ultra-fast"}}, "cognitiveSystem": {"available": true, "agents": []}, "environment": {"hasOpenAIKey": false, "hasAnthropicKey": false, "hasDeepSeekKey": false, "nodeVersion": "v23.11.0", "platform": "darwin"}, "system": {"uptime": 3604.844175167, "memoryUsage": {"rss": 200146944, "heapTotal": 213237760, "heapUsed": 72799440, "external": 5685361, "arrayBuffers": 2308690}, "timestamp": "2025-05-28T04:40:30.187Z"}}}