<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat avec Agents - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .chat-container {
            display: flex;
            height: calc(100vh - 70px);
        }

        .agents-panel {
            width: 300px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            overflow-y: auto;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .agent-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .agent-card.active {
            border-color: #e91e63;
            background: rgba(233, 30, 99, 0.2);
        }

        .agent-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .agent-role {
            font-size: 12px;
            color: #b0b0b0;
            margin-bottom: 8px;
        }

        .agent-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .status-dot.inactive {
            background: #f44336;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-avatar {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-content {
            max-width: 70%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .message.agent .message-content {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            opacity: 0.8;
        }

        .message-text {
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .chat-input-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .chat-input:focus {
            outline: none;
            border-color: #e91e63;
            box-shadow: 0 0 10px rgba(233, 30, 99, 0.3);
        }

        .send-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e91e63;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .agent-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .stats-panel {
            position: fixed;
            top: 70px;
            right: -300px;
            width: 300px;
            height: calc(100vh - 70px);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            transition: right 0.3s ease;
            overflow-y: auto;
        }

        .stats-panel.open {
            right: 0;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stats-title {
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: #e91e63;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 12px;
            color: #b0b0b0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-comments"></i>
            Chat avec Agents Louna
        </h1>
        <div class="nav-buttons">
            <button class="nav-btn" onclick="toggleStats()">
                <i class="fas fa-chart-bar"></i>
                Statistiques
            </button>
            <a href="/evolution-learning-center.html" class="nav-btn">
                <i class="fas fa-dna"></i>
                Évolution
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="chat-container">
        <!-- Panel des agents -->
        <div class="agents-panel">
            <h3 style="margin-bottom: 20px; color: #e91e63;">
                <i class="fas fa-robot"></i>
                Agents Disponibles
            </h3>

            <div class="agent-card active" data-agent="main" onclick="selectAgent('main')">
                <div class="agent-name">
                    <i class="fas fa-brain"></i>
                    Louna Principal
                </div>
                <div class="agent-role">Assistant IA Principal</div>
                <div class="agent-status">
                    <div class="status-dot"></div>
                    Actif
                </div>
                <div class="agent-actions">
                    <button class="action-btn" onclick="evaluateAgent('main')">Évaluer</button>
                    <button class="action-btn" onclick="viewHistory('main')">Historique</button>
                </div>
            </div>

            <div class="agent-card" data-agent="deepseek" onclick="selectAgent('deepseek')">
                <div class="agent-name">
                    <i class="fas fa-graduation-cap"></i>
                    DeepSeek Formation
                </div>
                <div class="agent-role">Professeur et Formateur IA</div>
                <div class="agent-status">
                    <div class="status-dot"></div>
                    Actif
                </div>
                <div class="agent-actions">
                    <button class="action-btn" onclick="startTraining()">Formation</button>
                    <button class="action-btn" onclick="viewTrainingHistory()">Historique</button>
                </div>
            </div>
        </div>

        <!-- Zone de chat principale -->
        <div class="chat-main">
            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <span>Louna Principal</span>
                            <span id="currentTime"></span>
                        </div>
                        <div class="message-text">Bonjour ! Je suis Louna, votre assistant IA avec mémoire thermique. Je travaille avec l'agent DeepSeek pour ma formation continue. Comment puis-je vous aider aujourd'hui ?</div>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <span id="typingAgentName">Agent</span> est en train d'écrire...
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea
                        class="chat-input"
                        id="messageInput"
                        placeholder="Tapez votre message... (Entrée pour envoyer, Shift+Entrée pour nouvelle ligne)"
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Panel des statistiques -->
    <div class="stats-panel" id="statsPanel">
        <h3 style="margin-bottom: 20px; color: #e91e63;">
            <i class="fas fa-chart-line"></i>
            Statistiques en Temps Réel
        </h3>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-brain"></i>
                QI Actuel
            </div>
            <div class="stats-value" id="currentQI">203</div>
            <div class="stats-label">Niveau Quasi-AGI</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-network-wired"></i>
                Neurones Actifs
            </div>
            <div class="stats-value" id="currentNeurons">89</div>
            <div class="stats-label">sur 100 neurones</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-thermometer-half"></i>
                Température
            </div>
            <div class="stats-value" id="currentTemp">37.0°C</div>
            <div class="stats-label">Optimale</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-rocket"></i>
                Évolution
            </div>
            <div class="stats-value" id="currentEvolution">100%</div>
            <div class="stats-label">Complète</div>
        </div>

        <div class="stats-card">
            <div class="stats-title">
                <i class="fas fa-comments"></i>
                Messages Échangés
            </div>
            <div class="stats-value" id="messageCount">0</div>
            <div class="stats-label">Cette session</div>
        </div>
    </div>

    <script>
        let currentAgent = 'main';
        let messageCount = 0;
        let isTyping = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            loadStats();
            setInterval(loadStats, 10000); // Mise à jour toutes les 10 secondes

            // Gestion de l'input
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize du textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        });

        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString();
        }

        function selectAgent(agentType) {
            currentAgent = agentType;

            // Mettre à jour l'interface
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-agent="${agentType}"]`).classList.add('active');

            // Ajouter un message système
            addSystemMessage(`Agent ${agentType === 'main' ? 'Louna Principal' : 'DeepSeek Formation'} sélectionné`);
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // Afficher le message utilisateur
            addUserMessage(message);
            input.value = '';
            input.style.height = 'auto';

            // Incrémenter le compteur
            messageCount++;
            document.getElementById('messageCount').textContent = messageCount;

            // Afficher l'indicateur de frappe
            showTypingIndicator();

            try {
                let response;
                if (currentAgent === 'deepseek') {
                    response = await sendToDeepSeek(message);
                } else {
                    response = await sendToCognitive(message);
                }

                hideTypingIndicator();

                if (response.success) {
                    addAgentMessage(response.response, currentAgent);
                } else {
                    addErrorMessage(response.error || 'Erreur de communication avec l\'agent');
                }
            } catch (error) {
                hideTypingIndicator();
                addErrorMessage('Erreur de connexion: ' + error.message);
            }
        }

        async function sendToCognitive(message) {
            const response = await fetch('/api/cognitive/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            });
            return await response.json();
        }

        async function sendToDeepSeek(message) {
            const response = await fetch('/api/deepseek/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    context: {
                        qi: parseInt(document.getElementById('currentQI').textContent),
                        neurones: parseInt(document.getElementById('currentNeurons').textContent),
                        evolution_level: parseInt(document.getElementById('currentEvolution').textContent)
                    }
                })
            });
            return await response.json();
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span>Vous</span>
                        <span>${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="message-text">${message}</div>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addAgentMessage(message, agentType) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message agent';

            const agentName = agentType === 'deepseek' ? 'DeepSeek Formation' : 'Louna Principal';
            const agentIcon = agentType === 'deepseek' ? 'fas fa-graduation-cap' : 'fas fa-brain';

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="${agentIcon}"></i>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span>${agentName}</span>
                        <span>${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="message-text">${message}</div>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addSystemMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 15px; margin: 10px 0; font-size: 12px; color: #b0b0b0;">
                    <i class="fas fa-info-circle"></i> ${message}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addErrorMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message error';
            messageDiv.innerHTML = `
                <div style="text-align: center; padding: 10px; background: rgba(244,67,54,0.2); border-radius: 15px; margin: 10px 0; font-size: 12px; color: #f44336;">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            isTyping = true;
            const indicator = document.getElementById('typingIndicator');
            const agentName = currentAgent === 'deepseek' ? 'DeepSeek Formation' : 'Louna Principal';
            document.getElementById('typingAgentName').textContent = agentName;
            indicator.style.display = 'flex';
            document.getElementById('sendBtn').disabled = true;
        }

        function hideTypingIndicator() {
            isTyping = false;
            document.getElementById('typingIndicator').style.display = 'none';
            document.getElementById('sendBtn').disabled = false;
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/global/qi-detailed');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('currentQI').textContent = data.qi_data.qi || 203;
                    document.getElementById('currentNeurons').textContent = data.qi_data.neurones || 89;
                    document.getElementById('currentTemp').textContent = (data.qi_data.temperature || 37.0) + '°C';
                    document.getElementById('currentEvolution').textContent = (data.qi_data.evolution_level || 100) + '%';
                }
            } catch (error) {
                console.error('Erreur chargement stats:', error);
            }
        }

        function toggleStats() {
            const panel = document.getElementById('statsPanel');
            panel.classList.toggle('open');
        }

        async function evaluateAgent(agentType) {
            showTypingIndicator();
            try {
                const response = await fetch('/api/cognitive/evaluate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                hideTypingIndicator();

                if (result.success) {
                    addAgentMessage(result.response, 'deepseek');
                } else {
                    addErrorMessage('Erreur lors de l\'évaluation');
                }
            } catch (error) {
                hideTypingIndicator();
                addErrorMessage('Erreur de connexion');
            }
        }

        async function startTraining() {
            showTypingIndicator();
            try {
                const response = await fetch('/api/cognitive/training', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ trainingType: 'ADVANCED' })
                });
                const result = await response.json();
                hideTypingIndicator();

                if (result.success) {
                    addAgentMessage(result.response, 'deepseek');
                } else {
                    addErrorMessage('Erreur lors du démarrage de la formation');
                }
            } catch (error) {
                hideTypingIndicator();
                addErrorMessage('Erreur de connexion');
            }
        }

        async function viewHistory(agentType) {
            try {
                const response = await fetch('/api/cognitive/history?limit=5');
                const result = await response.json();

                if (result.success && result.history.length > 0) {
                    let historyText = "📜 **Historique des 5 dernières interactions :**\n\n";
                    result.history.forEach((item, index) => {
                        historyText += `${index + 1}. **${item.role === 'user' ? 'Vous' : 'Agent'}** (${new Date(item.timestamp).toLocaleTimeString()}):\n${item.content}\n\n`;
                    });
                    addSystemMessage(historyText);
                } else {
                    addSystemMessage("Aucun historique disponible");
                }
            } catch (error) {
                addErrorMessage('Erreur lors de la récupération de l\'historique');
            }
        }

        async function viewTrainingHistory() {
            try {
                const response = await fetch('/api/deepseek/training-history');
                const result = await response.json();

                if (result.success && result.history.length > 0) {
                    let historyText = "🎓 **Historique de Formation DeepSeek :**\n\n";
                    result.history.slice(-3).forEach((item, index) => {
                        historyText += `${index + 1}. **${item.type}** (${new Date(item.timestamp).toLocaleTimeString()}):\n${item.request}\n\n`;
                    });
                    addSystemMessage(historyText);
                } else {
                    addSystemMessage("Aucun historique de formation disponible");
                }
            } catch (error) {
                addErrorMessage('Erreur lors de la récupération de l\'historique de formation');
            }
        }
    </script>
</body>
</html>
