<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 Test de Mémoire Biologique - Louna</title>
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        .biological-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: #ffffff;
        }
        
        .memory-zones {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .memory-zone {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid #ff69b4;
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .zone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .zone-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #ff69b4;
        }
        
        .zone-count {
            background: #ff69b4;
            color: #000;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .zone-usage {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .usage-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00, #ff0000);
            transition: width 0.3s ease;
        }
        
        .test-controls {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid #ff69b4;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ff69b4;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: #ffffff;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }
        
        .biological-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #ff69b4;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #ff69b4;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #cccccc;
        }
        
        .memory-log {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #ff69b4;
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #ff69b4;
            padding-left: 10px;
        }
        
        .emotional-state {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .emotion-indicator {
            text-align: center;
            padding: 10px;
        }
        
        .emotion-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            border: 2px solid #ff69b4;
        }
    </style>
</head>
<body>
    <div class="biological-container">
        <header class="louna-header">
            <h1>🧬 Test de Mémoire Biologique Artificielle</h1>
            <p>Système de mémoire fonctionnant exactement comme un cerveau humain</p>
            <button onclick="window.location.href='/'" class="nav-button">🏠 Retour Accueil</button>
        </header>

        <div class="biological-stats">
            <div class="stat-card">
                <div class="stat-value" id="totalMemories">0</div>
                <div class="stat-label">Mémoires Totales</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="memoryEfficiency">0%</div>
                <div class="stat-label">Efficacité Mémoire</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="neuralConnections">0</div>
                <div class="stat-label">Connexions Neuronales</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="sleepStatus">Éveillé</div>
                <div class="stat-label">État de Sommeil</div>
            </div>
        </div>

        <div class="emotional-state">
            <div class="emotion-indicator">
                <div class="emotion-circle" id="valenceCircle">😐</div>
                <div>Valence</div>
                <div id="valenceValue">50%</div>
            </div>
            <div class="emotion-indicator">
                <div class="emotion-circle" id="arousalCircle">😴</div>
                <div>Éveil</div>
                <div id="arousalValue">50%</div>
            </div>
            <div class="emotion-indicator">
                <div class="emotion-circle" id="dominanceCircle">🤝</div>
                <div>Contrôle</div>
                <div id="dominanceValue">50%</div>
            </div>
        </div>

        <div class="test-controls">
            <h3>🧪 Tests de Mémoire Biologique</h3>
            
            <div>
                <label>Contenu de la mémoire :</label>
                <input type="text" id="memoryContent" class="test-input" placeholder="Entrez quelque chose à mémoriser...">
            </div>
            
            <div>
                <label>Importance (0-1) :</label>
                <input type="number" id="memoryImportance" class="test-input" min="0" max="1" step="0.1" value="0.5">
            </div>
            
            <div>
                <label>Émotion :</label>
                <select id="emotionType" class="test-input">
                    <option value="">Aucune</option>
                    <option value="joy">Joie</option>
                    <option value="fear">Peur</option>
                    <option value="anger">Colère</option>
                    <option value="sadness">Tristesse</option>
                    <option value="surprise">Surprise</option>
                    <option value="calm">Calme</option>
                </select>
            </div>
            
            <div>
                <label>Intensité émotionnelle (0-1) :</label>
                <input type="number" id="emotionIntensity" class="test-input" min="0" max="1" step="0.1" value="0.8">
            </div>
            
            <button class="test-button" onclick="addMemory()">🧠 Ajouter Mémoire</button>
            <button class="test-button" onclick="searchMemory()">🔍 Rechercher</button>
            <button class="test-button" onclick="forceConsolidation()">😴 Forcer Sommeil</button>
            <button class="test-button" onclick="generateDream()">💭 Générer Rêve</button>
            <button class="test-button" onclick="triggerEmotion()">😊 Déclencher Émotion</button>
        </div>

        <div class="memory-zones" id="memoryZones">
            <!-- Les zones de mémoire seront générées dynamiquement -->
        </div>

        <div class="test-controls">
            <h3>📋 Journal de la Mémoire Biologique</h3>
            <div class="memory-log" id="memoryLog">
                <div class="log-entry">🧬 Système de mémoire biologique initialisé...</div>
            </div>
        </div>
    </div>

    <script>
        let biologicalMemoryData = null;
        
        // Initialiser l'interface
        document.addEventListener('DOMContentLoaded', function() {
            loadBiologicalMemoryStatus();
            setInterval(loadBiologicalMemoryStatus, 2000); // Mise à jour toutes les 2 secondes
        });
        
        // Charger le statut de la mémoire biologique
        async function loadBiologicalMemoryStatus() {
            try {
                const response = await fetch('/api/biological-memory/status');
                if (response.ok) {
                    biologicalMemoryData = await response.json();
                    updateInterface();
                } else {
                    // Fallback vers l'ancien système si le nouveau n'est pas disponible
                    const fallbackResponse = await fetch('/api/thermal/memory/status');
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        updateInterfaceWithFallback(fallbackData);
                    }
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion à la mémoire biologique: ' + error.message);
                // Essayer l'ancien système
                try {
                    const fallbackResponse = await fetch('/api/thermal/memory/status');
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        updateInterfaceWithFallback(fallbackData);
                    }
                } catch (fallbackError) {
                    logMessage('❌ Erreur de connexion aux systèmes de mémoire: ' + fallbackError.message);
                }
            }
        }
        
        // Mettre à jour l'interface avec les données biologiques
        function updateInterface() {
            if (!biologicalMemoryData || !biologicalMemoryData.data) return;
            
            const data = biologicalMemoryData.data;
            
            // Statistiques générales
            document.getElementById('totalMemories').textContent = data.totalMemories || 0;
            document.getElementById('memoryEfficiency').textContent = 
                Math.round((data.memoryEfficiency || 0) * 100) + '%';
            document.getElementById('neuralConnections').textContent = data.neuralConnections || 0;
            document.getElementById('sleepStatus').textContent = data.isAsleep ? 'Endormi' : 'Éveillé';
            
            // État émotionnel
            if (data.emotionalState) {
                updateEmotionalState(data.emotionalState);
            }
            
            // Zones de mémoire
            if (data.zones) {
                updateMemoryZones(data.zones);
            }
            
            logMessage(`🧠 Statut mis à jour: ${data.totalMemories} mémoires, efficacité ${Math.round((data.memoryEfficiency || 0) * 100)}%`);
        }
        
        // Mettre à jour avec l'ancien système (fallback)
        function updateInterfaceWithFallback(data) {
            document.getElementById('totalMemories').textContent = data.totalMemories || 0;
            document.getElementById('memoryEfficiency').textContent = '75%'; // Valeur par défaut
            document.getElementById('neuralConnections').textContent = '0';
            document.getElementById('sleepStatus').textContent = 'Système Legacy';
            
            if (data.zones) {
                updateMemoryZones(data.zones);
            }
            
            logMessage('⚠️ Utilisation du système de mémoire legacy');
        }
        
        // Mettre à jour l'état émotionnel
        function updateEmotionalState(emotionalState) {
            const valence = emotionalState.valence || 0.5;
            const arousal = emotionalState.arousal || 0.5;
            const dominance = emotionalState.dominance || 0.5;
            
            document.getElementById('valenceValue').textContent = Math.round(valence * 100) + '%';
            document.getElementById('arousalValue').textContent = Math.round(arousal * 100) + '%';
            document.getElementById('dominanceValue').textContent = Math.round(dominance * 100) + '%';
            
            // Mettre à jour les émojis
            document.getElementById('valenceCircle').textContent = valence > 0.7 ? '😊' : valence < 0.3 ? '😢' : '😐';
            document.getElementById('arousalCircle').textContent = arousal > 0.7 ? '😃' : arousal < 0.3 ? '😴' : '😌';
            document.getElementById('dominanceCircle').textContent = dominance > 0.7 ? '💪' : dominance < 0.3 ? '🤝' : '👤';
        }
        
        // Mettre à jour les zones de mémoire
        function updateMemoryZones(zones) {
            const container = document.getElementById('memoryZones');
            container.innerHTML = '';
            
            zones.forEach(zone => {
                const zoneElement = document.createElement('div');
                zoneElement.className = 'memory-zone';
                
                const usage = zone.usage || 0;
                const usagePercent = Math.round(usage * 100);
                
                zoneElement.innerHTML = `
                    <div class="zone-header">
                        <div class="zone-name">${zone.name}</div>
                        <div class="zone-count">${zone.count || 0}</div>
                    </div>
                    <div class="zone-usage">
                        <div class="usage-bar" style="width: ${usagePercent}%"></div>
                    </div>
                    <div>Usage: ${usagePercent}%</div>
                    <div>Température: ${Math.round((zone.temperature || 0) * 100)}%</div>
                    <div>Actif: ${zone.active ? 'Oui' : 'Non'}</div>
                `;
                
                container.appendChild(zoneElement);
            });
        }
        
        // Ajouter une mémoire
        async function addMemory() {
            const content = document.getElementById('memoryContent').value;
            const importance = parseFloat(document.getElementById('memoryImportance').value);
            const emotionType = document.getElementById('emotionType').value;
            const emotionIntensity = parseFloat(document.getElementById('emotionIntensity').value);
            
            if (!content) {
                alert('Veuillez entrer un contenu de mémoire');
                return;
            }
            
            const emotion = emotionType ? {
                type: emotionType,
                intensity: emotionIntensity
            } : null;
            
            try {
                const response = await fetch('/api/biological-memory/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        data: content,
                        importance: importance,
                        emotion: emotion
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logMessage(`✅ Mémoire ajoutée: "${content}" (ID: ${result.data.memoryId})`);
                    document.getElementById('memoryContent').value = '';
                } else {
                    logMessage('❌ Erreur lors de l\'ajout de la mémoire');
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion: ' + error.message);
            }
        }
        
        // Rechercher une mémoire
        async function searchMemory() {
            const query = document.getElementById('memoryContent').value;
            
            if (!query) {
                alert('Veuillez entrer une requête de recherche');
                return;
            }
            
            try {
                const response = await fetch('/api/biological-memory/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        limit: 5
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logMessage(`🔍 Recherche "${query}": ${result.data.count} résultats trouvés`);
                    result.data.results.forEach((memory, index) => {
                        logMessage(`  ${index + 1}. ${memory.data.substring(0, 50)}... (Zone: ${memory.zone})`);
                    });
                } else {
                    logMessage('❌ Erreur lors de la recherche');
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion: ' + error.message);
            }
        }
        
        // Forcer une consolidation (sommeil)
        async function forceConsolidation() {
            try {
                const response = await fetch('/api/biological-memory/consolidate', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    logMessage('😴 Consolidation forcée démarrée - Entrée en mode sommeil...');
                } else {
                    logMessage('❌ Erreur lors de la consolidation');
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion: ' + error.message);
            }
        }
        
        // Générer un rêve
        async function generateDream() {
            try {
                const response = await fetch('/api/biological-memory/dream', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    logMessage('💭 Génération de rêve démarrée - Traitement créatif des mémoires...');
                } else {
                    logMessage('❌ Erreur lors de la génération de rêve');
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion: ' + error.message);
            }
        }
        
        // Déclencher une émotion
        async function triggerEmotion() {
            const emotionType = document.getElementById('emotionType').value;
            const emotionIntensity = parseFloat(document.getElementById('emotionIntensity').value);
            
            if (!emotionType) {
                alert('Veuillez sélectionner un type d\'émotion');
                return;
            }
            
            try {
                const response = await fetch('/api/biological-memory/emotion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: emotionType,
                        intensity: emotionIntensity
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logMessage(`😊 Émotion ${emotionType} déclenchée (intensité: ${emotionIntensity})`);
                } else {
                    logMessage('❌ Erreur lors du déclenchement d\'émotion');
                }
            } catch (error) {
                logMessage('❌ Erreur de connexion: ' + error.message);
            }
        }
        
        // Ajouter un message au journal
        function logMessage(message) {
            const log = document.getElementById('memoryLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
    </script>
</body>
</html>
