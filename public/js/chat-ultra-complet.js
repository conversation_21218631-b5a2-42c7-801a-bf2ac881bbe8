/**
 * INTERFACE DE CHAT ULTRA-COMPLÈTE POUR LOUNA
 * Toutes les fonctionnalités : Chat, Code, Connectivité, Multimédia, Partage
 */

(function() {
    'use strict';
    
    console.log('🚀 [Chat Ultra-Complet] Initialisation...');
    
    // ===== VARIABLES GLOBALES =====
    let socket = null;
    let isConnected = false;
    let messageHistory = [];
    let currentUser = 'Utilisateur';
    let agentName = 'Vision Ultra';
    
    // États des fonctionnalités
    let speakerEnabled = true;
    let voiceEnabled = false;
    let micEnabled = false;
    let cameraEnabled = false;
    let codeEditorOpen = false;
    let connectivityPanelOpen = false;
    
    // Éléments DOM
    const elements = {};
    
    // Configuration
    const CONFIG = {
        socketEvents: {
            send: 'louna message',
            receive: 'louna response'
        },
        maxMessageLength: 2000,
        reconnectAttempts: 5,
        reconnectDelay: 2000
    };
    
    // ===== FONCTIONS UTILITAIRES =====
    
    function log(message, type = 'info') {
        const prefix = '🚀 [Chat Ultra-Complet]';
        console[type](`${prefix} ${message}`);
    }
    
    function formatTime(date = new Date()) {
        return date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function sanitizeHTML(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }
    
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#4CAF50'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-size: 14px;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // ===== GESTION DE LA CONNEXION SOCKET.IO =====
    
    function initializeSocket() {
        log('Initialisation de Socket.io...');
        
        try {
            if (typeof io === 'undefined') {
                log('Socket.io non disponible - Mode HTTP fallback', 'warn');
                updateConnectionStatus(false, 'Mode HTTP');
                return false;
            }
            
            socket = io({
                transports: ['websocket', 'polling'],
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: CONFIG.reconnectAttempts,
                reconnectionDelay: CONFIG.reconnectDelay
            });
            
            // Événements de connexion
            socket.on('connect', () => {
                isConnected = true;
                log('✅ Socket.io connecté avec ID: ' + socket.id);
                updateConnectionStatus(true, 'Connecté');
                showNotification('Connexion établie avec le serveur', 'success');
            });
            
            socket.on('disconnect', (reason) => {
                isConnected = false;
                log('❌ Socket.io déconnecté: ' + reason, 'warn');
                updateConnectionStatus(false, 'Déconnecté');
                showNotification('Connexion perdue', 'warning');
            });
            
            socket.on('connect_error', (error) => {
                log('❌ Erreur de connexion Socket.io: ' + error.message, 'error');
                updateConnectionStatus(false, 'Erreur');
                showNotification('Erreur de connexion', 'error');
            });
            
            socket.on('reconnect', (attemptNumber) => {
                log('🔄 Reconnexion réussie après ' + attemptNumber + ' tentatives');
                updateConnectionStatus(true, 'Reconnecté');
                showNotification('Reconnexion réussie', 'success');
            });
            
            // Écouter les réponses
            socket.on(CONFIG.socketEvents.receive, (data) => {
                log('📨 Réponse reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            socket.on('agent response', (data) => {
                log('📨 Agent response reçue');
                handleAgentResponse(data);
            });
            
            socket.on('chat response', (data) => {
                log('📨 Chat response reçue');
                handleAgentResponse(data);
            });
            
            return true;
        } catch (error) {
            log('❌ Erreur lors de l\'initialisation Socket.io: ' + error.message, 'error');
            updateConnectionStatus(false, 'Erreur');
            return false;
        }
    }
    
    function updateConnectionStatus(connected, text) {
        if (elements.connectionStatus && elements.connectionText) {
            elements.connectionStatus.className = `status-dot ${connected ? '' : 'disconnected'}`;
            elements.connectionText.textContent = text;
        }
    }
    
    // ===== GESTION DES MESSAGES =====
    
    function sendMessage(messageText) {
        if (!messageText || !messageText.trim()) {
            log('❌ Message vide', 'warn');
            return false;
        }
        
        if (messageText.length > CONFIG.maxMessageLength) {
            log('❌ Message trop long', 'warn');
            showNotification(`Message trop long (max ${CONFIG.maxMessageLength} caractères)`, 'error');
            return false;
        }
        
        const message = messageText.trim();
        log('📤 Envoi du message: ' + message);
        
        // Ajouter le message de l'utilisateur à l'interface
        addMessage(message, 'user');
        
        // Vider le champ de saisie
        if (elements.messageInput) {
            elements.messageInput.value = '';
            autoResizeTextarea(elements.messageInput);
        }
        
        // Ajouter à l'historique
        messageHistory.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
        
        // Limiter l'historique
        if (messageHistory.length > 20) {
            messageHistory = messageHistory.slice(-20);
        }
        
        // Afficher l'indicateur de frappe
        showTypingIndicator();
        
        // Envoyer le message
        if (socket && isConnected) {
            // Via Socket.io
            socket.emit(CONFIG.socketEvents.send, {
                message: message,
                history: messageHistory.slice(-5)
            });
        } else {
            // Via HTTP fallback
            sendMessageHTTP(message);
        }
        
        return true;
    }
    
    function sendMessageHTTP(message) {
        log('📤 Envoi via HTTP fallback: ' + message);
        
        fetch('/api/chat/message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                history: messageHistory.slice(-5)
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();
            if (data.success) {
                if (data.response) {
                    handleAgentResponse({ message: data.response });
                }
            } else {
                log('❌ Erreur HTTP: ' + data.error, 'error');
                addMessage('Désolé, une erreur s\'est produite lors de l\'envoi de votre message.', 'assistant', true);
            }
        })
        .catch(error => {
            hideTypingIndicator();
            log('❌ Erreur réseau: ' + error.message, 'error');
            addMessage('Désolé, impossible de se connecter au serveur. Veuillez réessayer.', 'assistant', true);
        });
    }
    
    function handleAgentResponse(data) {
        hideTypingIndicator();
        
        let message = '';
        if (typeof data === 'string') {
            message = data;
        } else if (data.message) {
            message = data.message;
        } else if (data.response) {
            message = data.response;
        } else {
            message = 'Réponse reçue mais format non reconnu.';
        }
        
        log('📨 Traitement de la réponse: ' + message);
        
        // Ajouter le message de l'agent à l'interface
        addMessage(message, 'assistant');
        
        // Ajouter à l'historique
        messageHistory.push({
            role: 'assistant',
            content: message,
            timestamp: new Date().toISOString()
        });
        
        // Lecture automatique si activée
        if (voiceEnabled && speakerEnabled) {
            speakText(message);
        }
        
        // Mettre à jour les statistiques si disponibles
        if (data.qi) updateQI(data.qi);
        if (data.neurons) updateNeurons(data.neurons);
        if (data.thermal) updateThermalMemory(data.thermal);
    }
    
    function addMessage(content, type, isError = false) {
        if (!elements.messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${type}`;
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';
        if (isError) {
            bubble.style.background = 'rgba(244, 67, 54, 0.2)';
            bubble.style.borderColor = 'rgba(244, 67, 54, 0.3)';
        }
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = formatMessageContent(content);
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = formatTime();
        
        // Actions pour les messages
        const messageActions = document.createElement('div');
        messageActions.className = 'message-actions';
        messageActions.innerHTML = `
            <button class="message-action-btn" onclick="copyMessage(this)" title="Copier">
                <i class="fas fa-copy"></i>
            </button>
            <button class="message-action-btn" onclick="speakMessage(this)" title="Lire">
                <i class="fas fa-volume-up"></i>
            </button>
        `;
        
        bubble.appendChild(messageContent);
        bubble.appendChild(messageTime);
        bubble.appendChild(messageActions);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);
        
        elements.messagesContainer.appendChild(messageDiv);
        
        // Scroll vers le bas
        elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
    }
    
    function formatMessageContent(content) {
        // Formatage basique du contenu
        return sanitizeHTML(content)
            .replace(/\n/g, '<br>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    function showTypingIndicator() {
        if (elements.typingIndicator) {
            elements.typingIndicator.classList.add('show');
            
            // Scroll vers le bas
            setTimeout(() => {
                if (elements.messagesContainer) {
                    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
                }
            }, 100);
        }
    }
    
    function hideTypingIndicator() {
        if (elements.typingIndicator) {
            elements.typingIndicator.classList.remove('show');
        }
    }
    
    // ===== FONCTIONS GLOBALES POUR LES ACTIONS =====
    
    window.copyMessage = function(button) {
        const messageContent = button.closest('.message-bubble').querySelector('.message-content');
        const text = messageContent.textContent;
        
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Message copié dans le presse-papiers', 'success');
        }).catch(() => {
            showNotification('Erreur lors de la copie', 'error');
        });
    };
    
    window.speakMessage = function(button) {
        const messageContent = button.closest('.message-bubble').querySelector('.message-content');
        const text = messageContent.textContent;
        speakText(text);
    };
    
    function speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'fr-FR';
            utterance.rate = 0.9;
            utterance.pitch = 1.1;
            speechSynthesis.speak(utterance);
        } else {
            showNotification('Synthèse vocale non supportée', 'warning');
        }
    }
    
    // ===== MISE À JOUR DES STATISTIQUES =====
    
    function updateQI(qi) {
        if (elements.qiValue) {
            elements.qiValue.textContent = qi;
        }
    }
    
    function updateNeurons(neurons) {
        if (elements.neuronsValue) {
            elements.neuronsValue.textContent = neurons;
        }
    }
    
    function updateThermalMemory(thermal) {
        if (thermal.hot && elements.hotZone) {
            elements.hotZone.textContent = thermal.hot + '°C';
        }
        if (thermal.warm && elements.warmZone) {
            elements.warmZone.textContent = thermal.warm + '°C';
        }
        if (thermal.cold && elements.coldZone) {
            elements.coldZone.textContent = thermal.cold + '°C';
        }
    }
    
    // ===== GESTIONNAIRES D'ÉVÉNEMENTS =====
    
    function setupEventListeners() {
        // Bouton d'envoi
        if (elements.sendButton) {
            elements.sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                const message = elements.messageInput?.value;
                if (message) {
                    sendMessage(message);
                }
            });
        }
        
        // Champ de saisie
        if (elements.messageInput) {
            // Touche Entrée
            elements.messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const message = elements.messageInput.value;
                    if (message) {
                        sendMessage(message);
                    }
                }
            });
            
            // Auto-resize
            elements.messageInput.addEventListener('input', () => {
                autoResizeTextarea(elements.messageInput);
            });
            
            // Focus initial
            elements.messageInput.focus();
        }
        
        // Boutons d'options
        setupOptionButtons();
    }
    
    function setupOptionButtons() {
        // Bouton effacer
        const clearBtn = document.getElementById('clearChatOptionBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', clearChat);
        }
        
        // Bouton audio
        const speakerBtn = document.getElementById('speakerBtn');
        if (speakerBtn) {
            speakerBtn.addEventListener('click', toggleSpeaker);
        }
        
        // Bouton voix
        const voiceBtn = document.getElementById('voiceBtn');
        if (voiceBtn) {
            voiceBtn.addEventListener('click', toggleVoice);
        }
        
        // Bouton micro
        const micBtn = document.getElementById('micBtn');
        if (micBtn) {
            micBtn.addEventListener('click', toggleMicrophone);
        }
        
        // Bouton caméra
        const cameraBtn = document.getElementById('cameraBtn');
        if (cameraBtn) {
            cameraBtn.addEventListener('click', toggleCamera);
        }
        
        // Bouton éditeur de code
        const codeBtn = document.getElementById('codeEditorBtn');
        if (codeBtn) {
            codeBtn.addEventListener('click', toggleCodeEditor);
        }
        
        // Bouton connectivité
        const connectivityBtn = document.getElementById('connectivityBtn');
        if (connectivityBtn) {
            connectivityBtn.addEventListener('click', toggleConnectivity);
        }
    }
    
    // ===== FONCTIONS DES BOUTONS D'OPTIONS =====
    
    function clearChat() {
        if (confirm('Êtes-vous sûr de vouloir effacer la conversation ?')) {
            elements.messagesContainer.innerHTML = '';
            messageHistory = [];
            showNotification('Conversation effacée', 'success');
        }
    }
    
    function toggleSpeaker() {
        speakerEnabled = !speakerEnabled;
        const btn = document.getElementById('speakerBtn');
        if (btn) {
            btn.classList.toggle('active', speakerEnabled);
            btn.querySelector('i').className = speakerEnabled ? 'fas fa-volume-up' : 'fas fa-volume-mute';
        }
        showNotification(speakerEnabled ? 'Audio activé' : 'Audio désactivé', 'info');
    }
    
    function toggleVoice() {
        voiceEnabled = !voiceEnabled;
        const btn = document.getElementById('voiceBtn');
        if (btn) {
            btn.classList.toggle('active', voiceEnabled);
        }
        showNotification(voiceEnabled ? 'Voix activée' : 'Voix désactivée', 'info');
    }
    
    function toggleMicrophone() {
        micEnabled = !micEnabled;
        const btn = document.getElementById('micBtn');
        if (btn) {
            btn.classList.toggle('active', micEnabled);
            btn.querySelector('i').className = micEnabled ? 'fas fa-microphone' : 'fas fa-microphone-slash';
        }
        
        if (micEnabled) {
            startVoiceRecognition();
        } else {
            stopVoiceRecognition();
        }
    }
    
    function toggleCamera() {
        cameraEnabled = !cameraEnabled;
        const btn = document.getElementById('cameraBtn');
        if (btn) {
            btn.classList.toggle('active', cameraEnabled);
            btn.querySelector('i').className = cameraEnabled ? 'fas fa-video' : 'fas fa-video-slash';
        }
        showNotification(cameraEnabled ? 'Caméra activée' : 'Caméra désactivée', 'info');
    }
    
    function toggleCodeEditor() {
        codeEditorOpen = !codeEditorOpen;
        const btn = document.getElementById('codeEditorBtn');
        if (btn) {
            btn.classList.toggle('active', codeEditorOpen);
        }
        showNotification(codeEditorOpen ? 'Éditeur de code ouvert' : 'Éditeur de code fermé', 'info');
    }
    
    function toggleConnectivity() {
        connectivityPanelOpen = !connectivityPanelOpen;
        const btn = document.getElementById('connectivityBtn');
        if (btn) {
            btn.classList.toggle('active', connectivityPanelOpen);
        }
        showNotification(connectivityPanelOpen ? 'Panneau de connectivité ouvert' : 'Panneau de connectivité fermé', 'info');
    }
    
    // ===== RECONNAISSANCE VOCALE =====
    
    let recognition = null;
    
    function startVoiceRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            recognition.lang = 'fr-FR';
            recognition.continuous = false;
            recognition.interimResults = false;
            
            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                if (elements.messageInput) {
                    elements.messageInput.value = transcript;
                    autoResizeTextarea(elements.messageInput);
                }
                showNotification('Texte reconnu: ' + transcript, 'success');
            };
            
            recognition.onerror = function(event) {
                showNotification('Erreur de reconnaissance vocale: ' + event.error, 'error');
                micEnabled = false;
                const btn = document.getElementById('micBtn');
                if (btn) {
                    btn.classList.remove('active');
                    btn.querySelector('i').className = 'fas fa-microphone-slash';
                }
            };
            
            recognition.start();
            showNotification('Reconnaissance vocale démarrée', 'info');
        } else {
            showNotification('Reconnaissance vocale non supportée', 'warning');
            micEnabled = false;
            const btn = document.getElementById('micBtn');
            if (btn) {
                btn.classList.remove('active');
            }
        }
    }
    
    function stopVoiceRecognition() {
        if (recognition) {
            recognition.stop();
            recognition = null;
            showNotification('Reconnaissance vocale arrêtée', 'info');
        }
    }
    
    // ===== INITIALISATION =====
    
    function initializeElements() {
        elements.messagesContainer = document.getElementById('messagesContainer');
        elements.messageInput = document.getElementById('messageInput');
        elements.sendButton = document.getElementById('sendButton');
        elements.typingIndicator = document.getElementById('typingIndicator');
        elements.connectionStatus = document.getElementById('connectionStatus');
        elements.connectionText = document.getElementById('connectionText');
        elements.qiValue = document.getElementById('qiValue');
        elements.neuronsValue = document.getElementById('neuronsValue');
        elements.hotZone = document.getElementById('hotZone');
        elements.warmZone = document.getElementById('warmZone');
        elements.coldZone = document.getElementById('coldZone');
        
        // Vérifier que les éléments essentiels sont présents
        if (!elements.messagesContainer || !elements.messageInput || !elements.sendButton) {
            log('❌ Éléments DOM essentiels manquants', 'error');
            return false;
        }
        
        return true;
    }
    
    function initialize() {
        log('🚀 Démarrage de l\'interface de chat ultra-complète');
        
        // Initialiser les éléments DOM
        if (!initializeElements()) {
            log('❌ Échec de l\'initialisation des éléments DOM', 'error');
            return;
        }
        
        // Configurer les gestionnaires d'événements
        setupEventListeners();
        
        // Initialiser Socket.io
        initializeSocket();
        
        // Afficher l'heure de bienvenue
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = formatTime();
        }
        
        log('✅ Interface de chat ultra-complète initialisée avec succès');
        showNotification('Interface de chat prête !', 'success');
    }
    
    // ===== DÉMARRAGE =====
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // ===== EXPOSITION GLOBALE POUR LE DEBUG =====
    
    window.ChatUltraComplet = {
        sendMessage: sendMessage,
        isConnected: () => isConnected,
        getSocket: () => socket,
        getHistory: () => messageHistory,
        updateQI: updateQI,
        updateNeurons: updateNeurons,
        updateThermalMemory: updateThermalMemory,
        speakText: speakText
    };
    
})();
