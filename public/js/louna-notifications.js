/* ===== LOUNA NOTIFICATION SYSTEM ===== */

class LounaNotifications {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 secondes
        this.init();
    }

    init() {
        // Créer le container de notifications
        this.createContainer();
        
        // Écouter les événements personnalisés
        document.addEventListener('louna:notify', (event) => {
            this.show(event.detail.message, event.detail.type, event.detail.duration);
        });

        console.log('🔔 Système de notifications Louna initialisé');
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'louna-notifications-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = null) {
        const notification = this.createNotification(message, type, duration);
        this.addNotification(notification);
        return notification.id;
    }

    createNotification(message, type, duration) {
        const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const actualDuration = duration || this.defaultDuration;

        const notification = {
            id: id,
            message: message,
            type: type,
            duration: actualDuration,
            element: null,
            timeout: null
        };

        // Créer l'élément DOM
        const element = document.createElement('div');
        element.className = `louna-toast ${type}`;
        element.style.cssText = `
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        `;

        // Icônes selon le type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle',
            loading: 'fas fa-spinner fa-spin'
        };

        const icon = icons[type] || icons.info;

        element.innerHTML = `
            <div class="louna-toast-header">
                <div class="louna-toast-title">
                    <i class="${icon}"></i>
                    <span>${this.getTypeTitle(type)}</span>
                </div>
                <button class="louna-toast-close" onclick="LounaNotify.dismiss('${id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="louna-toast-message">${message}</div>
        `;

        notification.element = element;

        // Auto-dismiss si durée spécifiée
        if (actualDuration > 0) {
            notification.timeout = setTimeout(() => {
                this.dismiss(id);
            }, actualDuration);
        }

        return notification;
    }

    getTypeTitle(type) {
        const titles = {
            success: 'Succès',
            error: 'Erreur',
            warning: 'Attention',
            info: 'Information',
            loading: 'Chargement'
        };
        return titles[type] || 'Notification';
    }

    addNotification(notification) {
        // Ajouter à la liste
        this.notifications.unshift(notification);

        // Limiter le nombre de notifications
        while (this.notifications.length > this.maxNotifications) {
            const oldest = this.notifications.pop();
            this.dismiss(oldest.id, false);
        }

        // Ajouter au DOM
        this.container.insertBefore(notification.element, this.container.firstChild);

        // Animation d'entrée
        requestAnimationFrame(() => {
            notification.element.style.transform = 'translateX(0)';
            notification.element.style.opacity = '1';
        });
    }

    dismiss(id, removeFromArray = true) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        // Annuler le timeout
        if (notification.timeout) {
            clearTimeout(notification.timeout);
        }

        // Animation de sortie
        notification.element.style.transform = 'translateX(100%)';
        notification.element.style.opacity = '0';

        // Supprimer du DOM après l'animation
        setTimeout(() => {
            if (notification.element && notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
        }, 300);

        // Supprimer de la liste
        if (removeFromArray) {
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }
    }

    dismissAll() {
        const ids = this.notifications.map(n => n.id);
        ids.forEach(id => this.dismiss(id));
    }

    // Méthodes de convenance
    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }

    loading(message) {
        return this.show(message, 'loading', 0); // Pas d'auto-dismiss pour loading
    }

    // Méthode pour mettre à jour une notification existante
    update(id, message, type) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        const messageElement = notification.element.querySelector('.louna-toast-message');
        const titleElement = notification.element.querySelector('.louna-toast-title span');
        const iconElement = notification.element.querySelector('.louna-toast-title i');

        if (messageElement) messageElement.textContent = message;
        if (titleElement) titleElement.textContent = this.getTypeTitle(type);
        
        if (iconElement && type) {
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle',
                loading: 'fas fa-spinner fa-spin'
            };
            iconElement.className = icons[type] || icons.info;
        }

        // Mettre à jour la classe CSS
        if (type) {
            notification.element.className = `louna-toast ${type}`;
            notification.type = type;
        }
    }

    // Méthode pour afficher une notification de progression
    progress(message, percentage) {
        const id = this.loading(message);
        const notification = this.notifications.find(n => n.id === id);
        
        if (notification) {
            const progressHtml = `
                <div class="louna-progress">
                    <div class="louna-progress-bar" style="width: ${percentage}%"></div>
                </div>
            `;
            
            const messageElement = notification.element.querySelector('.louna-toast-message');
            messageElement.innerHTML = message + progressHtml;
        }
        
        return id;
    }

    // Méthode pour les notifications système
    system(message, type = 'info') {
        const systemMessage = `🤖 Système Louna: ${message}`;
        return this.show(systemMessage, type, 7000);
    }

    // Méthode pour les notifications de l'agent
    agent(message, type = 'info') {
        const agentMessage = `🧠 Agent Claude: ${message}`;
        return this.show(agentMessage, type, 6000);
    }

    // Méthode pour les notifications de mémoire thermique
    memory(message, type = 'info') {
        const memoryMessage = `🔥 Mémoire Thermique: ${message}`;
        return this.show(memoryMessage, type, 5000);
    }
}

// Instance globale
window.LounaNotify = new LounaNotifications();

// Méthodes globales pour faciliter l'utilisation
window.showNotification = (message, type, duration) => {
    return window.LounaNotify.show(message, type, duration);
};

window.showSuccess = (message, duration) => {
    return window.LounaNotify.success(message, duration);
};

window.showError = (message, duration) => {
    return window.LounaNotify.error(message, duration);
};

window.showWarning = (message, duration) => {
    return window.LounaNotify.warning(message, duration);
};

window.showInfo = (message, duration) => {
    return window.LounaNotify.info(message, duration);
};

window.showLoading = (message) => {
    return window.LounaNotify.loading(message);
};

// Événement personnalisé pour déclencher des notifications
window.notifyLouna = (message, type, duration) => {
    document.dispatchEvent(new CustomEvent('louna:notify', {
        detail: { message, type, duration }
    }));
};

console.log('🔔 Système de notifications Louna chargé');
