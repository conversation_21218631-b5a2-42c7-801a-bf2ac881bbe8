/**
 * Système Vocal Avancé - Louna
 * Reconnaissance vocale intelligente et synthèse naturelle
 */

class AdvancedVoiceSystem {
    constructor() {
        this.isListening = false;
        this.isSpeaking = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.voices = [];
        this.selectedVoice = null;
        this.conversationHistory = [];
        this.voiceSettings = {
            rate: 0.9,
            pitch: 1.1,
            volume: 0.8,
            language: 'fr-FR'
        };
        this.emotionalStates = {
            neutral: { rate: 0.9, pitch: 1.0 },
            excited: { rate: 1.1, pitch: 1.2 },
            calm: { rate: 0.8, pitch: 0.9 },
            confident: { rate: 1.0, pitch: 1.1 },
            thoughtful: { rate: 0.7, pitch: 0.95 }
        };
        this.currentEmotion = 'neutral';
        this.init();
    }

    async init() {
        console.log('🎤 Initialisation du système vocal avancé...');
        
        await this.initSpeechRecognition();
        await this.initSpeechSynthesis();
        this.setupVoiceCommands();
        this.createVoiceInterface();
        
        console.log('✅ Système vocal avancé initialisé');
    }

    async initSpeechRecognition() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('⚠️ Reconnaissance vocale non supportée');
            return;
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = this.voiceSettings.language;
        this.recognition.maxAlternatives = 3;

        this.recognition.onstart = () => {
            console.log('🎤 Écoute démarrée');
            this.isListening = true;
            this.updateVoiceStatus('listening');
        };

        this.recognition.onend = () => {
            console.log('🎤 Écoute terminée');
            this.isListening = false;
            this.updateVoiceStatus('idle');
        };

        this.recognition.onresult = (event) => {
            this.handleSpeechResult(event);
        };

        this.recognition.onerror = (event) => {
            console.error('❌ Erreur reconnaissance vocale:', event.error);
            this.handleSpeechError(event.error);
        };
    }

    async initSpeechSynthesis() {
        // Attendre que les voix soient chargées
        return new Promise((resolve) => {
            const loadVoices = () => {
                this.voices = this.synthesis.getVoices();
                
                // Chercher une voix française féminine de qualité
                this.selectedVoice = this.voices.find(voice => 
                    voice.lang.startsWith('fr') && 
                    (voice.name.includes('Female') || voice.name.includes('Amélie') || voice.name.includes('Audrey'))
                ) || this.voices.find(voice => voice.lang.startsWith('fr')) || this.voices[0];

                console.log(`🗣️ Voix sélectionnée: ${this.selectedVoice?.name || 'Défaut'}`);
                resolve();
            };

            if (this.voices.length === 0) {
                this.synthesis.onvoiceschanged = loadVoices;
            } else {
                loadVoices();
            }
        });
    }

    setupVoiceCommands() {
        this.voiceCommands = {
            'louna': () => this.activateVoiceMode(),
            'écoute': () => this.startListening(),
            'stop': () => this.stopListening(),
            'silence': () => this.stopSpeaking(),
            'génère une image': (text) => this.handleGenerationCommand('image', text),
            'génère du code': (text) => this.handleGenerationCommand('code', text),
            'génère une vidéo': (text) => this.handleGenerationCommand('video', text),
            'ouvre le hub': () => this.navigateToHub(),
            'optimise le système': () => this.optimizeSystem(),
            'quel est mon qi': () => this.reportSystemStatus(),
            'comment ça va': () => this.reportWellbeing()
        };
    }

    createVoiceInterface() {
        // Créer l'interface vocale flottante
        const voiceInterface = document.createElement('div');
        voiceInterface.id = 'voice-interface';
        voiceInterface.innerHTML = `
            <div class="voice-container">
                <div class="voice-visualizer" id="voice-visualizer">
                    <div class="voice-wave"></div>
                    <div class="voice-wave"></div>
                    <div class="voice-wave"></div>
                    <div class="voice-wave"></div>
                    <div class="voice-wave"></div>
                </div>
                <div class="voice-controls">
                    <button class="voice-btn" id="voice-toggle" title="Activer/Désactiver la voix">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="voice-btn" id="voice-settings" title="Paramètres vocaux">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
                <div class="voice-status" id="voice-status">Prêt à écouter</div>
                <div class="voice-transcript" id="voice-transcript"></div>
            </div>
        `;

        // Ajouter les styles
        const styles = document.createElement('style');
        styles.textContent = `
            #voice-interface {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 10000;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                font-family: 'Segoe UI', sans-serif;
                min-width: 280px;
                max-width: 350px;
                transition: all 0.3s ease;
            }

            .voice-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .voice-visualizer {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 3px;
                height: 40px;
                opacity: 0.3;
                transition: opacity 0.3s ease;
            }

            .voice-visualizer.active {
                opacity: 1;
            }

            .voice-wave {
                width: 4px;
                height: 10px;
                background: linear-gradient(45deg, #4ecdc4, #45b7d1);
                border-radius: 2px;
                animation: wave 1.5s ease-in-out infinite;
            }

            .voice-wave:nth-child(2) { animation-delay: 0.1s; }
            .voice-wave:nth-child(3) { animation-delay: 0.2s; }
            .voice-wave:nth-child(4) { animation-delay: 0.3s; }
            .voice-wave:nth-child(5) { animation-delay: 0.4s; }

            @keyframes wave {
                0%, 100% { height: 10px; }
                50% { height: 30px; }
            }

            .voice-controls {
                display: flex;
                gap: 10px;
            }

            .voice-btn {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                border: none;
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                color: white;
                font-size: 18px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .voice-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 5px 20px rgba(255, 107, 107, 0.4);
            }

            .voice-btn.active {
                background: linear-gradient(45deg, #4ecdc4, #45b7d1);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(78, 205, 196, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(78, 205, 196, 0); }
                100% { box-shadow: 0 0 0 0 rgba(78, 205, 196, 0); }
            }

            .voice-status {
                font-size: 12px;
                color: #888;
                text-align: center;
                min-height: 16px;
            }

            .voice-transcript {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 10px;
                font-size: 14px;
                min-height: 40px;
                max-height: 100px;
                overflow-y: auto;
                width: 100%;
                display: none;
            }

            .voice-transcript.active {
                display: block;
            }

            .voice-settings-panel {
                position: absolute;
                bottom: 100%;
                right: 0;
                background: rgba(0, 0, 0, 0.9);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 10px;
                min-width: 250px;
                display: none;
            }

            .voice-settings-panel.active {
                display: block;
            }

            .voice-setting {
                margin-bottom: 15px;
            }

            .voice-setting label {
                display: block;
                font-size: 12px;
                color: #888;
                margin-bottom: 5px;
            }

            .voice-setting input[type="range"] {
                width: 100%;
                height: 5px;
                border-radius: 5px;
                background: rgba(255, 255, 255, 0.2);
                outline: none;
            }

            .voice-setting select {
                width: 100%;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 5px;
                color: white;
                font-size: 12px;
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(voiceInterface);

        // Configurer les événements
        this.setupVoiceEvents();
    }

    setupVoiceEvents() {
        const toggleBtn = document.getElementById('voice-toggle');
        const settingsBtn = document.getElementById('voice-settings');

        toggleBtn.addEventListener('click', () => {
            if (this.isListening) {
                this.stopListening();
            } else {
                this.startListening();
            }
        });

        settingsBtn.addEventListener('click', () => {
            this.showVoiceSettings();
        });
    }

    startListening() {
        if (!this.recognition) {
            this.speak("Désolée, la reconnaissance vocale n'est pas disponible sur ce navigateur.");
            return;
        }

        try {
            this.recognition.start();
            this.updateVoiceVisualizer(true);
            document.getElementById('voice-toggle').classList.add('active');
        } catch (error) {
            console.error('Erreur démarrage reconnaissance:', error);
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
            this.updateVoiceVisualizer(false);
            document.getElementById('voice-toggle').classList.remove('active');
        }
    }

    handleSpeechResult(event) {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        // Afficher la transcription
        const transcriptElement = document.getElementById('voice-transcript');
        transcriptElement.classList.add('active');
        transcriptElement.textContent = finalTranscript || interimTranscript;

        if (finalTranscript) {
            this.processVoiceCommand(finalTranscript.toLowerCase().trim());
            
            // Masquer la transcription après 3 secondes
            setTimeout(() => {
                transcriptElement.classList.remove('active');
            }, 3000);
        }
    }

    processVoiceCommand(command) {
        console.log('🎤 Commande vocale reçue:', command);

        // Vérifier les commandes directes
        for (const [trigger, action] of Object.entries(this.voiceCommands)) {
            if (command.includes(trigger)) {
                if (typeof action === 'function') {
                    action(command);
                    return;
                }
            }
        }

        // Si aucune commande spécifique, envoyer au chat
        this.sendToChat(command);
    }

    async sendToChat(message) {
        try {
            const response = await fetch('/api/chat/message', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: message,
                    userId: 'voice-user',
                    voiceMode: true
                })
            });

            const data = await response.json();
            
            if (data.response) {
                this.speak(data.response);
            }
        } catch (error) {
            console.error('Erreur envoi chat:', error);
            this.speak("Désolée, je n'ai pas pu traiter votre demande.");
        }
    }

    speak(text, emotion = null) {
        if (this.isSpeaking) {
            this.synthesis.cancel();
        }

        const utterance = new SpeechSynthesisUtterance(text);
        
        // Appliquer les paramètres émotionnels
        const emotionSettings = emotion ? this.emotionalStates[emotion] : this.emotionalStates[this.currentEmotion];
        
        utterance.voice = this.selectedVoice;
        utterance.rate = emotionSettings.rate * this.voiceSettings.rate;
        utterance.pitch = emotionSettings.pitch * this.voiceSettings.pitch;
        utterance.volume = this.voiceSettings.volume;
        utterance.lang = this.voiceSettings.language;

        utterance.onstart = () => {
            this.isSpeaking = true;
            this.updateVoiceStatus('speaking');
        };

        utterance.onend = () => {
            this.isSpeaking = false;
            this.updateVoiceStatus('idle');
        };

        utterance.onerror = (error) => {
            console.error('Erreur synthèse vocale:', error);
            this.isSpeaking = false;
            this.updateVoiceStatus('error');
        };

        this.synthesis.speak(utterance);
        
        // Ajouter à l'historique
        this.conversationHistory.push({
            type: 'speech',
            text: text,
            emotion: emotion || this.currentEmotion,
            timestamp: new Date().toISOString()
        });
    }

    stopSpeaking() {
        if (this.synthesis.speaking) {
            this.synthesis.cancel();
            this.isSpeaking = false;
            this.updateVoiceStatus('idle');
        }
    }

    updateVoiceStatus(status) {
        const statusElement = document.getElementById('voice-status');
        const statusTexts = {
            idle: 'Prêt à écouter',
            listening: 'Écoute en cours...',
            speaking: 'Louna parle...',
            processing: 'Traitement...',
            error: 'Erreur détectée'
        };

        statusElement.textContent = statusTexts[status] || status;
    }

    updateVoiceVisualizer(active) {
        const visualizer = document.getElementById('voice-visualizer');
        if (active) {
            visualizer.classList.add('active');
        } else {
            visualizer.classList.remove('active');
        }
    }

    // Commandes vocales spécialisées
    activateVoiceMode() {
        this.speak("Mode vocal activé. Je vous écoute !", 'confident');
        this.startListening();
    }

    async handleGenerationCommand(type, fullCommand) {
        const prompt = fullCommand.replace(/génère (une |du |une )?/i, '').trim();
        
        this.speak(`Génération ${type} en cours pour : ${prompt}`, 'excited');
        
        try {
            const response = await fetch('/api/generation/create', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ type, prompt })
            });

            const result = await response.json();
            
            if (result.success) {
                this.speak(`${type} généré avec succès ! Vous pouvez le voir maintenant.`, 'confident');
            } else {
                this.speak(`Désolée, la génération a échoué.`, 'calm');
            }
        } catch (error) {
            this.speak("Une erreur s'est produite lors de la génération.", 'calm');
        }
    }

    navigateToHub() {
        this.speak("Ouverture du hub central", 'neutral');
        window.location.href = '/hub';
    }

    async optimizeSystem() {
        this.speak("Optimisation du système en cours...", 'confident');
        
        try {
            const response = await fetch('/api/system/optimize', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.speak("Optimisation terminée avec succès !", 'excited');
            } else {
                this.speak("L'optimisation a rencontré un problème.", 'calm');
            }
        } catch (error) {
            this.speak("Erreur lors de l'optimisation.", 'calm');
        }
    }

    async reportSystemStatus() {
        try {
            const response = await fetch('/api/monitoring/status');
            const data = await response.json();
            
            if (data.status && data.status.qi) {
                this.speak(`Votre QI actuel est de ${data.status.qi}. Vous avez ${data.status.neurons || 0} neurones actifs.`, 'confident');
            } else {
                this.speak("Je ne peux pas accéder aux informations de QI pour le moment.", 'calm');
            }
        } catch (error) {
            this.speak("Erreur lors de la récupération du statut.", 'calm');
        }
    }

    reportWellbeing() {
        const responses = [
            "Je vais très bien, merci ! Mes systèmes fonctionnent parfaitement.",
            "Excellente forme ! Ma mémoire thermique est optimale.",
            "Je me sens en pleine forme, prête à vous aider !",
            "Tout va bien de mon côté. Et vous, comment allez-vous ?"
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        this.speak(randomResponse, 'confident');
    }

    showVoiceSettings() {
        // Créer le panneau de paramètres s'il n'existe pas
        let settingsPanel = document.querySelector('.voice-settings-panel');
        
        if (!settingsPanel) {
            settingsPanel = document.createElement('div');
            settingsPanel.className = 'voice-settings-panel';
            settingsPanel.innerHTML = `
                <h4 style="margin-bottom: 15px; color: #4ecdc4;">Paramètres Vocaux</h4>
                
                <div class="voice-setting">
                    <label>Vitesse de parole</label>
                    <input type="range" id="voice-rate" min="0.5" max="2" step="0.1" value="${this.voiceSettings.rate}">
                </div>
                
                <div class="voice-setting">
                    <label>Tonalité</label>
                    <input type="range" id="voice-pitch" min="0.5" max="2" step="0.1" value="${this.voiceSettings.pitch}">
                </div>
                
                <div class="voice-setting">
                    <label>Volume</label>
                    <input type="range" id="voice-volume" min="0" max="1" step="0.1" value="${this.voiceSettings.volume}">
                </div>
                
                <div class="voice-setting">
                    <label>État émotionnel</label>
                    <select id="voice-emotion">
                        <option value="neutral">Neutre</option>
                        <option value="excited">Enthousiaste</option>
                        <option value="calm">Calme</option>
                        <option value="confident">Confiant</option>
                        <option value="thoughtful">Réfléchi</option>
                    </select>
                </div>
                
                <button onclick="voiceSystem.testVoice()" style="width: 100%; padding: 10px; background: #4ecdc4; border: none; border-radius: 5px; color: white; margin-top: 10px; cursor: pointer;">
                    Tester la voix
                </button>
            `;
            
            document.getElementById('voice-interface').appendChild(settingsPanel);
            
            // Configurer les événements des paramètres
            this.setupSettingsEvents();
        }
        
        settingsPanel.classList.toggle('active');
    }

    setupSettingsEvents() {
        document.getElementById('voice-rate').addEventListener('input', (e) => {
            this.voiceSettings.rate = parseFloat(e.target.value);
        });
        
        document.getElementById('voice-pitch').addEventListener('input', (e) => {
            this.voiceSettings.pitch = parseFloat(e.target.value);
        });
        
        document.getElementById('voice-volume').addEventListener('input', (e) => {
            this.voiceSettings.volume = parseFloat(e.target.value);
        });
        
        document.getElementById('voice-emotion').addEventListener('change', (e) => {
            this.currentEmotion = e.target.value;
        });
    }

    testVoice() {
        this.speak("Voici un test de ma voix avec les nouveaux paramètres. Comment trouvez-vous le résultat ?", this.currentEmotion);
    }

    handleSpeechError(error) {
        const errorMessages = {
            'no-speech': "Aucune parole détectée. Essayez de parler plus fort.",
            'audio-capture': "Impossible d'accéder au microphone.",
            'not-allowed': "Permission microphone refusée.",
            'network': "Erreur réseau lors de la reconnaissance vocale."
        };

        const message = errorMessages[error] || "Erreur de reconnaissance vocale.";
        this.speak(message, 'calm');
    }
}

// Initialiser le système vocal
const voiceSystem = new AdvancedVoiceSystem();
window.voiceSystem = voiceSystem;
