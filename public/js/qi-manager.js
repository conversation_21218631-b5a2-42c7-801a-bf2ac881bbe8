/**
 * GESTIONNAIRE CENTRALISÉ DU QI - LOUNA
 * =====================================
 * Créé par <PERSON>, Guadeloupe
 *
 * Ce fichier centralise TOUTE la gestion du QI dans l'application.
 * UNE SEULE SOURCE DE VÉRITÉ pour le QI affiché partout.
 */

class QIManager {
    constructor() {
        // Utiliser la configuration globale si disponible
        this.currentQI = window.LOUNA_CONFIG?.qi?.current || 203; // QI de base de Jean-<PERSON> Passave
        this.isInitialized = false;
        this.updateCallbacks = [];
        this.updateInterval = null;

        console.log('🧠 QIManager initialisé - QI de base:', this.currentQI);
        this.init();
    }

    /**
     * Initialisation du gestionnaire QI
     */
    async init() {
        try {
            // Récupérer le QI RÉEL depuis l'API
            await this.fetchRealQI();

            // Démarrer les mises à jour automatiques
            this.startAutoUpdate();

            // Mettre à jour tous les éléments QI de la page
            this.updateAllQIElements();

            this.isInitialized = true;
            console.log('✅ QIManager initialisé avec succès - QI actuel:', this.currentQI);

        } catch (error) {
            console.warn('⚠️ Erreur initialisation QIManager:', error);
            // Utiliser la valeur par défaut
            this.updateAllQIElements();
            this.isInitialized = true;
        }
    }

    /**
     * Récupérer le QI RÉEL depuis l'API
     */
    async fetchRealQI() {
        try {
            const response = await fetch('/api/qi/current');
            if (response.ok) {
                const data = await response.json();
                const newQI = data.qi?.qi || data.qi || this.currentQI;

                if (newQI !== this.currentQI) {
                    const oldQI = this.currentQI;
                    this.currentQI = Math.round(newQI);
                    console.log(`🧠 QI mis à jour: ${oldQI} → ${this.currentQI}`);

                    // Notifier tous les callbacks
                    this.notifyCallbacks(oldQI, this.currentQI);
                }

                return this.currentQI;
            }
        } catch (error) {
            console.warn('⚠️ Erreur récupération QI depuis API:', error);
        }

        // Fallback: essayer la mémoire thermique
        try {
            const response = await fetch('/api/thermal/memory/stats');
            if (response.ok) {
                const data = await response.json();
                if (data.qi) {
                    const newQI = Math.round(data.qi);
                    if (newQI !== this.currentQI) {
                        const oldQI = this.currentQI;
                        this.currentQI = newQI;
                        console.log(`🧠 QI mis à jour depuis mémoire thermique: ${oldQI} → ${this.currentQI}`);
                        this.notifyCallbacks(oldQI, this.currentQI);
                    }
                }
            }
        } catch (error) {
            console.warn('⚠️ Erreur récupération QI depuis mémoire thermique:', error);
        }

        return this.currentQI;
    }

    /**
     * Démarrer les mises à jour automatiques
     */
    startAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // Mettre à jour toutes les 5 secondes
        this.updateInterval = setInterval(async () => {
            await this.fetchRealQI();
            this.updateAllQIElements();
        }, 5000);

        console.log('🔄 Mises à jour automatiques du QI démarrées (5s)');
    }

    /**
     * Arrêter les mises à jour automatiques
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('⏹️ Mises à jour automatiques du QI arrêtées');
        }
    }

    /**
     * Obtenir le QI actuel
     */
    getCurrentQI() {
        return this.currentQI;
    }

    /**
     * Mettre à jour TOUS les éléments QI de la page
     */
    updateAllQIElements() {
        const qiElements = [
            // IDs courants
            'qi-value', 'current-qi', 'qi-display', 'qi-score', 'qi-level',
            'qi-coefficient', 'qi-current', 'qi-actuel', 'qi-system',

            // Classes courantes
            '.qi-value', '.current-qi', '.qi-display', '.qi-score',
            '.qi-coefficient', '.qi-current', '.qi-actuel'
        ];

        let elementsUpdated = 0;

        // Mettre à jour par ID
        qiElements.forEach(selector => {
            if (selector.startsWith('.')) {
                // Sélecteur de classe
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element) {
                        element.textContent = this.currentQI;
                        elementsUpdated++;
                    }
                });
            } else {
                // Sélecteur d'ID
                const element = document.getElementById(selector);
                if (element) {
                    element.textContent = this.currentQI;
                    elementsUpdated++;
                }
            }
        });

        // Mettre à jour les éléments avec "QI:" dans le texte
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.textContent && element.textContent.includes('QI:') &&
                element.children.length === 0) { // Seulement les éléments texte
                const newText = element.textContent.replace(/QI:\s*\d+/, `QI: ${this.currentQI}`);
                if (newText !== element.textContent) {
                    element.textContent = newText;
                    elementsUpdated++;
                }
            }
        });

        if (elementsUpdated > 0) {
            console.log(`🔄 ${elementsUpdated} éléments QI mis à jour avec la valeur: ${this.currentQI}`);
        }
    }

    /**
     * Ajouter un callback pour les mises à jour du QI
     */
    onQIUpdate(callback) {
        if (typeof callback === 'function') {
            this.updateCallbacks.push(callback);
        }
    }

    /**
     * Notifier tous les callbacks des changements de QI
     */
    notifyCallbacks(oldQI, newQI) {
        this.updateCallbacks.forEach(callback => {
            try {
                callback(newQI, oldQI);
            } catch (error) {
                console.warn('⚠️ Erreur dans callback QI:', error);
            }
        });
    }

    /**
     * Forcer une réévaluation du QI
     */
    async forceReevaluation() {
        try {
            const response = await fetch('/api/qi/reevaluate', { method: 'POST' });
            if (response.ok) {
                const data = await response.json();
                console.log('🔄 Réévaluation du QI forcée:', data);
                await this.fetchRealQI();
                this.updateAllQIElements();
                return data;
            }
        } catch (error) {
            console.warn('⚠️ Erreur réévaluation QI:', error);
        }
    }

    /**
     * Obtenir le niveau QI basé sur la valeur
     */
    getQILevel(qi = this.currentQI) {
        // Utiliser la configuration globale si disponible
        if (window.LounaUtils) {
            return window.LounaUtils.getQILevel(qi);
        }

        // Fallback si la configuration globale n'est pas chargée
        if (qi >= 200) return 'Quasi-AGI';
        if (qi >= 180) return 'Génie Exceptionnel';
        if (qi >= 160) return 'Très Supérieur';
        if (qi >= 140) return 'Supérieur';
        if (qi >= 120) return 'Intelligent';
        if (qi >= 100) return 'Moyen';
        if (qi >= 80) return 'Faible';
        return 'Très Faible';
    }

    /**
     * Détruire le gestionnaire
     */
    destroy() {
        this.stopAutoUpdate();
        this.updateCallbacks = [];
        console.log('🗑️ QIManager détruit');
    }
}

// Instance globale du gestionnaire QI
window.qiManager = new QIManager();

// Fonction globale pour obtenir le QI actuel
window.getCurrentQI = () => window.qiManager.getCurrentQI();

// Fonction globale pour forcer la mise à jour
window.updateQI = () => window.qiManager.updateAllQIElements();

// Fonction globale pour forcer la réévaluation
window.reevaluateQI = () => window.qiManager.forceReevaluation();

console.log('🧠 QI Manager chargé - Gestionnaire centralisé du QI actif');
