/**
 * Script pour ajouter des fonctionnalités natives à l'application Louna
 * Ce script est chargé uniquement lorsque l'application est exécutée en mode natif
 */

// Vérifier si l'application est exécutée en mode natif
const isElectron = window.electron !== undefined;

// Fonction pour initialiser l'application native
function initNativeApp() {
  if (!isElectron) return;
  
  console.log('Initialisation de l\'application native Louna...');
  
  // Ajouter la classe electron-app au body
  document.body.classList.add('electron-app');
  
  // Ajouter la classe de plateforme
  document.body.classList.add(`platform-${window.electron.system.platform()}`);
  
  // Créer la barre de titre personnalisée
  createTitleBar();
  
  // Initialiser les raccourcis clavier
  initKeyboardShortcuts();
  
  // Initialiser les notifications natives
  initNotifications();
  
  // Initialiser les liens externes
  initExternalLinks();
  
  // Initialiser le menu contextuel
  initContextMenu();
  
  console.log('Application native Louna initialisée');
}

// Fonction pour créer la barre de titre personnalisée
function createTitleBar() {
  // Ne pas créer de barre de titre personnalisée sur macOS
  if (window.electron.system.platform() === 'darwin') return;
  
  // Créer la barre de titre
  const titlebar = document.createElement('div');
  titlebar.className = 'titlebar';
  
  // Ajouter le titre
  const title = document.createElement('div');
  title.className = 'titlebar-title';
  title.textContent = 'Louna';
  titlebar.appendChild(title);
  
  // Ajouter les boutons de contrôle
  const controls = document.createElement('div');
  controls.className = 'titlebar-controls';
  
  // Bouton de fermeture
  const closeButton = document.createElement('div');
  closeButton.className = 'titlebar-button titlebar-close';
  closeButton.addEventListener('click', () => {
    window.close();
  });
  controls.appendChild(closeButton);
  
  // Bouton de minimisation
  const minimizeButton = document.createElement('div');
  minimizeButton.className = 'titlebar-button titlebar-minimize';
  minimizeButton.addEventListener('click', () => {
    window.electron.ipc.send('minimize-window');
  });
  controls.appendChild(minimizeButton);
  
  // Bouton de maximisation
  const maximizeButton = document.createElement('div');
  maximizeButton.className = 'titlebar-button titlebar-maximize';
  maximizeButton.addEventListener('click', () => {
    window.electron.ipc.send('maximize-window');
  });
  controls.appendChild(maximizeButton);
  
  titlebar.appendChild(controls);
  
  // Ajouter la barre de titre au document
  document.body.insertBefore(titlebar, document.body.firstChild);
  
  // Ajouter la classe main-content au contenu principal
  const mainContent = document.querySelector('main') || document.querySelector('.container') || document.querySelector('.content');
  if (mainContent) {
    mainContent.classList.add('main-content');
  }
}

// Fonction pour initialiser les raccourcis clavier
function initKeyboardShortcuts() {
  // Raccourci Cmd+Q / Ctrl+Q pour quitter l'application
  document.addEventListener('keydown', (event) => {
    if ((event.metaKey || event.ctrlKey) && event.key === 'q') {
      window.close();
    }
  });
  
  // Raccourci Cmd+R / Ctrl+R pour actualiser la page
  document.addEventListener('keydown', (event) => {
    if ((event.metaKey || event.ctrlKey) && event.key === 'r') {
      window.location.reload();
    }
  });
  
  // Raccourci Cmd+, / Ctrl+, pour ouvrir les paramètres
  document.addEventListener('keydown', (event) => {
    if ((event.metaKey || event.ctrlKey) && event.key === ',') {
      window.location.href = '/settings-new.html';
    }
  });
}

// Fonction pour initialiser les notifications natives
function initNotifications() {
  // Remplacer la fonction de notification par la fonction native
  window.showNotification = function(title, message) {
    if (isElectron) {
      window.electron.notification.show(title, message);
    } else {
      // Fallback pour les navigateurs
      if ('Notification' in window) {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification(title, { body: message });
          }
        });
      }
    }
  };
}

// Fonction pour initialiser les liens externes
function initExternalLinks() {
  // Ouvrir les liens externes dans le navigateur par défaut
  document.addEventListener('click', (event) => {
    const link = event.target.closest('a');
    if (link && link.href && link.href.startsWith('http') && !link.hasAttribute('target')) {
      event.preventDefault();
      if (isElectron) {
        window.electron.navigation.openExternal(link.href);
      } else {
        window.open(link.href, '_blank');
      }
    }
  });
}

// Fonction pour initialiser le menu contextuel
function initContextMenu() {
  // Ajouter un menu contextuel personnalisé
  document.addEventListener('contextmenu', (event) => {
    if (isElectron) {
      window.electron.ipc.send('show-context-menu');
    }
  });
}

// Fonction pour créer une notification
function createNotification(title, message, type = 'info') {
  // Créer l'élément de notification
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  
  // Ajouter le titre
  const notificationTitle = document.createElement('div');
  notificationTitle.className = 'notification-title';
  notificationTitle.textContent = title;
  notification.appendChild(notificationTitle);
  
  // Ajouter le message
  const notificationBody = document.createElement('div');
  notificationBody.className = 'notification-body';
  notificationBody.textContent = message;
  notification.appendChild(notificationBody);
  
  // Ajouter la notification au document
  document.body.appendChild(notification);
  
  // Afficher la notification
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // Masquer la notification après 5 secondes
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

// Initialiser l'application native lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', initNativeApp);

// Exposer les fonctions globalement
window.nativeApp = {
  createNotification,
  isElectron
};
