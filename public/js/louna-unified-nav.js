/**
 * Système de navigation unifié pour toutes les interfaces Louna
 * Assure la cohérence et la navigation entre toutes les pages
 */

class LounaUnifiedNav {
    constructor() {
        this.currentPage = this.detectCurrentPage();
        this.init();
    }

    init() {
        this.injectNavigation();
        this.setupEventListeners();
        this.addQuickNavigation();
        this.markActivePage();
        console.log('🧭 Navigation unifiée Louna initialisée');
    }

    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index';
        return filename.replace('.html', '');
    }

    injectNavigation() {
        // Supprimer les anciennes navigations
        const oldNavs = document.querySelectorAll('.top-navbar, .nav-container, .old-nav');
        oldNavs.forEach(nav => nav.remove());

        // Créer la nouvelle navigation
        const nav = document.createElement('nav');
        nav.className = 'louna-nav';
        nav.innerHTML = this.getNavigationHTML();

        // Injecter au début du body
        document.body.insertBefore(nav, document.body.firstChild);

        // Ajouter la classe container au body
        document.body.classList.add('louna-unified');

        // Ajuster le contenu principal
        this.adjustMainContent();
    }

    getNavigationHTML() {
        return `
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <i class="fas fa-brain"></i>
                    <span>Louna</span>
                </a>
                
                <div class="nav-links">
                    <a href="/" class="nav-item" data-page="index">
                        <i class="fas fa-home"></i>
                        <span>Accueil</span>
                    </a>
                    <a href="/unified-hub.html" class="nav-item" data-page="unified-hub">
                        <i class="fas fa-th-large"></i>
                        <span>Hub Central</span>
                    </a>
                    <a href="/chat-simple" class="nav-item" data-page="chat">
                        <i class="fas fa-comments"></i>
                        <span>Chat</span>
                    </a>
                    <a href="/futuristic-interface.html" class="nav-item" data-page="futuristic-interface">
                        <i class="fas fa-fire"></i>
                        <span>Mémoire</span>
                    </a>
                    <a href="/brain-visualization.html" class="nav-item" data-page="brain-visualization">
                        <i class="fas fa-brain"></i>
                        <span>Cerveau 3D</span>
                    </a>
                    <a href="/qi-neuron-monitor.html" class="nav-item" data-page="qi-neuron-monitor">
                        <i class="fas fa-yin-yang"></i>
                        <span>QI Monitor</span>
                    </a>
                    <a href="/generation-studio.html" class="nav-item" data-page="generation-studio">
                        <i class="fas fa-magic"></i>
                        <span>Studio</span>
                    </a>
                    <a href="/navigation" class="nav-item" data-page="navigation">
                        <i class="fas fa-sitemap"></i>
                        <span>Toutes les pages</span>
                    </a>
                </div>
                
                <a href="/" class="nav-home-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
            </div>
        `;
    }

    adjustMainContent() {
        // Ajouter un conteneur unifié si il n'existe pas
        let container = document.querySelector('.louna-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'louna-container';
            
            // Déplacer tout le contenu existant dans le conteneur
            const bodyChildren = Array.from(document.body.children);
            bodyChildren.forEach(child => {
                if (!child.classList.contains('louna-nav') && !child.classList.contains('quick-nav')) {
                    container.appendChild(child);
                }
            });
            
            document.body.appendChild(container);
        }
    }

    addQuickNavigation() {
        const quickNav = document.createElement('div');
        quickNav.className = 'quick-nav';
        quickNav.innerHTML = `
            <button class="quick-nav-btn" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="Retour en haut">
                <i class="fas fa-arrow-up"></i>
            </button>
            <button class="quick-nav-btn" onclick="window.history.back()" title="Page précédente">
                <i class="fas fa-arrow-left"></i>
            </button>
            <button class="quick-nav-btn" onclick="window.location.href='/navigation'" title="Navigation complète">
                <i class="fas fa-th"></i>
            </button>
        `;
        
        document.body.appendChild(quickNav);
    }

    markActivePage() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const page = item.getAttribute('data-page');
            if (page === this.currentPage || 
                (this.currentPage === 'index' && page === 'index') ||
                (this.currentPage.includes(page) && page !== 'index')) {
                item.classList.add('active');
            }
        });
    }

    setupEventListeners() {
        // Smooth scrolling pour les liens internes
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });

        // Gestion du scroll pour la navigation
        let lastScrollY = window.scrollY;
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('.louna-nav');
            if (nav) {
                if (window.scrollY > lastScrollY && window.scrollY > 100) {
                    nav.style.transform = 'translateY(-100%)';
                } else {
                    nav.style.transform = 'translateY(0)';
                }
                lastScrollY = window.scrollY;
            }
        });
    }

    // Méthodes utilitaires pour les pages
    static showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `louna-toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            <span>${message}</span>
        `;
        
        // Styles inline pour le toast
        Object.assign(toast.style, {
            position: 'fixed',
            top: '100px',
            right: '20px',
            background: type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        // Animation d'apparition
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Suppression automatique
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    static addPageHeader(title, subtitle, icon) {
        const existingHeader = document.querySelector('.page-header');
        if (existingHeader) return;

        const header = document.createElement('div');
        header.className = 'page-header fade-in-up';
        header.innerHTML = `
            <h1 class="page-title">
                ${icon ? `<i class="${icon}"></i>` : ''}
                ${title}
            </h1>
            ${subtitle ? `<p class="page-subtitle">${subtitle}</p>` : ''}
        `;
        
        const container = document.querySelector('.louna-container');
        if (container) {
            container.insertBefore(header, container.firstChild);
        }
    }

    static wrapInCard(element, title, icon) {
        if (element.closest('.louna-card')) return;

        const card = document.createElement('div');
        card.className = 'louna-card fade-in-up';
        
        if (title) {
            const header = document.createElement('div');
            header.className = 'card-header';
            header.innerHTML = `
                ${icon ? `<i class="card-icon ${icon}"></i>` : ''}
                <h3 class="card-title">${title}</h3>
            `;
            card.appendChild(header);
        }
        
        element.parentNode.insertBefore(card, element);
        card.appendChild(element);
    }
}

// Auto-initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Charger le CSS unifié
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/css/louna-unified-theme.css?v=' + Date.now();
    document.head.appendChild(link);
    
    // Initialiser la navigation
    setTimeout(() => {
        window.lounaNav = new LounaUnifiedNav();
        
        // Ajouter des animations aux éléments existants
        const elements = document.querySelectorAll('div, section, article');
        elements.forEach((el, index) => {
            if (!el.classList.contains('louna-nav') && !el.classList.contains('quick-nav')) {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.5s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 50);
            }
        });
        
        LounaUnifiedNav.showToast('Interface unifiée Louna chargée', 'success');
    }, 100);
});

// Export pour utilisation externe
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LounaUnifiedNav;
}
