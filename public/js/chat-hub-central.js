/**
 * HUB CENTRAL DE CHAT - CONNECTÉ À TOUTES LES APPLICATIONS LOUNA
 * Ce script gère l'interface de chat qui peut interagir avec toutes les applications
 */

(function() {
    'use strict';
    
    console.log('🚀 [Chat Hub Central] Initialisation...');
    
    // ===== CONFIGURATION DES APPLICATIONS =====
    const APPLICATIONS = {
        'Interface & Monitoring': [
            {
                id: 'thermal-memory',
                name: '<PERSON><PERSON><PERSON><PERSON> Thermique',
                description: 'Interface de mémoire thermique avancée',
                icon: 'fas fa-fire',
                url: '/thermal-memory.html',
                status: 'online',
                keywords: ['mémoire', 'thermique', 'température', 'zones']
            },
            {
                id: 'brain-visualization',
                name: 'Visualisation Cerveau',
                description: 'Visualisation 3D du cerveau artificiel',
                icon: 'fas fa-brain',
                url: '/brain-visualization.html',
                status: 'online',
                keywords: ['cerveau', 'visualisation', '3d', 'neurones']
            },
            {
                id: 'qi-neuron-monitor',
                name: 'Monitoring QI/Neurones',
                description: 'Surveillance QI et neurones en temps réel',
                icon: 'fas fa-chart-line',
                url: '/qi-neuron-monitor.html',
                status: 'online',
                keywords: ['qi', 'neurones', 'monitoring', 'statistiques']
            },
            {
                id: 'kyber-dashboard',
                name: 'Accélérateurs Kyber',
                description: 'Tableau de bord des accélérateurs',
                icon: 'fas fa-bolt',
                url: '/kyber-dashboard.html',
                status: 'online',
                keywords: ['kyber', 'accélérateurs', 'performance', 'boost']
            },
            {
                id: 'performance',
                name: 'Performance',
                description: 'Monitoring des performances système',
                icon: 'fas fa-tachometer-alt',
                url: '/performance.html',
                status: 'online',
                keywords: ['performance', 'système', 'monitoring']
            }
        ],
        'Développement & Code': [
            {
                id: 'code-editor',
                name: 'Éditeur de Code',
                description: 'Éditeur de code intégré',
                icon: 'fas fa-code',
                url: '/code-editor',
                status: 'online',
                keywords: ['code', 'éditeur', 'programmation', 'développement']
            },
            {
                id: 'code-extensions',
                name: 'Extensions Code',
                description: 'Extensions pour le développement',
                icon: 'fas fa-puzzle-piece',
                url: '/code-extensions',
                status: 'online',
                keywords: ['extensions', 'plugins', 'outils']
            },
            {
                id: 'coding-evolution',
                name: 'Évolution Codage',
                description: 'Système d\'évolution du codage',
                icon: 'fas fa-dna',
                url: '/coding-evolution.html',
                status: 'online',
                keywords: ['évolution', 'apprentissage', 'amélioration']
            }
        ],
        'Multimédia & Génération': [
            {
                id: 'generation-studio',
                name: 'Studio de Génération',
                description: 'Studio de génération multimédia',
                icon: 'fas fa-magic',
                url: '/generation-studio',
                status: 'online',
                keywords: ['génération', 'multimédia', 'création', 'studio']
            },
            {
                id: 'ltx-video',
                name: 'LTX Vidéo',
                description: 'Génération vidéo avec LTX',
                icon: 'fas fa-video',
                url: '/ltx-video',
                status: 'online',
                keywords: ['vidéo', 'ltx', 'génération', 'film']
            },
            {
                id: 'cinema-3d',
                name: 'Cinéma 3D',
                description: 'Interface cinéma 3D',
                icon: 'fas fa-film',
                url: '/cinema-3d.html',
                status: 'online',
                keywords: ['cinéma', '3d', 'film', 'production']
            }
        ],
        'Agents & Formation': [
            {
                id: 'agents',
                name: 'Gestion Agents',
                description: 'Gestion des agents IA',
                icon: 'fas fa-users',
                url: '/agents',
                status: 'online',
                keywords: ['agents', 'ia', 'gestion', 'équipe']
            },
            {
                id: 'training',
                name: 'Formation',
                description: 'Formation et apprentissage',
                icon: 'fas fa-graduation-cap',
                url: '/training',
                status: 'online',
                keywords: ['formation', 'apprentissage', 'entraînement']
            },
            {
                id: 'advanced-course-monitor',
                name: 'Cours Avancés',
                description: 'Monitoring des cours avancés',
                icon: 'fas fa-university',
                url: '/advanced-course-monitor.html',
                status: 'online',
                keywords: ['cours', 'avancé', 'éducation']
            }
        ],
        'Système & Sécurité': [
            {
                id: 'settings',
                name: 'Paramètres',
                description: 'Configuration système',
                icon: 'fas fa-cog',
                url: '/settings',
                status: 'online',
                keywords: ['paramètres', 'configuration', 'réglages']
            },
            {
                id: 'security-dashboard',
                name: 'Sécurité',
                description: 'Tableau de bord sécurité',
                icon: 'fas fa-shield-alt',
                url: '/security-dashboard.html',
                status: 'online',
                keywords: ['sécurité', 'protection', 'firewall']
            },
            {
                id: 'agent-navigation',
                name: 'Navigation Agent',
                description: 'Navigation et contrôle agent',
                icon: 'fas fa-compass',
                url: '/agent-navigation.html',
                status: 'online',
                keywords: ['navigation', 'contrôle', 'direction']
            }
        ],
        'Mémoire & Données': [
            {
                id: 'memory-fusion',
                name: 'Fusion Mémoire',
                description: 'Système de fusion de mémoire',
                icon: 'fas fa-layer-group',
                url: '/memory-fusion.html',
                status: 'online',
                keywords: ['fusion', 'mémoire', 'intégration']
            },
            {
                id: 'memory-sync',
                name: 'Sync Mémoire',
                description: 'Synchronisation de mémoire',
                icon: 'fas fa-sync',
                url: '/memory-sync.html',
                status: 'online',
                keywords: ['synchronisation', 'sync', 'mémoire']
            },
            {
                id: 'memory-graph',
                name: 'Graphique Mémoire',
                description: 'Visualisation graphique mémoire',
                icon: 'fas fa-project-diagram',
                url: '/memory-graph.html',
                status: 'online',
                keywords: ['graphique', 'visualisation', 'réseau']
            }
        ]
    };

    // ===== VARIABLES GLOBALES =====
    let currentActiveApp = null;
    let appWindows = new Map(); // Pour gérer les fenêtres ouvertes
    
    // ===== FONCTIONS UTILITAIRES =====
    
    function log(message, type = 'info') {
        const prefix = '🚀 [Chat Hub Central]';
        console[type](`${prefix} ${message}`);
    }
    
    function formatTime(date = new Date()) {
        return date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function showNotification(message, type = 'info') {
        // Utilise le système de notifications existant
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
    
    // ===== GESTION DES APPLICATIONS =====
    
    function renderApplications() {
        const container = document.getElementById('appsContainer');
        if (!container) return;
        
        container.innerHTML = '';
        
        Object.entries(APPLICATIONS).forEach(([category, apps]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'app-category';
            
            const categoryTitle = document.createElement('div');
            categoryTitle.className = 'category-title';
            categoryTitle.innerHTML = `<i class="fas fa-folder"></i> ${category}`;
            categoryDiv.appendChild(categoryTitle);
            
            apps.forEach(app => {
                const appItem = document.createElement('div');
                appItem.className = 'app-item';
                appItem.dataset.appId = app.id;
                
                appItem.innerHTML = `
                    <div class="app-icon">
                        <i class="${app.icon}"></i>
                    </div>
                    <div class="app-info">
                        <div class="app-name">${app.name}</div>
                        <div class="app-description">${app.description}</div>
                    </div>
                    <div class="app-status ${app.status}"></div>
                `;
                
                appItem.addEventListener('click', () => openApplication(app));
                categoryDiv.appendChild(appItem);
            });
            
            container.appendChild(categoryDiv);
        });
        
        log('Applications rendues dans la sidebar');
    }
    
    function openApplication(app) {
        log(`Ouverture de l'application: ${app.name}`);
        
        // Marquer comme active
        setActiveApp(app);
        
        // Ajouter un message système
        addSystemMessage(`🚀 Ouverture de ${app.name}...`);
        
        // Ouvrir dans une nouvelle fenêtre ou iframe selon le contexte
        if (window.electronAPI) {
            // Mode Electron - ouvrir dans une nouvelle fenêtre
            openInElectronWindow(app);
        } else {
            // Mode navigateur - ouvrir dans un nouvel onglet
            window.open(app.url, '_blank');
        }
        
        // Ajouter des actions contextuelles
        setTimeout(() => {
            addAssistantMessage(`${app.name} est maintenant ouvert ! Je peux vous aider à naviguer dans cette application. Que souhaitez-vous faire ?`, [
                { text: 'Aide', action: () => showAppHelp(app) },
                { text: 'Fermer', action: () => closeApplication(app) },
                { text: 'Actualiser', action: () => refreshApplication(app) }
            ]);
        }, 1000);
    }
    
    function openInElectronWindow(app) {
        // Pour Electron, on peut ouvrir dans une nouvelle fenêtre
        if (window.electronAPI && window.electronAPI.openWindow) {
            window.electronAPI.openWindow({
                url: app.url,
                title: app.name,
                width: 1200,
                height: 800
            });
        } else {
            // Fallback
            window.open(app.url, '_blank');
        }
    }
    
    function setActiveApp(app) {
        // Retirer l'état actif de tous les éléments
        document.querySelectorAll('.app-item.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // Ajouter l'état actif à l'application sélectionnée
        const appElement = document.querySelector(`[data-app-id="${app.id}"]`);
        if (appElement) {
            appElement.classList.add('active');
        }
        
        // Mettre à jour l'indicateur
        const indicator = document.getElementById('activeAppIndicator');
        if (indicator) {
            indicator.textContent = app.name;
        }
        
        currentActiveApp = app;
    }
    
    function closeApplication(app) {
        log(`Fermeture de l'application: ${app.name}`);
        addSystemMessage(`❌ ${app.name} fermé`);
        
        if (currentActiveApp && currentActiveApp.id === app.id) {
            currentActiveApp = null;
            const indicator = document.getElementById('activeAppIndicator');
            if (indicator) {
                indicator.textContent = 'Hub Central';
            }
        }
    }
    
    function refreshApplication(app) {
        log(`Actualisation de l'application: ${app.name}`);
        addSystemMessage(`🔄 ${app.name} actualisé`);
    }
    
    function showAppHelp(app) {
        const helpText = `
            <strong>${app.name}</strong><br>
            ${app.description}<br><br>
            <strong>Mots-clés:</strong> ${app.keywords.join(', ')}<br>
            <strong>URL:</strong> ${app.url}
        `;
        
        addAssistantMessage(helpText);
    }
    
    // ===== GESTION DES MESSAGES =====
    
    function addSystemMessage(content) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system';
        
        messageDiv.innerHTML = `
            <div class="message-avatar system">
                <i class="fas fa-cog"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                <div class="message-time">${formatTime()}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function addAssistantMessage(content, actions = []) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';
        
        let actionsHTML = '';
        if (actions.length > 0) {
            actionsHTML = '<div class="app-action-buttons">';
            actions.forEach(action => {
                actionsHTML += `<button class="app-action-btn" onclick="(${action.action.toString()})()">${action.text}</button>`;
            });
            actionsHTML += '</div>';
        }
        
        messageDiv.innerHTML = `
            <div class="message-avatar assistant">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                ${actionsHTML}
                <div class="message-time">${formatTime()}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // ===== TRAITEMENT DES COMMANDES =====
    
    function processCommand(message) {
        const lowerMessage = message.toLowerCase();
        
        // Rechercher des applications par mots-clés
        let foundApps = [];
        Object.values(APPLICATIONS).flat().forEach(app => {
            if (app.keywords.some(keyword => lowerMessage.includes(keyword)) ||
                lowerMessage.includes(app.name.toLowerCase())) {
                foundApps.push(app);
            }
        });
        
        if (foundApps.length > 0) {
            if (foundApps.length === 1) {
                openApplication(foundApps[0]);
            } else {
                let response = "J'ai trouvé plusieurs applications correspondantes :<br><br>";
                foundApps.forEach(app => {
                    response += `• <strong>${app.name}</strong> - ${app.description}<br>`;
                });
                response += "<br>Laquelle souhaitez-vous ouvrir ?";
                
                const actions = foundApps.map(app => ({
                    text: app.name,
                    action: () => openApplication(app)
                }));
                
                addAssistantMessage(response, actions);
            }
            return true;
        }
        
        // Commandes spéciales
        if (lowerMessage.includes('aide') || lowerMessage.includes('help')) {
            showHelp();
            return true;
        }
        
        if (lowerMessage.includes('applications') || lowerMessage.includes('apps')) {
            showAllApplications();
            return true;
        }
        
        return false; // Commande non reconnue
    }
    
    function showHelp() {
        const helpText = `
            <strong>🚀 Aide du Hub Central Louna</strong><br><br>
            
            <strong>Commandes disponibles :</strong><br>
            • "Ouvre [nom d'application]" - Ouvre une application<br>
            • "Montre-moi [fonctionnalité]" - Recherche une fonctionnalité<br>
            • "Applications" - Liste toutes les applications<br>
            • "Aide" - Affiche cette aide<br><br>
            
            <strong>Applications disponibles :</strong><br>
            ${Object.values(APPLICATIONS).flat().map(app => `• ${app.name}`).join('<br>')}
        `;
        
        addAssistantMessage(helpText);
    }
    
    function showAllApplications() {
        let response = "<strong>📱 Toutes les applications disponibles :</strong><br><br>";
        
        Object.entries(APPLICATIONS).forEach(([category, apps]) => {
            response += `<strong>${category} :</strong><br>`;
            apps.forEach(app => {
                response += `• ${app.name} - ${app.description}<br>`;
            });
            response += "<br>";
        });
        
        addAssistantMessage(response);
    }
    
    // ===== GESTIONNAIRES D'ÉVÉNEMENTS =====
    
    function setupEventListeners() {
        // Toggle sidebar
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('appsSidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });
        }
        
        // Boutons d'action rapide
        const appsBtn = document.getElementById('appsBtn');
        if (appsBtn) {
            appsBtn.addEventListener('click', showAllApplications);
        }
        
        const helpBtn = document.getElementById('helpBtn');
        if (helpBtn) {
            helpBtn.addEventListener('click', showHelp);
        }
        
        // Intercepter les messages du chat principal
        if (window.ChatUltraComplet && window.ChatUltraComplet.sendMessage) {
            const originalSendMessage = window.ChatUltraComplet.sendMessage;
            window.ChatUltraComplet.sendMessage = function(message) {
                // Traiter d'abord comme une commande
                if (!processCommand(message)) {
                    // Si ce n'est pas une commande, envoyer normalement
                    return originalSendMessage(message);
                }
                return true;
            };
        }
        
        // Fermer la sidebar en cliquant en dehors sur mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 && sidebar) {
                if (!sidebar.contains(e.target) && 
                    !sidebarToggle.contains(e.target) && 
                    sidebar.classList.contains('open')) {
                    sidebar.classList.remove('open');
                }
            }
        });
    }
    
    // ===== INITIALISATION =====
    
    function initialize() {
        log('🚀 Démarrage du Hub Central de Chat');
        
        // Rendre les applications
        renderApplications();
        
        // Configurer les gestionnaires d'événements
        setupEventListeners();
        
        // Afficher l'heure de bienvenue
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = formatTime();
        }
        
        // Message de bienvenue avec actions
        setTimeout(() => {
            addAssistantMessage(
                "Toutes les applications sont maintenant disponibles ! Vous pouvez :<br>• Cliquer sur une application dans la sidebar<br>• Me demander d'ouvrir une application<br>• Taper 'aide' pour plus d'informations",
                [
                    { text: 'Voir Applications', action: showAllApplications },
                    { text: 'Aide', action: showHelp }
                ]
            );
        }, 2000);
        
        log('✅ Hub Central de Chat initialisé avec succès');
        showNotification('Hub Central prêt ! Toutes les applications sont connectées.', 'success');
    }
    
    // ===== DÉMARRAGE =====
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // ===== EXPOSITION GLOBALE =====
    
    window.ChatHubCentral = {
        openApplication: openApplication,
        closeApplication: closeApplication,
        refreshApplication: refreshApplication,
        showHelp: showHelp,
        showAllApplications: showAllApplications,
        processCommand: processCommand,
        APPLICATIONS: APPLICATIONS
    };
    
})();
