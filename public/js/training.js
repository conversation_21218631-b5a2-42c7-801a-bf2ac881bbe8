/**
 * Script pour la gestion de la formation des agents
 */

// Variables globales
let agents = {};
let datasets = [];
let trainingHistory = [];
let trainingState = {
    isTraining: false,
    progress: 0,
    currentAgent: null,
    currentDataset: null,
    startTime: null,
    endTime: null,
    results: null,
    error: null
};

// Éléments DOM
const agentList = document.getElementById('agent-list');
const datasetsGrid = document.getElementById('datasets-grid');
const historyList = document.getElementById('history-list');
const trainingOptions = document.getElementById('training-options');
const trainingProgress = document.getElementById('training-progress');
const progressFill = document.getElementById('progress-fill');
const progressPercentage = document.getElementById('progress-percentage');
const progressSamples = document.getElementById('progress-samples');
const trainingStatus = document.getElementById('training-status');
const startTrainingBtn = document.getElementById('start-training-btn');
const cancelTrainingBtn = document.getElementById('cancel-training-btn');
const refreshBtn = document.getElementById('refresh-btn');
const createDatasetBtn = document.getElementById('create-dataset-btn');
const epochsInput = document.getElementById('epochs');
const batchSizeInput = document.getElementById('batch-size');
const learningRateInput = document.getElementById('learning-rate');
const useMemoryCheckbox = document.getElementById('use-memory');
const saveToMemoryCheckbox = document.getElementById('save-to-memory');
const notification = document.getElementById('notification');
const notificationMessage = document.getElementById('notification-message');

// Sélections actuelles
let selectedAgentId = null;
let selectedDatasetId = null;

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Initialiser l'interface
    initializeInterface();
});

/**
 * Initialise l'interface utilisateur
 */
async function initializeInterface() {
    // Ajouter les écouteurs d'événements
    refreshBtn.addEventListener('click', refreshData);
    createDatasetBtn.addEventListener('click', showCreateDatasetModal);
    startTrainingBtn.addEventListener('click', startTraining);
    cancelTrainingBtn.addEventListener('click', cancelTraining);

    // Charger les données
    await refreshData();

    // Vérifier l'état de la formation
    checkTrainingState();

    // Démarrer la vérification périodique de l'état de la formation
    setInterval(checkTrainingState, 5000);
}

/**
 * Rafraîchit les données (agents, ensembles de données, historique)
 */
async function refreshData() {
    try {
        // Charger les agents
        await loadAgents();

        // Charger les ensembles de données
        await loadDatasets();

        // Charger l'historique des formations
        await loadTrainingHistory();

        // Mettre à jour l'interface
        updateInterface();
    } catch (error) {
        console.error('Erreur lors du rafraîchissement des données:', error);
        showNotification('Erreur lors du rafraîchissement des données', 'error');
    }
}

/**
 * Charge les agents depuis l'API
 */
async function loadAgents() {
    try {
        const response = await fetch('/api/training/agents');
        const data = await response.json();

        if (data.success) {
            agents = data.agents;
        } else {
            showNotification(`Erreur lors du chargement des agents: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des agents:', error);
        showNotification('Erreur lors du chargement des agents', 'error');
    }
}

/**
 * Charge les ensembles de données depuis l'API
 */
async function loadDatasets() {
    try {
        const response = await fetch('/api/training/datasets');
        const data = await response.json();

        if (data.success) {
            datasets = data.datasets;
        } else {
            showNotification(`Erreur lors du chargement des ensembles de données: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des ensembles de données:', error);
        showNotification('Erreur lors du chargement des ensembles de données', 'error');
    }
}

/**
 * Charge l'historique des formations depuis l'API
 */
async function loadTrainingHistory() {
    try {
        const response = await fetch('/api/training/history');
        const data = await response.json();

        if (data.success) {
            trainingHistory = data.history;
        } else {
            showNotification(`Erreur lors du chargement de l'historique des formations: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des formations:', error);
        showNotification('Erreur lors du chargement de l\'historique des formations', 'error');
    }
}

/**
 * Vérifie l'état de la formation en cours
 */
async function checkTrainingState() {
    try {
        const response = await fetch('/api/training/state');
        const data = await response.json();

        if (data.success) {
            trainingState = data.state;
            updateTrainingProgress();
        } else {
            console.error('Erreur lors de la vérification de l\'état de la formation:', data.error);
        }
    } catch (error) {
        console.error('Erreur lors de la vérification de l\'état de la formation:', error);
    }
}

/**
 * Met à jour l'interface utilisateur
 */
function updateInterface() {
    // Mettre à jour la liste des agents
    renderAgents();

    // Mettre à jour la grille des ensembles de données
    renderDatasets();

    // Mettre à jour l'historique des formations
    renderTrainingHistory();

    // Mettre à jour l'état du bouton de formation
    updateStartButton();
}

/**
 * Affiche les agents dans l'interface
 */
function renderAgents() {
    // Vider la liste des agents
    agentList.innerHTML = '';

    // Afficher les agents
    Object.values(agents).forEach(agent => {
        const agentItem = document.createElement('div');
        agentItem.className = `agent-item ${agent.id === selectedAgentId ? 'selected' : ''}`;
        agentItem.dataset.agentId = agent.id;

        agentItem.innerHTML = `
            <div class="agent-header">
                <div class="agent-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <div class="agent-name">${agent.name}</div>
                    <div class="agent-model">${agent.type === 'ollama' ? agent.model : agent.type}</div>
                </div>
            </div>
        `;

        agentItem.addEventListener('click', () => {
            // Désélectionner l'agent précédemment sélectionné
            const selectedAgents = document.querySelectorAll('.agent-item.selected');
            selectedAgents.forEach(item => item.classList.remove('selected'));

            // Sélectionner le nouvel agent
            agentItem.classList.add('selected');
            selectedAgentId = agent.id;

            // Mettre à jour l'état du bouton de formation
            updateStartButton();
        });

        agentList.appendChild(agentItem);
    });

    // Si aucun agent n'est disponible, afficher un message
    if (Object.keys(agents).length === 0) {
        agentList.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-robot" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucun agent disponible. Veuillez créer un agent dans la section Agents.</p>
                <a href="/agents.html" class="btn btn-outline" style="display: inline-block; margin-top: 10px;">
                    <i class="fas fa-plus"></i> Créer un Agent
                </a>
            </div>
        `;
    }
}

/**
 * Affiche les ensembles de données dans l'interface
 */
function renderDatasets() {
    // Vider la grille des ensembles de données
    datasetsGrid.innerHTML = '';

    // Afficher les ensembles de données
    datasets.forEach(dataset => {
        const datasetCard = document.createElement('div');
        datasetCard.className = `dataset-card ${dataset.id === selectedDatasetId ? 'selected' : ''}`;
        datasetCard.dataset.datasetId = dataset.id;

        // Formater la date de création
        const createdAt = new Date(dataset.createdAt);
        const formattedDate = createdAt.toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        datasetCard.innerHTML = `
            <div class="dataset-header">
                <div class="dataset-title">${dataset.name}</div>
                <div class="dataset-samples">${dataset.samples} échantillons</div>
            </div>
            <div class="dataset-description">${dataset.description || 'Aucune description'}</div>
            <div class="dataset-footer">
                <div>Créé le ${formattedDate}</div>
            </div>
        `;

        datasetCard.addEventListener('click', () => {
            // Désélectionner l'ensemble de données précédemment sélectionné
            const selectedDatasets = document.querySelectorAll('.dataset-card.selected');
            selectedDatasets.forEach(item => item.classList.remove('selected'));

            // Sélectionner le nouvel ensemble de données
            datasetCard.classList.add('selected');
            selectedDatasetId = dataset.id;

            // Mettre à jour l'état du bouton de formation
            updateStartButton();
        });

        datasetsGrid.appendChild(datasetCard);
    });

    // Si aucun ensemble de données n'est disponible, afficher un message
    if (datasets.length === 0) {
        datasetsGrid.innerHTML = `
            <div style="grid-column: 1 / -1; padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-database" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucun ensemble de données disponible. Veuillez créer un ensemble de données.</p>
                <button id="create-dataset-btn-empty" class="btn btn-outline" style="margin-top: 10px;">
                    <i class="fas fa-plus"></i> Créer un Ensemble de Données
                </button>
            </div>
        `;

        // Ajouter l'écouteur d'événement pour le bouton de création d'ensemble de données
        const createDatasetBtnEmpty = document.getElementById('create-dataset-btn-empty');
        if (createDatasetBtnEmpty) {
            createDatasetBtnEmpty.addEventListener('click', showCreateDatasetModal);
        }
    }
}

/**
 * Affiche l'historique des formations dans l'interface
 */
function renderTrainingHistory() {
    // Vider la liste de l'historique des formations
    historyList.innerHTML = '';

    // Trier l'historique par date (plus récent en premier)
    const sortedHistory = [...trainingHistory].sort((a, b) => b.startTime - a.startTime);

    // Afficher l'historique des formations
    sortedHistory.forEach(entry => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';

        // Récupérer les informations sur l'agent et l'ensemble de données
        const agent = agents[entry.agentId] || { name: 'Agent inconnu' };
        const dataset = datasets.find(d => d.id === entry.datasetId) || { name: 'Ensemble de données inconnu' };

        // Formater la date
        const date = new Date(entry.startTime);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // Formater la durée
        const duration = entry.duration ? Math.round(entry.duration / 1000) : 0;
        const formattedDuration = `${duration} secondes`;

        historyItem.innerHTML = `
            <div class="history-info">
                <div class="history-agent">${agent.name}</div>
                <div class="history-dataset">${dataset.name}</div>
            </div>
            <div class="history-stats">
                <div class="history-stat">
                    <i class="fas fa-layer-group"></i> ${entry.options.epochs} époques
                </div>
                <div class="history-stat">
                    <i class="fas fa-database"></i> ${entry.samplesProcessed} échantillons
                </div>
                <div class="history-stat">
                    <i class="fas fa-clock"></i> ${formattedDuration}
                </div>
            </div>
            <div class="history-date">${formattedDate}</div>
            <div class="history-actions">
                <a href="/training-results.html?id=${entry.id}" class="btn btn-outline btn-sm">
                    <i class="fas fa-chart-bar"></i> Voir les résultats
                </a>
            </div>
        `;

        historyItem.addEventListener('click', (e) => {
            // Ne pas déclencher le clic si on a cliqué sur le bouton
            if (e.target.closest('.history-actions')) {
                return;
            }

            // Rediriger vers la page de résultats
            window.location.href = `/training-results.html?id=${entry.id}`;
        });

        historyList.appendChild(historyItem);
    });

    // Si aucune formation n'est disponible, afficher un message
    if (sortedHistory.length === 0) {
        historyList.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-history" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucun historique de formation disponible.</p>
            </div>
        `;
    }
}

/**
 * Met à jour l'état du bouton de formation
 */
function updateStartButton() {
    // Activer le bouton si un agent et un ensemble de données sont sélectionnés
    startTrainingBtn.disabled = !(selectedAgentId && selectedDatasetId);
}

/**
 * Met à jour l'affichage de la progression de la formation
 */
function updateTrainingProgress() {
    if (trainingState.isTraining) {
        // Afficher la progression
        trainingProgress.style.display = 'block';
        trainingOptions.style.display = 'none';

        // Mettre à jour la barre de progression
        progressFill.style.width = `${trainingState.progress}%`;
        progressPercentage.textContent = `${Math.round(trainingState.progress)}%`;

        // Mettre à jour les informations sur les échantillons
        if (trainingState.results) {
            const totalSamples = trainingState.currentDataset.data.length * (trainingState.results.options.epochs || 1);
            progressSamples.textContent = `${trainingState.results.samplesProcessed}/${totalSamples} échantillons`;
        }

        // Mettre à jour le statut
        if (trainingState.currentAgent && trainingState.currentDataset) {
            trainingStatus.textContent = `Formation de l'agent "${trainingState.currentAgent.name}" avec l'ensemble de données "${trainingState.currentDataset.name}"...`;
        } else {
            trainingStatus.textContent = 'Formation en cours...';
        }
    } else {
        // Masquer la progression
        trainingProgress.style.display = 'none';
        trainingOptions.style.display = 'block';

        // Si la formation vient de se terminer, rafraîchir les données et synchroniser la mémoire
        if (trainingState.endTime && trainingState.results) {
            refreshData();
            showNotification('Formation terminée avec succès', 'success');

            // Synchroniser la mémoire entre les agents
            syncMemoryAfterTraining(trainingState.results);
        }

        // Si une erreur s'est produite, afficher une notification
        if (trainingState.error) {
            showNotification(`Erreur lors de la formation: ${trainingState.error}`, 'error');
        }
    }
}

/**
 * Démarre la formation
 */
async function startTraining() {
    try {
        // Vérifier si un agent et un ensemble de données sont sélectionnés
        if (!selectedAgentId || !selectedDatasetId) {
            showNotification('Veuillez sélectionner un agent et un ensemble de données', 'warning');
            return;
        }

        // Récupérer les options de formation
        const options = {
            epochs: parseInt(epochsInput.value) || 1,
            batchSize: parseInt(batchSizeInput.value) || 10,
            learningRate: parseFloat(learningRateInput.value) || 0.001,
            useMemory: useMemoryCheckbox.checked,
            saveToMemory: saveToMemoryCheckbox.checked
        };

        // Envoyer la demande de formation
        const response = await fetch('/api/training/train', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agentId: selectedAgentId,
                datasetId: selectedDatasetId,
                options
            })
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Formation lancée avec succès', 'success');

            // Vérifier l'état de la formation
            await checkTrainingState();
        } else {
            showNotification(`Erreur lors du lancement de la formation: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du lancement de la formation:', error);
        showNotification('Erreur lors du lancement de la formation', 'error');
    }
}

/**
 * Annule la formation en cours
 */
async function cancelTraining() {
    try {
        const response = await fetch('/api/training/cancel', {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Formation annulée', 'info');

            // Vérifier l'état de la formation
            await checkTrainingState();
        } else {
            showNotification(`Erreur lors de l'annulation de la formation: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de l\'annulation de la formation:', error);
        showNotification('Erreur lors de l\'annulation de la formation', 'error');
    }
}

/**
 * Affiche la modal pour créer un ensemble de données
 */
function showCreateDatasetModal() {
    // TODO: Implémenter la modal pour créer un ensemble de données
    showNotification('Fonctionnalité en cours de développement', 'info');
}

/**
 * Synchronise la mémoire entre les agents après une formation
 * @param {Object} trainingResults - Résultats de la formation
 */
async function syncMemoryAfterTraining(trainingResults) {
    try {
        // Vérifier si la synchronisation automatique est activée
        const autoSyncEnabled = localStorage.getItem('autoSyncAfterTraining') === 'true';

        if (!autoSyncEnabled) {
            // Demander à l'utilisateur s'il souhaite synchroniser la mémoire
            const shouldSync = confirm('Voulez-vous synchroniser la mémoire entre les agents après cette formation ?');

            if (!shouldSync) {
                return;
            }

            // Demander à l'utilisateur s'il souhaite activer la synchronisation automatique
            const shouldEnableAutoSync = confirm('Voulez-vous activer la synchronisation automatique après chaque formation ?');

            if (shouldEnableAutoSync) {
                localStorage.setItem('autoSyncAfterTraining', 'true');
            }
        }

        showNotification('Synchronisation de la mémoire en cours...', 'info');

        // Récupérer les options de synchronisation
        const threshold = 0.5; // Seuil d'importance par défaut
        const isBidirectional = true; // Synchronisation bidirectionnelle par défaut

        // Récupérer les entrées de mémoire de l'agent principal
        const mainAgentResponse = await fetch('/api/thermal/memory/entries?agentId=agent_claude');
        const mainAgentData = await mainAgentResponse.json();

        if (!mainAgentData.success) {
            throw new Error(`Erreur lors de la récupération des entrées de l'agent principal: ${mainAgentData.error}`);
        }

        const mainAgentEntries = mainAgentData.entries;

        // Récupérer les entrées de mémoire de l'agent de formation
        const trainingAgentResponse = await fetch('/api/thermal/memory/entries?agentId=agent_training');
        const trainingAgentData = await trainingAgentResponse.json();

        if (!trainingAgentData.success) {
            throw new Error(`Erreur lors de la récupération des entrées de l'agent de formation: ${trainingAgentData.error}`);
        }

        const trainingAgentEntries = trainingAgentData.entries;

        // Identifier les entrées à synchroniser
        const entriesToSync = [];

        // Synchroniser de l'agent principal vers l'agent de formation
        for (const entry of mainAgentEntries) {
            // Vérifier si l'entrée existe déjà dans la mémoire de l'agent de formation
            const existsInTrainingAgent = trainingAgentEntries.some(e => e.key === entry.key);

            // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
            if (!existsInTrainingAgent && entry.importance >= threshold) {
                entriesToSync.push({
                    source: 'agent_claude',
                    target: 'agent_training',
                    entry
                });
            }
        }

        // Synchroniser de l'agent de formation vers l'agent principal si la synchronisation est bidirectionnelle
        if (isBidirectional) {
            for (const entry of trainingAgentEntries) {
                // Vérifier si l'entrée existe déjà dans la mémoire de l'agent principal
                const existsInMainAgent = mainAgentEntries.some(e => e.key === entry.key);

                // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
                if (!existsInMainAgent && entry.importance >= threshold) {
                    entriesToSync.push({
                        source: 'agent_training',
                        target: 'agent_claude',
                        entry
                    });
                }
            }
        }

        // Synchroniser les entrées
        if (entriesToSync.length > 0) {
            let syncedCount = 0;

            for (const syncItem of entriesToSync) {
                const { source, target, entry } = syncItem;

                // Créer une copie de l'entrée pour l'agent cible
                const entryCopy = { ...entry };
                delete entryCopy.id; // Supprimer l'ID pour en générer un nouveau

                // Ajouter des métadonnées de synchronisation
                entryCopy.metadata = entryCopy.metadata || {};
                entryCopy.metadata.syncedFrom = source;
                entryCopy.metadata.syncedAt = new Date().toISOString();
                entryCopy.metadata.agentId = target;
                entryCopy.metadata.syncedAfterTraining = trainingResults.id;

                // Ajouter l'entrée à la mémoire de l'agent cible
                const response = await fetch('/api/thermal/memory/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agentId: target,
                        key: entryCopy.key,
                        data: entryCopy.data,
                        category: entryCopy.category,
                        importance: entryCopy.importance,
                        metadata: entryCopy.metadata
                    })
                });

                const data = await response.json();

                if (data.success) {
                    syncedCount++;
                }
            }

            showNotification(`Synchronisation terminée : ${syncedCount}/${entriesToSync.length} entrées synchronisées`, 'success');
        } else {
            showNotification('Aucune entrée de mémoire à synchroniser', 'info');
        }
    } catch (error) {
        console.error('Erreur lors de la synchronisation de la mémoire:', error);
        showNotification('Erreur lors de la synchronisation de la mémoire', 'error');
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    notificationMessage.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.add('visible');

    // Masquer la notification après 3 secondes
    setTimeout(() => {
        notification.classList.remove('visible');
    }, 3000);
}
