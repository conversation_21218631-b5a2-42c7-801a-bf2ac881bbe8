/**
 * Script pour la comparaison des performances des agents
 */

// Variables globales
let mainAgentData = {
    name: '<PERSON> (4GB)',
    model: 'incept5/llama3.1-claude:latest',
    entries: 0,
    temperature: 0,
    performance: 0,
    performanceHistory: [],
    metrics: {
        accuracy: 0,
        recall: 0,
        precision: 0,
        f1Score: 0,
        responseTime: 0
    }
};

let trainingAgentData = {
    name: 'Agent de Formation',
    model: 'llama3:8b',
    entries: 0,
    temperature: 0,
    performance: 0,
    performanceHistory: [],
    metrics: {
        accuracy: 0,
        recall: 0,
        precision: 0,
        f1Score: 0,
        responseTime: 0
    }
};

let syncHistory = [];
let performanceChart = null;
let mainAgentRadarChart = null;
let trainingAgentRadarChart = null;

// Éléments DOM
const mainAgentName = document.getElementById('main-agent-name');
const mainAgentModel = document.getElementById('main-agent-model');
const mainAgentEntries = document.getElementById('main-agent-entries');
const mainAgentTemperature = document.getElementById('main-agent-temperature');
const mainAgentPerformance = document.getElementById('main-agent-performance');

const trainingAgentName = document.getElementById('training-agent-name');
const trainingAgentModel = document.getElementById('training-agent-model');
const trainingAgentEntries = document.getElementById('training-agent-entries');
const trainingAgentTemperature = document.getElementById('training-agent-temperature');
const trainingAgentPerformance = document.getElementById('training-agent-performance');

const syncHistoryContainer = document.getElementById('sync-history');
const refreshBtn = document.getElementById('refresh-btn');

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Initialiser l'interface
    initializeInterface();

    // Ajouter les écouteurs d'événements
    refreshBtn.addEventListener('click', refreshData);
});

/**
 * Initialise l'interface utilisateur
 */
async function initializeInterface() {
    try {
        // Charger les données
        await loadAgentsData();
        await loadPerformanceHistory();
        await loadSyncHistory();

        // Mettre à jour l'interface
        updateAgentsUI();
        initializeCharts();
        updateSyncHistoryUI();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'interface:', error);
        showNotification('Erreur lors de l\'initialisation de l\'interface', 'error');
    }
}

/**
 * Charge les données des agents
 */
async function loadAgentsData() {
    try {
        // Charger les données de l'agent principal
        const mainAgentResponse = await fetch('/api/agents/agent_claude');
        const mainAgentResponseData = await mainAgentResponse.json();

        if (mainAgentResponseData.success) {
            mainAgentData = {
                ...mainAgentData,
                ...mainAgentResponseData.agent
            };
        } else {
            showNotification(`Erreur lors du chargement des données de l'agent principal: ${mainAgentResponseData.error}`, 'error');
        }

        // Charger les données de l'agent de formation
        const trainingAgentResponse = await fetch('/api/agents/agent_training');
        const trainingAgentResponseData = await trainingAgentResponse.json();

        if (trainingAgentResponseData.success) {
            trainingAgentData = {
                ...trainingAgentData,
                ...trainingAgentResponseData.agent
            };
        } else {
            showNotification(`Erreur lors du chargement des données de l'agent de formation: ${trainingAgentResponseData.error}`, 'error');
        }

        // Charger les entrées de mémoire de l'agent principal
        const mainAgentEntriesResponse = await fetch('/api/thermal/memory/entries?agentId=agent_claude');
        const mainAgentEntriesData = await mainAgentEntriesResponse.json();

        if (mainAgentEntriesData.success) {
            mainAgentData.entries = mainAgentEntriesData.entries.length;
            mainAgentData.temperature = calculateAverageTemperature(mainAgentEntriesData.entries);
        } else {
            showNotification(`Erreur lors du chargement des entrées de l'agent principal: ${mainAgentEntriesData.error}`, 'error');
        }

        // Charger les entrées de mémoire de l'agent de formation
        const trainingAgentEntriesResponse = await fetch('/api/thermal/memory/entries?agentId=agent_training');
        const trainingAgentEntriesData = await trainingAgentEntriesResponse.json();

        if (trainingAgentEntriesData.success) {
            trainingAgentData.entries = trainingAgentEntriesData.entries.length;
            trainingAgentData.temperature = calculateAverageTemperature(trainingAgentEntriesData.entries);
        } else {
            showNotification(`Erreur lors du chargement des entrées de l'agent de formation: ${trainingAgentEntriesData.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données des agents:', error);
        showNotification('Erreur lors du chargement des données des agents', 'error');
    }
}

/**
 * Charge l'historique des performances
 */
async function loadPerformanceHistory() {
    try {
        // Charger l'historique des performances de l'agent principal
        const mainAgentHistoryResponse = await fetch('/api/performance/history?agentId=agent_claude');
        const mainAgentHistoryData = await mainAgentHistoryResponse.json();

        if (mainAgentHistoryData.success) {
            mainAgentData.performanceHistory = mainAgentHistoryData.history;
            mainAgentData.metrics = mainAgentHistoryData.metrics || mainAgentData.metrics;
            mainAgentData.performance = mainAgentHistoryData.currentPerformance || 0;
        } else {
            showNotification(`Erreur lors du chargement de l'historique des performances de l'agent principal: ${mainAgentHistoryData.error}`, 'error');
        }

        // Charger l'historique des performances de l'agent de formation
        const trainingAgentHistoryResponse = await fetch('/api/performance/history?agentId=agent_training');
        const trainingAgentHistoryData = await trainingAgentHistoryResponse.json();

        if (trainingAgentHistoryData.success) {
            trainingAgentData.performanceHistory = trainingAgentHistoryData.history;
            trainingAgentData.metrics = trainingAgentHistoryData.metrics || trainingAgentData.metrics;
            trainingAgentData.performance = trainingAgentHistoryData.currentPerformance || 0;
        } else {
            showNotification(`Erreur lors du chargement de l'historique des performances de l'agent de formation: ${trainingAgentHistoryData.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des performances:', error);
        showNotification('Erreur lors du chargement de l\'historique des performances', 'error');
    }
}

/**
 * Charge l'historique des synchronisations
 */
async function loadSyncHistory() {
    try {
        const response = await fetch('/api/sync/history');
        const data = await response.json();

        if (data.success) {
            syncHistory = data.history;
        } else {
            showNotification(`Erreur lors du chargement de l'historique des synchronisations: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des synchronisations:', error);
        showNotification('Erreur lors du chargement de l\'historique des synchronisations', 'error');
    }
}

/**
 * Met à jour l'interface utilisateur des agents
 */
function updateAgentsUI() {
    // Mettre à jour l'agent principal
    mainAgentName.textContent = mainAgentData.name;
    mainAgentModel.textContent = mainAgentData.model;
    mainAgentEntries.textContent = `${mainAgentData.entries} entrées`;
    mainAgentTemperature.textContent = `Température: ${mainAgentData.temperature.toFixed(2)}`;
    mainAgentPerformance.textContent = `Performance: ${mainAgentData.performance.toFixed(2)}`;

    // Mettre à jour l'agent de formation
    trainingAgentName.textContent = trainingAgentData.name;
    trainingAgentModel.textContent = trainingAgentData.model;
    trainingAgentEntries.textContent = `${trainingAgentData.entries} entrées`;
    trainingAgentTemperature.textContent = `Température: ${trainingAgentData.temperature.toFixed(2)}`;
    trainingAgentPerformance.textContent = `Performance: ${trainingAgentData.performance.toFixed(2)}`;
}

/**
 * Initialise les graphiques
 */
function initializeCharts() {
    // Initialiser le graphique de performance
    initializePerformanceChart();

    // Initialiser les graphiques radar
    initializeRadarCharts();
}

/**
 * Initialise le graphique de performance
 */
function initializePerformanceChart() {
    const ctx = document.getElementById('performance-chart').getContext('2d');

    // Préparer les données
    const labels = [];
    const mainAgentPerformanceData = [];
    const trainingAgentPerformanceData = [];
    const syncEvents = [];

    // Fusionner les historiques de performance des deux agents
    const allDates = new Set();

    mainAgentData.performanceHistory.forEach(entry => {
        allDates.add(entry.date);
    });

    trainingAgentData.performanceHistory.forEach(entry => {
        allDates.add(entry.date);
    });

    // Trier les dates
    const sortedDates = Array.from(allDates).sort();

    // Remplir les données
    sortedDates.forEach(date => {
        labels.push(new Date(date).toLocaleDateString());

        const mainAgentEntry = mainAgentData.performanceHistory.find(entry => entry.date === date);
        mainAgentPerformanceData.push(mainAgentEntry ? mainAgentEntry.performance : null);

        const trainingAgentEntry = trainingAgentData.performanceHistory.find(entry => entry.date === date);
        trainingAgentPerformanceData.push(trainingAgentEntry ? trainingAgentEntry.performance : null);

        // Vérifier si une synchronisation a eu lieu à cette date
        const syncEvent = syncHistory.find(sync => {
            const syncDate = new Date(sync.date).toLocaleDateString();
            return syncDate === new Date(date).toLocaleDateString();
        });

        syncEvents.push(syncEvent ? true : false);
    });

    // Créer le graphique
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Agent Principal',
                    data: mainAgentPerformanceData,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6
                },
                {
                    label: 'Agent de Formation',
                    data: trainingAgentPerformanceData,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const index = context.dataIndex;
                            if (syncEvents[index]) {
                                return 'Synchronisation effectuée à cette date';
                            }
                            return '';
                        }
                    }
                },
                annotation: {
                    annotations: syncEvents.map((isSyncEvent, index) => {
                        if (isSyncEvent) {
                            return {
                                type: 'line',
                                xMin: index,
                                xMax: index,
                                borderColor: 'rgba(255, 255, 255, 0.5)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                label: {
                                    content: 'Sync',
                                    enabled: true,
                                    position: 'top',
                                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                    color: 'rgba(255, 255, 255, 0.7)'
                                }
                            };
                        }
                        return null;
                    }).filter(annotation => annotation !== null)
                }
            }
        }
    });
}

/**
 * Initialise les graphiques radar
 */
function initializeRadarCharts() {
    // Graphique radar de l'agent principal
    const mainCtx = document.getElementById('main-agent-radar').getContext('2d');
    mainAgentRadarChart = new Chart(mainCtx, {
        type: 'radar',
        data: {
            labels: ['Précision', 'Rappel', 'Exactitude', 'Score F1', 'Temps de réponse'],
            datasets: [{
                label: 'Agent Principal',
                data: [
                    mainAgentData.metrics.precision,
                    mainAgentData.metrics.recall,
                    mainAgentData.metrics.accuracy,
                    mainAgentData.metrics.f1Score,
                    1 - (mainAgentData.metrics.responseTime / 10) // Normaliser le temps de réponse
                ],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(255, 99, 132, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    angleLines: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    pointLabels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        backdropColor: 'transparent'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            }
        }
    });

    // Graphique radar de l'agent de formation
    const trainingCtx = document.getElementById('training-agent-radar').getContext('2d');
    trainingAgentRadarChart = new Chart(trainingCtx, {
        type: 'radar',
        data: {
            labels: ['Précision', 'Rappel', 'Exactitude', 'Score F1', 'Temps de réponse'],
            datasets: [{
                label: 'Agent de Formation',
                data: [
                    trainingAgentData.metrics.precision,
                    trainingAgentData.metrics.recall,
                    trainingAgentData.metrics.accuracy,
                    trainingAgentData.metrics.f1Score,
                    1 - (trainingAgentData.metrics.responseTime / 10) // Normaliser le temps de réponse
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    angleLines: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    pointLabels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        backdropColor: 'transparent'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            }
        }
    });
}

/**
 * Met à jour l'interface utilisateur de l'historique des synchronisations
 */
function updateSyncHistoryUI() {
    // Vider le conteneur
    syncHistoryContainer.innerHTML = '';

    // Si aucune synchronisation n'est disponible, afficher un message
    if (syncHistory.length === 0) {
        syncHistoryContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-history" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucun historique de synchronisation disponible.</p>
            </div>
        `;
        return;
    }

    // Trier l'historique par date (plus récent en premier)
    const sortedHistory = [...syncHistory].sort((a, b) => new Date(b.date) - new Date(a.date));

    // Afficher l'historique des synchronisations
    sortedHistory.forEach(sync => {
        const syncItem = document.createElement('div');
        syncItem.className = 'sync-item';

        // Formater la date
        const date = new Date(sync.date);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // Déterminer la classe de performance
        let performanceChangeClass = 'neutral';
        let performanceChangeIcon = 'fa-minus';

        if (sync.performanceChange > 0) {
            performanceChangeClass = 'positive';
            performanceChangeIcon = 'fa-arrow-up';
        } else if (sync.performanceChange < 0) {
            performanceChangeClass = 'negative';
            performanceChangeIcon = 'fa-arrow-down';
        }

        syncItem.innerHTML = `
            <div class="sync-header">
                <div class="sync-title">Synchronisation #${sync.id}</div>
                <div class="sync-date">${formattedDate}</div>
            </div>
            <div class="sync-stats">
                <div class="sync-stat">
                    <i class="fas fa-exchange-alt"></i>
                    <span>${sync.entriesSynced} entrées synchronisées</span>
                </div>
                <div class="sync-stat">
                    <i class="fas fa-brain"></i>
                    <span>${sync.mainToTraining} Principal → Formation</span>
                </div>
                <div class="sync-stat">
                    <i class="fas fa-graduation-cap"></i>
                    <span>${sync.trainingToMain} Formation → Principal</span>
                </div>
            </div>
            <div class="sync-footer">
                <div>Durée: ${sync.duration} secondes</div>
                <div class="performance-change ${performanceChangeClass}">
                    <i class="fas ${performanceChangeIcon}"></i>
                    <span>Performance: ${sync.performanceChange > 0 ? '+' : ''}${sync.performanceChange.toFixed(2)}</span>
                </div>
            </div>
        `;

        syncHistoryContainer.appendChild(syncItem);
    });
}

/**
 * Rafraîchit les données
 */
async function refreshData() {
    try {
        // Charger les données
        await loadAgentsData();
        await loadPerformanceHistory();
        await loadSyncHistory();

        // Mettre à jour l'interface
        updateAgentsUI();

        // Mettre à jour les graphiques
        updateCharts();

        // Mettre à jour l'historique des synchronisations
        updateSyncHistoryUI();

        // Vérifier les performances
        if (window.performanceAlerts) {
            window.performanceAlerts.check();

            // Mettre à jour le tableau de bord des alertes si la fonction existe
            if (typeof displayAlertsDashboard === 'function') {
                displayAlertsDashboard();
            }
        }

        showNotification('Données actualisées avec succès', 'success');
    } catch (error) {
        console.error('Erreur lors du rafraîchissement des données:', error);
        showNotification('Erreur lors du rafraîchissement des données', 'error');
    }
}

/**
 * Met à jour les graphiques
 */
function updateCharts() {
    // Détruire les graphiques existants
    if (performanceChart) {
        performanceChart.destroy();
    }

    if (mainAgentRadarChart) {
        mainAgentRadarChart.destroy();
    }

    if (trainingAgentRadarChart) {
        trainingAgentRadarChart.destroy();
    }

    // Réinitialiser les graphiques
    initializeCharts();
}

/**
 * Calcule la température moyenne des entrées
 * @param {Array} entries - Entrées de mémoire
 * @returns {number} - Température moyenne
 */
function calculateAverageTemperature(entries) {
    if (entries.length === 0) {
        return 0;
    }

    const totalTemperature = entries.reduce((sum, entry) => sum + (entry.temperature || 0), 0);
    return totalTemperature / entries.length;
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    if (window.notifications) {
        window.notifications[type](message, '', { duration: 3000 });
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}