/**
 * Moniteur de performances pour l'application Louna
 * Surveille et affiche les performances de la mémoire thermique et des accélérateurs Kyber
 */

class PerformanceMonitor {
    constructor() {
        // Éléments DOM
        this.lastUpdateElement = document.getElementById('last-update');
        this.avgTemperatureElement = document.getElementById('avg-temperature');
        this.totalEntriesElement = document.getElementById('total-entries');
        this.cyclesPerformedElement = document.getElementById('cycles-performed');
        this.kyberEfficiencyElement = document.getElementById('kyber-efficiency');

        // Éléments de chargement
        this.temperatureLoading = document.getElementById('temperature-loading');
        this.entriesLoading = document.getElementById('entries-loading');
        this.acceleratorsLoading = document.getElementById('accelerators-loading');
        this.cyclesLoading = document.getElementById('cycles-loading');

        // Graphiques
        this.temperatureChart = null;
        this.entriesChart = null;
        this.acceleratorsChart = null;
        this.cyclesChart = null;
        this.comparisonChart = null;

        // Données de performance
        this.performanceData = {
            temperature: [],
            entries: [],
            accelerators: [],
            cycles: []
        };

        // Plage de temps actuelle
        this.currentTimeRange = 'hour';

        // Initialiser le moniteur
        this.initialize();
    }

    /**
     * Initialise le moniteur de performances
     */
    async initialize() {
        // Initialiser les graphiques
        this.initializeCharts();

        // Charger les données initiales
        await this.loadPerformanceData();

        // Mettre à jour les graphiques
        this.updateCharts();

        // Ajouter les écouteurs d'événements
        this.addEventListeners();

        // Mettre à jour périodiquement (toutes les 2 minutes pour éviter les problèmes de performance)
        this.updateInterval = setInterval(() => {
            this.loadPerformanceData();
        }, 120000); // Toutes les 2 minutes
    }

    /**
     * Initialise les graphiques
     */
    initializeCharts() {
        // Graphique de température
        const temperatureCtx = document.getElementById('temperature-chart').getContext('2d');
        this.temperatureChart = new Chart(temperatureCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Température moyenne',
                    data: [],
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y: {
                        min: 0,
                        max: 1,
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    }
                }
            }
        });

        // Graphique des entrées
        const entriesCtx = document.getElementById('entries-chart').getContext('2d');
        this.entriesChart = new Chart(entriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Instantanée', 'Court terme', 'Travail', 'Moyen terme', 'Long terme', 'Rêves'],
                datasets: [{
                    data: [0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#ff6b6b',
                        '#ff9e7d',
                        '#ffd166',
                        '#06d6a0',
                        '#118ab2',
                        '#9b5de5'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'right',
                        labels: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                        }
                    }
                }
            }
        });

        // Graphique des accélérateurs
        const acceleratorsCtx = document.getElementById('accelerators-chart').getContext('2d');
        this.acceleratorsChart = new Chart(acceleratorsCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Efficacité',
                        data: [],
                        borderColor: '#06d6a0',
                        backgroundColor: 'rgba(6, 214, 160, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Stabilité',
                        data: [],
                        borderColor: '#118ab2',
                        backgroundColor: 'rgba(17, 138, 178, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        min: 0,
                        max: 100,
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        min: 0,
                        max: 100,
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Graphique des cycles
        const cyclesCtx = document.getElementById('cycles-chart').getContext('2d');
        this.cyclesChart = new Chart(cyclesCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Cycles de mémoire',
                    data: [],
                    backgroundColor: '#9b5de5',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y: {
                        min: 0,
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    }
                }
            }
        });
    }

    /**
     * Charge les données de performance
     */
    async loadPerformanceData() {
        try {
            // Afficher les indicateurs de chargement
            this.showLoadingIndicators();

            // Récupérer les statistiques de la mémoire thermique
            const memoryStats = await this.fetchMemoryStats();

            // Récupérer les statistiques des accélérateurs Kyber
            const kyberStats = await this.fetchKyberStats();

            // Récupérer l'historique des performances
            const performanceHistory = await this.fetchPerformanceHistory();

            // Mettre à jour les métriques
            this.updateMetrics(memoryStats, kyberStats);

            // Mettre à jour les données de performance avec l'historique
            this.updatePerformanceData(performanceHistory);

            // Mettre à jour les graphiques
            this.updateCharts();

            // Mettre à jour l'heure de la dernière mise à jour
            this.updateLastUpdateTime();

            // Masquer les indicateurs de chargement
            this.hideLoadingIndicators();

            // Afficher une notification de succès
            notifications.success(
                'Données de performance mises à jour avec succès.',
                'Mise à jour réussie',
                { duration: 3000 }
            );
        } catch (error) {
            console.error('Erreur lors du chargement des données de performance:', error);

            // Afficher une notification d'erreur
            notifications.error(
                'Impossible de charger les données de performance. Veuillez réessayer plus tard.',
                'Erreur de chargement'
            );

            // Masquer les indicateurs de chargement
            this.hideLoadingIndicators();
        }
    }

    /**
     * Récupère les statistiques de la mémoire thermique
     * @returns {Promise<Object>} - Statistiques de la mémoire thermique
     */
    async fetchMemoryStats() {
        try {
            const response = await fetch('/api/thermal/memory/stats');
            const data = await response.json();

            if (data.success) {
                return data.stats;
            } else {
                throw new Error(data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des statistiques de la mémoire:', error);
            throw error;
        }
    }

    /**
     * Récupère les statistiques des accélérateurs Kyber
     * @returns {Promise<Object>} - Statistiques des accélérateurs Kyber
     */
    async fetchKyberStats() {
        try {
            const response = await fetch('/api/kyber/stats');
            const data = await response.json();

            if (data.success) {
                return data.stats;
            } else {
                throw new Error(data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des statistiques des accélérateurs:', error);
            throw error;
        }
    }

    /**
     * Met à jour les métriques affichées
     * @param {Object} memoryStats - Statistiques de la mémoire thermique
     * @param {Object} kyberStats - Statistiques des accélérateurs Kyber
     */
    updateMetrics(memoryStats, kyberStats) {
        // Température moyenne
        this.avgTemperatureElement.textContent = memoryStats.averageTemperature.toFixed(2);

        // Entrées totales
        this.totalEntriesElement.textContent = memoryStats.totalMemories;

        // Cycles effectués
        this.cyclesPerformedElement.textContent = memoryStats.cyclesPerformed;

        // Efficacité Kyber
        const efficiency = kyberStats.efficiency || 0;
        this.kyberEfficiencyElement.textContent = `${Math.round(efficiency * 100)}%`;
    }

    /**
     * Récupère l'historique des performances
     * @returns {Promise<Object>} - Historique des performances
     */
    async fetchPerformanceHistory() {
        try {
            const response = await fetch(`/api/performance/history?timeRange=${this.currentTimeRange}`);
            const data = await response.json();

            if (data.success) {
                return data.history;
            } else {
                throw new Error(data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('Erreur lors de la récupération de l\'historique des performances:', error);
            throw error;
        }
    }

    /**
     * Met à jour les données de performance avec l'historique
     * @param {Object} history - Historique des performances
     */
    updatePerformanceData(history) {
        // Mettre à jour les données de performance
        this.performanceData = {
            labels: history.labels,
            temperature: history.temperature,
            entries: history.entries,
            accelerators: {
                efficiency: history.accelerators.efficiency,
                stability: history.accelerators.stability
            },
            cycles: history.cycles
        };
    }

    /**
     * Génère des données de performance simulées (méthode de secours)
     * @param {Object} memoryStats - Statistiques de la mémoire thermique
     * @param {Object} kyberStats - Statistiques des accélérateurs Kyber
     */
    generatePerformanceData(memoryStats, kyberStats) {
        // Déterminer le nombre de points de données en fonction de la plage de temps
        let dataPoints = 24; // Par défaut pour une heure (24 points de 2,5 minutes)

        switch (this.currentTimeRange) {
            case 'hour':
                dataPoints = 24; // 24 points de 2,5 minutes
                break;
            case 'day':
                dataPoints = 24; // 24 points d'une heure
                break;
            case 'week':
                dataPoints = 7; // 7 points d'un jour
                break;
            case 'month':
                dataPoints = 30; // 30 points d'un jour
                break;
        }

        // Générer les étiquettes de temps
        const labels = this.generateTimeLabels(dataPoints);

        // Générer les données de température
        const temperatureData = this.generateTemperatureData(dataPoints, memoryStats.averageTemperature);

        // Générer les données d'entrées
        const entriesData = [
            memoryStats.zone1Count || 0,
            memoryStats.zone2Count || 0,
            memoryStats.zone3Count || 0,
            memoryStats.zone4Count || 0,
            memoryStats.zone5Count || 0,
            memoryStats.zone6Count || 0
        ];

        // Générer les données des accélérateurs
        const acceleratorsData = this.generateAcceleratorsData(dataPoints, kyberStats);

        // Générer les données des cycles
        const cyclesData = this.generateCyclesData(dataPoints, memoryStats.cyclesPerformed);

        // Mettre à jour les données de performance
        this.performanceData = {
            labels,
            temperature: temperatureData,
            entries: entriesData,
            accelerators: acceleratorsData,
            cycles: cyclesData
        };

        // Afficher un avertissement
        console.warn('Utilisation de données de performance simulées (méthode de secours)');
    }

    /**
     * Génère des étiquettes de temps
     * @param {number} count - Nombre d'étiquettes à générer
     * @returns {Array<string>} - Étiquettes de temps
     */
    generateTimeLabels(count) {
        const labels = [];
        const now = new Date();

        for (let i = count - 1; i >= 0; i--) {
            let date = new Date(now);

            switch (this.currentTimeRange) {
                case 'hour':
                    date.setMinutes(now.getMinutes() - (i * 2.5));
                    labels.push(date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
                    break;
                case 'day':
                    date.setHours(now.getHours() - i);
                    labels.push(date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
                    break;
                case 'week':
                    date.setDate(now.getDate() - i);
                    labels.push(date.toLocaleDateString([], { weekday: 'short' }));
                    break;
                case 'month':
                    date.setDate(now.getDate() - i);
                    labels.push(date.toLocaleDateString([], { day: 'numeric', month: 'short' }));
                    break;
            }
        }

        return labels;
    }

    /**
     * Génère des données de température simulées
     * @param {number} count - Nombre de points de données
     * @param {number} currentTemperature - Température actuelle
     * @returns {Array<number>} - Données de température
     */
    generateTemperatureData(count, currentTemperature) {
        const data = [];
        const baseTemperature = currentTemperature || 0.5;

        for (let i = 0; i < count; i++) {
            // Générer une température aléatoire autour de la température de base
            const variation = (Math.random() - 0.5) * 0.2;
            let temperature = baseTemperature + variation;

            // Limiter la température entre 0 et 1
            temperature = Math.max(0, Math.min(1, temperature));

            data.push(temperature);
        }

        return data;
    }

    /**
     * Génère des données d'accélérateurs simulées
     * @param {number} count - Nombre de points de données
     * @param {Object} kyberStats - Statistiques des accélérateurs Kyber
     * @returns {Object} - Données des accélérateurs
     */
    generateAcceleratorsData(count, kyberStats) {
        const efficiency = [];
        const stability = [];

        const baseEfficiency = (kyberStats.efficiency || 0.7) * 100;
        const baseStability = (kyberStats.stability || 0.8) * 100;

        for (let i = 0; i < count; i++) {
            // Générer une efficacité aléatoire autour de l'efficacité de base
            const efficiencyVariation = (Math.random() - 0.5) * 10;
            let currentEfficiency = baseEfficiency + efficiencyVariation;

            // Limiter l'efficacité entre 0 et 100
            currentEfficiency = Math.max(0, Math.min(100, currentEfficiency));

            // Générer une stabilité aléatoire autour de la stabilité de base
            const stabilityVariation = (Math.random() - 0.5) * 5;
            let currentStability = baseStability + stabilityVariation;

            // Limiter la stabilité entre 0 et 100
            currentStability = Math.max(0, Math.min(100, currentStability));

            efficiency.push(currentEfficiency);
            stability.push(currentStability);
        }

        return { efficiency, stability };
    }

    /**
     * Génère des données de cycles simulées
     * @param {number} count - Nombre de points de données
     * @param {number} totalCycles - Nombre total de cycles
     * @returns {Array<number>} - Données des cycles
     */
    generateCyclesData(count, totalCycles) {
        const data = [];
        const cyclesPerPoint = Math.max(1, Math.floor(totalCycles / count));

        for (let i = 0; i < count; i++) {
            // Générer un nombre aléatoire de cycles autour du nombre moyen
            const variation = (Math.random() - 0.5) * (cyclesPerPoint * 0.5);
            let cycles = Math.max(0, Math.round(cyclesPerPoint + variation));

            data.push(cycles);
        }

        return data;
    }

    /**
     * Met à jour les graphiques avec les données de performance
     */
    updateCharts() {
        // Désactiver les animations pour améliorer les performances
        Chart.defaults.animation = false;

        // Mettre à jour le graphique de température
        this.temperatureChart.data.labels = this.performanceData.labels;
        this.temperatureChart.data.datasets[0].data = this.performanceData.temperature;
        this.temperatureChart.update('none'); // Pas d'animation pour de meilleures performances

        // Mettre à jour le graphique des entrées
        this.entriesChart.data.datasets[0].data = this.performanceData.entries;
        this.entriesChart.update('none');

        // Mettre à jour le graphique des accélérateurs
        this.acceleratorsChart.data.labels = this.performanceData.labels;
        this.acceleratorsChart.data.datasets[0].data = this.performanceData.accelerators.efficiency;
        this.acceleratorsChart.data.datasets[1].data = this.performanceData.accelerators.stability;
        this.acceleratorsChart.update('none');

        // Mettre à jour le graphique des cycles
        this.cyclesChart.data.labels = this.performanceData.labels;
        this.cyclesChart.data.datasets[0].data = this.performanceData.cycles;
        this.cyclesChart.update('none');

        // Réactiver les animations après la mise à jour
        setTimeout(() => {
            Chart.defaults.animation = true;
        }, 100);
    }

    /**
     * Met à jour l'heure de la dernière mise à jour
     */
    updateLastUpdateTime() {
        const now = new Date();
        this.lastUpdateElement.textContent = `Dernière mise à jour: ${now.toLocaleTimeString()}`;
    }

    /**
     * Affiche les indicateurs de chargement
     */
    showLoadingIndicators() {
        this.temperatureLoading.style.display = 'flex';
        this.entriesLoading.style.display = 'flex';
        this.acceleratorsLoading.style.display = 'flex';
        this.cyclesLoading.style.display = 'flex';
    }

    /**
     * Masque les indicateurs de chargement
     */
    hideLoadingIndicators() {
        this.temperatureLoading.style.display = 'none';
        this.entriesLoading.style.display = 'none';
        this.acceleratorsLoading.style.display = 'none';
        this.cyclesLoading.style.display = 'none';
    }

    /**
     * Ajoute les écouteurs d'événements
     */
    addEventListeners() {
        // Bouton de rafraîchissement
        const refreshButton = document.getElementById('refresh-btn');
        refreshButton.addEventListener('click', () => {
            this.loadPerformanceData();
        });

        // Boutons de plage de temps
        const timeRangeButtons = document.querySelectorAll('.time-range-button');
        timeRangeButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Mettre à jour la plage de temps active
                timeRangeButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Mettre à jour la plage de temps actuelle
                this.currentTimeRange = button.dataset.range;

                // Recharger les données
                this.loadPerformanceData();
            });
        });

        // Bouton d'exportation
        const exportButton = document.getElementById('export-btn');
        if (exportButton) {
            exportButton.addEventListener('click', () => {
                this.showExportModal();
            });
        }

        // Bouton de comparaison
        const compareButton = document.getElementById('compare-btn');
        if (compareButton) {
            compareButton.addEventListener('click', () => {
                this.showCompareModal();
            });
        }

        // Boutons de fermeture des modales
        const modalCloseButtons = document.querySelectorAll('.modal-close');
        modalCloseButtons.forEach(button => {
            button.addEventListener('click', () => {
                const modal = button.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                }
            });
        });

        // Bouton de téléchargement des données
        const exportDownloadButton = document.getElementById('export-download-btn');
        if (exportDownloadButton) {
            exportDownloadButton.addEventListener('click', () => {
                this.exportData();
            });
        }

        // Bouton de génération de comparaison
        const compareGenerateButton = document.getElementById('compare-generate-btn');
        if (compareGenerateButton) {
            compareGenerateButton.addEventListener('click', () => {
                this.generateComparison();
            });
        }

        // Bouton de fermeture de la comparaison
        const comparisonCloseButton = document.getElementById('comparison-close');
        if (comparisonCloseButton) {
            comparisonCloseButton.addEventListener('click', () => {
                const comparisonContainer = document.getElementById('comparison-container');
                if (comparisonContainer) {
                    comparisonContainer.classList.remove('show');
                }
            });
        }
    }

    /**
     * Affiche la modale d'exportation
     */
    showExportModal() {
        const exportModal = document.getElementById('export-modal');
        if (exportModal) {
            exportModal.classList.add('show');
        }
    }

    /**
     * Affiche la modale de comparaison
     */
    showCompareModal() {
        const compareModal = document.getElementById('compare-modal');
        if (compareModal) {
            compareModal.classList.add('show');
        }
    }

    /**
     * Exporte les données de performance
     */
    exportData() {
        // Récupérer les options d'exportation
        const exportTemperature = document.getElementById('export-temperature').checked;
        const exportEntries = document.getElementById('export-entries').checked;
        const exportAccelerators = document.getElementById('export-accelerators').checked;
        const exportCycles = document.getElementById('export-cycles').checked;
        const exportFormat = document.getElementById('export-format-select').value;

        // Préparer les données à exporter
        const exportData = {
            timeRange: this.currentTimeRange,
            timestamp: new Date().toISOString(),
            labels: this.performanceData.labels
        };

        // Ajouter les données sélectionnées
        if (exportTemperature) {
            exportData.temperature = this.performanceData.temperature;
        }

        if (exportEntries) {
            exportData.entries = this.performanceData.entries;
        }

        if (exportAccelerators) {
            exportData.accelerators = this.performanceData.accelerators;
        }

        if (exportCycles) {
            exportData.cycles = this.performanceData.cycles;
        }

        // Exporter les données au format sélectionné
        if (exportFormat === 'json') {
            this.exportAsJson(exportData);
        } else if (exportFormat === 'csv') {
            this.exportAsCsv(exportData);
        }

        // Fermer la modale
        const exportModal = document.getElementById('export-modal');
        if (exportModal) {
            exportModal.classList.remove('show');
        }

        // Afficher une notification de succès
        notifications.success(
            'Les données de performance ont été exportées avec succès.',
            'Exportation réussie'
        );
    }

    /**
     * Exporte les données au format JSON
     * @param {Object} data - Données à exporter
     */
    exportAsJson(data) {
        // Convertir les données en JSON
        const jsonData = JSON.stringify(data, null, 2);

        // Créer un blob
        const blob = new Blob([jsonData], { type: 'application/json' });

        // Créer une URL pour le blob
        const url = URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;
        link.download = `louna-performance-${this.currentTimeRange}-${new Date().toISOString().slice(0, 10)}.json`;

        // Cliquer sur le lien
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * Exporte les données au format CSV
     * @param {Object} data - Données à exporter
     */
    exportAsCsv(data) {
        // Préparer les en-têtes
        const headers = ['Timestamp'];

        // Ajouter les en-têtes pour les données sélectionnées
        if (data.temperature) {
            headers.push('Temperature');
        }

        if (data.entries) {
            headers.push('Instant', 'Short Term', 'Working Memory', 'Medium Term', 'Long Term', 'Dream Memory');
        }

        if (data.accelerators) {
            headers.push('Efficiency', 'Stability');
        }

        if (data.cycles) {
            headers.push('Cycles');
        }

        // Préparer les lignes
        const rows = [];

        // Ajouter une ligne pour chaque point de données
        for (let i = 0; i < data.labels.length; i++) {
            const row = [data.labels[i]];

            // Ajouter les données pour chaque colonne
            if (data.temperature) {
                row.push(data.temperature[i]);
            }

            if (data.entries) {
                for (let j = 0; j < data.entries.length; j++) {
                    row.push(data.entries[j]);
                }
            }

            if (data.accelerators) {
                row.push(data.accelerators.efficiency[i]);
                row.push(data.accelerators.stability[i]);
            }

            if (data.cycles) {
                row.push(data.cycles[i]);
            }

            rows.push(row);
        }

        // Convertir les données en CSV
        const csvContent = [
            headers.join(','),
            ...rows.map(row => row.join(','))
        ].join('\n');

        // Créer un blob
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        // Créer une URL pour le blob
        const url = URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;
        link.download = `louna-performance-${this.currentTimeRange}-${new Date().toISOString().slice(0, 10)}.csv`;

        // Cliquer sur le lien
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * Génère une comparaison des performances
     */
    async generateComparison() {
        try {
            // Récupérer les options de comparaison
            const baselineRange = document.getElementById('compare-baseline').value;
            const targetRange = document.getElementById('compare-target').value;
            const metric = document.getElementById('compare-metric').value;

            // Afficher les indicateurs de chargement
            this.showLoadingIndicators();

            // Récupérer les données de référence
            const baselineData = await this.fetchPerformanceHistory(baselineRange);

            // Récupérer les données cibles
            const targetData = await this.fetchPerformanceHistory(targetRange);

            // Créer le graphique de comparaison
            this.createComparisonChart(baselineData, targetData, metric);

            // Masquer les indicateurs de chargement
            this.hideLoadingIndicators();

            // Fermer la modale
            const compareModal = document.getElementById('compare-modal');
            if (compareModal) {
                compareModal.classList.remove('show');
            }

            // Afficher le conteneur de comparaison
            const comparisonContainer = document.getElementById('comparison-container');
            if (comparisonContainer) {
                comparisonContainer.classList.add('show');
            }

            // Afficher une notification de succès
            notifications.success(
                'La comparaison des performances a été générée avec succès.',
                'Comparaison réussie'
            );
        } catch (error) {
            console.error('Erreur lors de la génération de la comparaison:', error);

            // Afficher une notification d'erreur
            notifications.error(
                'Impossible de générer la comparaison des performances. Veuillez réessayer plus tard.',
                'Erreur de comparaison'
            );

            // Masquer les indicateurs de chargement
            this.hideLoadingIndicators();
        }
    }

    /**
     * Récupère l'historique des performances pour une plage de temps spécifique
     * @param {string} timeRange - Plage de temps
     * @returns {Promise<Object>} - Historique des performances
     */
    async fetchPerformanceHistory(timeRange) {
        try {
            const response = await fetch(`/api/performance/history?timeRange=${timeRange}`);
            const data = await response.json();

            if (data.success) {
                return data.history;
            } else {
                throw new Error(data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error(`Erreur lors de la récupération de l'historique des performances pour ${timeRange}:`, error);
            throw error;
        }
    }

    /**
     * Crée un graphique de comparaison
     * @param {Object} baselineData - Données de référence
     * @param {Object} targetData - Données cibles
     * @param {string} metric - Métrique à comparer
     */
    createComparisonChart(baselineData, targetData, metric) {
        // Récupérer le contexte du graphique
        const ctx = document.getElementById('comparison-chart').getContext('2d');

        // Déterminer les données à comparer
        let baselineValues = [];
        let targetValues = [];
        let title = '';

        switch (metric) {
            case 'temperature':
                baselineValues = baselineData.temperature;
                targetValues = targetData.temperature;
                title = 'Comparaison de la température';
                break;
            case 'efficiency':
                baselineValues = baselineData.accelerators.efficiency;
                targetValues = targetData.accelerators.efficiency;
                title = 'Comparaison de l\'efficacité';
                break;
            case 'stability':
                baselineValues = baselineData.accelerators.stability;
                targetValues = targetData.accelerators.stability;
                title = 'Comparaison de la stabilité';
                break;
            case 'cycles':
                baselineValues = baselineData.cycles;
                targetValues = targetData.cycles;
                title = 'Comparaison des cycles';
                break;
        }

        // Calculer les moyennes
        const baselineAvg = baselineValues.reduce((sum, value) => sum + value, 0) / baselineValues.length;
        const targetAvg = targetValues.reduce((sum, value) => sum + value, 0) / targetValues.length;

        // Calculer la différence en pourcentage
        const diffPercent = ((targetAvg - baselineAvg) / baselineAvg) * 100;

        // Mettre à jour le titre de la comparaison
        const comparisonTitle = document.querySelector('.comparison-title');
        if (comparisonTitle) {
            comparisonTitle.textContent = `${title} (${diffPercent >= 0 ? '+' : ''}${diffPercent.toFixed(2)}%)`;
        }

        // Créer le graphique
        if (this.comparisonChart) {
            this.comparisonChart.destroy();
        }

        this.comparisonChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: Array.from({ length: Math.max(baselineValues.length, targetValues.length) }, (_, i) => i + 1),
                datasets: [
                    {
                        label: 'Référence',
                        data: baselineValues,
                        borderColor: '#118ab2',
                        backgroundColor: 'rgba(17, 138, 178, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: 'Cible',
                        data: targetValues,
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y: {
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    }
                }
            }
        });
    }
    /**
     * Nettoie les ressources utilisées par le moniteur
     */
    cleanup() {
        // Annuler l'intervalle de mise à jour
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // Détruire les graphiques
        if (this.temperatureChart) {
            this.temperatureChart.destroy();
        }

        if (this.entriesChart) {
            this.entriesChart.destroy();
        }

        if (this.acceleratorsChart) {
            this.acceleratorsChart.destroy();
        }

        if (this.cyclesChart) {
            this.cyclesChart.destroy();
        }

        if (this.comparisonChart) {
            this.comparisonChart.destroy();
        }
    }
}

// Variable globale pour le moniteur de performances
let performanceMonitor;

// Initialiser le moniteur de performances lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    performanceMonitor = new PerformanceMonitor();
});

// Nettoyer les ressources lorsque l'utilisateur quitte la page
window.addEventListener('beforeunload', () => {
    if (performanceMonitor) {
        performanceMonitor.cleanup();
    }
});
