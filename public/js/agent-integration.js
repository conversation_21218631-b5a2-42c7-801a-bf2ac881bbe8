/**
 * Script pour l'intégration entre l'agent de formation et l'agent principal
 */

// Configuration
const CONFIG = {
    MAIN_AGENT_ID: 'agent_claude',
    TRAINING_AGENT_ID: 'agent_training',
    MEMORY_SYNC_INTERVAL: 60000, // 1 minute
    TRAINING_CHECK_INTERVAL: 300000, // 5 minutes
    AUTO_TRAINING_THRESHOLD: 0.7, // Seuil de confiance pour la formation automatique
    MAX_MEMORY_ENTRIES_PER_SYNC: 50
};

// Variables globales
let isInitialized = false;
let memoryStats = {
    totalEntries: 0,
    hotZoneEntries: 0,
    warmZoneEntries: 0,
    coolZoneEntries: 0,
    coldZoneEntries: 0,
    lastSyncTime: null
};

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Initialiser l'intégration des agents
    initializeAgentIntegration();
});

/**
 * Initialise l'intégration entre l'agent de formation et l'agent principal
 */
async function initializeAgentIntegration() {
    try {
        // Vérifier si les agents sont disponibles
        const agentsAvailable = await checkAgentsAvailability();
        
        if (!agentsAvailable) {
            console.error('Les agents nécessaires ne sont pas disponibles');
            return;
        }

        // Initialiser la synchronisation de la mémoire
        initializeMemorySync();

        // Initialiser la vérification périodique de la formation
        initializeTrainingCheck();

        isInitialized = true;
        console.log('Intégration des agents initialisée avec succès');
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'intégration des agents:', error);
    }
}

/**
 * Vérifie si les agents nécessaires sont disponibles
 */
async function checkAgentsAvailability() {
    try {
        const response = await fetch('/api/agents');
        const data = await response.json();

        if (!data.success) {
            console.error('Erreur lors de la récupération des agents:', data.error);
            return false;
        }

        const agents = data.agents;
        const mainAgentAvailable = agents.some(agent => agent.id === CONFIG.MAIN_AGENT_ID);
        const trainingAgentAvailable = agents.some(agent => agent.id === CONFIG.TRAINING_AGENT_ID);

        if (!mainAgentAvailable) {
            console.error('L\'agent principal n\'est pas disponible');
        }

        if (!trainingAgentAvailable) {
            console.error('L\'agent de formation n\'est pas disponible');
        }

        return mainAgentAvailable && trainingAgentAvailable;
    } catch (error) {
        console.error('Erreur lors de la vérification de la disponibilité des agents:', error);
        return false;
    }
}

/**
 * Initialise la synchronisation périodique de la mémoire entre les agents
 */
function initializeMemorySync() {
    // Effectuer une synchronisation initiale
    syncAgentMemory();

    // Configurer la synchronisation périodique
    setInterval(syncAgentMemory, CONFIG.MEMORY_SYNC_INTERVAL);
}

/**
 * Synchronise la mémoire entre l'agent principal et l'agent de formation
 */
async function syncAgentMemory() {
    try {
        // Récupérer les statistiques de la mémoire
        await updateMemoryStats();

        // Récupérer les entrées de mémoire de l'agent principal
        const mainAgentMemory = await fetchAgentMemory(CONFIG.MAIN_AGENT_ID);

        // Récupérer les entrées de mémoire de l'agent de formation
        const trainingAgentMemory = await fetchAgentMemory(CONFIG.TRAINING_AGENT_ID);

        // Identifier les entrées à synchroniser
        const entriesToSync = identifyEntriesToSync(mainAgentMemory, trainingAgentMemory);

        // Synchroniser les entrées
        if (entriesToSync.length > 0) {
            await syncMemoryEntries(entriesToSync);
            console.log(`${entriesToSync.length} entrées de mémoire synchronisées`);
        } else {
            console.log('Aucune entrée de mémoire à synchroniser');
        }

        // Mettre à jour les statistiques de la mémoire
        memoryStats.lastSyncTime = new Date();
    } catch (error) {
        console.error('Erreur lors de la synchronisation de la mémoire:', error);
    }
}

/**
 * Récupère les entrées de mémoire d'un agent
 */
async function fetchAgentMemory(agentId) {
    try {
        const response = await fetch(`/api/memory/entries?agentId=${agentId}`);
        const data = await response.json();

        if (!data.success) {
            console.error(`Erreur lors de la récupération de la mémoire de l'agent ${agentId}:`, data.error);
            return [];
        }

        return data.entries || [];
    } catch (error) {
        console.error(`Erreur lors de la récupération de la mémoire de l'agent ${agentId}:`, error);
        return [];
    }
}

/**
 * Identifie les entrées de mémoire à synchroniser
 */
function identifyEntriesToSync(mainAgentMemory, trainingAgentMemory) {
    // Créer un ensemble des IDs des entrées de l'agent de formation
    const trainingAgentEntryIds = new Set(trainingAgentMemory.map(entry => entry.id));

    // Filtrer les entrées de l'agent principal qui ne sont pas dans l'agent de formation
    const entriesToSync = mainAgentMemory
        .filter(entry => !trainingAgentEntryIds.has(entry.id))
        .filter(entry => entry.zone === 'hot' || entry.zone === 'warm') // Synchroniser uniquement les zones chaudes et tièdes
        .sort((a, b) => b.importance - a.importance) // Trier par importance décroissante
        .slice(0, CONFIG.MAX_MEMORY_ENTRIES_PER_SYNC); // Limiter le nombre d'entrées à synchroniser

    return entriesToSync;
}

/**
 * Synchronise les entrées de mémoire
 */
async function syncMemoryEntries(entries) {
    try {
        const response = await fetch('/api/memory/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sourceAgentId: CONFIG.MAIN_AGENT_ID,
                targetAgentId: CONFIG.TRAINING_AGENT_ID,
                entries: entries.map(entry => entry.id)
            })
        });

        const data = await response.json();

        if (!data.success) {
            console.error('Erreur lors de la synchronisation des entrées de mémoire:', data.error);
        }

        return data.success;
    } catch (error) {
        console.error('Erreur lors de la synchronisation des entrées de mémoire:', error);
        return false;
    }
}

/**
 * Met à jour les statistiques de la mémoire
 */
async function updateMemoryStats() {
    try {
        const response = await fetch('/api/memory/stats');
        const data = await response.json();

        if (!data.success) {
            console.error('Erreur lors de la récupération des statistiques de la mémoire:', data.error);
            return;
        }

        memoryStats = {
            ...memoryStats,
            totalEntries: data.stats.totalEntries,
            hotZoneEntries: data.stats.zoneStats.hot,
            warmZoneEntries: data.stats.zoneStats.warm,
            coolZoneEntries: data.stats.zoneStats.cool,
            coldZoneEntries: data.stats.zoneStats.cold
        };
    } catch (error) {
        console.error('Erreur lors de la mise à jour des statistiques de la mémoire:', error);
    }
}

/**
 * Initialise la vérification périodique de la formation
 */
function initializeTrainingCheck() {
    // Effectuer une vérification initiale
    checkTrainingNeeded();

    // Configurer la vérification périodique
    setInterval(checkTrainingNeeded, CONFIG.TRAINING_CHECK_INTERVAL);
}

/**
 * Vérifie si une formation est nécessaire
 */
async function checkTrainingNeeded() {
    try {
        // Vérifier si une formation est déjà en cours
        const trainingState = await getTrainingState();
        
        if (trainingState.isTraining) {
            console.log('Une formation est déjà en cours');
            return;
        }

        // Vérifier les performances de l'agent principal
        const agentPerformance = await getAgentPerformance(CONFIG.MAIN_AGENT_ID);
        
        // Si les performances sont inférieures au seuil, suggérer une formation
        if (agentPerformance.confidence < CONFIG.AUTO_TRAINING_THRESHOLD) {
            console.log('Les performances de l\'agent principal sont inférieures au seuil, une formation est recommandée');
            
            // Notifier l'utilisateur
            if (window.showNotification) {
                window.showNotification(
                    'Formation recommandée',
                    'Les performances de l\'agent principal sont inférieures au seuil. Une formation est recommandée.',
                    'info'
                );
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification de la nécessité d\'une formation:', error);
    }
}

/**
 * Récupère l'état de la formation
 */
async function getTrainingState() {
    try {
        const response = await fetch('/api/training/state');
        const data = await response.json();

        if (!data.success) {
            console.error('Erreur lors de la récupération de l\'état de la formation:', data.error);
            return { isTraining: false };
        }

        return data.state || { isTraining: false };
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'état de la formation:', error);
        return { isTraining: false };
    }
}

/**
 * Récupère les performances d'un agent
 */
async function getAgentPerformance(agentId) {
    try {
        const response = await fetch(`/api/agents/performance?agentId=${agentId}`);
        const data = await response.json();

        if (!data.success) {
            console.error(`Erreur lors de la récupération des performances de l'agent ${agentId}:`, data.error);
            return { confidence: 1.0 }; // Valeur par défaut élevée pour éviter les faux positifs
        }

        return data.performance || { confidence: 1.0 };
    } catch (error) {
        console.error(`Erreur lors de la récupération des performances de l'agent ${agentId}:`, error);
        return { confidence: 1.0 };
    }
}

// Exporter les fonctions pour les tests
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        memoryStats,
        checkAgentsAvailability,
        syncAgentMemory,
        fetchAgentMemory,
        identifyEntriesToSync,
        syncMemoryEntries,
        updateMemoryStats,
        checkTrainingNeeded,
        getTrainingState,
        getAgentPerformance
    };
}
