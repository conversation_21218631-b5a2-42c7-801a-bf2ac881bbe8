// INTÉGRATION MULTIMÉDIA DANS LE CHAT - LOUNA

class MultimediaIntegration {
    constructor() {
        this.generationQueue = [];
        this.activeGenerations = new Map();
        this.supportedFormats = {
            image: ['jpg', 'png', 'webp', 'gif'],
            video: ['mp4', 'webm', 'avi'],
            audio: ['mp3', 'wav', 'ogg'],
            code: ['js', 'html', 'css', 'py', 'java', 'cpp']
        };
        this.init();
    }

    init() {
        console.log('🎨 Intégration multimédia initialisée');
        this.setupMessageHandlers();
        this.setupGenerationHandlers();
    }

    setupMessageHandlers() {
        // Intercepter les messages pour détecter les demandes de génération
        const originalSendMessage = window.sendMessage;
        if (originalSendMessage) {
            window.sendMessage = (message) => {
                if (this.isGenerationRequest(message)) {
                    this.handleGenerationRequest(message);
                } else if (this.isCodeRequest(message)) {
                    this.handleCodeRequest(message);
                } else {
                    originalSendMessage(message);
                }
            };
        }
    }

    isGenerationRequest(message) {
        const generationKeywords = [
            'génère', 'crée', 'fais', 'produis', 'génération',
            'image', 'vidéo', 'audio', 'musique', 'photo',
            'dessine', 'compose', 'enregistre'
        ];
        
        return generationKeywords.some(keyword => 
            message.toLowerCase().includes(keyword)
        );
    }

    isCodeRequest(message) {
        const codeKeywords = [
            'code', 'programme', 'script', 'fonction',
            'javascript', 'html', 'css', 'python',
            'éditeur', 'développe', 'écris du code'
        ];
        
        return codeKeywords.some(keyword => 
            message.toLowerCase().includes(keyword)
        );
    }

    async handleGenerationRequest(message) {
        console.log('🎨 Demande de génération détectée:', message);
        
        // Ajouter le message utilisateur
        this.addMessage('user', message);
        
        // Analyser le type de génération demandé
        const generationType = this.detectGenerationType(message);
        const prompt = this.extractPrompt(message);
        
        // Afficher un message de traitement
        const processingMessageId = this.addMessage('assistant', 
            `🎨 Génération ${generationType} en cours...\n\n**Prompt:** ${prompt}\n\n⏳ Veuillez patienter...`
        );
        
        try {
            // Lancer la génération
            const result = await this.generateContent(generationType, prompt);
            
            // Mettre à jour le message avec le résultat
            this.updateMessage(processingMessageId, this.formatGenerationResult(result));
            
        } catch (error) {
            console.error('Erreur génération:', error);
            this.updateMessage(processingMessageId, 
                `❌ Erreur lors de la génération: ${error.message}`
            );
        }
    }

    async handleCodeRequest(message) {
        console.log('💻 Demande de code détectée:', message);
        
        // Ajouter le message utilisateur
        this.addMessage('user', message);
        
        // Afficher un message de traitement
        const processingMessageId = this.addMessage('assistant', 
            `💻 Génération de code en cours...\n\n⏳ Analyse de votre demande...`
        );
        
        try {
            // Générer le code
            const codeResult = await this.generateCode(message);
            
            // Mettre à jour le message avec le code généré
            this.updateMessage(processingMessageId, this.formatCodeResult(codeResult));
            
        } catch (error) {
            console.error('Erreur génération code:', error);
            this.updateMessage(processingMessageId, 
                `❌ Erreur lors de la génération de code: ${error.message}`
            );
        }
    }

    detectGenerationType(message) {
        const msg = message.toLowerCase();
        
        if (msg.includes('image') || msg.includes('photo') || msg.includes('dessine')) {
            return 'image';
        } else if (msg.includes('vidéo') || msg.includes('video') || msg.includes('film')) {
            return 'video';
        } else if (msg.includes('audio') || msg.includes('musique') || msg.includes('son')) {
            return 'audio';
        } else if (msg.includes('3d') || msg.includes('modèle')) {
            return '3d';
        }
        
        return 'image'; // Par défaut
    }

    extractPrompt(message) {
        // Extraire le prompt de génération du message
        const patterns = [
            /(?:génère|crée|fais|produis)\s+(?:une?\s+)?(?:image|vidéo|audio|musique|photo)\s+(?:de\s+|avec\s+|représentant\s+)?(.+)/i,
            /(?:dessine|compose|enregistre)\s+(.+)/i,
            /(.+)/i // Fallback
        ];
        
        for (const pattern of patterns) {
            const match = message.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        
        return message;
    }

    async generateContent(type, prompt) {
        const response = await fetch('/api/generation/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: type,
                prompt: prompt,
                options: {
                    quality: 'high',
                    format: this.getDefaultFormat(type)
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        return await response.json();
    }

    async generateCode(request) {
        const response = await fetch('/api/code/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                request: request,
                language: this.detectCodeLanguage(request)
            })
        });
        
        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        return await response.json();
    }

    detectCodeLanguage(request) {
        const msg = request.toLowerCase();
        
        if (msg.includes('javascript') || msg.includes('js')) return 'javascript';
        if (msg.includes('python') || msg.includes('py')) return 'python';
        if (msg.includes('html')) return 'html';
        if (msg.includes('css')) return 'css';
        if (msg.includes('java')) return 'java';
        if (msg.includes('c++') || msg.includes('cpp')) return 'cpp';
        
        return 'javascript'; // Par défaut
    }

    getDefaultFormat(type) {
        const formats = {
            image: 'png',
            video: 'mp4',
            audio: 'mp3',
            '3d': 'obj'
        };
        return formats[type] || 'png';
    }

    formatGenerationResult(result) {
        if (!result.success) {
            return `❌ Échec de la génération: ${result.error}`;
        }
        
        let message = `✅ **Génération terminée avec succès !**\n\n`;
        
        if (result.url) {
            message += `🔗 **Fichier généré:** [${result.filename}](${result.url})\n\n`;
            
            // Ajouter un aperçu selon le type
            if (result.type === 'image') {
                message += `![Aperçu](${result.url})\n\n`;
            } else if (result.type === 'video') {
                message += `<video controls width="400">\n  <source src="${result.url}" type="video/mp4">\n</video>\n\n`;
            } else if (result.type === 'audio') {
                message += `<audio controls>\n  <source src="${result.url}" type="audio/mp3">\n</audio>\n\n`;
            }
        }
        
        message += `📊 **Détails:**\n`;
        message += `- Type: ${result.type}\n`;
        message += `- Durée: ${result.duration || 'N/A'}\n`;
        message += `- Taille: ${result.size || 'N/A'}\n`;
        
        // Boutons d'action
        message += `\n🎯 **Actions:**\n`;
        message += `<button onclick="downloadFile('${result.url}', '${result.filename}')">📥 Télécharger</button> `;
        message += `<button onclick="openInStudio('${result.url}')">🎨 Ouvrir dans le Studio</button> `;
        message += `<button onclick="shareFile('${result.url}')">📤 Partager</button>`;
        
        return message;
    }

    formatCodeResult(result) {
        if (!result.success) {
            return `❌ Échec de la génération de code: ${result.error}`;
        }
        
        let message = `💻 **Code généré avec succès !**\n\n`;
        message += `**Langage:** ${result.language}\n\n`;
        message += `\`\`\`${result.language}\n${result.code}\n\`\`\`\n\n`;
        
        if (result.explanation) {
            message += `📝 **Explication:**\n${result.explanation}\n\n`;
        }
        
        // Boutons d'action
        message += `🎯 **Actions:**\n`;
        message += `<button onclick="copyToClipboard(\`${result.code.replace(/`/g, '\\`')}\`)">📋 Copier</button> `;
        message += `<button onclick="openInEditor(\`${result.code.replace(/`/g, '\\`')}\`, '${result.language}')">📝 Ouvrir dans l'éditeur</button> `;
        message += `<button onclick="runCode(\`${result.code.replace(/`/g, '\\`')}\`, '${result.language}')">▶️ Exécuter</button>`;
        
        return message;
    }

    addMessage(sender, content) {
        const messageId = 'msg_' + Date.now();
        const messagesContainer = document.getElementById('chatMessages');
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.id = messageId;
        
        messageDiv.innerHTML = `
            <div class="message-avatar ${sender}">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${this.parseMarkdown(content)}</div>
                <div class="message-time">${new Date().toLocaleTimeString('fr-FR')}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        return messageId;
    }

    updateMessage(messageId, newContent) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-content');
            if (contentElement) {
                contentElement.innerHTML = this.parseMarkdown(newContent);
            }
        }
    }

    parseMarkdown(text) {
        // Simple parser Markdown pour le chat
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    setupGenerationHandlers() {
        // Fonctions globales pour les boutons d'action
        window.downloadFile = (url, filename) => {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
        };
        
        window.openInStudio = (url) => {
            window.open(`/generation-studio.html?file=${encodeURIComponent(url)}`, '_blank');
        };
        
        window.shareFile = (url) => {
            navigator.share({
                title: 'Fichier généré par Louna',
                url: url
            }).catch(console.error);
        };
        
        window.copyToClipboard = (text) => {
            navigator.clipboard.writeText(text).then(() => {
                console.log('Code copié dans le presse-papiers');
            });
        };
        
        window.openInEditor = (code, language) => {
            const editorUrl = `/code-editor.html?code=${encodeURIComponent(code)}&lang=${language}`;
            window.open(editorUrl, '_blank');
        };
        
        window.runCode = (code, language) => {
            if (language === 'javascript') {
                try {
                    eval(code);
                } catch (error) {
                    console.error('Erreur d\'exécution:', error);
                }
            } else {
                console.log('Exécution non supportée pour ce langage');
            }
        };
    }
}

// Initialiser l'intégration multimédia
document.addEventListener('DOMContentLoaded', () => {
    window.multimediaIntegration = new MultimediaIntegration();
    console.log('🎨 Intégration multimédia prête !');
});
