// NAVIGATION AMÉLIORÉE - ACCESSIBILITÉ ET INTERACTIONS

document.addEventListener('DOMContentLoaded', function() {
    
    // Gestion du menu déroulant
    const dropdown = document.querySelector('.nav-dropdown');
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    // Variables pour gérer l'état du menu
    let isDropdownOpen = false;
    let isMobileMenuOpen = false;
    
    // Gestion du clic sur le menu déroulant
    if (dropdownToggle) {
        dropdownToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            isDropdownOpen = !isDropdownOpen;
            
            if (isDropdownOpen) {
                dropdownMenu.style.opacity = '1';
                dropdownMenu.style.visibility = 'visible';
                dropdownMenu.style.transform = 'translateX(-50%) translateY(0)';
                dropdownToggle.querySelector('.dropdown-arrow').style.transform = 'rotate(180deg)';
            } else {
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.visibility = 'hidden';
                dropdownMenu.style.transform = 'translateX(-50%) translateY(-10px)';
                dropdownToggle.querySelector('.dropdown-arrow').style.transform = 'rotate(0deg)';
            }
        });
    }
    
    // Fermer le menu déroulant en cliquant ailleurs
    document.addEventListener('click', function(e) {
        if (dropdown && !dropdown.contains(e.target)) {
            isDropdownOpen = false;
            if (dropdownMenu) {
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.visibility = 'hidden';
                dropdownMenu.style.transform = 'translateX(-50%) translateY(-10px)';
            }
            if (dropdownToggle) {
                const arrow = dropdownToggle.querySelector('.dropdown-arrow');
                if (arrow) {
                    arrow.style.transform = 'rotate(0deg)';
                }
            }
        }
    });
    
    // Gestion du menu mobile
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            isMobileMenuOpen = !isMobileMenuOpen;
            
            if (isMobileMenuOpen) {
                navLinks.style.display = 'flex';
                navLinks.style.position = 'absolute';
                navLinks.style.top = '100%';
                navLinks.style.left = '0';
                navLinks.style.right = '0';
                navLinks.style.background = 'rgba(0, 0, 0, 0.98)';
                navLinks.style.flexDirection = 'column';
                navLinks.style.padding = '20px';
                navLinks.style.borderTop = '1px solid rgba(255, 20, 147, 0.3)';
                navLinks.style.zIndex = '999';
                mobileMenuToggle.innerHTML = '<i class="fas fa-times"></i>';
            } else {
                navLinks.style.display = 'none';
                mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            }
        });
    }
    
    // Gestion du redimensionnement de la fenêtre
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Réinitialiser le menu mobile sur grand écran
            if (navLinks) {
                navLinks.style.display = 'flex';
                navLinks.style.position = 'static';
                navLinks.style.flexDirection = 'row';
                navLinks.style.background = 'transparent';
                navLinks.style.padding = '0';
                navLinks.style.borderTop = 'none';
            }
            if (mobileMenuToggle) {
                mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                isMobileMenuOpen = false;
            }
        } else {
            // Cacher le menu sur petit écran
            if (navLinks && !isMobileMenuOpen) {
                navLinks.style.display = 'none';
            }
        }
    });
    
    // Animation des éléments du menu déroulant
    if (dropdownMenu) {
        const dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
        
        dropdown.addEventListener('mouseenter', function() {
            dropdownItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.animation = 'fadeInUp 0.3s ease forwards';
                }, index * 50);
            });
        });
        
        dropdown.addEventListener('mouseleave', function() {
            dropdownItems.forEach(item => {
                item.style.animation = 'none';
            });
        });
    }
    
    // Amélioration de l'accessibilité avec le clavier
    document.addEventListener('keydown', function(e) {
        // Échapper pour fermer les menus
        if (e.key === 'Escape') {
            if (isDropdownOpen) {
                isDropdownOpen = false;
                if (dropdownMenu) {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateX(-50%) translateY(-10px)';
                }
                if (dropdownToggle) {
                    const arrow = dropdownToggle.querySelector('.dropdown-arrow');
                    if (arrow) {
                        arrow.style.transform = 'rotate(0deg)';
                    }
                    dropdownToggle.focus();
                }
            }
            
            if (isMobileMenuOpen) {
                isMobileMenuOpen = false;
                if (navLinks) {
                    navLinks.style.display = 'none';
                }
                if (mobileMenuToggle) {
                    mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                    mobileMenuToggle.focus();
                }
            }
        }
        
        // Entrée pour activer les boutons
        if (e.key === 'Enter') {
            if (document.activeElement === dropdownToggle) {
                dropdownToggle.click();
            }
            if (document.activeElement === mobileMenuToggle) {
                mobileMenuToggle.click();
            }
        }
    });
    
    // Gestion du focus pour l'accessibilité
    const navItems = document.querySelectorAll('.nav-item, .dropdown-item');
    navItems.forEach(item => {
        item.addEventListener('focus', function() {
            this.style.outline = '2px solid #ff1493';
            this.style.outlineOffset = '2px';
        });
        
        item.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });
    
    // Indicateur de page active
    const currentPath = window.location.pathname;
    const navLinks_items = document.querySelectorAll('.nav-item, .dropdown-item');
    
    navLinks_items.forEach(link => {
        const href = link.getAttribute('href');
        if (href && (href === currentPath || (currentPath === '/' && href === '/'))) {
            link.classList.add('active');
        }
    });
    
    // Smooth scroll pour les ancres
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Notification de bienvenue améliorée
    setTimeout(() => {
        if (typeof notifications !== 'undefined') {
            notifications.success(
                "🎉 Navigation améliorée ! Utilisez le menu 'Plus' pour accéder à toutes les fonctionnalités.",
                "Interface Optimisée",
                { duration: 6000 }
            );
        }
    }, 2000);
    
    // Gestion des tooltips pour les icônes
    const iconOnlyItems = document.querySelectorAll('.nav-item');
    iconOnlyItems.forEach(item => {
        const span = item.querySelector('span');
        if (span && window.innerWidth <= 1400) {
            const text = span.textContent;
            item.setAttribute('title', text);
            item.setAttribute('aria-label', text);
        }
    });
    
    // Performance: Lazy loading pour les animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observer les cartes de fonctionnalités
    document.querySelectorAll('.feature-card').forEach(card => {
        observer.observe(card);
    });
    
    console.log('🚀 Navigation améliorée initialisée avec succès !');
});

// Fonction utilitaire pour déboguer la navigation
window.debugNavigation = function() {
    console.log('=== DEBUG NAVIGATION ===');
    console.log('Dropdown:', document.querySelector('.nav-dropdown'));
    console.log('Mobile toggle:', document.querySelector('.mobile-menu-toggle'));
    console.log('Nav links:', document.querySelector('.nav-links'));
    console.log('Window width:', window.innerWidth);
    console.log('========================');
};
