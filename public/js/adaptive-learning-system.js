/**
 * Système d'Apprentissage Adaptatif - Louna
 * Apprentissage en temps réel et amélioration continue
 */

class AdaptiveLearningSystem {
    constructor() {
        this.learningData = {
            userPreferences: new Map(),
            interactionPatterns: [],
            successfulResponses: new Map(),
            failedResponses: new Map(),
            contextualLearning: new Map(),
            emotionalResponses: new Map()
        };
        
        this.learningMetrics = {
            totalInteractions: 0,
            successfulInteractions: 0,
            learningRate: 0.1,
            adaptationSpeed: 0.05,
            confidenceThreshold: 0.7
        };

        this.personalityTraits = {
            helpfulness: 0.9,
            creativity: 0.8,
            formality: 0.6,
            enthusiasm: 0.7,
            patience: 0.8
        };

        this.contextualMemory = new Map();
        this.learningHistory = [];
        this.adaptationRules = new Map();
        
        this.init();
    }

    async init() {
        console.log('🧠 Initialisation du système d\'apprentissage adaptatif...');
        
        await this.loadLearningData();
        this.setupLearningMonitoring();
        this.createLearningInterface();
        this.startAdaptiveLearning();
        
        console.log('✅ Système d\'apprentissage adaptatif initialisé');
    }

    async loadLearningData() {
        try {
            const response = await fetch('/api/learning/data');
            if (response.ok) {
                const data = await response.json();
                this.learningData = { ...this.learningData, ...data };
                console.log('📚 Données d\'apprentissage chargées');
            }
        } catch (error) {
            console.log('⚠️ Impossible de charger les données d\'apprentissage, démarrage à zéro');
        }
    }

    setupLearningMonitoring() {
        // Intercepter toutes les interactions pour apprendre
        this.monitorChatInteractions();
        this.monitorVoiceInteractions();
        this.monitorVisionInteractions();
        this.monitorSystemUsage();
    }

    monitorChatInteractions() {
        // Intercepter les messages du chat
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const response = await originalFetch(...args);
            
            if (args[0].includes('/api/chat/message')) {
                this.learnFromChatInteraction(args[1]);
            }
            
            return response;
        };
    }

    monitorVoiceInteractions() {
        // Écouter les interactions vocales
        if (window.voiceSystem) {
            const originalSpeak = window.voiceSystem.speak;
            window.voiceSystem.speak = (text, emotion) => {
                this.learnFromVoiceInteraction(text, emotion);
                return originalSpeak.call(window.voiceSystem, text, emotion);
            };
        }
    }

    monitorVisionInteractions() {
        // Écouter les analyses de vision
        if (window.visionSystem) {
            const originalUpdateResults = window.visionSystem.updateAnalysisResults;
            window.visionSystem.updateAnalysisResults = (analyses) => {
                this.learnFromVisionAnalysis(analyses);
                return originalUpdateResults.call(window.visionSystem, analyses);
            };
        }
    }

    monitorSystemUsage() {
        // Surveiller l'utilisation générale du système
        document.addEventListener('click', (e) => {
            this.learnFromUserAction('click', e.target);
        });

        document.addEventListener('keypress', (e) => {
            this.learnFromUserAction('keypress', e);
        });

        // Surveiller les changements de section
        if (window.hub) {
            const originalSwitchSection = window.hub.switchSection;
            window.hub.switchSection = (section) => {
                this.learnFromSectionChange(section);
                return originalSwitchSection.call(window.hub, section);
            };
        }
    }

    learnFromChatInteraction(requestData) {
        if (!requestData || !requestData.body) return;

        try {
            const data = JSON.parse(requestData.body);
            const message = data.message;
            const userId = data.userId || 'anonymous';

            // Analyser le type de demande
            const requestType = this.classifyRequest(message);
            
            // Apprendre les préférences utilisateur
            this.updateUserPreferences(userId, requestType, message);
            
            // Analyser les patterns d'interaction
            this.analyzeInteractionPattern(userId, requestType, new Date());
            
            // Mettre à jour les métriques
            this.learningMetrics.totalInteractions++;
            
            console.log(`📖 Apprentissage: ${requestType} pour ${userId}`);
            
        } catch (error) {
            console.error('Erreur apprentissage chat:', error);
        }
    }

    learnFromVoiceInteraction(text, emotion) {
        // Apprendre des interactions vocales
        const emotionData = this.learningData.emotionalResponses.get(emotion) || {
            count: 0,
            contexts: [],
            effectiveness: 0.5
        };

        emotionData.count++;
        emotionData.contexts.push({
            text: text.substring(0, 100), // Limiter la taille
            timestamp: new Date().toISOString()
        });

        this.learningData.emotionalResponses.set(emotion, emotionData);
        
        // Adapter la personnalité en fonction de l'émotion utilisée
        this.adaptPersonalityFromEmotion(emotion);
    }

    learnFromVisionAnalysis(analyses) {
        // Apprendre des analyses de vision
        analyses.forEach(analysis => {
            const contextKey = analysis.type;
            const contextData = this.contextualMemory.get(contextKey) || {
                frequency: 0,
                lastSeen: null,
                patterns: []
            };

            contextData.frequency++;
            contextData.lastSeen = new Date().toISOString();
            contextData.patterns.push({
                result: analysis.result,
                timestamp: new Date().toISOString()
            });

            // Garder seulement les 20 derniers patterns
            if (contextData.patterns.length > 20) {
                contextData.patterns = contextData.patterns.slice(-20);
            }

            this.contextualMemory.set(contextKey, contextData);
        });
    }

    learnFromUserAction(actionType, target) {
        // Apprendre des actions utilisateur
        const actionData = {
            type: actionType,
            element: target.tagName || 'unknown',
            className: target.className || '',
            timestamp: new Date().toISOString()
        };

        this.learningData.interactionPatterns.push(actionData);
        
        // Garder seulement les 1000 dernières interactions
        if (this.learningData.interactionPatterns.length > 1000) {
            this.learningData.interactionPatterns = this.learningData.interactionPatterns.slice(-1000);
        }
    }

    learnFromSectionChange(section) {
        // Apprendre des changements de section
        const sectionData = this.learningData.userPreferences.get('sections') || new Map();
        const currentCount = sectionData.get(section) || 0;
        sectionData.set(section, currentCount + 1);
        this.learningData.userPreferences.set('sections', sectionData);

        // Adapter l'interface en fonction des préférences
        this.adaptInterfaceFromUsage(section);
    }

    classifyRequest(message) {
        const msg = message.toLowerCase();
        
        if (msg.includes('génère') || msg.includes('crée') || msg.includes('fais')) {
            if (msg.includes('image') || msg.includes('photo')) return 'generation_image';
            if (msg.includes('code') || msg.includes('programme')) return 'generation_code';
            if (msg.includes('vidéo') || msg.includes('video')) return 'generation_video';
            if (msg.includes('audio') || msg.includes('musique')) return 'generation_audio';
            return 'generation_general';
        }
        
        if (msg.includes('analyse') || msg.includes('étudie')) return 'analysis';
        if (msg.includes('optimise') || msg.includes('améliore')) return 'optimization';
        if (msg.includes('aide') || msg.includes('comment')) return 'help';
        if (msg.includes('explique') || msg.includes('qu\'est-ce')) return 'explanation';
        
        return 'conversation';
    }

    updateUserPreferences(userId, requestType, message) {
        const userPrefs = this.learningData.userPreferences.get(userId) || {
            requestTypes: new Map(),
            communicationStyle: 'neutral',
            preferredTopics: [],
            responseLength: 'medium',
            technicalLevel: 'intermediate'
        };

        // Mettre à jour les types de requêtes
        const currentCount = userPrefs.requestTypes.get(requestType) || 0;
        userPrefs.requestTypes.set(requestType, currentCount + 1);

        // Analyser le style de communication
        userPrefs.communicationStyle = this.analyzeCommunicationStyle(message);
        
        // Détecter le niveau technique
        userPrefs.technicalLevel = this.detectTechnicalLevel(message);

        this.learningData.userPreferences.set(userId, userPrefs);
    }

    analyzeCommunicationStyle(message) {
        const msg = message.toLowerCase();
        
        if (msg.includes('s\'il te plaît') || msg.includes('merci') || msg.includes('pourrais-tu')) {
            return 'polite';
        }
        if (msg.includes('!') || msg.includes('super') || msg.includes('génial')) {
            return 'enthusiastic';
        }
        if (msg.length < 20) {
            return 'concise';
        }
        if (msg.length > 100) {
            return 'detailed';
        }
        
        return 'neutral';
    }

    detectTechnicalLevel(message) {
        const technicalTerms = ['api', 'fonction', 'variable', 'algorithme', 'base de données', 'serveur', 'client'];
        const advancedTerms = ['machine learning', 'neural network', 'blockchain', 'kubernetes', 'microservices'];
        
        const msg = message.toLowerCase();
        const technicalCount = technicalTerms.filter(term => msg.includes(term)).length;
        const advancedCount = advancedTerms.filter(term => msg.includes(term)).length;
        
        if (advancedCount > 0) return 'expert';
        if (technicalCount > 2) return 'advanced';
        if (technicalCount > 0) return 'intermediate';
        return 'beginner';
    }

    adaptPersonalityFromEmotion(emotion) {
        // Adapter la personnalité en fonction des émotions utilisées
        switch (emotion) {
            case 'excited':
                this.personalityTraits.enthusiasm = Math.min(1.0, this.personalityTraits.enthusiasm + 0.01);
                break;
            case 'calm':
                this.personalityTraits.patience = Math.min(1.0, this.personalityTraits.patience + 0.01);
                break;
            case 'confident':
                this.personalityTraits.helpfulness = Math.min(1.0, this.personalityTraits.helpfulness + 0.01);
                break;
        }
    }

    adaptInterfaceFromUsage(section) {
        // Adapter l'interface en fonction de l'utilisation
        const sectionUsage = this.learningData.userPreferences.get('sections') || new Map();
        const totalUsage = Array.from(sectionUsage.values()).reduce((sum, count) => sum + count, 0);
        
        if (totalUsage > 10) {
            // Réorganiser l'interface en fonction des préférences
            this.suggestInterfaceOptimization(sectionUsage);
        }
    }

    suggestInterfaceOptimization(sectionUsage) {
        const sortedSections = Array.from(sectionUsage.entries())
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        console.log('🎯 Sections les plus utilisées:', sortedSections);
        
        // Ici on pourrait réorganiser l'interface automatiquement
        // ou suggérer des améliorations à l'utilisateur
    }

    startAdaptiveLearning() {
        // Démarrer l'apprentissage continu
        setInterval(() => {
            this.performAdaptiveLearning();
        }, 30000); // Toutes les 30 secondes

        setInterval(() => {
            this.saveLearningData();
        }, 60000); // Sauvegarder toutes les minutes
    }

    performAdaptiveLearning() {
        // Analyser les patterns récents
        this.analyzeRecentPatterns();
        
        // Adapter les réponses
        this.adaptResponseStrategies();
        
        // Optimiser les performances
        this.optimizeSystemBehavior();
        
        // Mettre à jour les métriques
        this.updateLearningMetrics();
    }

    analyzeRecentPatterns() {
        const recentInteractions = this.learningData.interactionPatterns.slice(-50);
        
        // Analyser les patterns temporels
        const timePatterns = this.analyzeTimePatterns(recentInteractions);
        
        // Analyser les patterns d'utilisation
        const usagePatterns = this.analyzeUsagePatterns(recentInteractions);
        
        // Stocker les insights
        this.learningHistory.push({
            timestamp: new Date().toISOString(),
            timePatterns,
            usagePatterns,
            insights: this.generateInsights(timePatterns, usagePatterns)
        });
    }

    analyzeTimePatterns(interactions) {
        const hourCounts = new Array(24).fill(0);
        
        interactions.forEach(interaction => {
            const hour = new Date(interaction.timestamp).getHours();
            hourCounts[hour]++;
        });
        
        return {
            peakHours: hourCounts.map((count, hour) => ({ hour, count }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 3),
            totalInteractions: interactions.length
        };
    }

    analyzeUsagePatterns(interactions) {
        const elementCounts = {};
        
        interactions.forEach(interaction => {
            const key = `${interaction.type}_${interaction.element}`;
            elementCounts[key] = (elementCounts[key] || 0) + 1;
        });
        
        return Object.entries(elementCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);
    }

    generateInsights(timePatterns, usagePatterns) {
        const insights = [];
        
        // Insights temporels
        if (timePatterns.peakHours[0].count > 5) {
            insights.push(`Pic d'activité à ${timePatterns.peakHours[0].hour}h`);
        }
        
        // Insights d'utilisation
        if (usagePatterns.length > 0) {
            insights.push(`Action la plus fréquente: ${usagePatterns[0][0]}`);
        }
        
        return insights;
    }

    adaptResponseStrategies() {
        // Adapter les stratégies de réponse en fonction de l'apprentissage
        const userPrefs = Array.from(this.learningData.userPreferences.values());
        
        if (userPrefs.length > 0) {
            const avgTechnicalLevel = this.calculateAverageTechnicalLevel(userPrefs);
            const dominantStyle = this.findDominantCommunicationStyle(userPrefs);
            
            // Adapter le système vocal
            if (window.voiceSystem) {
                this.adaptVoiceSystem(dominantStyle);
            }
            
            // Adapter les réponses du chat
            this.adaptChatResponses(avgTechnicalLevel, dominantStyle);
        }
    }

    calculateAverageTechnicalLevel(userPrefs) {
        const levels = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 };
        const avgLevel = userPrefs.reduce((sum, pref) => sum + levels[pref.technicalLevel], 0) / userPrefs.length;
        
        if (avgLevel < 1.5) return 'beginner';
        if (avgLevel < 2.5) return 'intermediate';
        if (avgLevel < 3.5) return 'advanced';
        return 'expert';
    }

    findDominantCommunicationStyle(userPrefs) {
        const styleCounts = {};
        userPrefs.forEach(pref => {
            styleCounts[pref.communicationStyle] = (styleCounts[pref.communicationStyle] || 0) + 1;
        });
        
        return Object.entries(styleCounts)
            .sort(([,a], [,b]) => b - a)[0][0];
    }

    adaptVoiceSystem(style) {
        if (!window.voiceSystem) return;
        
        switch (style) {
            case 'enthusiastic':
                window.voiceSystem.currentEmotion = 'excited';
                break;
            case 'polite':
                window.voiceSystem.currentEmotion = 'calm';
                break;
            case 'concise':
                window.voiceSystem.voiceSettings.rate = 1.1;
                break;
            case 'detailed':
                window.voiceSystem.voiceSettings.rate = 0.8;
                break;
        }
    }

    adaptChatResponses(technicalLevel, style) {
        // Adapter les réponses du chat (à implémenter côté serveur)
        const adaptationData = {
            technicalLevel,
            communicationStyle: style,
            personalityTraits: this.personalityTraits
        };
        
        // Envoyer les données d'adaptation au serveur
        fetch('/api/learning/adapt', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(adaptationData)
        }).catch(error => console.log('Adaptation serveur non disponible'));
    }

    optimizeSystemBehavior() {
        // Optimiser le comportement du système
        const recentSuccess = this.calculateRecentSuccessRate();
        
        if (recentSuccess < 0.7) {
            // Ajuster les paramètres pour améliorer les performances
            this.learningMetrics.learningRate = Math.min(0.2, this.learningMetrics.learningRate + 0.01);
        } else if (recentSuccess > 0.9) {
            // Réduire le taux d'apprentissage si les performances sont excellentes
            this.learningMetrics.learningRate = Math.max(0.05, this.learningMetrics.learningRate - 0.005);
        }
    }

    calculateRecentSuccessRate() {
        // Calculer le taux de succès récent (simulation)
        return Math.random() * 0.4 + 0.6; // Entre 0.6 et 1.0
    }

    updateLearningMetrics() {
        // Mettre à jour les métriques d'apprentissage
        this.learningMetrics.successfulInteractions = Math.floor(
            this.learningMetrics.totalInteractions * this.calculateRecentSuccessRate()
        );
        
        // Calculer l'efficacité d'apprentissage
        const efficiency = this.learningMetrics.successfulInteractions / 
                          Math.max(1, this.learningMetrics.totalInteractions);
        
        console.log(`📊 Métriques d'apprentissage: ${Math.round(efficiency * 100)}% d'efficacité`);
    }

    async saveLearningData() {
        try {
            const dataToSave = {
                learningData: this.learningData,
                learningMetrics: this.learningMetrics,
                personalityTraits: this.personalityTraits,
                learningHistory: this.learningHistory.slice(-100) // Garder les 100 dernières entrées
            };

            await fetch('/api/learning/save', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(dataToSave)
            });
            
            console.log('💾 Données d\'apprentissage sauvegardées');
            
        } catch (error) {
            console.log('⚠️ Impossible de sauvegarder les données d\'apprentissage');
        }
    }

    createLearningInterface() {
        // Créer une interface de monitoring de l'apprentissage
        const learningInterface = document.createElement('div');
        learningInterface.id = 'learning-interface';
        learningInterface.innerHTML = `
            <div class="learning-indicator">
                <div class="learning-icon">🧠</div>
                <div class="learning-status">Apprentissage actif</div>
                <div class="learning-metrics" id="learning-metrics">
                    Interactions: ${this.learningMetrics.totalInteractions}
                </div>
            </div>
        `;

        // Ajouter les styles
        const styles = document.createElement('style');
        styles.textContent = `
            #learning-interface {
                position: fixed;
                top: 80px;
                left: 20px;
                background: rgba(78, 205, 196, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(78, 205, 196, 0.3);
                border-radius: 15px;
                padding: 10px 15px;
                color: white;
                font-family: 'Segoe UI', sans-serif;
                font-size: 12px;
                z-index: 9998;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            #learning-interface:hover {
                background: rgba(78, 205, 196, 0.2);
                transform: scale(1.05);
            }

            .learning-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .learning-icon {
                font-size: 16px;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 0.7; }
                50% { opacity: 1; }
            }

            .learning-status {
                font-weight: bold;
                color: #4ecdc4;
            }

            .learning-metrics {
                color: #888;
                font-size: 10px;
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(learningInterface);

        // Mettre à jour les métriques toutes les 5 secondes
        setInterval(() => {
            document.getElementById('learning-metrics').textContent = 
                `Interactions: ${this.learningMetrics.totalInteractions}`;
        }, 5000);
    }

    // API publique
    getLearningInsights() {
        return {
            totalInteractions: this.learningMetrics.totalInteractions,
            successRate: this.calculateRecentSuccessRate(),
            personalityTraits: this.personalityTraits,
            recentInsights: this.learningHistory.slice(-5)
        };
    }

    resetLearning() {
        this.learningData = {
            userPreferences: new Map(),
            interactionPatterns: [],
            successfulResponses: new Map(),
            failedResponses: new Map(),
            contextualLearning: new Map(),
            emotionalResponses: new Map()
        };
        
        this.learningMetrics.totalInteractions = 0;
        this.learningMetrics.successfulInteractions = 0;
        
        console.log('🔄 Système d\'apprentissage réinitialisé');
    }
}

// Initialiser le système d'apprentissage adaptatif
const learningSystem = new AdaptiveLearningSystem();
window.learningSystem = learningSystem;
