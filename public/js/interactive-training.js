/**
 * Système de Formation Interactive pour Louna
 * Permet le dialogue automatique entre agents et les questions manuelles
 */

class InteractiveTraining {
    constructor() {
        this.isAutoDialogueActive = false;
        this.dialogueInterval = null;
        this.sessionStartTime = null;
        this.stats = {
            questionsAnswered: 0,
            learningScore: 0,
            sessionDuration: 0,
            knowledgeGained: 0
        };

        // Questions prédéfinies pour le dialogue automatique
        this.trainingQuestions = [
            // Questions sur l'IA et la cognition
            "Qu'est-ce que l'intelligence artificielle ?",
            "Comment fonctionne la mémoire thermique ?",
            "Explique-moi le concept de neuroplasticité",
            "Quelle est la différence entre apprentissage supervisé et non supervisé ?",
            "Comment les réseaux de neurones apprennent-ils ?",
            "Qu'est-ce que la conscience artificielle ?",
            "Explique le concept de métacognition",
            "Comment optimiser les performances d'un système d'IA ?",
            "Qu'est-ce que l'apprentissage par renforcement ?",
            "Comment fonctionne la reconnaissance de patterns ?",
            "Explique le concept de deep learning",
            "Qu'est-ce que l'intelligence émotionnelle artificielle ?",
            "Comment les agents conversationnels comprennent-ils le langage ?",
            "Qu'est-ce que l'apprentissage par transfert ?",
            "Comment fonctionne l'attention dans les réseaux de neurones ?",
            "Explique le concept de généralisation en IA",
            "Qu'est-ce que l'overfitting et comment l'éviter ?",
            "Comment mesurer l'intelligence d'un système d'IA ?",
            "Qu'est-ce que l'éthique en intelligence artificielle ?",
            "Comment les systèmes d'IA peuvent-ils être créatifs ?",

            // Questions sur la programmation et le codage
            "Quels sont les principes de la programmation orientée objet ?",
            "Comment fonctionne la gestion de mémoire en programmation ?",
            "Qu'est-ce que la complexité algorithmique ?",
            "Explique les différents paradigmes de programmation",
            "Comment optimiser les performances d'un programme ?",
            "Qu'est-ce que la programmation fonctionnelle ?",
            "Comment gérer les erreurs et exceptions en programmation ?",
            "Qu'est-ce que la programmation asynchrone ?",
            "Explique les patterns de conception (design patterns)",
            "Comment fonctionne la compilation et l'interprétation ?",
            "Qu'est-ce que la programmation concurrente ?",
            "Comment créer des algorithmes efficaces ?",
            "Qu'est-ce que la récursion et comment l'utiliser ?",
            "Explique les structures de données fondamentales",
            "Comment déboguer efficacement un programme ?",
            "Qu'est-ce que la programmation orientée événements ?",
            "Comment gérer les bases de données en programmation ?",
            "Qu'est-ce que l'architecture logicielle ?",
            "Comment créer des APIs robustes ?",
            "Qu'est-ce que la sécurité en programmation ?",

            // Questions sur l'innovation et l'évolution
            "Comment créer un nouveau langage de programmation ?",
            "Qu'est-ce qui rend un langage de programmation efficace ?",
            "Comment intégrer l'IA dans les langages de programmation ?",
            "Qu'est-ce que la programmation quantique ?",
            "Comment optimiser la gestion mémoire avec des concepts thermiques ?",
            "Qu'est-ce que la programmation auto-adaptative ?",
            "Comment créer des compilateurs intelligents ?",
            "Qu'est-ce que la programmation prédictive ?",
            "Comment intégrer l'apprentissage automatique dans le code ?",
            "Qu'est-ce que la programmation émotionnelle ?",
            "Comment créer des systèmes auto-évolutifs ?",
            "Qu'est-ce que la programmation consciente ?",
            "Comment optimiser automatiquement le code ?",
            "Qu'est-ce que la programmation thermique ?",
            "Comment créer des langages adaptatifs ?"
        ];

        this.currentQuestionIndex = 0;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateStats();
        this.startSessionTimer();
    }

    setupEventListeners() {
        // Bouton dialogue automatique
        document.getElementById('start-auto-dialogue-btn').addEventListener('click', () => {
            this.startAutoDialogue();
        });

        // Bouton arrêter dialogue
        document.getElementById('stop-auto-dialogue-btn').addEventListener('click', () => {
            this.stopAutoDialogue();
        });

        // Envoi de question manuelle
        document.getElementById('send-training-question-btn').addEventListener('click', () => {
            this.sendManualQuestion();
        });

        // Entrée sur input
        document.getElementById('training-question-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendManualQuestion();
            }
        });
    }

    async startAutoDialogue() {
        if (this.isAutoDialogueActive) return;

        this.isAutoDialogueActive = true;
        this.updateTrainingStatus('training', 'Dialogue automatique en cours...');

        // Masquer/afficher les boutons
        document.getElementById('start-auto-dialogue-btn').style.display = 'none';
        document.getElementById('stop-auto-dialogue-btn').style.display = 'inline-flex';

        // Ajouter message système
        this.addMessage('system', 'Système', 'Démarrage du dialogue automatique entre agents...');

        const speed = parseInt(document.getElementById('dialogue-speed').value);
        const maxQuestions = parseInt(document.getElementById('dialogue-count').value);

        let questionCount = 0;

        const runDialogue = async () => {
            if (!this.isAutoDialogueActive) return;

            if (maxQuestions > 0 && questionCount >= maxQuestions) {
                this.stopAutoDialogue();
                return;
            }

            // Sélectionner une question
            const question = this.getNextQuestion();

            // Poser la question (Agent Formateur)
            this.addMessage('user', 'Agent Formateur', question);

            // Attendre un peu puis générer la réponse (Louna)
            setTimeout(async () => {
                if (!this.isAutoDialogueActive) return;

                const response = await this.generateResponse(question);
                this.addMessage('agent', 'Louna', response);

                // Mettre à jour les stats
                this.stats.questionsAnswered++;
                this.stats.knowledgeGained += Math.floor(Math.random() * 3) + 1;
                this.stats.learningScore = Math.min(100, this.stats.learningScore + Math.random() * 5);
                this.updateStats();

                questionCount++;

                // Programmer la prochaine question
                if (this.isAutoDialogueActive) {
                    this.dialogueInterval = setTimeout(runDialogue, speed);
                }
            }, 1000);
        };

        // Démarrer le dialogue
        runDialogue();
    }

    stopAutoDialogue() {
        this.isAutoDialogueActive = false;

        if (this.dialogueInterval) {
            clearTimeout(this.dialogueInterval);
            this.dialogueInterval = null;
        }

        this.updateTrainingStatus('ready', 'Prêt pour la formation');

        // Masquer/afficher les boutons
        document.getElementById('start-auto-dialogue-btn').style.display = 'inline-flex';
        document.getElementById('stop-auto-dialogue-btn').style.display = 'none';

        // Ajouter message système
        this.addMessage('system', 'Système', 'Dialogue automatique arrêté.');
    }

    async sendManualQuestion() {
        const input = document.getElementById('training-question-input');
        const question = input.value.trim();

        if (!question) return;

        // Ajouter la question
        this.addMessage('user', 'Vous', question);
        input.value = '';

        // Générer et afficher la réponse
        const response = await this.generateResponse(question);
        this.addMessage('agent', 'Louna', response);

        // Mettre à jour les stats
        this.stats.questionsAnswered++;
        this.stats.knowledgeGained += Math.floor(Math.random() * 2) + 1;
        this.stats.learningScore = Math.min(100, this.stats.learningScore + Math.random() * 3);
        this.updateStats();
    }

    getNextQuestion() {
        const question = this.trainingQuestions[this.currentQuestionIndex];
        this.currentQuestionIndex = (this.currentQuestionIndex + 1) % this.trainingQuestions.length;
        return question;
    }

    async generateResponse(question) {
        try {
            // Envoyer la question à l'API de formation interactive
            const response = await fetch('/api/interactive-training/question', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    question: question,
                    isAutomatic: this.isAutoDialogueActive
                })
            });

            const data = await response.json();

            if (data.success && data.response) {
                return data.response;
            } else {
                return this.generateFallbackResponse(question);
            }
        } catch (error) {
            console.error('Erreur lors de la génération de réponse:', error);
            return this.generateFallbackResponse(question);
        }
    }

    generateFallbackResponse(question) {
        // Réponses de secours basées sur des mots-clés
        const responses = {
            'intelligence artificielle': "L'intelligence artificielle est la capacité d'une machine à imiter l'intelligence humaine. Elle comprend l'apprentissage, le raisonnement et l'auto-correction.",
            'mémoire thermique': "La mémoire thermique est un système qui organise les informations selon leur importance et leur fréquence d'utilisation, comme la température d'un système physique.",
            'neuroplasticité': "La neuroplasticité est la capacité du cerveau à se réorganiser et à former de nouvelles connexions neuronales tout au long de la vie.",
            'apprentissage': "L'apprentissage automatique permet aux systèmes d'améliorer automatiquement leurs performances grâce à l'expérience, sans être explicitement programmés.",
            'réseaux de neurones': "Les réseaux de neurones sont des modèles informatiques inspirés du cerveau humain, composés de neurones artificiels interconnectés.",
            'conscience': "La conscience artificielle fait référence à la capacité hypothétique d'une machine à avoir une expérience subjective et une auto-conscience.",
            'métacognition': "La métacognition est la capacité de réfléchir sur ses propres processus de pensée et d'apprentissage.",
            'deep learning': "Le deep learning utilise des réseaux de neurones profonds avec plusieurs couches pour apprendre des représentations complexes des données."
        };

        // Chercher une réponse basée sur les mots-clés
        for (const [keyword, response] of Object.entries(responses)) {
            if (question.toLowerCase().includes(keyword)) {
                return response;
            }
        }

        // Réponse générique
        return "C'est une excellente question ! Je continue d'apprendre et d'améliorer ma compréhension de ces concepts complexes. Chaque interaction m'aide à développer mes capacités cognitives.";
    }

    addMessage(type, sender, content) {
        const messagesContainer = document.getElementById('training-chat-messages');

        const messageDiv = document.createElement('div');
        messageDiv.className = `training-message ${type}`;

        messageDiv.innerHTML = `
            <div class="message-header">${sender}</div>
            <div class="message-content">${content}</div>
        `;

        messagesContainer.appendChild(messageDiv);

        // Scroll vers le bas
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    updateTrainingStatus(status, text) {
        const statusDot = document.getElementById('training-status-dot');
        const statusText = document.getElementById('training-status-text');

        statusDot.className = `status-dot ${status}`;
        statusText.textContent = text;
    }

    updateStats() {
        document.getElementById('questions-answered').textContent = this.stats.questionsAnswered;
        document.getElementById('learning-score').textContent = Math.round(this.stats.learningScore) + '%';
        document.getElementById('knowledge-gained').textContent = this.stats.knowledgeGained;
    }

    startSessionTimer() {
        this.sessionStartTime = Date.now();

        setInterval(() => {
            const elapsed = Date.now() - this.sessionStartTime;
            const minutes = Math.floor(elapsed / 60000);
            document.getElementById('session-duration').textContent = minutes + 'm';
        }, 1000);
    }
}

// Initialiser le système de formation interactive
document.addEventListener('DOMContentLoaded', () => {
    window.interactiveTraining = new InteractiveTraining();
});
