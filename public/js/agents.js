/**
 * Script pour la gestion des agents dans l'application Louna
 */

// Variables globales
let agents = {};
let availableModels = [];
let activeAgentId = null;
let ollamaStatus = {
    isRunning: false,
    version: null
};

// Éléments DOM
const agentsContainer = document.getElementById('agents-container');
const ollamaStatusElement = document.getElementById('ollama-status');
const refreshStatusButton = document.getElementById('refresh-status');
const addAgentButton = document.getElementById('add-agent-btn');
const addFirstAgentButton = document.getElementById('add-first-agent-btn');
const agentModal = document.getElementById('agent-modal');
const modalTitle = document.getElementById('modal-title');
const modalClose = document.getElementById('modal-close');
const agentForm = document.getElementById('agent-form');
const agentIdInput = document.getElementById('agent-id');
const agentNameInput = document.getElementById('agent-name');
const agentTypeInput = document.getElementById('agent-type');
const agentModelInput = document.getElementById('agent-model');
const agentTemperatureInput = document.getElementById('agent-temperature');
const temperatureValue = document.getElementById('temperature-value');
const agentMaxTokensInput = document.getElementById('agent-max-tokens');
const agentDescriptionInput = document.getElementById('agent-description');
const saveAgentButton = document.getElementById('save-agent-btn');
const cancelButton = document.getElementById('cancel-btn');
const notification = document.getElementById('notification');
const notificationMessage = document.getElementById('notification-message');

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Initialiser l'interface
    initializeInterface();
});

/**
 * Initialise l'interface utilisateur
 */
function initializeInterface() {
    // Ajouter les écouteurs d'événements
    refreshStatusButton.addEventListener('click', checkOllamaStatus);
    addAgentButton.addEventListener('click', showAddAgentModal);
    addFirstAgentButton.addEventListener('click', showAddAgentModal);
    modalClose.addEventListener('click', hideModal);
    cancelButton.addEventListener('click', hideModal);
    agentForm.addEventListener('submit', saveAgent);
    agentTemperatureInput.addEventListener('input', updateTemperatureValue);
    agentTypeInput.addEventListener('change', toggleModelSettings);

    // Vérifier l'état d'Ollama
    checkOllamaStatus();

    // Charger les agents
    loadAgents();
}

/**
 * Vérifie l'état d'Ollama
 */
async function checkOllamaStatus() {
    ollamaStatusElement.innerHTML = '<div class="loader"></div> Vérification de l\'état d\'Ollama...';
    
    try {
        const response = await fetch('/api/agents/ollama-status');
        const data = await response.json();
        
        ollamaStatus = data;
        
        if (data.isRunning) {
            ollamaStatusElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span class="status-indicator online"></span>
                    <strong>Ollama est en cours d'exécution</strong>
                </div>
                <div>
                    <p><strong>Version:</strong> ${data.version || 'Inconnue'}</p>
                    <p><strong>Modèles disponibles:</strong> ${data.models ? data.models.length : 0}</p>
                </div>
                <div style="margin-top: 15px;">
                    <button id="start-ollama-btn" class="btn btn-outline btn-sm" style="display: none;">
                        <i class="fas fa-play"></i> Démarrer Ollama
                    </button>
                    <button id="load-models-btn" class="btn btn-secondary btn-sm">
                        <i class="fas fa-download"></i> Charger les modèles
                    </button>
                </div>
            `;
            
            // Ajouter l'écouteur d'événement pour le bouton de chargement des modèles
            document.getElementById('load-models-btn').addEventListener('click', loadAvailableModels);
            
            // Charger les modèles disponibles
            loadAvailableModels();
        } else {
            ollamaStatusElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span class="status-indicator offline"></span>
                    <strong>Ollama n'est pas en cours d'exécution</strong>
                </div>
                <p>Vous devez démarrer Ollama pour utiliser les agents basés sur des modèles locaux.</p>
                <div style="margin-top: 15px;">
                    <button id="start-ollama-btn" class="btn btn-primary btn-sm">
                        <i class="fas fa-play"></i> Démarrer Ollama
                    </button>
                </div>
            `;
            
            // Ajouter l'écouteur d'événement pour le bouton de démarrage d'Ollama
            document.getElementById('start-ollama-btn').addEventListener('click', startOllama);
        }
    } catch (error) {
        console.error('Erreur lors de la vérification de l\'état d\'Ollama:', error);
        
        ollamaStatusElement.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span class="status-indicator offline"></span>
                <strong>Erreur lors de la vérification de l'état d'Ollama</strong>
            </div>
            <p>Une erreur s'est produite lors de la vérification de l'état d'Ollama. Veuillez réessayer.</p>
            <div style="margin-top: 15px;">
                <button id="start-ollama-btn" class="btn btn-primary btn-sm">
                    <i class="fas fa-play"></i> Démarrer Ollama
                </button>
            </div>
        `;
        
        // Ajouter l'écouteur d'événement pour le bouton de démarrage d'Ollama
        document.getElementById('start-ollama-btn').addEventListener('click', startOllama);
    }
}

/**
 * Démarre Ollama
 */
async function startOllama() {
    try {
        const startButton = document.getElementById('start-ollama-btn');
        startButton.innerHTML = '<div class="loader"></div> Démarrage d\'Ollama...';
        startButton.disabled = true;
        
        const response = await fetch('/api/agents/start-ollama', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('Ollama a été démarré avec succès', 'success');
            checkOllamaStatus();
        } else {
            showNotification(`Erreur lors du démarrage d'Ollama: ${data.error}`, 'error');
            startButton.innerHTML = '<i class="fas fa-play"></i> Démarrer Ollama';
            startButton.disabled = false;
        }
    } catch (error) {
        console.error('Erreur lors du démarrage d\'Ollama:', error);
        showNotification('Erreur lors du démarrage d\'Ollama', 'error');
        
        const startButton = document.getElementById('start-ollama-btn');
        startButton.innerHTML = '<i class="fas fa-play"></i> Démarrer Ollama';
        startButton.disabled = false;
    }
}

/**
 * Charge les modèles disponibles dans Ollama
 */
async function loadAvailableModels() {
    try {
        const loadButton = document.getElementById('load-models-btn');
        if (loadButton) {
            loadButton.innerHTML = '<div class="loader"></div> Chargement des modèles...';
            loadButton.disabled = true;
        }
        
        const response = await fetch('/api/agents/available-models');
        const data = await response.json();
        
        if (data.success) {
            availableModels = data.models;
            
            // Mettre à jour le sélecteur de modèles
            updateModelSelector();
            
            if (loadButton) {
                loadButton.innerHTML = '<i class="fas fa-download"></i> Charger les modèles';
                loadButton.disabled = false;
            }
            
            showNotification(`${availableModels.length} modèles disponibles`, 'info');
        } else {
            showNotification(`Erreur lors du chargement des modèles: ${data.error}`, 'error');
            
            if (loadButton) {
                loadButton.innerHTML = '<i class="fas fa-download"></i> Charger les modèles';
                loadButton.disabled = false;
            }
        }
    } catch (error) {
        console.error('Erreur lors du chargement des modèles disponibles:', error);
        showNotification('Erreur lors du chargement des modèles', 'error');
        
        const loadButton = document.getElementById('load-models-btn');
        if (loadButton) {
            loadButton.innerHTML = '<i class="fas fa-download"></i> Charger les modèles';
            loadButton.disabled = false;
        }
    }
}

/**
 * Met à jour le sélecteur de modèles
 */
function updateModelSelector() {
    agentModelInput.innerHTML = '';
    
    if (availableModels.length === 0) {
        agentModelInput.innerHTML = '<option value="">Aucun modèle disponible</option>';
        return;
    }
    
    availableModels.forEach(model => {
        const option = document.createElement('option');
        option.value = model.name;
        
        // Ajouter des informations sur le modèle si disponibles
        let modelInfo = model.name;
        if (model.details && model.details.parameter_size) {
            modelInfo += ` (${model.details.parameter_size})`;
        }
        
        option.textContent = modelInfo;
        agentModelInput.appendChild(option);
    });
    
    // Sélectionner le modèle DeepSeek r1 par défaut s'il est disponible
    const deepseekOption = Array.from(agentModelInput.options).find(option => option.value.includes('deepseek-r1'));
    if (deepseekOption) {
        deepseekOption.selected = true;
    }
}

/**
 * Charge les agents depuis l'API
 */
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        const data = await response.json();
        
        if (data.success) {
            agents = data.agents;
            activeAgentId = data.defaultAgent;
            
            renderAgents();
        } else {
            showNotification(`Erreur lors du chargement des agents: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des agents:', error);
        showNotification('Erreur lors du chargement des agents', 'error');
    }
}

/**
 * Affiche les agents dans l'interface
 */
function renderAgents() {
    // Vider le conteneur
    agentsContainer.innerHTML = '';
    
    const agentIds = Object.keys(agents);
    
    if (agentIds.length === 0) {
        // Afficher l'état vide
        agentsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-robot"></i>
                <h3>Aucun agent disponible</h3>
                <p>Ajoutez un nouvel agent pour commencer à interagir avec l'application.</p>
                <button id="add-first-agent-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajouter un Agent
                </button>
            </div>
        `;
        
        // Ajouter l'écouteur d'événement pour le bouton d'ajout d'agent
        document.getElementById('add-first-agent-btn').addEventListener('click', showAddAgentModal);
        return;
    }
    
    // Afficher les agents
    agentIds.forEach(agentId => {
        const agent = agents[agentId];
        const isActive = agentId === activeAgentId;
        
        const agentCard = document.createElement('div');
        agentCard.className = `agent-card ${isActive ? 'active' : ''}`;
        agentCard.dataset.agentId = agentId;
        
        let modelInfo = '';
        if (agent.type === 'ollama' && agent.model) {
            modelInfo = `<p>Modèle: ${agent.model}</p>`;
        }
        
        agentCard.innerHTML = `
            <div class="agent-header">
                <div class="agent-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="agent-info">
                    <h3>${agent.name}</h3>
                    ${modelInfo}
                </div>
            </div>
            <p>${agent.description || 'Aucune description'}</p>
            <div class="agent-stats">
                <div class="stat-item">
                    <div class="stat-value">${agent.temperature || 0.7}</div>
                    <div class="stat-label">Température</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${agent.maxTokens || 1000}</div>
                    <div class="stat-label">Max Tokens</div>
                </div>
            </div>
            <div class="agent-actions">
                ${isActive ? `
                    <button class="btn btn-outline btn-sm" disabled>
                        <i class="fas fa-check"></i> Actif
                    </button>
                ` : `
                    <button class="btn btn-primary btn-sm activate-agent-btn">
                        <i class="fas fa-play"></i> Activer
                    </button>
                `}
                <button class="btn btn-secondary btn-sm edit-agent-btn">
                    <i class="fas fa-edit"></i> Modifier
                </button>
                <button class="btn btn-danger btn-sm delete-agent-btn">
                    <i class="fas fa-trash"></i> Supprimer
                </button>
            </div>
        `;
        
        agentsContainer.appendChild(agentCard);
        
        // Ajouter les écouteurs d'événements pour les boutons
        const activateButton = agentCard.querySelector('.activate-agent-btn');
        if (activateButton) {
            activateButton.addEventListener('click', () => activateAgent(agentId));
        }
        
        const editButton = agentCard.querySelector('.edit-agent-btn');
        if (editButton) {
            editButton.addEventListener('click', () => showEditAgentModal(agentId));
        }
        
        const deleteButton = agentCard.querySelector('.delete-agent-btn');
        if (deleteButton) {
            deleteButton.addEventListener('click', () => deleteAgent(agentId));
        }
    });
}

/**
 * Affiche la modal pour ajouter un agent
 */
function showAddAgentModal() {
    // Réinitialiser le formulaire
    agentForm.reset();
    agentIdInput.value = '';
    modalTitle.textContent = 'Ajouter un Agent';
    
    // Mettre à jour le sélecteur de modèles
    updateModelSelector();
    
    // Afficher la modal
    agentModal.classList.add('visible');
}

/**
 * Affiche la modal pour modifier un agent
 * @param {string} agentId - ID de l'agent à modifier
 */
function showEditAgentModal(agentId) {
    const agent = agents[agentId];
    
    if (!agent) {
        showNotification('Agent non trouvé', 'error');
        return;
    }
    
    // Remplir le formulaire avec les données de l'agent
    agentIdInput.value = agentId;
    agentNameInput.value = agent.name || '';
    agentTypeInput.value = agent.type || 'ollama';
    agentDescriptionInput.value = agent.description || '';
    agentTemperatureInput.value = agent.temperature || 0.7;
    temperatureValue.textContent = agent.temperature || 0.7;
    agentMaxTokensInput.value = agent.maxTokens || 1000;
    
    // Mettre à jour le sélecteur de modèles
    updateModelSelector();
    
    // Sélectionner le modèle de l'agent
    if (agent.type === 'ollama' && agent.model) {
        const modelOption = Array.from(agentModelInput.options).find(option => option.value === agent.model);
        if (modelOption) {
            modelOption.selected = true;
        }
    }
    
    // Mettre à jour le titre de la modal
    modalTitle.textContent = 'Modifier l\'Agent';
    
    // Afficher la modal
    agentModal.classList.add('visible');
}

/**
 * Cache la modal
 */
function hideModal() {
    agentModal.classList.remove('visible');
}

/**
 * Met à jour l'affichage de la valeur de température
 */
function updateTemperatureValue() {
    temperatureValue.textContent = agentTemperatureInput.value;
}

/**
 * Affiche ou masque les paramètres spécifiques au type d'agent
 */
function toggleModelSettings() {
    const ollamaSettings = document.getElementById('ollama-settings');
    
    if (agentTypeInput.value === 'ollama') {
        ollamaSettings.style.display = 'block';
    } else {
        ollamaSettings.style.display = 'none';
    }
}

/**
 * Enregistre un agent (ajout ou modification)
 * @param {Event} event - Événement de soumission du formulaire
 */
async function saveAgent(event) {
    event.preventDefault();
    
    const agentId = agentIdInput.value;
    const isNewAgent = !agentId;
    
    // Récupérer les données du formulaire
    const agentData = {
        id: isNewAgent ? `agent_${Date.now()}` : agentId,
        name: agentNameInput.value,
        type: agentTypeInput.value,
        description: agentDescriptionInput.value,
        temperature: parseFloat(agentTemperatureInput.value),
        maxTokens: parseInt(agentMaxTokensInput.value)
    };
    
    // Ajouter les paramètres spécifiques au type d'agent
    if (agentData.type === 'ollama') {
        agentData.model = agentModelInput.value;
    }
    
    try {
        // Désactiver le bouton de sauvegarde
        saveAgentButton.disabled = true;
        saveAgentButton.innerHTML = '<div class="loader"></div> Enregistrement...';
        
        // Envoyer les données à l'API
        const url = isNewAgent ? '/api/agents' : `/api/agents/${agentId}`;
        const method = isNewAgent ? 'POST' : 'PUT';
        
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(agentData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Mettre à jour la liste des agents
            if (isNewAgent) {
                agents[data.agent.id] = data.agent;
                
                // Si c'est le premier agent, le définir comme agent par défaut
                if (Object.keys(agents).length === 1) {
                    activeAgentId = data.agent.id;
                }
            } else {
                agents[agentId] = data.agent;
            }
            
            // Masquer la modal
            hideModal();
            
            // Afficher les agents
            renderAgents();
            
            // Afficher une notification
            showNotification(isNewAgent ? 'Agent ajouté avec succès' : 'Agent mis à jour avec succès', 'success');
        } else {
            showNotification(`Erreur: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de l\'enregistrement de l\'agent:', error);
        showNotification('Erreur lors de l\'enregistrement de l\'agent', 'error');
    } finally {
        // Réactiver le bouton de sauvegarde
        saveAgentButton.disabled = false;
        saveAgentButton.innerHTML = 'Enregistrer';
    }
}

/**
 * Active un agent
 * @param {string} agentId - ID de l'agent à activer
 */
async function activateAgent(agentId) {
    try {
        const response = await fetch(`/api/agents/${agentId}/activate`, {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            activeAgentId = agentId;
            renderAgents();
            showNotification(`Agent ${agents[agentId].name} activé avec succès`, 'success');
        } else {
            showNotification(`Erreur lors de l'activation de l'agent: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de l\'activation de l\'agent:', error);
        showNotification('Erreur lors de l\'activation de l\'agent', 'error');
    }
}

/**
 * Supprime un agent
 * @param {string} agentId - ID de l'agent à supprimer
 */
async function deleteAgent(agentId) {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'agent ${agents[agentId].name} ?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/agents/${agentId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Supprimer l'agent de la liste
            delete agents[agentId];
            
            // Si c'était l'agent actif, désactiver l'agent actif
            if (activeAgentId === agentId) {
                activeAgentId = null;
            }
            
            // Afficher les agents
            renderAgents();
            
            // Afficher une notification
            showNotification('Agent supprimé avec succès', 'success');
        } else {
            showNotification(`Erreur lors de la suppression de l'agent: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de la suppression de l\'agent:', error);
        showNotification('Erreur lors de la suppression de l\'agent', 'error');
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    notificationMessage.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.add('visible');
    
    // Masquer la notification après 3 secondes
    setTimeout(() => {
        notification.classList.remove('visible');
    }, 3000);
}
