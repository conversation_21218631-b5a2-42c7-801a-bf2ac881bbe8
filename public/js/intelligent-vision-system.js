/**
 * Système de Vision Intelligente - Louna
 * Analyse d'images, vidéos et vision par caméra en temps réel
 */

class IntelligentVisionSystem {
    constructor() {
        this.isActive = false;
        this.camera = null;
        this.canvas = null;
        this.context = null;
        this.analysisInterval = null;
        this.detectedObjects = [];
        this.faceDetector = null;
        this.emotionAnalyzer = null;
        this.textRecognizer = null;
        this.analysisHistory = [];
        this.settings = {
            analysisFrequency: 2000, // ms
            confidenceThreshold: 0.7,
            enableFaceDetection: true,
            enableEmotionAnalysis: true,
            enableTextRecognition: true,
            enableObjectDetection: true
        };
        this.init();
    }

    async init() {
        console.log('👁️ Initialisation du système de vision intelligente...');
        
        await this.initializeDetectors();
        this.createVisionInterface();
        this.setupEventListeners();
        
        console.log('✅ Système de vision intelligente initialisé');
    }

    async initializeDetectors() {
        try {
            // Initialiser la détection de visages si disponible
            if ('FaceDetector' in window) {
                this.faceDetector = new FaceDetector({
                    maxDetectedFaces: 10,
                    fastMode: false
                });
                console.log('✅ Détecteur de visages initialisé');
            }

            // Initialiser la reconnaissance de texte si disponible
            if ('TextDetector' in window) {
                this.textRecognizer = new TextDetector();
                console.log('✅ Reconnaissance de texte initialisée');
            }

        } catch (error) {
            console.log('⚠️ Certains détecteurs ne sont pas disponibles:', error.message);
        }
    }

    createVisionInterface() {
        const visionInterface = document.createElement('div');
        visionInterface.id = 'vision-interface';
        visionInterface.innerHTML = `
            <div class="vision-container">
                <div class="vision-header">
                    <h3><i class="fas fa-eye"></i> Vision Intelligente</h3>
                    <div class="vision-controls">
                        <button class="vision-btn" id="camera-toggle" title="Activer/Désactiver la caméra">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button class="vision-btn" id="screenshot-btn" title="Capturer l'écran">
                            <i class="fas fa-camera-retro"></i>
                        </button>
                        <button class="vision-btn" id="upload-btn" title="Analyser une image">
                            <i class="fas fa-upload"></i>
                        </button>
                        <button class="vision-btn" id="vision-settings" title="Paramètres">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                
                <div class="vision-content">
                    <div class="camera-container" id="camera-container">
                        <video id="camera-video" autoplay muted></video>
                        <canvas id="analysis-canvas"></canvas>
                        <div class="camera-overlay" id="camera-overlay"></div>
                    </div>
                    
                    <div class="analysis-panel" id="analysis-panel">
                        <div class="analysis-section">
                            <h4><i class="fas fa-search"></i> Analyse en Temps Réel</h4>
                            <div class="analysis-results" id="analysis-results">
                                <div class="no-analysis">Aucune analyse en cours</div>
                            </div>
                        </div>
                        
                        <div class="detection-section">
                            <h4><i class="fas fa-bullseye"></i> Détections</h4>
                            <div class="detection-list" id="detection-list">
                                <div class="no-detections">Aucune détection</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <input type="file" id="image-upload" accept="image/*,video/*" style="display: none;">
            </div>
        `;

        // Ajouter les styles
        const styles = document.createElement('style');
        styles.textContent = `
            #vision-interface {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 400px;
                height: 500px;
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(15px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                font-family: 'Segoe UI', sans-serif;
                z-index: 9999;
                display: none;
                overflow: hidden;
            }

            #vision-interface.active {
                display: block;
                animation: slideIn 0.3s ease;
            }

            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .vision-container {
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .vision-header {
                padding: 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .vision-header h3 {
                margin: 0;
                font-size: 16px;
                color: #4ecdc4;
            }

            .vision-controls {
                display: flex;
                gap: 8px;
            }

            .vision-btn {
                width: 35px;
                height: 35px;
                border-radius: 50%;
                border: none;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .vision-btn:hover {
                background: rgba(78, 205, 196, 0.3);
                transform: scale(1.1);
            }

            .vision-btn.active {
                background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            }

            .vision-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .camera-container {
                position: relative;
                height: 200px;
                background: #000;
                overflow: hidden;
            }

            #camera-video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            #analysis-canvas {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }

            .camera-overlay {
                position: absolute;
                top: 10px;
                left: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.7);
                border-radius: 10px;
                padding: 8px;
                font-size: 12px;
                display: none;
            }

            .camera-overlay.active {
                display: block;
            }

            .analysis-panel {
                flex: 1;
                padding: 15px;
                overflow-y: auto;
            }

            .analysis-section, .detection-section {
                margin-bottom: 20px;
            }

            .analysis-section h4, .detection-section h4 {
                margin: 0 0 10px 0;
                font-size: 14px;
                color: #4ecdc4;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .analysis-results, .detection-list {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 10px;
                padding: 10px;
                min-height: 60px;
                font-size: 12px;
            }

            .no-analysis, .no-detections {
                color: #888;
                text-align: center;
                padding: 20px;
            }

            .detection-item {
                background: rgba(78, 205, 196, 0.1);
                border-left: 3px solid #4ecdc4;
                padding: 8px;
                margin-bottom: 8px;
                border-radius: 5px;
            }

            .detection-item .label {
                font-weight: bold;
                color: #4ecdc4;
            }

            .detection-item .confidence {
                color: #888;
                font-size: 11px;
            }

            .analysis-item {
                background: rgba(69, 183, 209, 0.1);
                border-left: 3px solid #45b7d1;
                padding: 8px;
                margin-bottom: 8px;
                border-radius: 5px;
            }

            .analysis-item .type {
                font-weight: bold;
                color: #45b7d1;
            }

            .analysis-item .result {
                margin-top: 5px;
                font-size: 11px;
            }

            .vision-toggle {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(45deg, #4ecdc4, #45b7d1);
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                z-index: 10000;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .vision-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 5px 20px rgba(78, 205, 196, 0.4);
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(visionInterface);

        // Créer le bouton de toggle
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'vision-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        toggleBtn.title = 'Activer/Désactiver la vision intelligente';
        toggleBtn.onclick = () => this.toggleVisionInterface();
        document.body.appendChild(toggleBtn);

        // Initialiser le canvas
        this.canvas = document.getElementById('analysis-canvas');
        this.context = this.canvas.getContext('2d');
    }

    setupEventListeners() {
        document.getElementById('camera-toggle').addEventListener('click', () => {
            this.toggleCamera();
        });

        document.getElementById('screenshot-btn').addEventListener('click', () => {
            this.captureScreen();
        });

        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('image-upload').click();
        });

        document.getElementById('image-upload').addEventListener('change', (e) => {
            this.analyzeUploadedFile(e.target.files[0]);
        });

        document.getElementById('vision-settings').addEventListener('click', () => {
            this.showVisionSettings();
        });
    }

    toggleVisionInterface() {
        const interface = document.getElementById('vision-interface');
        interface.classList.toggle('active');
        
        if (interface.classList.contains('active')) {
            this.isActive = true;
            this.speak("Interface de vision activée");
        } else {
            this.isActive = false;
            this.stopCamera();
            this.speak("Interface de vision désactivée");
        }
    }

    async toggleCamera() {
        const video = document.getElementById('camera-video');
        const toggleBtn = document.getElementById('camera-toggle');

        if (this.camera) {
            this.stopCamera();
        } else {
            try {
                this.camera = await navigator.mediaDevices.getUserMedia({
                    video: { 
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    },
                    audio: false
                });

                video.srcObject = this.camera;
                toggleBtn.classList.add('active');
                
                video.onloadedmetadata = () => {
                    this.canvas.width = video.videoWidth;
                    this.canvas.height = video.videoHeight;
                    this.startAnalysis();
                };

                this.speak("Caméra activée, analyse en cours");

            } catch (error) {
                console.error('Erreur accès caméra:', error);
                this.speak("Impossible d'accéder à la caméra");
            }
        }
    }

    stopCamera() {
        if (this.camera) {
            this.camera.getTracks().forEach(track => track.stop());
            this.camera = null;
            
            const video = document.getElementById('camera-video');
            video.srcObject = null;
            
            document.getElementById('camera-toggle').classList.remove('active');
            this.stopAnalysis();
            
            this.speak("Caméra désactivée");
        }
    }

    startAnalysis() {
        if (this.analysisInterval) {
            clearInterval(this.analysisInterval);
        }

        this.analysisInterval = setInterval(() => {
            this.performAnalysis();
        }, this.settings.analysisFrequency);

        document.getElementById('camera-overlay').classList.add('active');
        document.getElementById('camera-overlay').textContent = 'Analyse en cours...';
    }

    stopAnalysis() {
        if (this.analysisInterval) {
            clearInterval(this.analysisInterval);
            this.analysisInterval = null;
        }

        document.getElementById('camera-overlay').classList.remove('active');
        this.clearAnalysisResults();
    }

    async performAnalysis() {
        const video = document.getElementById('camera-video');
        if (!video.videoWidth || !video.videoHeight) return;

        // Capturer l'image actuelle
        this.context.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
        
        const analyses = [];

        try {
            // Détection de visages
            if (this.settings.enableFaceDetection && this.faceDetector) {
                const faces = await this.detectFaces();
                if (faces.length > 0) {
                    analyses.push({
                        type: 'Visages détectés',
                        result: `${faces.length} visage(s) trouvé(s)`,
                        data: faces
                    });
                    this.drawFaceBoxes(faces);
                }
            }

            // Reconnaissance de texte
            if (this.settings.enableTextRecognition && this.textRecognizer) {
                const texts = await this.recognizeText();
                if (texts.length > 0) {
                    analyses.push({
                        type: 'Texte détecté',
                        result: texts.map(t => t.rawValue).join(', '),
                        data: texts
                    });
                }
            }

            // Analyse des couleurs dominantes
            const colors = this.analyzeColors();
            analyses.push({
                type: 'Couleurs dominantes',
                result: colors.join(', '),
                data: colors
            });

            // Détection de mouvement (simple)
            const motion = this.detectMotion();
            if (motion.detected) {
                analyses.push({
                    type: 'Mouvement',
                    result: `Intensité: ${motion.intensity}%`,
                    data: motion
                });
            }

            this.updateAnalysisResults(analyses);

        } catch (error) {
            console.error('Erreur analyse:', error);
        }
    }

    async detectFaces() {
        try {
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const faces = await this.faceDetector.detect(imageData);
            return faces;
        } catch (error) {
            console.error('Erreur détection visages:', error);
            return [];
        }
    }

    async recognizeText() {
        try {
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const texts = await this.textRecognizer.detect(imageData);
            return texts;
        } catch (error) {
            console.error('Erreur reconnaissance texte:', error);
            return [];
        }
    }

    analyzeColors() {
        const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;
        const colorCounts = {};

        // Échantillonner les pixels
        for (let i = 0; i < data.length; i += 40) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            // Simplifier les couleurs en groupes
            const colorGroup = this.getColorGroup(r, g, b);
            colorCounts[colorGroup] = (colorCounts[colorGroup] || 0) + 1;
        }

        // Retourner les 3 couleurs les plus fréquentes
        return Object.entries(colorCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([color]) => color);
    }

    getColorGroup(r, g, b) {
        if (r > 200 && g > 200 && b > 200) return 'Blanc';
        if (r < 50 && g < 50 && b < 50) return 'Noir';
        if (r > g && r > b) return 'Rouge';
        if (g > r && g > b) return 'Vert';
        if (b > r && b > g) return 'Bleu';
        if (r > 150 && g > 150 && b < 100) return 'Jaune';
        if (r > 150 && g < 100 && b > 150) return 'Magenta';
        if (r < 100 && g > 150 && b > 150) return 'Cyan';
        return 'Gris';
    }

    detectMotion() {
        // Implémentation simple de détection de mouvement
        // Dans une vraie application, on comparerait avec l'image précédente
        return {
            detected: Math.random() > 0.7,
            intensity: Math.floor(Math.random() * 100)
        };
    }

    drawFaceBoxes(faces) {
        this.context.strokeStyle = '#4ecdc4';
        this.context.lineWidth = 2;

        faces.forEach(face => {
            const box = face.boundingBox;
            this.context.strokeRect(box.x, box.y, box.width, box.height);
            
            // Ajouter un label
            this.context.fillStyle = '#4ecdc4';
            this.context.font = '12px Arial';
            this.context.fillText('Visage', box.x, box.y - 5);
        });
    }

    updateAnalysisResults(analyses) {
        const resultsContainer = document.getElementById('analysis-results');
        
        if (analyses.length === 0) {
            resultsContainer.innerHTML = '<div class="no-analysis">Aucune analyse disponible</div>';
            return;
        }

        resultsContainer.innerHTML = analyses.map(analysis => `
            <div class="analysis-item">
                <div class="type">${analysis.type}</div>
                <div class="result">${analysis.result}</div>
            </div>
        `).join('');

        // Mettre à jour l'historique
        this.analysisHistory.push({
            timestamp: new Date().toISOString(),
            analyses: analyses
        });

        // Garder seulement les 50 dernières analyses
        if (this.analysisHistory.length > 50) {
            this.analysisHistory = this.analysisHistory.slice(-50);
        }
    }

    clearAnalysisResults() {
        document.getElementById('analysis-results').innerHTML = '<div class="no-analysis">Aucune analyse en cours</div>';
        document.getElementById('detection-list').innerHTML = '<div class="no-detections">Aucune détection</div>';
    }

    async captureScreen() {
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: { mediaSource: 'screen' }
            });

            const video = document.createElement('video');
            video.srcObject = stream;
            video.play();

            video.onloadedmetadata = () => {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                
                ctx.drawImage(video, 0, 0);
                
                // Arrêter le stream
                stream.getTracks().forEach(track => track.stop());
                
                // Analyser l'image capturée
                this.analyzeImage(canvas);
                
                this.speak("Capture d'écran analysée");
            };

        } catch (error) {
            console.error('Erreur capture écran:', error);
            this.speak("Impossible de capturer l'écran");
        }
    }

    async analyzeUploadedFile(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                
                this.analyzeImage(canvas);
                this.speak(`Analyse de ${file.name} terminée`);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    async analyzeImage(canvas) {
        // Analyser une image statique
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        const analyses = [];

        // Analyse des couleurs
        const colors = this.analyzeColorsFromImageData(imageData);
        analyses.push({
            type: 'Couleurs dominantes',
            result: colors.join(', '),
            data: colors
        });

        // Détection de contours/formes
        const shapes = this.detectShapes(imageData);
        if (shapes.length > 0) {
            analyses.push({
                type: 'Formes détectées',
                result: `${shapes.length} forme(s)`,
                data: shapes
            });
        }

        this.updateAnalysisResults(analyses);
    }

    analyzeColorsFromImageData(imageData) {
        const data = imageData.data;
        const colorCounts = {};

        for (let i = 0; i < data.length; i += 40) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            const colorGroup = this.getColorGroup(r, g, b);
            colorCounts[colorGroup] = (colorCounts[colorGroup] || 0) + 1;
        }

        return Object.entries(colorCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([color]) => color);
    }

    detectShapes(imageData) {
        // Implémentation simple de détection de formes
        // Dans une vraie application, on utiliserait des algorithmes plus sophistiqués
        const shapes = [];
        
        // Simuler la détection de quelques formes
        if (Math.random() > 0.5) {
            shapes.push({ type: 'Rectangle', confidence: 0.8 });
        }
        if (Math.random() > 0.7) {
            shapes.push({ type: 'Cercle', confidence: 0.6 });
        }
        
        return shapes;
    }

    showVisionSettings() {
        // Implémenter l'interface des paramètres
        console.log('Paramètres de vision à implémenter');
    }

    speak(message) {
        // Utiliser le système vocal si disponible
        if (window.voiceSystem) {
            window.voiceSystem.speak(message);
        } else {
            console.log('🗣️ Vision:', message);
        }
    }

    // API publique pour intégration avec d'autres systèmes
    getAnalysisHistory() {
        return this.analysisHistory;
    }

    getCurrentAnalysis() {
        return this.analysisHistory[this.analysisHistory.length - 1] || null;
    }

    setAnalysisFrequency(frequency) {
        this.settings.analysisFrequency = frequency;
        if (this.analysisInterval) {
            this.stopAnalysis();
            this.startAnalysis();
        }
    }
}

// Initialiser le système de vision
const visionSystem = new IntelligentVisionSystem();
window.visionSystem = visionSystem;
