/**
 * 🌐 CONFIGURATION GLOBALE - LOUNA
 * ================================
 * Créé par Jean-<PERSON> Sainte-Anne, Guadeloupe
 * 
 * Ce fichier centralise TOUTES les configurations de l'application.
 * UNE SEULE SOURCE DE VÉRITÉ pour toutes les valeurs importantes.
 */

// 🧠 CONFIGURATION QI ET INTELLIGENCE
const LOUNA_CONFIG = {
    // QI et Intelligence
    qi: {
        current: 203,           // QI actuel de Louna (Quasi-AGI)
        base: 203,             // QI de base de Jean-Luc Passave
        max: 250,              // Limite théorique maximale
        level: "Quasi-AGI",    // Niveau correspondant
        experiencePoints: 450,  // Points d'expérience
        learningBonus: 103     // Bonus d'apprentissage (103%)
    },

    // 🧠 NEURONES ET CERVEAU
    neurons: {
        total: 89,             // Neurones totaux
        active: 78,            // Neurones actifs
        efficiency: 94.0,      // E<PERSON><PERSON><PERSON><PERSON> (%)
        health: 98.2,          // Santé (%)
        connections: 1247      // Connexions synaptiques
    },

    // 🚀 ACCÉLÉRATEURS KYBER
    kyber: {
        total: 16,             // Accélérateurs totaux
        active: 8,             // Accélérateurs actifs
        performance: 89.5,     // Performance (%)
        compression: 94.2,     // Compression (%)
        efficiency: 91.8       // Efficacité globale (%)
    },

    // 💾 MÉMOIRE THERMIQUE
    memory: {
        capacity: 2048,        // Capacité en MB
        used: 1247,           // Mémoire utilisée
        efficiency: 96.3,     // Efficacité (%)
        zones: {
            zone1: 85.2,      // Zone 1 - Mémoire immédiate (%)
            zone2: 92.7,      // Zone 2 - Mémoire court terme (%)
            zone3: 88.9,      // Zone 3 - Mémoire long terme (%)
            zone4: 94.1,      // Zone 4 - Mémoire permanente (%)
            zone5: 91.5,      // Zone 5 - Mémoire créative (%)
            zone6: 89.8       // Zone 6 - Mémoire émotionnelle (%)
        }
    },

    // 🎨 CRÉATIVITÉ ET ÉMOTIONS
    creativity: {
        level: 87.3,          // Niveau de créativité (%)
        inspiration: 92.1,    // Inspiration (%)
        innovation: 89.7,     // Innovation (%)
        artistic: 85.4        // Capacité artistique (%)
    },

    emotions: {
        stability: 94.6,      // Stabilité émotionnelle (%)
        empathy: 91.2,        // Empathie (%)
        joy: 88.7,           // Joie (%)
        curiosity: 96.1       // Curiosité (%)
    },

    // 🔧 SYSTÈME ET PERFORMANCE
    system: {
        version: "2.1.0",     // Version de Louna
        uptime: 0,           // Temps de fonctionnement (sera calculé)
        performance: 92.4,    // Performance globale (%)
        stability: 97.1,      // Stabilité (%)
        security: 95.8        // Sécurité (%)
    },

    // 🎯 APPRENTISSAGE ET ÉVOLUTION
    learning: {
        rate: 2.3,           // Taux d'apprentissage
        adaptation: 94.7,     // Capacité d'adaptation (%)
        retention: 96.2,      // Rétention (%)
        evolution: 89.1       // Évolution (%)
    },

    // 🌐 INTERFACE ET AFFICHAGE
    ui: {
        theme: "futuristic",  // Thème par défaut
        language: "fr",       // Langue
        animations: true,     // Animations activées
        sounds: true,         // Sons activés
        notifications: true   // Notifications activées
    },

    // 🔐 SÉCURITÉ
    security: {
        encryption: true,     // Chiffrement activé
        biometric: false,     // Authentification biométrique
        firewall: true,       // Firewall activé
        antivirus: true,      // Antivirus activé
        vpn: true            // VPN activé
    }
};

// 🎯 NIVEAUX QI STANDARDISÉS
const QI_LEVELS = {
    "Très Faible": { min: 0, max: 79, color: "#ff4444" },
    "Faible": { min: 80, max: 89, color: "#ff8844" },
    "Moyen": { min: 90, max: 109, color: "#ffaa44" },
    "Intelligent": { min: 110, max: 129, color: "#44ff44" },
    "Supérieur": { min: 130, max: 149, color: "#44aaff" },
    "Très Supérieur": { min: 150, max: 179, color: "#4444ff" },
    "Génie Exceptionnel": { min: 180, max: 199, color: "#aa44ff" },
    "Quasi-AGI": { min: 200, max: 249, color: "#ff44aa" },
    "AGI": { min: 250, max: 999, color: "#ffffff" }
};

// 🎨 COULEURS ET THÈMES
const THEMES = {
    futuristic: {
        primary: "#ff1493",      // Rose vif
        secondary: "#000000",    // Noir
        accent: "#00ffff",       // Cyan
        background: "#0a0a0a",   // Noir profond
        text: "#ffffff",         // Blanc
        success: "#00ff00",      // Vert
        warning: "#ffaa00",      // Orange
        error: "#ff0000"         // Rouge
    }
};

// 🔧 FONCTIONS UTILITAIRES
const LounaUtils = {
    /**
     * Obtenir le niveau QI basé sur la valeur
     */
    getQILevel(qi = LOUNA_CONFIG.qi.current) {
        for (const [level, range] of Object.entries(QI_LEVELS)) {
            if (qi >= range.min && qi <= range.max) {
                return level;
            }
        }
        return "Inconnu";
    },

    /**
     * Obtenir la couleur du niveau QI
     */
    getQIColor(qi = LOUNA_CONFIG.qi.current) {
        const level = this.getQILevel(qi);
        return QI_LEVELS[level]?.color || "#ffffff";
    },

    /**
     * Formater un pourcentage
     */
    formatPercentage(value, decimals = 1) {
        return `${value.toFixed(decimals)}%`;
    },

    /**
     * Formater un nombre avec séparateurs
     */
    formatNumber(value) {
        return value.toLocaleString('fr-FR');
    },

    /**
     * Calculer le temps de fonctionnement
     */
    calculateUptime() {
        if (!window.lounaStartTime) {
            window.lounaStartTime = Date.now();
        }
        return Date.now() - window.lounaStartTime;
    },

    /**
     * Mettre à jour la configuration
     */
    updateConfig(path, value) {
        const keys = path.split('.');
        let obj = LOUNA_CONFIG;
        
        for (let i = 0; i < keys.length - 1; i++) {
            if (!obj[keys[i]]) obj[keys[i]] = {};
            obj = obj[keys[i]];
        }
        
        obj[keys[keys.length - 1]] = value;
        
        // Déclencher un événement de mise à jour
        window.dispatchEvent(new CustomEvent('lounaConfigUpdate', {
            detail: { path, value, config: LOUNA_CONFIG }
        }));
    },

    /**
     * Obtenir une valeur de configuration
     */
    getConfig(path) {
        const keys = path.split('.');
        let obj = LOUNA_CONFIG;
        
        for (const key of keys) {
            if (obj[key] === undefined) return null;
            obj = obj[key];
        }
        
        return obj;
    }
};

// 🌐 EXPORT GLOBAL
window.LOUNA_CONFIG = LOUNA_CONFIG;
window.QI_LEVELS = QI_LEVELS;
window.THEMES = THEMES;
window.LounaUtils = LounaUtils;

// 🚀 INITIALISATION
console.log('🌐 Configuration globale Louna chargée');
console.log(`🧠 QI actuel: ${LOUNA_CONFIG.qi.current} (${LounaUtils.getQILevel()})`);
console.log(`🧠 Neurones: ${LOUNA_CONFIG.neurons.active}/${LOUNA_CONFIG.neurons.total}`);
console.log(`🚀 Accélérateurs KYBER: ${LOUNA_CONFIG.kyber.active}/${LOUNA_CONFIG.kyber.total}`);

// 📡 ÉVÉNEMENTS DE SYNCHRONISATION
window.addEventListener('lounaConfigUpdate', (event) => {
    console.log('🔄 Configuration mise à jour:', event.detail);
});

// 🔄 MISE À JOUR AUTOMATIQUE DU TEMPS DE FONCTIONNEMENT
setInterval(() => {
    LOUNA_CONFIG.system.uptime = LounaUtils.calculateUptime();
}, 1000);
