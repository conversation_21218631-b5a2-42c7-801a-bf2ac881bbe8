/**
 * Corrections essentielles pour l'interface de chat Louna
 * - Bouton d'envoi de message
 * - Boutons caméra
 * - Affichage des pensées
 */

console.log('🔧 Chargement des corrections essentielles du chat...');

// ===== CORRECTION 1: BOUTON D'ENVOI DE MESSAGE =====
function fixSendButton() {
    const sendButton = document.getElementById('sendButton');
    const messageInput = document.getElementById('messageInput');
    
    if (sendButton && messageInput) {
        console.log('✅ Bouton d\'envoi et champ de message trouvés');
        
        // Supprimer les anciens event listeners
        sendButton.replaceWith(sendButton.cloneNode(true));
        const newSendButton = document.getElementById('sendButton');
        
        // Ajouter le nouvel event listener
        newSendButton.addEventListener('click', function() {
            const message = messageInput.value.trim();
            if (message) {
                console.log('📤 Envoi du message:', message);
                sendMessageToAgent(message);
                messageInput.value = '';
            }
        });
        
        // Permettre l'envoi avec Entrée
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                newSendButton.click();
            }
        });
        
        console.log('✅ Bouton d\'envoi corrigé');
        showNotification('✅ Bouton d\'envoi corrigé', 'success');
    } else {
        console.log('❌ Bouton d\'envoi ou champ de message non trouvé');
        console.log('sendButton:', sendButton);
        console.log('messageInput:', messageInput);
    }
}

// ===== FONCTION D'ENVOI DE MESSAGE =====
function sendMessageToAgent(message) {
    console.log('🤖 Envoi vers l\'agent:', message);
    
    // Afficher le message de l'utilisateur
    addMessageToChat('Vous', message, 'user');
    
    // Envoyer au serveur
    fetch('/api/chat/message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        console.log('�� Réponse de l\'agent:', data);
        if (data.response) {
            addMessageToChat('Vision Ultra', data.response, 'agent');
        }
    })
    .catch(error => {
        console.error('❌ Erreur lors de l\'envoi:', error);
        addMessageToChat('Système', 'Erreur de connexion avec l\'agent', 'error');
    });
}

// ===== FONCTION D'AFFICHAGE DES MESSAGES =====
function addMessageToChat(sender, message, type) {
    const chatContainer = document.querySelector('.chat-messages') || 
                         document.querySelector('.messages') ||
                         document.querySelector('.chat-container');
    
    if (!chatContainer) {
        console.log('❌ Conteneur de chat non trouvé');
        return;
    }
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <div class="message-header">
            <strong>${sender}</strong>
            <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        </div>
        <div class="message-content">${message}</div>
    `;
    
    chatContainer.appendChild(messageElement);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// ===== CORRECTION 2: BOUTONS CAMÉRA ET PENSÉES =====
function addCameraControls() {
    console.log('📹 Ajout des contrôles de caméra...');
    
    // Chercher le conteneur input-wrapper pour ajouter les boutons
    const inputWrapper = document.querySelector('.input-wrapper');
    
    if (!inputWrapper) {
        console.log('❌ Conteneur input-wrapper non trouvé');
        return;
    }
    
    // Créer les boutons de caméra AVANT le bouton d'envoi
    const cameraControls = document.createElement('div');
    cameraControls.className = 'camera-controls';
    cameraControls.innerHTML = `
        <button id="toggleCamera" class="camera-button" title="Activer/Désactiver la caméra">📹</button>
        <button id="toggleThoughts" class="thoughts-button" title="Afficher/Masquer les pensées">💭</button>
    `;
    
    // Ajouter les styles
    const style = document.createElement('style');
    style.textContent = `
        .camera-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .camera-button, .thoughts-button {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(0, 0, 0, 0.3);
            color: #ffffff;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .camera-button:hover, .thoughts-button:hover {
            background: rgba(156, 137, 184, 0.3);
            border-color: #9c89b8;
            transform: scale(1.05);
        }
        .camera-button.active {
            background: #9c89b8;
            border-color: #9c89b8;
            box-shadow: 0 0 10px rgba(156, 137, 184, 0.5);
        }
        .thoughts-button.active {
            background: #9c89b8;
            border-color: #9c89b8;
            box-shadow: 0 0 10px rgba(156, 137, 184, 0.5);
        }
    `;
    document.head.appendChild(style);
    
    // Insérer les boutons avant le bouton d'envoi
    const sendButton = document.getElementById('sendButton');
    inputWrapper.insertBefore(cameraControls, sendButton);
    
    // Ajouter les event listeners
    document.getElementById('toggleCamera').addEventListener('click', toggleCamera);
    document.getElementById('toggleThoughts').addEventListener('click', toggleThoughts);
    
    console.log('✅ Contrôles de caméra ajoutés');
    showNotification('✅ Boutons caméra et pensées ajoutés', 'success');
}

// ===== GESTION DE LA CAMÉRA =====
let cameraStream = null;
let cameraActive = false;

function toggleCamera() {
    const button = document.getElementById('toggleCamera');
    
    if (cameraActive) {
        stopCamera();
        button.classList.remove('active');
        button.title = 'Activer la caméra';
    } else {
        startCamera();
        button.classList.add('active');
        button.title = 'Désactiver la caméra';
    }
}

async function startCamera() {
    try {
        console.log('📹 Démarrage de la caméra...');
        
        cameraStream = await navigator.mediaDevices.getUserMedia({
            video: { 
                width: { ideal: 640 }, 
                height: { ideal: 480 },
                aspectRatio: 16/9
            },
            audio: false
        });
        
        // Créer l'élément vidéo
        let videoElement = document.getElementById('cameraVideo');
        if (!videoElement) {
            videoElement = document.createElement('video');
            videoElement.id = 'cameraVideo';
            videoElement.autoplay = true;
            videoElement.muted = true;
            videoElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                height: 180px;
                border: 2px solid #9c89b8;
                border-radius: 12px;
                z-index: 1000;
                background: #000;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            `;
            document.body.appendChild(videoElement);
        }
        
        videoElement.srcObject = cameraStream;
        cameraActive = true;
        
        console.log('✅ Caméra démarrée');
        showNotification('📹 Caméra activée - Vision Ultra peut vous voir !', 'success');
        
    } catch (error) {
        console.error('❌ Erreur caméra:', error);
        showNotification('❌ Impossible d\'activer la caméra', 'error');
    }
}

function stopCamera() {
    if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
        cameraStream = null;
    }
    
    const videoElement = document.getElementById('cameraVideo');
    if (videoElement) {
        videoElement.remove();
    }
    
    cameraActive = false;
    console.log('📹 Caméra arrêtée');
    showNotification('📹 Caméra désactivée', 'info');
}

// ===== GESTION DES PENSÉES =====
let thoughtsVisible = false;
let thoughtInterval = null;

function toggleThoughts() {
    const button = document.getElementById('toggleThoughts');
    
    if (thoughtsVisible) {
        hideThoughts();
        button.classList.remove('active');
        button.title = 'Afficher les pensées';
    } else {
        showThoughts();
        button.classList.add('active');
        button.title = 'Masquer les pensées';
    }
}

function showThoughts() {
    let thoughtsContainer = document.getElementById('thoughtsContainer');
    
    if (!thoughtsContainer) {
        thoughtsContainer = document.createElement('div');
        thoughtsContainer.id = 'thoughtsContainer';
        thoughtsContainer.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            width: 380px;
            max-height: 500px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #9c89b8;
            border-radius: 12px;
            padding: 20px;
            z-index: 999;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #ffffff;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        `;
        
        thoughtsContainer.innerHTML = `
            <h3 style="color: #9c89b8; margin: 0 0 15px 0; font-size: 18px; text-align: center;">
                💭 Pensées de Vision Ultra
            </h3>
            <div id="thoughtsList"></div>
        `;
        
        document.body.appendChild(thoughtsContainer);
    }
    
    thoughtsContainer.style.display = 'block';
    thoughtsVisible = true;
    
    // Démarrer la génération de pensées
    startThoughtGeneration();
    
    console.log('💭 Pensées affichées');
    showNotification('💭 Pensées de Vision Ultra visibles', 'info');
}

function hideThoughts() {
    const thoughtsContainer = document.getElementById('thoughtsContainer');
    if (thoughtsContainer) {
        thoughtsContainer.style.display = 'none';
    }
    
    thoughtsVisible = false;
    stopThoughtGeneration();
    
    console.log('💭 Pensées masquées');
    showNotification('💭 Pensées masquées', 'info');
}

// ===== GÉNÉRATION DE PENSÉES =====
const thoughtTemplates = [
    { type: 'ANALYSE', content: 'Analyse de la conversation en cours...' },
    { type: 'MÉMOIRE', content: 'Consolidation des informations récentes...' },
    { type: 'RÉFLEXION', content: 'Évaluation des réponses possibles...' },
    { type: 'APPRENTISSAGE', content: 'Intégration de nouvelles connaissances...' },
    { type: 'CRÉATIVITÉ', content: 'Génération d\'idées innovantes...' },
    { type: 'EMPATHIE', content: 'Analyse de l\'état émotionnel de l\'utilisateur...' },
    { type: 'LOGIQUE', content: 'Vérification de la cohérence des données...' },
    { type: 'INTUITION', content: 'Exploration de possibilités non-évidentes...' },
    { type: 'OPTIMISATION', content: 'Amélioration des processus cognitifs...' },
    { type: 'SURVEILLANCE', content: 'Monitoring des systèmes internes...' }
];

function startThoughtGeneration() {
    if (thoughtInterval) return;
    
    // Ajouter une pensée initiale
    addThought('SYSTÈME', 'Vision Ultra activé - Surveillance cognitive démarrée');
    
    // Générer des pensées toutes les 6 secondes
    thoughtInterval = setInterval(() => {
        const randomThought = thoughtTemplates[Math.floor(Math.random() * thoughtTemplates.length)];
        addThought(randomThought.type, randomThought.content);
    }, 6000);
}

function stopThoughtGeneration() {
    if (thoughtInterval) {
        clearInterval(thoughtInterval);
        thoughtInterval = null;
    }
}

function addThought(type, content) {
    const thoughtsList = document.getElementById('thoughtsList');
    if (!thoughtsList) return;
    
    const thoughtElement = document.createElement('div');
    thoughtElement.style.cssText = `
        margin-bottom: 12px;
        padding: 12px;
        background: rgba(156, 137, 184, 0.15);
        border-left: 4px solid #9c89b8;
        border-radius: 6px;
        animation: fadeIn 0.5s ease-in;
    `;
    
    thoughtElement.innerHTML = `
        <div style="color: #9c89b8; font-weight: bold; font-size: 12px; margin-bottom: 4px;">[${type}]</div>
        <div style="color: #e0e0e0; line-height: 1.4;">${content}</div>
        <div style="color: #888; font-size: 11px; margin-top: 6px; text-align: right;">${new Date().toLocaleTimeString()}</div>
    `;
    
    // Ajouter au début
    thoughtsList.insertBefore(thoughtElement, thoughtsList.firstChild);
    
    // Limiter à 15 pensées
    const thoughts = thoughtsList.children;
    if (thoughts.length > 15) {
        thoughtsList.removeChild(thoughts[thoughts.length - 1]);
    }
}

// ===== FONCTION UTILITAIRE: NOTIFICATIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;
    notification.textContent = message;
    
    // Ajouter l'animation CSS
    if (!document.getElementById('notificationStyles')) {
        const animationStyle = document.createElement('style');
        animationStyle.id = 'notificationStyles';
        animationStyle.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(animationStyle);
    }
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }, 4000);
}

// ===== INITIALISATION =====
function initChatFixes() {
    console.log('🚀 Initialisation des corrections du chat...');
    
    // Attendre que la page soit chargée
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initChatFixes);
        return;
    }
    
    // Appliquer les corrections après un délai
    setTimeout(() => {
        fixSendButton();
        addCameraControls();
        
        console.log('✅ Corrections du chat appliquées');
        showNotification('✅ Interface de chat améliorée !', 'success');
    }, 1500);
}

// Démarrer immédiatement
initChatFixes();

console.log('🔧 Module de corrections du chat chargé');

// ===== AFFICHAGE DE LA DATE 2025 =====
function addDateDisplay() {
    console.log('📅 Ajout de l\'affichage de la date 2025...');
    
    // Créer l'affichage de la date
    const dateDisplay = document.createElement('div');
    dateDisplay.id = 'dateDisplay2025';
    dateDisplay.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: #ffffff;
        padding: 15px 20px;
        border-radius: 12px;
        border: 2px solid #9c89b8;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        z-index: 1000;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        min-width: 250px;
    `;
    
    // Ajouter le contenu initial
    dateDisplay.innerHTML = `
        <div style="color: #9c89b8; font-weight: bold; margin-bottom: 8px;">
            📅 ANNÉE ACTUELLE: 2025
        </div>
        <div id="currentDateTime" style="color: #e0e0e0; margin-bottom: 8px;">
            Chargement de la date...
        </div>
        <div style="color: #888; font-size: 12px;">
            🔍 Recherches automatiquement en 2025
        </div>
    `;
    
    document.body.appendChild(dateDisplay);
    
    // Mettre à jour la date
    updateDateDisplay();
    
    // Mettre à jour toutes les minutes
    setInterval(updateDateDisplay, 60000);
    
    console.log('✅ Affichage de la date 2025 ajouté');
}

function updateDateDisplay() {
    const dateTimeElement = document.getElementById('currentDateTime');
    if (!dateTimeElement) return;
    
    // Récupérer la date du serveur
    fetch('/api/chat/current-date')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.dateInfo) {
                dateTimeElement.innerHTML = `
                    🕒 ${data.dateInfo.currentDate}
                `;
            } else {
                // Fallback avec date locale
                const now = new Date();
                now.setFullYear(2025); // Forcer 2025
                dateTimeElement.innerHTML = `
                    �� ${now.toLocaleDateString('fr-FR', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}
                `;
            }
        })
        .catch(error => {
            console.log('⚠️ Erreur récupération date:', error);
            // Fallback avec date locale
            const now = new Date();
            now.setFullYear(2025); // Forcer 2025
            dateTimeElement.innerHTML = `
                🕒 ${now.toLocaleDateString('fr-FR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}
            `;
        });
}

// Modifier l'initialisation pour inclure la date
const originalInitChatFixes = initChatFixes;
initChatFixes = function() {
    originalInitChatFixes();
    
    // Ajouter l'affichage de la date après un délai
    setTimeout(() => {
        addDateDisplay();
        showNotification('📅 Système de calendrier 2025 activé !', 'info');
    }, 2000);
};
