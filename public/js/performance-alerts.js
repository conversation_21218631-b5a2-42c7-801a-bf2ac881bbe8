/**
 * Système d'alertes de performance pour l'application Louna
 *
 * Ce module surveille les performances des agents et de la mémoire thermique
 * et génère des alertes en cas de problèmes détectés.
 */

// Configuration des seuils d'alerte
const alertThresholds = {
    // Seuils pour la mémoire thermique
    memory: {
        temperature: {
            high: 0.9,    // Alerte si la température moyenne dépasse 0.9
            low: 0.2      // Alerte si la température moyenne est inférieure à 0.2
        },
        capacity: {
            critical: 0.9, // Alerte si une zone de mémoire est remplie à plus de 90%
            warning: 0.8   // Avertissement si une zone de mémoire est remplie à plus de 80%
        },
        cycleTime: 3600000 // Alerte si aucun cycle n'a été effectué depuis 1 heure (en ms)
    },

    // Seuils pour les accélérateurs Kyber
    accelerators: {
        efficiency: {
            critical: 0.5, // Alerte si l'efficacité est inférieure à 50%
            warning: 0.7   // Avertissement si l'efficacité est inférieure à 70%
        },
        stability: {
            critical: 0.6, // Alerte si la stabilité est inférieure à 60%
            warning: 0.8   // Avertissement si la stabilité est inférieure à 80%
        }
    },

    // Seuils pour les agents
    agents: {
        performance: {
            critical: 0.5, // Alerte si la performance est inférieure à 50%
            warning: 0.7   // Avertissement si la performance est inférieure à 70%
        },
        responseTime: {
            critical: 5000, // Alerte si le temps de réponse dépasse 5 secondes
            warning: 2000   // Avertissement si le temps de réponse dépasse 2 secondes
        }
    },

    // Seuils pour la synchronisation
    sync: {
        failureRate: {
            critical: 0.3, // Alerte si le taux d'échec de synchronisation dépasse 30%
            warning: 0.1   // Avertissement si le taux d'échec dépasse 10%
        },
        performanceImpact: {
            negative: -0.1 // Alerte si la synchronisation a un impact négatif sur les performances
        }
    }
};

// État des alertes
let alertState = {
    activeAlerts: [],
    dismissedAlerts: [],
    lastCheck: null
};

// Historique des alertes
let alertHistory = [];

// Initialisation du système d'alertes
function initializeAlerts() {
    console.log('Initialisation du système d\'alertes de performance');

    // Charger l'état des alertes depuis le stockage local
    loadAlertState();

    // Démarrer la vérification périodique
    startPeriodicCheck();

    // Ajouter les écouteurs d'événements
    addEventListeners();
}

/**
 * Charge l'état des alertes depuis le stockage local
 */
function loadAlertState() {
    try {
        const savedState = localStorage.getItem('performanceAlertState');
        if (savedState) {
            alertState = JSON.parse(savedState);
        }

        const savedHistory = localStorage.getItem('performanceAlertHistory');
        if (savedHistory) {
            alertHistory = JSON.parse(savedHistory);
        }
    } catch (error) {
        console.error('Erreur lors du chargement de l\'état des alertes:', error);
    }
}

/**
 * Sauvegarde l'état des alertes dans le stockage local
 */
function saveAlertState() {
    try {
        localStorage.setItem('performanceAlertState', JSON.stringify(alertState));
        localStorage.setItem('performanceAlertHistory', JSON.stringify(alertHistory));
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de l\'état des alertes:', error);
    }
}

/**
 * Démarre la vérification périodique des performances
 */
function startPeriodicCheck() {
    // Vérifier immédiatement
    checkPerformance();

    // Planifier les vérifications périodiques (toutes les 5 minutes)
    setInterval(checkPerformance, 5 * 60 * 1000);
}

/**
 * Ajoute les écouteurs d'événements
 */
function addEventListeners() {
    // Écouter les événements de cycle de mémoire
    if (window.thermalMemory) {
        window.thermalMemory.addListener((type) => {
            if (type === 'cycle') {
                checkMemoryPerformance();
            }
        });
    }

    // Écouter les événements des accélérateurs Kyber
    if (window.kyberAccelerators) {
        window.kyberAccelerators.addListener((type) => {
            if (type === 'update' || type === 'optimize') {
                checkAcceleratorPerformance();
            }
        });
    }

    // Écouter les événements de synchronisation
    document.addEventListener('syncCompleted', (event) => {
        checkSyncPerformance(event.detail);
    });
}

/**
 * Vérifie les performances globales
 */
async function checkPerformance() {
    console.log('Vérification des performances...');

    // Mettre à jour la date de dernière vérification
    alertState.lastCheck = Date.now();

    // Vérifier les différents composants
    await checkMemoryPerformance();
    await checkAcceleratorPerformance();
    await checkAgentPerformance();

    // Sauvegarder l'état des alertes
    saveAlertState();
}

/**
 * Vérifie les performances de la mémoire thermique
 */
async function checkMemoryPerformance() {
    try {
        // Récupérer les statistiques de la mémoire
        let memoryStats;

        if (window.thermalMemory) {
            memoryStats = window.thermalMemory.getStats();
        } else {
            const response = await fetch('/api/thermal/memory/stats');
            const data = await response.json();

            if (data.success) {
                memoryStats = data.stats;
            } else {
                throw new Error(data.error);
            }
        }

        // Vérifier la température moyenne
        if (memoryStats.averageTemperature > alertThresholds.memory.temperature.high) {
            createAlert('memory', 'temperature_high', 'critical',
                `Température moyenne élevée (${memoryStats.averageTemperature.toFixed(2)})`,
                'La température moyenne de la mémoire est trop élevée, ce qui peut indiquer une surcharge d\'informations importantes.');
        } else if (memoryStats.averageTemperature < alertThresholds.memory.temperature.low) {
            createAlert('memory', 'temperature_low', 'warning',
                `Température moyenne basse (${memoryStats.averageTemperature.toFixed(2)})`,
                'La température moyenne de la mémoire est trop basse, ce qui peut indiquer un manque d\'informations importantes.');
        }

        // Vérifier les capacités des zones de mémoire
        const zones = ['instant', 'shortTerm', 'workingMemory', 'mediumTerm', 'longTerm', 'dreamMemory'];

        for (const zone of zones) {
            if (memoryStats[`${zone}Capacity`]) {
                const capacity = memoryStats[`${zone}Entries`] / memoryStats[`${zone}Capacity`];

                if (capacity > alertThresholds.memory.capacity.critical) {
                    createAlert('memory', `capacity_${zone}_critical`, 'critical',
                        `Capacité critique de la zone ${zone} (${Math.round(capacity * 100)}%)`,
                        `La zone de mémoire ${zone} est presque pleine, ce qui peut entraîner des pertes d'informations.`);
                } else if (capacity > alertThresholds.memory.capacity.warning) {
                    createAlert('memory', `capacity_${zone}_warning`, 'warning',
                        `Capacité élevée de la zone ${zone} (${Math.round(capacity * 100)}%)`,
                        `La zone de mémoire ${zone} se remplit rapidement.`);
                }
            }
        }

        // Vérifier le dernier cycle de mémoire
        if (memoryStats.lastCycleTime) {
            const timeSinceLastCycle = Date.now() - memoryStats.lastCycleTime;

            if (timeSinceLastCycle > alertThresholds.memory.cycleTime) {
                createAlert('memory', 'cycle_time', 'warning',
                    'Aucun cycle de mémoire récent',
                    `Aucun cycle de mémoire n'a été effectué depuis ${Math.round(timeSinceLastCycle / 60000)} minutes.`);
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances de la mémoire:', error);
    }
}

/**
 * Vérifie les performances des accélérateurs Kyber
 */
async function checkAcceleratorPerformance() {
    try {
        // Récupérer les statistiques des accélérateurs
        let acceleratorStats;

        if (window.kyberAccelerators) {
            acceleratorStats = window.kyberAccelerators.getStats();
        } else {
            const response = await fetch('/api/kyber/stats');
            const data = await response.json();

            if (data.success) {
                acceleratorStats = data.stats;
            } else {
                throw new Error(data.error);
            }
        }

        // Vérifier l'efficacité
        if (acceleratorStats.efficiency < alertThresholds.accelerators.efficiency.critical) {
            createAlert('accelerators', 'efficiency_critical', 'critical',
                `Efficacité critique des accélérateurs (${Math.round(acceleratorStats.efficiency * 100)}%)`,
                'L\'efficacité des accélérateurs Kyber est très basse, ce qui peut affecter les performances globales.');
        } else if (acceleratorStats.efficiency < alertThresholds.accelerators.efficiency.warning) {
            createAlert('accelerators', 'efficiency_warning', 'warning',
                `Efficacité réduite des accélérateurs (${Math.round(acceleratorStats.efficiency * 100)}%)`,
                'L\'efficacité des accélérateurs Kyber est en baisse.');
        }

        // Vérifier la stabilité
        if (acceleratorStats.stability < alertThresholds.accelerators.stability.critical) {
            createAlert('accelerators', 'stability_critical', 'critical',
                `Stabilité critique des accélérateurs (${Math.round(acceleratorStats.stability * 100)}%)`,
                'La stabilité des accélérateurs Kyber est très basse, ce qui peut causer des comportements imprévisibles.');
        } else if (acceleratorStats.stability < alertThresholds.accelerators.stability.warning) {
            createAlert('accelerators', 'stability_warning', 'warning',
                `Stabilité réduite des accélérateurs (${Math.round(acceleratorStats.stability * 100)}%)`,
                'La stabilité des accélérateurs Kyber est en baisse.');
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances des accélérateurs:', error);
    }
}

/**
 * Vérifie les performances des agents
 */
async function checkAgentPerformance() {
    try {
        // Récupérer les statistiques des agents
        const response = await fetch('/api/agents/stats');
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error);
        }

        const agentStats = data.stats;

        // Vérifier les performances de chaque agent
        for (const agentId in agentStats) {
            const agent = agentStats[agentId];

            // Vérifier la performance
            if (agent.performance < alertThresholds.agents.performance.critical) {
                createAlert('agents', `performance_${agentId}_critical`, 'critical',
                    `Performance critique de l'agent ${agent.name} (${Math.round(agent.performance * 100)}%)`,
                    'La performance de l\'agent est très basse, ce qui peut affecter la qualité des réponses.');
            } else if (agent.performance < alertThresholds.agents.performance.warning) {
                createAlert('agents', `performance_${agentId}_warning`, 'warning',
                    `Performance réduite de l'agent ${agent.name} (${Math.round(agent.performance * 100)}%)`,
                    'La performance de l\'agent est en baisse.');
            }

            // Vérifier le temps de réponse
            if (agent.responseTime > alertThresholds.agents.responseTime.critical) {
                createAlert('agents', `response_time_${agentId}_critical`, 'critical',
                    `Temps de réponse critique de l'agent ${agent.name} (${agent.responseTime}ms)`,
                    'Le temps de réponse de l\'agent est très élevé, ce qui peut affecter l\'expérience utilisateur.');
            } else if (agent.responseTime > alertThresholds.agents.responseTime.warning) {
                createAlert('agents', `response_time_${agentId}_warning`, 'warning',
                    `Temps de réponse élevé de l'agent ${agent.name} (${agent.responseTime}ms)`,
                    'Le temps de réponse de l\'agent est en hausse.');
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances des agents:', error);
    }
}

/**
 * Vérifie les performances de synchronisation
 * @param {Object} syncResult - Résultat de la synchronisation
 */
function checkSyncPerformance(syncResult) {
    try {
        // Vérifier le taux d'échec
        const failureRate = syncResult.failedEntries / syncResult.totalEntries;

        if (failureRate > alertThresholds.sync.failureRate.critical) {
            createAlert('sync', 'failure_rate_critical', 'critical',
                `Taux d'échec critique de synchronisation (${Math.round(failureRate * 100)}%)`,
                'Un grand nombre d\'entrées n\'ont pas pu être synchronisées.');
        } else if (failureRate > alertThresholds.sync.failureRate.warning) {
            createAlert('sync', 'failure_rate_warning', 'warning',
                `Taux d'échec élevé de synchronisation (${Math.round(failureRate * 100)}%)`,
                'Certaines entrées n\'ont pas pu être synchronisées.');
        }

        // Vérifier l'impact sur les performances
        if (syncResult.performanceImpact < alertThresholds.sync.performanceImpact.negative) {
            createAlert('sync', 'negative_impact', 'warning',
                `Impact négatif de la synchronisation (${syncResult.performanceImpact.toFixed(2)})`,
                'La synchronisation a eu un impact négatif sur les performances des agents.');
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des performances de synchronisation:', error);
    }
}

/**
 * Crée une alerte de performance
 * @param {string} category - Catégorie de l'alerte (memory, accelerators, agents, sync)
 * @param {string} id - Identifiant unique de l'alerte
 * @param {string} severity - Sévérité de l'alerte (critical, warning, info)
 * @param {string} title - Titre de l'alerte
 * @param {string} message - Message détaillé de l'alerte
 */
function createAlert(category, id, severity, title, message) {
    // Vérifier si l'alerte existe déjà et n'a pas été ignorée
    const existingActiveAlert = alertState.activeAlerts.find(alert => alert.id === id);
    if (existingActiveAlert) {
        // Mettre à jour l'alerte existante
        existingActiveAlert.count++;
        existingActiveAlert.lastOccurrence = Date.now();
        return;
    }

    // Vérifier si l'alerte a été ignorée récemment
    const existingDismissedAlert = alertState.dismissedAlerts.find(alert => alert.id === id);
    if (existingDismissedAlert) {
        // Si l'alerte a été ignorée il y a moins de 24 heures, ne pas la recréer
        const timeSinceDismissal = Date.now() - existingDismissedAlert.dismissedAt;
        if (timeSinceDismissal < 24 * 60 * 60 * 1000) {
            return;
        }

        // Supprimer l'alerte de la liste des alertes ignorées
        alertState.dismissedAlerts = alertState.dismissedAlerts.filter(alert => alert.id !== id);
    }

    // Créer une nouvelle alerte
    const alert = {
        id,
        category,
        severity,
        title,
        message,
        createdAt: Date.now(),
        lastOccurrence: Date.now(),
        count: 1,
        read: false
    };

    // Ajouter l'alerte à la liste des alertes actives
    alertState.activeAlerts.push(alert);

    // Ajouter l'alerte à l'historique
    alertHistory.push({
        ...alert,
        timestamp: Date.now()
    });

    // Limiter la taille de l'historique
    if (alertHistory.length > 100) {
        alertHistory = alertHistory.slice(-100);
    }

    // Sauvegarder l'état des alertes
    saveAlertState();

    // Afficher l'alerte
    displayAlert(alert);
}

/**
 * Affiche une alerte à l'utilisateur
 * @param {Object} alert - Alerte à afficher
 */
function displayAlert(alert) {
    // Vérifier si l'élément de notification existe
    let alertContainer = document.getElementById('performance-alerts-container');

    // Créer le conteneur s'il n'existe pas
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'performance-alerts-container';
        alertContainer.className = 'performance-alerts-container';

        // Ajouter des styles au conteneur
        alertContainer.style.position = 'fixed';
        alertContainer.style.bottom = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.display = 'flex';
        alertContainer.style.flexDirection = 'column';
        alertContainer.style.gap = '10px';

        // Ajouter le conteneur au document
        document.body.appendChild(alertContainer);
    }

    // Créer l'élément d'alerte
    const alertElement = document.createElement('div');
    alertElement.className = `performance-alert performance-alert-${alert.severity}`;
    alertElement.dataset.alertId = alert.id;

    // Ajouter des styles à l'alerte
    alertElement.style.backgroundColor = alert.severity === 'critical' ? 'rgba(220, 53, 69, 0.9)' :
                                        alert.severity === 'warning' ? 'rgba(255, 193, 7, 0.9)' :
                                        'rgba(23, 162, 184, 0.9)';
    alertElement.style.color = '#fff';
    alertElement.style.padding = '15px';
    alertElement.style.borderRadius = '5px';
    alertElement.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    alertElement.style.maxWidth = '400px';
    alertElement.style.animation = 'fadeIn 0.3s ease-in-out';

    // Ajouter le contenu de l'alerte
    alertElement.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div style="font-weight: bold;">${alert.title}</div>
            <div style="display: flex; gap: 5px;">
                <button class="alert-action-btn" data-action="view" title="Voir les détails">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="alert-action-btn" data-action="dismiss" title="Ignorer">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div style="font-size: 14px; margin-bottom: 10px;">${alert.message}</div>
        <div style="font-size: 12px; opacity: 0.8;">
            ${new Date(alert.createdAt).toLocaleTimeString()} - ${getCategoryLabel(alert.category)}
        </div>
    `;

    // Ajouter des styles aux boutons
    const buttons = alertElement.querySelectorAll('.alert-action-btn');
    buttons.forEach(button => {
        button.style.background = 'none';
        button.style.border = 'none';
        button.style.color = '#fff';
        button.style.cursor = 'pointer';
        button.style.fontSize = '16px';
        button.style.opacity = '0.8';
        button.style.transition = 'opacity 0.2s';

        // Ajouter un effet de survol
        button.addEventListener('mouseover', () => {
            button.style.opacity = '1';
        });

        button.addEventListener('mouseout', () => {
            button.style.opacity = '0.8';
        });
    });

    // Ajouter les écouteurs d'événements
    const viewButton = alertElement.querySelector('[data-action="view"]');
    viewButton.addEventListener('click', () => {
        viewAlertDetails(alert);
    });

    const dismissButton = alertElement.querySelector('[data-action="dismiss"]');
    dismissButton.addEventListener('click', () => {
        dismissAlert(alert.id);
        alertElement.remove();
    });

    // Ajouter l'alerte au conteneur
    alertContainer.appendChild(alertElement);

    // Supprimer l'alerte après 10 secondes
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.style.animation = 'fadeOut 0.3s ease-in-out';
            setTimeout(() => {
                if (alertElement.parentNode) {
                    alertElement.remove();
                }
            }, 300);
        }
    }, 10000);

    // Jouer un son d'alerte si la sévérité est critique
    if (alert.severity === 'critical') {
        playAlertSound();
    }
}

/**
 * Affiche les détails d'une alerte
 * @param {Object} alert - Alerte à afficher
 */
function viewAlertDetails(alert) {
    // Marquer l'alerte comme lue
    const alertIndex = alertState.activeAlerts.findIndex(a => a.id === alert.id);
    if (alertIndex !== -1) {
        alertState.activeAlerts[alertIndex].read = true;
        saveAlertState();
    }

    // Rediriger vers la page de performances avec l'alerte sélectionnée
    window.location.href = `/performance-comparison.html?alert=${alert.id}`;
}

/**
 * Ignore une alerte
 * @param {string} alertId - Identifiant de l'alerte à ignorer
 */
function dismissAlert(alertId) {
    // Trouver l'alerte
    const alertIndex = alertState.activeAlerts.findIndex(alert => alert.id === alertId);
    if (alertIndex === -1) {
        return;
    }

    // Récupérer l'alerte
    const alert = alertState.activeAlerts[alertIndex];

    // Supprimer l'alerte de la liste des alertes actives
    alertState.activeAlerts.splice(alertIndex, 1);

    // Ajouter l'alerte à la liste des alertes ignorées
    alertState.dismissedAlerts.push({
        id: alert.id,
        dismissedAt: Date.now()
    });

    // Sauvegarder l'état des alertes
    saveAlertState();
}

/**
 * Joue un son d'alerte
 */
function playAlertSound() {
    // Vérifier si les sons sont activés
    const soundsEnabled = localStorage.getItem('enableAlertSounds') !== 'false';
    if (!soundsEnabled) {
        return;
    }

    // Créer un élément audio
    const audio = new Audio('/sounds/alert.mp3');
    audio.volume = 0.5;
    audio.play().catch(error => {
        console.error('Erreur lors de la lecture du son d\'alerte:', error);
    });
}

/**
 * Retourne le libellé d'une catégorie d'alerte
 * @param {string} category - Catégorie de l'alerte
 * @returns {string} - Libellé de la catégorie
 */
function getCategoryLabel(category) {
    switch (category) {
        case 'memory':
            return 'Mémoire Thermique';
        case 'accelerators':
            return 'Accélérateurs Kyber';
        case 'agents':
            return 'Agents';
        case 'sync':
            return 'Synchronisation';
        default:
            return category;
    }
}

// Exporter les fonctions
window.performanceAlerts = {
    initialize: initializeAlerts,
    check: checkPerformance,
    getActiveAlerts: () => alertState.activeAlerts,
    getDismissedAlerts: () => alertState.dismissedAlerts,
    getAlertHistory: () => alertHistory,
    dismissAlert: dismissAlert,
    viewAlertDetails: viewAlertDetails
};
