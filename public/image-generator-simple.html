<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'Images - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            flex: 1;
        }

        .image-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 14px;
        }

        .image-info {
            padding: 10px;
        }

        .image-prompt {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }

        .image-meta {
            font-size: 11px;
            color: #999;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff69b4;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            text-align: center;
            padding: 40px;
            color: #888;
            font-style: italic;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-image"></i>
            Générateur d'Images IA
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Panel de contrôles -->
        <div class="controls-panel">
            <div class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Paramètres de Génération
            </div>

            <form id="imageForm">
                <div class="form-group">
                    <label class="form-label" for="prompt">
                        <i class="fas fa-pen"></i> Description de l'image
                    </label>
                    <textarea 
                        id="prompt" 
                        class="form-input form-textarea" 
                        placeholder="Décrivez l'image que vous voulez générer... (ex: un chat robot futuriste dans un paysage cyberpunk)"
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="style">
                        <i class="fas fa-palette"></i> Style artistique
                    </label>
                    <select id="style" class="form-select">
                        <option value="realistic">Réaliste</option>
                        <option value="artistic">Artistique</option>
                        <option value="anime">Anime/Manga</option>
                        <option value="cyberpunk">Cyberpunk</option>
                        <option value="fantasy">Fantasy</option>
                        <option value="abstract">Abstrait</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="resolution">
                        <i class="fas fa-expand"></i> Résolution
                    </label>
                    <select id="resolution" class="form-select">
                        <option value="512x512">512x512 (Rapide)</option>
                        <option value="768x768">768x768 (Standard)</option>
                        <option value="1024x1024">1024x1024 (Haute qualité)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    Générer l'Image
                </button>
            </form>
        </div>

        <!-- Panel des résultats -->
        <div class="results-panel">
            <div class="results-title">
                <i class="fas fa-images"></i>
                Galerie d'Images Générées
            </div>

            <div class="image-grid" id="imageGrid">
                <div class="status-message">
                    <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>Aucune image générée pour le moment.</p>
                    <p>Utilisez le panneau de gauche pour créer votre première image !</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let generatedImages = [];
        let isGenerating = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Générateur d\'images simple initialisé');
            setupEventListeners();
            loadSavedImages();
        });

        function setupEventListeners() {
            const form = document.getElementById('imageForm');
            form.addEventListener('submit', handleImageGeneration);
        }

        async function handleImageGeneration(event) {
            event.preventDefault();
            
            if (isGenerating) return;
            
            const prompt = document.getElementById('prompt').value.trim();
            
            if (!prompt) {
                showNotification('Veuillez entrer une description pour l\'image', 'error');
                return;
            }

            isGenerating = true;
            updateGenerateButton(true);

            try {
                const imageData = {
                    prompt: prompt,
                    style: document.getElementById('style').value,
                    resolution: document.getElementById('resolution').value,
                    timestamp: new Date().toISOString()
                };

                // Générer l'image (version simplifiée)
                await generateImageSimple(imageData);
                
            } catch (error) {
                console.error('Erreur génération image:', error);
                showNotification('Erreur lors de la génération de l\'image', 'error');
            } finally {
                isGenerating = false;
                updateGenerateButton(false);
            }
        }

        async function generateImageSimple(imageData) {
            // Simuler un délai de génération
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Générer une image placeholder intelligente
            const width = parseInt(imageData.resolution.split('x')[0]) || 512;
            const height = parseInt(imageData.resolution.split('x')[1]) || 512;
            
            let imageUrl;
            switch (imageData.style) {
                case 'anime':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${Date.now()}&blur=1`;
                    break;
                case 'artistic':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${Date.now()}&grayscale`;
                    break;
                default:
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${Date.now()}`;
            }
            
            const newImage = {
                id: Date.now(),
                url: imageUrl,
                prompt: imageData.prompt,
                style: imageData.style,
                resolution: imageData.resolution,
                timestamp: imageData.timestamp,
                source: 'placeholder-simple'
            };
            
            generatedImages.unshift(newImage);
            addImageToGallery(newImage);
            saveImages();
            
            showNotification('Image générée avec succès !', 'success');
        }

        function addImageToGallery(imageData) {
            const grid = document.getElementById('imageGrid');
            
            // Supprimer le message de statut s'il existe
            const statusMessage = grid.querySelector('.status-message');
            if (statusMessage) {
                statusMessage.remove();
            }
            
            const imageCard = document.createElement('div');
            imageCard.className = 'image-card';
            imageCard.innerHTML = `
                <img src="${imageData.url}" alt="${imageData.prompt}" 
                     style="width: 100%; height: 200px; object-fit: cover;"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                <div class="image-placeholder" style="display: none;">
                    <i class="fas fa-image"></i>
                    Image non disponible
                </div>
                <div class="image-info">
                    <div class="image-prompt">${imageData.prompt}</div>
                    <div class="image-meta">
                        ${imageData.style} • ${imageData.resolution}
                    </div>
                </div>
            `;
            
            grid.insertBefore(imageCard, grid.firstChild);
        }

        function updateGenerateButton(generating) {
            const btn = document.getElementById('generateBtn');
            if (generating) {
                btn.innerHTML = '<div class="loading-spinner"></div> Génération en cours...';
                btn.disabled = true;
            } else {
                btn.innerHTML = '<i class="fas fa-magic"></i> Générer l\'Image';
                btn.disabled = false;
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            // Animation d'entrée
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Suppression automatique
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function saveImages() {
            localStorage.setItem('lounaGeneratedImages', JSON.stringify(generatedImages));
        }

        function loadSavedImages() {
            const saved = localStorage.getItem('lounaGeneratedImages');
            if (saved) {
                generatedImages = JSON.parse(saved);
                generatedImages.forEach(image => addImageToGallery(image));
            }
        }
    </script>
</body>
</html>
