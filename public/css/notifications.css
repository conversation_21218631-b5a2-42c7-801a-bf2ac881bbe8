/**
 * Système de notifications pour l'application Louna
 */

/* Conteneur des notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    max-width: 90%;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

/* Notification */
.notification {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--accent);
    display: flex;
    align-items: flex-start;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    pointer-events: auto;
    max-width: 100%;
    overflow: hidden;
    position: relative;
}

.notification.show {
    transform: translateX(0);
}

/* Types de notifications */
.notification.info {
    border-left-color: var(--info);
}

.notification.success {
    border-left-color: var(--success);
}

.notification.warning {
    border-left-color: var(--warning);
}

.notification.error {
    border-left-color: var(--danger);
}

/* Icône de la notification */
.notification-icon {
    margin-right: 12px;
    font-size: 20px;
    color: var(--text-primary);
}

.notification.info .notification-icon {
    color: var(--info);
}

.notification.success .notification-icon {
    color: var(--success);
}

.notification.warning .notification-icon {
    color: var(--warning);
}

.notification.error .notification-icon {
    color: var(--danger);
}

/* Contenu de la notification */
.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.notification-message {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Bouton de fermeture */
.notification-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
    transition: color 0.3s ease;
}

.notification-close:hover {
    color: var(--text-primary);
}

/* Barre de progression */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.2);
    width: 100%;
}

.notification-progress-bar {
    height: 100%;
    width: 100%;
    background-color: var(--accent);
    transform-origin: left;
    animation: progress-animation linear forwards;
}

.notification.info .notification-progress-bar {
    background-color: var(--info);
}

.notification.success .notification-progress-bar {
    background-color: var(--success);
}

.notification.warning .notification-progress-bar {
    background-color: var(--warning);
}

.notification.error .notification-progress-bar {
    background-color: var(--danger);
}

@keyframes progress-animation {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* Animation d'entrée et de sortie */
@keyframes notification-in {
    from {
        transform: translateX(120%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes notification-out {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(120%);
    }
}

.notification.show {
    animation: notification-in 0.3s ease forwards;
}

.notification.hide {
    animation: notification-out 0.3s ease forwards;
}
