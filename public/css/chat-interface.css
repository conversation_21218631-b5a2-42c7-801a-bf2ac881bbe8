/* ===== INTERFACE DE CHAT LOUNA - STYLES OPTIMISÉS ===== */

/* Variables CSS pour cohérence */
:root {
    --primary-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --accent-color: #ff69b4;
    --accent-gradient: linear-gradient(135deg, #ff69b4, #ff1493);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --bg-glass: rgba(255, 255, 255, 0.05);
    --bg-glass-strong: rgba(255, 255, 255, 0.1);
    --border-glass: rgba(255, 255, 255, 0.1);
    --shadow-glow: 0 4px 15px rgba(255, 105, 180, 0.3);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    overflow-x: hidden;
}

/* ===== SIDEBAR GAUCHE ===== */
.left-sidebar {
    width: 280px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-glass);
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
    position: relative;
    z-index: 100;
}

.sidebar-header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-glass);
}

.sidebar-title {
    color: var(--accent-color);
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
    letter-spacing: 1px;
}

.sidebar-subtitle {
    color: var(--text-muted);
    font-size: 12px;
    letter-spacing: 0.5px;
}

.sidebar-nav-section {
    margin-bottom: 20px;
}

.sidebar-nav-title {
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    border-radius: 10px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-smooth);
    font-size: 14px;
    margin-bottom: 5px;
}

.sidebar-nav-item:hover {
    background: rgba(255, 105, 180, 0.2);
    color: var(--text-primary);
    transform: translateX(5px);
}

.sidebar-nav-item.active {
    background: rgba(255, 105, 180, 0.3);
    color: var(--accent-color);
    border-left: 3px solid var(--accent-color);
    box-shadow: var(--shadow-glow);
}

.sidebar-nav-item i {
    width: 16px;
    text-align: center;
    font-size: 14px;
}

/* ===== CONTENU PRINCIPAL ===== */
.main-content {
    flex: 1;
    display: flex;
    margin: 20px;
    gap: 20px;
    min-height: calc(100vh - 40px);
}

/* ===== CHAT CONTAINER ===== */
.chat-container {
    flex: 2;
    display: flex;
    flex-direction: column;
    background: var(--bg-glass);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-glass);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.chat-header {
    background: var(--bg-glass-strong);
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-glass);
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-title i {
    color: var(--accent-color);
    font-size: 20px;
}

/* ===== MESSAGES ===== */
.chat-messages {
    flex-grow: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--accent-gradient);
    border-radius: 3px;
}

.message {
    max-width: 80%;
    padding: 12px 15px 12px 50px;
    border-radius: 18px;
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
    word-wrap: break-word;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 12px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 14px;
}

.message.user {
    align-self: flex-end;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: var(--text-primary);
    border-bottom-right-radius: 5px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.message.user::before {
    content: '\f007';
    background: linear-gradient(135deg, #0056b3, #004085);
    color: var(--text-primary);
}

.message.agent {
    align-self: flex-start;
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: var(--text-primary);
    border-bottom-left-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.message.agent::before {
    content: '\f544';
    background: linear-gradient(135deg, #1e7e34, #155724);
    color: var(--text-primary);
}

.message-content {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 5px;
}

.message-time {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    text-align: right;
    font-weight: 500;
}

/* ===== ZONE DE SAISIE ===== */
.chat-input-container {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid var(--border-glass);
    gap: 12px;
}

.chat-input {
    flex-grow: 1;
    padding: 12px 20px;
    border-radius: 25px;
    border: 1px solid var(--border-glass);
    background: var(--bg-glass-strong);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: var(--transition-smooth);
    font-weight: 500;
    resize: none;
}

.chat-input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.chat-input::placeholder {
    color: var(--text-muted);
}

.chat-send-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    font-size: 16px;
    box-shadow: var(--shadow-glow);
}

.chat-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
}

.chat-send-btn:active {
    transform: scale(0.95);
}

/* ===== INDICATEUR DE FRAPPE ===== */
.typing-indicator {
    display: none;
    align-items: center;
    padding: 10px 15px;
    background: var(--bg-glass);
    border-radius: 18px;
    align-self: flex-start;
    margin-top: 5px;
    border: 1px solid var(--border-glass);
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
    margin: 0 2px;
    animation: typingPulse 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingPulse {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* ===== BOUTON RETOUR ACCUEIL ===== */
.home-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--accent-gradient);
    color: white;
    padding: 12px 18px;
    border-radius: 25px;
    text-decoration: none;
    box-shadow: var(--shadow-glow);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    transition: var(--transition-smooth);
    font-size: 14px;
}

.home-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
    color: white;
    text-decoration: none;
}

.home-button:active {
    transform: translateY(0);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
    body {
        flex-direction: column;
    }
    
    .left-sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        overflow-x: auto;
        padding: 15px;
    }
    
    .sidebar-nav {
        display: flex;
        gap: 20px;
    }
    
    .sidebar-nav-section {
        margin-bottom: 0;
        min-width: 200px;
    }
    
    .main-content {
        flex-direction: column;
        margin: 10px;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin: 5px;
        gap: 10px;
    }
    
    .message {
        max-width: 90%;
        padding: 10px 12px 10px 45px;
    }
    
    .chat-input-container {
        padding: 10px;
    }
    
    .home-button {
        padding: 10px 15px;
        font-size: 12px;
    }
}
