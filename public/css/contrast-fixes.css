/* CORRECTIONS FINALES COMPLÈTES DE LISIBILITÉ - APPLICATION LOUNA */
/* Garantit une lisibilité parfaite sur toutes les 39 interfaces */

/* ===== CORRECTIONS GLOBALES OPTIMISÉES ===== */

/* Amélioration du rendu des polices - Performance maximale */
* {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* CORRECTION UNIVERSELLE - TOUS LES TEXTES LISIBLES */
h1, h2, h3, h4, h5, h6, p, span, div, li, a, label, button, input, textarea, select {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
    font-weight: 600 !important;
}

/* BOUTONS - LISIBILITÉ MAXIMALE GARANTIE */
button, .btn, .button, .toolbar-btn, .demo-button, .cta-button, .unified-button {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
    font-weight: bold !important;
}

button:hover, .btn:hover, .button:hover, .toolbar-btn:hover {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Correction des variables CSS pour un meilleur contraste */
:root {
    --text-primary: #ffffff !important;
    --text-secondary: #ffffff !important;
    --text-muted: rgba(255, 255, 255, 0.9) !important;
}

/* ===== CORRECTIONS SPÉCIFIQUES PAR ÉLÉMENT ===== */

/* Titres et textes principaux */
h1, h2, h3, h4, h5, h6,
.hero-title,
.hero-subtitle,
.interface-title,
.interface-subtitle,
.feature-title,
.cta-title,
.card-title {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* Textes de description et contenu */
p, span, div,
.feature-description,
.footer-text,
.message-content,
.stat-label,
.zone-name,
.accelerator-name {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

/* Valeurs et métriques */
.stat-value,
.zone-temp,
.accelerator-value,
.metric-value {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Navigation */
.nav-item,
.nav-item span,
.logo-text {
    color: #000000 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

.nav-item:hover {
    color: #333333 !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

/* Boutons */
.cta-button,
.action-button,
.control-btn,
.chat-send-btn,
.agent-dropdown-btn {
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

.cta-button.primary {
    background: #ff69b4 !important;
    color: #ffffff !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.cta-button.secondary {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #000000 !important;
    border: 2px solid rgba(255, 255, 255, 0.5) !important;
}

/* Cartes et conteneurs */
.feature-card,
.card,
.thermal-card,
.chat-container {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    backdrop-filter: blur(20px) !important;
}

.feature-card:hover,
.card:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
}

/* Messages de chat */
.message.user {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.message.agent {
    background-color: #28a745 !important;
    color: #ffffff !important;
}

.message-time {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Champs de saisie */
.chat-input,
input, textarea, select {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
}

.chat-input::placeholder,
input::placeholder,
textarea::placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Icônes */
.feature-icon,
.fas, .fab, .far,
i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* Barres de progression */
.progress-bar {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Liens */
a {
    color: #ff69b4 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

a:hover {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Badges et étiquettes */
.badge,
.tag,
.chip,
.card-badge {
    background: rgba(255, 105, 180, 0.3) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Notifications et alertes */
.notification,
.alert,
.message,
.info-box {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Tableaux */
table, tr, td, th {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Code et texte monospace */
code, pre, .monospace {
    background: rgba(0, 0, 0, 0.6) !important;
    color: #00ff00 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-weight: 500 !important;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 12px;
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 105, 180, 0.6);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 105, 180, 0.8);
}

/* États de focus */
input:focus,
textarea:focus,
select:focus,
button:focus {
    outline: 2px solid #ff69b4 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 10px rgba(255, 105, 180, 0.5) !important;
}

/* ===== CORRECTIONS SPÉCIFIQUES PAR PAGE ===== */

/* Page d'accueil */
.hero-section p,
.hero-section span,
.feature-card p,
.feature-card span,
.cta-section p,
.cta-section span {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

/* Chat */
.chat-title,
.agent-name,
.agent-model {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Sélecteur d'agent */
.agent-selector-btn,
.agent-dropdown-header {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* ===== CORRECTIONS D'URGENCE ===== */

/* Forcer la visibilité de tous les textes potentiellement invisibles */
.white-on-white-fix,
.invisible-text-fix {
    color: #ffffff !important;
    background: rgba(0, 0, 0, 0.3) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9) !important;
}

/* Correction pour les éléments avec couleur héritée problématique */
[style*="color: white"],
[style*="color: #fff"],
[style*="color: #ffffff"] {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Assurer la lisibilité sur tous les fonds */
.ensure-readability {
    color: #ffffff !important;
    background: rgba(0, 0, 0, 0.5) !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9) !important;
}

/* ===== CORRECTIONS SPÉCIFIQUES POUR LA NAVIGATION ===== */

/* Amélioration de la barre de navigation */
.top-navbar {
    background-color: #c8a2c8 !important; /* Rose/violet clair */
}

.logo-container i {
    color: #000000 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

/* Amélioration des icônes de navigation */
.nav-item i {
    color: #000000 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

/* ===== CORRECTIONS POUR LES ÉLÉMENTS SPÉCIFIQUES ===== */

/* Correction pour les sous-titres d'agent */
.agent-subtitle,
.agent-description,
.hero-subtitle {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
    background: rgba(0, 0, 0, 0.3) !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    display: inline-block !important;
}

/* Correction pour les textes dans les cartes */
.card p,
.card span,
.card div {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

/* Correction pour les labels et descriptions */
.description,
.subtitle,
.secondary-text {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    background: rgba(0, 0, 0, 0.2) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
}

/* ===== CORRECTIONS D'URGENCE POUR TEXTE INVISIBLE ===== */

/* Forcer la visibilité de tous les éléments de texte */
.force-visible {
    color: #ffffff !important;
    background: rgba(0, 0, 0, 0.5) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Correction pour les éléments avec couleur héritée problématique */
*[style*="color: white"],
*[style*="color: #fff"],
*[style*="color: #ffffff"],
.white-text {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
    background: rgba(0, 0, 0, 0.3) !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
}

/* Correction pour les éléments potentiellement invisibles */
.potentially-invisible,
.check-visibility {
    color: #ffffff !important;
    background: rgba(255, 105, 180, 0.2) !important;
    border: 1px solid rgba(255, 105, 180, 0.5) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* ===== AMÉLIORATION DES CONTRASTES GLOBAUX ===== */

/* Amélioration des fonds pour un meilleur contraste */
.improve-contrast {
    background: rgba(0, 0, 0, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    backdrop-filter: blur(10px) !important;
}

/* Amélioration des textes sur fonds clairs */
.light-background {
    color: #000000 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* Amélioration des textes sur fonds sombres */
.dark-background {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* ===== CORRECTIONS RESPONSIVES ===== */

@media (max-width: 768px) {
    /* Amélioration pour mobile */
    .nav-item span {
        color: #000000 !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
    }

    .hero-subtitle,
    .feature-description {
        font-size: 16px !important;
        line-height: 1.4 !important;
        padding: 6px 10px !important;
    }
}
