/**
 * Système de thème pour l'application Louna
 * Permet de basculer entre un thème sombre et un thème clair
 */

/* Variables pour le thème sombre (par défaut) */
:root {
    /* Couleurs de base */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-card: rgba(30, 30, 30, 0.7);
    --bg-gradient-start: rgba(25, 25, 50, 0.8);
    --bg-gradient-end: rgba(10, 10, 30, 0.9);

    /* Couleurs de texte */
    --text-primary: rgba(255, 255, 255, 0.87);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --text-tertiary: rgba(255, 255, 255, 0.4);

    /* Couleurs d'accent */
    --accent: #ff6b6b;
    --accent-light: #ff8e8e;
    --accent-dark: #e05050;
    --accent-glow: rgba(255, 107, 107, 0.4);

    /* Couleurs primaires */
    --primary: #3f51b5;
    --primary-light: #5c6bc0;
    --primary-dark: #303f9f;

    /* Couleurs secondaires */
    --secondary: #00bcd4;
    --secondary-light: #4dd0e1;
    --secondary-dark: #0097a7;

    /* Couleurs d'état */
    --success: #4caf50;
    --warning: #ff9800;
    --danger: #f44336;
    --info: #2196f3;

    /* Couleurs de température */
    --temp-hot: #ff6b6b;
    --temp-warm: #ffa502;
    --temp-cool: #2ed573;
    --temp-cold: #1e90ff;

    /* Bordures et ombres */
    --border-radius: 10px;
    --border-light: 1px solid rgba(255, 255, 255, 0.1);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    --shadow-hover: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);

    /* Effets */
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --glow-effect: 0 0 15px var(--accent-glow);

    /* En-têtes */
    --header-bg: #c8a2c8; /* Rose/violet clair */
    --header-border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Variables pour le thème clair */
[data-theme="light"] {
    /* Couleurs de base */
    --bg-primary: #f5f5f5;
    --bg-secondary: #e9e9e9;
    --bg-tertiary: #d5d5d5;
    --bg-card: rgba(255, 255, 255, 0.9); /* Plus opaque pour un meilleur contraste */
    --bg-gradient-start: rgba(230, 230, 250, 0.9); /* Plus opaque */
    --bg-gradient-end: rgba(240, 240, 255, 0.95); /* Plus opaque */

    /* Couleurs de texte */
    --text-primary: rgba(0, 0, 0, 0.9); /* Plus foncé pour un meilleur contraste */
    --text-secondary: rgba(0, 0, 0, 0.75); /* Plus foncé pour un meilleur contraste */
    --text-tertiary: rgba(0, 0, 0, 0.6); /* Plus foncé pour un meilleur contraste */

    /* Couleurs d'accent */
    --accent: #d81b60; /* Légèrement plus foncé pour un meilleur contraste */
    --accent-light: #e91e63;
    --accent-dark: #ad1457;
    --accent-glow: rgba(216, 27, 96, 0.4);

    /* Couleurs primaires */
    --primary: #303f9f; /* Légèrement plus foncé pour un meilleur contraste */
    --primary-light: #3f51b5;
    --primary-dark: #1a237e;

    /* Couleurs secondaires */
    --secondary: #0097a7; /* Légèrement plus foncé pour un meilleur contraste */
    --secondary-light: #00bcd4;
    --secondary-dark: #006064;

    /* Couleurs d'état */
    --success: #2e7d32; /* Plus foncé pour un meilleur contraste */
    --warning: #ef6c00; /* Plus foncé pour un meilleur contraste */
    --danger: #d32f2f; /* Plus foncé pour un meilleur contraste */
    --info: #1565c0; /* Plus foncé pour un meilleur contraste */

    /* Couleurs de température */
    --temp-hot: #c2185b; /* Plus foncé pour un meilleur contraste */
    --temp-warm: #ef6c00; /* Plus foncé pour un meilleur contraste */
    --temp-cool: #2e7d32; /* Plus foncé pour un meilleur contraste */
    --temp-cold: #0d47a1; /* Plus foncé pour un meilleur contraste */

    /* Bordures et ombres */
    --border-radius: 10px;
    --border-light: 1px solid rgba(0, 0, 0, 0.15); /* Plus visible */
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.15); /* Plus visible */
    --shadow-hover: 0 7px 14px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.2); /* Plus visible */

    /* Effets */
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --glow-effect: 0 0 15px var(--accent-glow);

    /* En-têtes */
    --header-bg: #d6a5d6; /* Rose/violet légèrement plus foncé pour un meilleur contraste */
    --header-border: 1px solid rgba(0, 0, 0, 0.1); /* Plus visible */
}

/* Styles pour le bouton de changement de thème */
.theme-switcher {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin: 0 10px;
}

.theme-switcher input {
    opacity: 0;
    width: 0;
    height: 0;
}

.theme-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-tertiary);
    transition: var(--transition-fast);
    border-radius: 30px;
    border: var(--border-light);
}

.theme-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: var(--text-primary);
    transition: var(--transition-fast);
    border-radius: 50%;
}

input:checked + .theme-slider {
    background-color: var(--accent);
}

input:checked + .theme-slider:before {
    transform: translateX(30px);
}

.theme-slider .theme-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    transition: var(--transition-fast);
}

.theme-slider .theme-icon.sun {
    left: 7px;
    color: var(--warning);
    opacity: 0;
}

.theme-slider .theme-icon.moon {
    right: 7px;
    color: var(--text-primary);
    opacity: 1;
}

input:checked + .theme-slider .theme-icon.sun {
    opacity: 1;
}

input:checked + .theme-slider .theme-icon.moon {
    opacity: 0;
}

/* Animation de transition entre les thèmes */
body {
    transition: background-color 0.5s ease, color 0.5s ease;
}

.card, .top-navbar, .footer, .main-container, .interface-header, .action-button,
.status-container, .grid-container, .grid-item, .dream-card, .chat-container,
.chat-header, .chat-input-container, .chat-messages, .message, .node-tooltip,
.graph-container, .graph-info, .graph-loading, .graph-empty, .settings-item,
.settings-section, .settings-group, .settings-control, .settings-button {
    transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
}
