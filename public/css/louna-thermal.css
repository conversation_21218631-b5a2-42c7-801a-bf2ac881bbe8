/**
 * Design futuriste et moderne pour l'Agent à Mémoire Thermique Louna
 * Interface révolutionnaire avec visualisations avancées
 */

:root {
    /* Palette de couleurs principale */
    --primary-dark: #0a1929;
    --primary: #0d47a1;
    --primary-light: #5e92f3;
    --secondary: #00bcd4;
    --accent: #00e5ff;
    --accent-glow: rgba(0, 229, 255, 0.4);

    /* Couleurs de la mémoire thermique */
    --temp-hot: #ff6b6b; /* Rouge pour les zones chaudes */
    --temp-warm: #ff9f43; /* Orange pour les zones tièdes */
    --temp-medium: #1dd1a1; /* Vert pour les zones moyennes */
    --temp-cool: #54a0ff; /* Bleu pour les zones froides */

    /* Couleurs de l'interface Louna */
    --header-bg: #c8a2c8; /* Barre de navigation rose/violet clair */
    --card-bg: #262640; /* Fond des cartes légèrement plus clair que le fond principal */

    /* Couleurs fonctionnelles */
    --success: #00e676;
    --warning: #ffab00;
    --danger: #ff1744;
    --info: #00b0ff;

    /* Couleurs de fond et texte */
    --bg-dark: #0a1929;
    --bg-main: #0f2942;
    --bg-light: #1a3b5d;
    --bg-card: rgba(26, 59, 93, 0.7);
    --bg-card-hover: rgba(30, 70, 110, 0.8);
    --text-primary: #ffffff;
    --text-secondary: #ffffff; /* Changé pour améliorer la lisibilité */
    --text-muted: rgba(255, 255, 255, 0.8); /* Changé pour améliorer la lisibilité */

    /* Effets et bordures */
    --glow-effect: 0 0 15px var(--accent-glow);
    --border-light: 1px solid rgba(255, 255, 255, 0.1);
    --border-accent: 1px solid rgba(0, 229, 255, 0.3);
    --border-radius-sm: 6px;
    --border-radius: 10px;
    --border-radius-lg: 16px;

    /* Taille de police */
    --font-size-factor: 1;

    /* Ombres */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Barre de navigation horizontale */
.top-navbar {
    height: 60px;
    background-color: var(--header-bg);
    display: flex;
    align-items: center;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.logo-container {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.logo-container i {
    font-size: 24px;
    margin-right: 10px;
}

.logo-text {
    font-size: 20px;
    font-weight: bold;
    color: #000000 !important; /* Noir plus foncé pour un meilleur contraste */
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

.nav-links {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    text-decoration: none;
    color: #000000 !important; /* Noir plus foncé pour un meilleur contraste */
    font-size: 12px;
    position: relative;
    transition: var(--transition-fast);
    font-weight: 600 !important; /* Plus gras pour améliorer la lisibilité */
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

.nav-item i {
    font-size: 18px;
    margin-bottom: 4px;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent);
}

.nav-item:hover {
    color: var(--primary);
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-right .nav-item {
    margin-left: 15px;
    margin-right: 0;
}

/* Conteneur principal */
.main-container {
    margin-top: 60px;
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    overflow-x: hidden;
}

/* En-tête de l'interface */
.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
}

.interface-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.interface-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 5px;
}

.status-container {
    display: flex;
    align-items: center;
}

.status-label {
    margin-right: 10px;
    font-size: 14px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--success);
    margin-right: 5px;
}

.status-text {
    font-size: 14px;
    margin-right: 15px;
}

.action-button {
    background-color: var(--accent);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition-fast);
}

.action-button:hover {
    background-color: var(--primary-light);
    box-shadow: var(--glow-effect);
}

/* Cartes */
.card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: var(--border-light);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    border: var(--border-accent);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.card-icon {
    font-size: 20px;
    margin-right: 10px;
    color: var(--accent);
}

.card-title {
    font-size: 18px;
    font-weight: bold;
}

.card-badge {
    background-color: var(--temp-warm);
    color: white;
    font-size: 10px;
    padding: 3px 8px;
    border-radius: 10px;
    margin-left: 10px;
}

/* Grilles et colonnes */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.grid-item {
    min-width: 0;
}

/* Barres de progression */
.progress-bar {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background-color: var(--temp-medium);
    border-radius: 4px;
    transition: width var(--transition);
}

.progress-fill.hot {
    background-color: var(--temp-hot);
}

.progress-fill.warm {
    background-color: var(--temp-warm);
}

.progress-fill.cool {
    background-color: var(--temp-cool);
}

/* Zones thermiques */
.thermal-zone {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.zone-name {
    font-size: 14px;
}

.zone-temp {
    font-size: 14px;
    font-weight: bold;
}

.zone-temp.hot {
    color: var(--temp-hot);
}

.zone-temp.warm {
    color: var(--temp-warm);
}

.zone-temp.medium {
    color: var(--temp-medium);
}

.zone-temp.cool {
    color: var(--temp-cool);
}

/* Statistiques */
.stats-container {
    display: flex;
    flex-direction: column;
}

.stat-item {
    margin-bottom: 15px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
}

.stat-badge {
    display: inline-block;
    background-color: rgba(29, 209, 161, 0.2);
    color: var(--temp-medium);
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 5px;
    margin-top: 5px;
}

/* Accélérateurs */
.accelerator-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.accelerator-name {
    font-size: 14px;
}

.accelerator-value {
    font-size: 14px;
    background-color: rgba(84, 160, 255, 0.2);
    color: var(--temp-cool);
    padding: 2px 8px;
    border-radius: 5px;
}

/* Responsive */
@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr;
    }

    .nav-item span {
        display: none;
    }

    .nav-item i {
        font-size: 20px;
        margin-bottom: 0;
    }
}

/* Hub Central - Bouton d'accès rapide */
.hub-access-button {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white !important;
    padding: 20px 40px;
    border-radius: 50px;
    text-decoration: none !important;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
    margin: 20px 0;
}

.hub-access-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
    text-decoration: none !important;
    color: white !important;
}

.hub-access-button:active {
    transform: translateY(-2px);
}

/* Navigation - Hub Central en évidence */
nav ul li a[href="/chat"] {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white !important;
    border-radius: 25px;
    padding: 10px 15px;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

nav ul li a[href="/chat"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}
