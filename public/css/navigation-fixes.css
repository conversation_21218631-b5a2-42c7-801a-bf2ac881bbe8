/* CORRECTIONS NAVIGATION - ACCESSIBILITÉ MAXIMALE */

/* Navigation principale */
.top-navbar {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(255, 20, 147, 0.3) !important;
    padding: 10px 20px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    height: 70px !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3) !important;
}

/* Logo */
.logo-container {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    color: #ff69b4 !important;
    font-size: 1.5em !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
}

.logo-text {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* Navigation links */
.nav-links {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    flex: 1 !important;
    justify-content: center !important;
    max-width: 800px !important;
}

.nav-item {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 12px 16px !important;
    color: #ffffff !important;
    text-decoration: none !important;
    border-radius: 25px !important;
    transition: all 0.3s ease !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-width: 120px !important;
    justify-content: center !important;
}

.nav-item:hover {
    background: rgba(255, 20, 147, 0.2) !important;
    color: #ff1493 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(255, 20, 147, 0.3) !important;
    border: 1px solid rgba(255, 20, 147, 0.4) !important;
}

.nav-item.active {
    background: linear-gradient(45deg, #ff1493, #ff69b4) !important;
    color: #ffffff !important;
    box-shadow: 0 5px 15px rgba(255, 20, 147, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.nav-item i {
    font-size: 1.1em !important;
    color: inherit !important;
}

.nav-item span {
    color: inherit !important;
    font-weight: inherit !important;
}

/* Menu déroulant amélioré */
.nav-dropdown {
    position: relative !important;
    display: inline-block !important;
}

.dropdown-toggle {
    cursor: pointer !important;
    position: relative !important;
}

.dropdown-arrow {
    margin-left: 8px !important;
    font-size: 0.8em !important;
    transition: transform 0.3s ease !important;
}

.dropdown-menu {
    position: absolute !important;
    top: calc(100% + 10px) !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-10px) !important;
    background: rgba(0, 0, 0, 0.98) !important;
    backdrop-filter: blur(30px) !important;
    border: 1px solid rgba(255, 20, 147, 0.4) !important;
    border-radius: 15px !important;
    min-width: 280px !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1001 !important;
    padding: 15px 0 !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(-50%) translateY(0) !important;
}

.nav-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg) !important;
}

.dropdown-item {
    display: flex !important;
    align-items: center !important;
    padding: 15px 25px !important;
    color: #ffffff !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    margin: 3px 15px !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.dropdown-item:hover {
    background: rgba(255, 20, 147, 0.25) !important;
    color: #ff1493 !important;
    transform: translateX(8px) !important;
    box-shadow: 0 3px 10px rgba(255, 20, 147, 0.2) !important;
}

.dropdown-item i {
    margin-right: 12px !important;
    width: 20px !important;
    text-align: center !important;
    color: #ff69b4 !important;
    font-size: 1.1em !important;
}

.dropdown-item span {
    color: inherit !important;
    font-weight: inherit !important;
}

/* Navigation droite */
.nav-right {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

/* Theme switcher */
.theme-switcher {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
}

.theme-toggle {
    display: none !important;
}

.theme-slider {
    width: 60px !important;
    height: 30px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    position: relative !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 8px !important;
}

.theme-icon {
    font-size: 0.9em !important;
    color: rgba(255, 255, 255, 0.7) !important;
    transition: all 0.3s ease !important;
}

/* Responsive design */
@media (max-width: 1400px) {
    .nav-item span {
        display: none !important;
    }
    
    .nav-item {
        min-width: 50px !important;
        padding: 12px !important;
    }
    
    .dropdown-menu {
        min-width: 220px !important;
    }
}

@media (max-width: 1000px) {
    .nav-links {
        gap: 10px !important;
    }
    
    .nav-item {
        min-width: 45px !important;
        padding: 10px !important;
    }
}

@media (max-width: 768px) {
    .top-navbar {
        padding: 10px 15px !important;
        height: 60px !important;
    }
    
    .nav-links {
        display: none !important;
    }
    
    .mobile-menu-toggle {
        display: block !important;
        background: rgba(255, 20, 147, 0.2) !important;
        border: 1px solid rgba(255, 20, 147, 0.4) !important;
        color: #ffffff !important;
        font-size: 1.2em !important;
        cursor: pointer !important;
        padding: 10px !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
    }
    
    .mobile-menu-toggle:hover {
        background: rgba(255, 20, 147, 0.3) !important;
        transform: scale(1.05) !important;
    }
}

/* Correction du décalage du contenu principal */
body {
    padding-top: 80px !important;
}

/* Scrollbar personnalisée pour le dropdown */
.dropdown-menu::-webkit-scrollbar {
    width: 6px !important;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 3px !important;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: rgba(255, 20, 147, 0.5) !important;
    border-radius: 3px !important;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 20, 147, 0.7) !important;
}

/* Animation d'entrée pour les éléments du dropdown */
.dropdown-item {
    animation: fadeInUp 0.3s ease forwards !important;
    opacity: 0 !important;
    transform: translateY(10px) !important;
}

.nav-dropdown:hover .dropdown-item:nth-child(1) { animation-delay: 0.05s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(2) { animation-delay: 0.1s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(3) { animation-delay: 0.15s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(4) { animation-delay: 0.2s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(5) { animation-delay: 0.25s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(6) { animation-delay: 0.3s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(7) { animation-delay: 0.35s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(8) { animation-delay: 0.4s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(9) { animation-delay: 0.45s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(10) { animation-delay: 0.5s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(11) { animation-delay: 0.55s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(12) { animation-delay: 0.6s !important; }
.nav-dropdown:hover .dropdown-item:nth-child(13) { animation-delay: 0.65s !important; }

@keyframes fadeInUp {
    to {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
}
