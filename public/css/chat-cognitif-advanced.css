/* ===== STYLES AVANCÉS POUR LE CHAT COGNITIF ===== */

/* Conteneur de notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    margin-bottom: 10px;
    padding: 15px;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left-color: #4ade80;
    background: linear-gradient(135deg, rgba(74, 222, 128, 0.1), rgba(0, 0, 0, 0.9));
}

.notification-error {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(0, 0, 0, 0.9));
}

.notification-warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(0, 0, 0, 0.9));
}

.notification-info {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(0, 0, 0, 0.9));
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.notification-message {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Métriques de performance */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 15px;
}

.metric-item {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.metric-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.metric-label {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 1.1rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.metric-good {
    color: #4ade80;
}

.metric-warning {
    color: #f59e0b;
}

.metric-critical {
    color: #ef4444;
    animation: pulse-critical 2s infinite;
}

.metric-normal {
    color: #ffffff;
}

@keyframes pulse-critical {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Accélérateurs KYBER */
.accelerator-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.accelerator-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.accelerator-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 107, 157, 0.3);
}

.accelerator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.accelerator-name {
    font-weight: 600;
    color: #ff6b9d;
}

.accelerator-boost {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.accelerator-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.accelerator-type {
    font-size: 0.8rem;
    opacity: 0.7;
}

.accelerator-status {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 8px;
}

.accelerator-status.active {
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
}

.accelerator-status.inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.accelerator-progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d, #c44569);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    opacity: 0.8;
    min-width: 35px;
    text-align: right;
}

/* Contrôles avancés */
.advanced-controls {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    margin-top: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.advanced-controls-header {
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.advanced-controls-header:hover {
    background: rgba(255, 255, 255, 0.1);
}

.advanced-controls-title {
    font-weight: 600;
    color: #ff6b9d;
}

.advanced-controls-toggle {
    transition: transform 0.3s ease;
}

.advanced-controls.expanded .advanced-controls-toggle {
    transform: rotate(180deg);
}

.advanced-controls-panel {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.advanced-controls.expanded .advanced-controls-panel {
    max-height: 500px;
}

.advanced-controls-content {
    padding: 15px;
}

/* Statut de connexion amélioré */
.chat-status.disconnected {
    color: #ef4444;
}

.chat-status.disconnected .status-dot {
    animation: pulse-error 1s infinite;
}

@keyframes pulse-error {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Boutons de contrôle améliorés */
.control-btn.recording {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: pulse-recording 1.5s infinite;
}

@keyframes pulse-recording {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.control-btn.processing {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Indicateurs de charge */
.loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ff6b9d;
    animation: spin 1s ease-in-out infinite;
    margin-left: 8px;
}

/* Améliorations des réflexions */
.reflection-item {
    position: relative;
    overflow: hidden;
}

.reflection-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: currentColor;
    opacity: 0.6;
}

.reflection-item.thinking::before { background: #3b82f6; }
.reflection-item.memory::before { background: #8b5cf6; }
.reflection-item.internet::before { background: #10b981; }
.reflection-item.response::before { background: #f59e0b; }
.reflection-item.complete::before { background: #ef4444; }

/* Animations fluides */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-right {
    animation: slideInRight 0.3s ease;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive amélioré */
@media (max-width: 768px) {
    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }

    .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
    }

    .accelerator-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* Sections de contrôle */
.control-section {
    margin-bottom: 20px;
}

.control-section h4 {
    color: #ff6b9d;
    font-size: 0.9rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.action-btn {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(196, 69, 105, 0.1));
    border: 1px solid rgba(255, 107, 157, 0.3);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.action-btn:hover {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(196, 69, 105, 0.2));
    border-color: rgba(255, 107, 157, 0.5);
    transform: translateY(-1px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn i {
    font-size: 0.9rem;
}

/* Thème sombre amélioré */
@media (prefers-color-scheme: dark) {
    .notification {
        background: rgba(0, 0, 0, 0.95);
    }

    .metric-item {
        background: rgba(255, 255, 255, 0.03);
    }

    .accelerator-item {
        background: rgba(255, 255, 255, 0.03);
    }

    .action-btn {
        background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(196, 69, 105, 0.05));
    }

    .action-btn:hover {
        background: linear-gradient(135deg, rgba(255, 107, 157, 0.15), rgba(196, 69, 105, 0.15));
    }
}
