/* ===== PANNEAU DE DROITE - STYLES OPTIMISÉS ===== */

/* ===== PANNEAU PRINCIPAL ===== */
.right-panel {
    width: 320px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    padding-right: 5px;
}

.right-panel::-webkit-scrollbar {
    width: 6px;
}

.right-panel::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.right-panel::-webkit-scrollbar-thumb {
    background: var(--accent-gradient);
    border-radius: 3px;
}

/* ===== ÉTAT DU SYSTÈME ===== */
.system-status {
    background: var(--bg-glass);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-glass);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.system-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.status-header h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.status-item {
    text-align: center;
    padding: 15px;
    background: var(--bg-glass);
    border-radius: 12px;
    border: 1px solid var(--border-glass);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.status-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.status-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.status-item:hover::before {
    transform: scaleX(1);
}

.status-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(255, 105, 180, 0.3);
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.status-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== PANNEAU DE RÉFLEXIONS ===== */
.thoughts-panel {
    display: flex;
    flex-direction: column;
    background: rgba(255, 105, 180, 0.1);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 105, 180, 0.3);
    max-height: 350px;
    box-shadow: 0 8px 32px rgba(255, 105, 180, 0.2);
}

.thoughts-header {
    background: rgba(255, 105, 180, 0.2);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 105, 180, 0.3);
    gap: 10px;
}

.thoughts-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.thoughts-header i {
    color: var(--accent-color);
    animation: brainPulse 2s ease-in-out infinite;
}

@keyframes brainPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.view-reflections-btn {
    background: rgba(255, 105, 180, 0.3);
    border: 1px solid rgba(255, 105, 180, 0.5);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition-smooth);
    font-weight: 500;
}

.view-reflections-btn:hover {
    background: rgba(255, 105, 180, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
}

.thoughts-content {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.4;
    scroll-behavior: smooth;
}

.thoughts-content::-webkit-scrollbar {
    width: 4px;
}

.thoughts-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

.thoughts-content::-webkit-scrollbar-thumb {
    background: rgba(255, 105, 180, 0.5);
    border-radius: 2px;
}

.thought-section {
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid rgba(255, 105, 180, 0.6);
    animation: thoughtFadeIn 0.3s ease-out;
    transition: var(--transition-smooth);
}

.thought-section:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(3px);
}

@keyframes thoughtFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.thought-title {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.thought-title i {
    font-size: 14px;
}

.thought-detail {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 6px;
    font-size: 12px;
}

/* ===== ACTIONS RAPIDES ===== */
.quick-actions {
    background: var(--bg-glass);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-glass);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.actions-header h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: 11px;
    font-weight: 500;
    text-align: center;
}

.action-btn:hover {
    background: rgba(255, 105, 180, 0.2);
    color: var(--text-primary);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.2);
}

.action-btn:active {
    transform: translateY(-1px);
}

.action-btn i {
    font-size: 18px;
    color: var(--accent-color);
    transition: var(--transition-smooth);
}

.action-btn:hover i {
    transform: scale(1.1);
    color: #ff1493;
}

/* ===== ACTIVITÉ RÉCENTE ===== */
.recent-activity {
    background: var(--bg-glass);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-glass);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.activity-header h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.activity-content {
    text-align: center;
}

.activity-item {
    padding: 12px;
    background: var(--bg-glass);
    border-radius: 8px;
    border: 1px solid var(--border-glass);
    transition: var(--transition-smooth);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.activity-text {
    color: var(--text-muted);
    font-size: 12px;
    font-style: italic;
}

/* ===== RESPONSIVE POUR PANNEAU DROIT ===== */
@media (max-width: 1200px) {
    .right-panel {
        width: 100%;
        flex-direction: row;
        gap: 15px;
        max-height: none;
        overflow-x: auto;
        padding-right: 0;
        padding-bottom: 10px;
    }
    
    .system-status,
    .thoughts-panel,
    .quick-actions,
    .recent-activity {
        min-width: 280px;
        flex-shrink: 0;
    }
    
    .thoughts-panel {
        max-height: 250px;
    }
}

@media (max-width: 768px) {
    .right-panel {
        flex-direction: column;
        gap: 10px;
    }
    
    .system-status,
    .thoughts-panel,
    .quick-actions,
    .recent-activity {
        min-width: auto;
    }
    
    .status-grid,
    .actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
    
    .status-item,
    .action-btn {
        padding: 10px 5px;
    }
    
    .status-value {
        font-size: 18px;
    }
    
    .status-label,
    .action-btn {
        font-size: 10px;
    }
}
