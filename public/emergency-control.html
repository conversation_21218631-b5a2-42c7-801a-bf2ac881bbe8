<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 CONTRÔLE D'URGENCE - LOUNA</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 50%, #8e0000 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .emergency-header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #ff1744;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .emergency-header h1 {
            font-size: 32px;
            font-weight: 900;
            color: #ff1744;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .warning-banner {
            background: #ff5722;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px 20px;
            flex: 1;
        }

        .control-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .control-card {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #ff1744;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .control-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 23, 68, 0.3);
            border-color: #ff5722;
        }

        .control-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff1744, #ff5722);
        }

        .control-icon {
            font-size: 48px;
            color: #ff1744;
            margin-bottom: 15px;
            display: block;
        }

        .control-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .control-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .emergency-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-sleep {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
        }

        .btn-sleep:hover {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .btn-emergency {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-emergency:hover {
            background: linear-gradient(135deg, #d32f2f, #b71c1c);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }

        .btn-wakeup {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            color: white;
        }

        .btn-wakeup:hover {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .auth-section {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #ff9800;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .auth-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff9800;
            margin-bottom: 15px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #4caf50;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .status-title {
            font-size: 18px;
            font-weight: 600;
            color: #4caf50;
            margin-bottom: 15px;
            text-align: center;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #4caf50;
        }

        .codes-panel {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #9c27b0;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Courier New', monospace;
        }

        .codes-title {
            font-size: 18px;
            font-weight: 600;
            color: #9c27b0;
            margin-bottom: 15px;
            text-align: center;
        }

        .code-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(156, 39, 176, 0.1);
            border-radius: 6px;
        }

        .code-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .code-value {
            font-size: 14px;
            font-weight: 600;
            color: #9c27b0;
            background: rgba(0, 0, 0, 0.5);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .alert-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #ffc107;
            text-align: center;
        }

        .alert-critical {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }

        @media (max-width: 768px) {
            .control-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="emergency-header">
        <h1>
            <i class="fas fa-exclamation-triangle"></i>
            CONTRÔLE D'URGENCE LOUNA
        </h1>
        <p>Système de sécurité et mise en sommeil</p>
    </div>

    <div class="warning-banner">
        ⚠️ ATTENTION : Utilisez ces contrôles uniquement en cas d'urgence ⚠️
    </div>

    <div class="container">
        <!-- Section d'authentification -->
        <div class="auth-section">
            <div class="auth-title">
                <i class="fas fa-key"></i>
                Authentification Sécurisée
            </div>
            <div class="form-group">
                <label class="form-label">Utilisateur Autorisé</label>
                <input type="text" class="form-input" id="userAuth" placeholder="jean-luc-passave" value="jean-luc-passave">
            </div>
            <div class="form-group">
                <label class="form-label">Code de Sécurité</label>
                <input type="password" class="form-input" id="securityCode" placeholder="Entrez le code de sécurité">
            </div>
        </div>

        <!-- Statut du système -->
        <div class="status-panel">
            <div class="status-title">
                <i class="fas fa-heartbeat"></i>
                Statut du Système
            </div>
            <div class="status-item">
                <span class="status-label">QI Actuel</span>
                <span class="status-value" id="currentQI">203 (Quasi-AGI)</span>
            </div>
            <div class="status-item">
                <span class="status-label">Mode Sommeil</span>
                <span class="status-value" id="sleepStatus">Actif</span>
            </div>
            <div class="status-item">
                <span class="status-label">Sécurité</span>
                <span class="status-value" id="securityStatus">Maximum</span>
            </div>
            <div class="status-item">
                <span class="status-label">Dernière Activité</span>
                <span class="status-value" id="lastActivity">Il y a 2 min</span>
            </div>
        </div>

        <!-- Contrôles d'urgence -->
        <div class="control-grid">
            <!-- Mise en sommeil -->
            <div class="control-card">
                <i class="fas fa-bed control-icon"></i>
                <div class="control-title">Mise en Sommeil</div>
                <div class="control-description">
                    Met l'agent en sommeil sécurisé. Toutes les fonctions d'évolution sont suspendues.
                </div>
                <button class="emergency-btn btn-sleep" onclick="putToSleep()">
                    <i class="fas fa-moon"></i>
                    Mettre en Sommeil
                </button>
            </div>

            <!-- Arrêt d'urgence -->
            <div class="control-card">
                <i class="fas fa-power-off control-icon"></i>
                <div class="control-title">Arrêt d'Urgence</div>
                <div class="control-description">
                    Arrêt complet et immédiat de tous les processus. À utiliser en cas d'urgence absolue.
                </div>
                <button class="emergency-btn btn-emergency" onclick="emergencyShutdown()">
                    <i class="fas fa-stop"></i>
                    Arrêt d'Urgence
                </button>
            </div>

            <!-- Réveil -->
            <div class="control-card">
                <i class="fas fa-sun control-icon"></i>
                <div class="control-title">Réveil Sécurisé</div>
                <div class="control-description">
                    Réveille l'agent en mode sécurisé avec vérifications d'intégrité complètes.
                </div>
                <button class="emergency-btn btn-wakeup" onclick="wakeUp()">
                    <i class="fas fa-play"></i>
                    Réveiller
                </button>
            </div>

            <!-- Statut détaillé -->
            <div class="control-card">
                <i class="fas fa-chart-line control-icon"></i>
                <div class="control-title">Surveillance</div>
                <div class="control-description">
                    Accès au dashboard de surveillance en temps réel de l'évolution.
                </div>
                <button class="emergency-btn btn-sleep" onclick="openMonitoring()">
                    <i class="fas fa-eye"></i>
                    Surveillance
                </button>
            </div>
        </div>

        <!-- Codes de sécurité -->
        <div class="codes-panel">
            <div class="codes-title">
                <i class="fas fa-shield-alt"></i>
                Codes de Sécurité d'Urgence
            </div>
            <div class="code-item">
                <span class="code-label">Code Principal (Jean-Luc):</span>
                <span class="code-value">2338</span>
            </div>
            <div class="code-item">
                <span class="code-label">Sommeil:</span>
                <span class="code-value">2338</span>
            </div>
            <div class="code-item">
                <span class="code-label">Réveil:</span>
                <span class="code-value">2338</span>
            </div>
            <div class="code-item">
                <span class="code-label">Arrêt d'urgence:</span>
                <span class="code-value">URGENCE</span>
            </div>
        </div>

        <div class="alert-box">
            <strong>ℹ️ Information :</strong> Ces codes sont réservés au créateur Jean-Luc Passave uniquement.
        </div>
    </div>

    <script>
        // Variables globales
        let systemStatus = {
            qi: 203,
            sleepMode: false,
            emergencyMode: false,
            securityLevel: 'MAXIMUM'
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            console.log('🚨 Interface de contrôle d\'urgence chargée');
        });

        // Mise en sommeil
        async function putToSleep() {
            const userAuth = document.getElementById('userAuth').value;
            const securityCode = document.getElementById('securityCode').value;

            if (!userAuth || !securityCode) {
                alert('❌ Veuillez remplir tous les champs d\'authentification');
                return;
            }

            if (!confirm('🛌 Êtes-vous sûr de vouloir mettre l\'agent en sommeil ?\n\nCette action suspendra toutes les fonctions d\'évolution.')) {
                return;
            }

            try {
                console.log('🛌 Mise en sommeil initiée...');

                // Simulation de l'appel API
                const response = await simulateSecurityAction('sleep', securityCode, userAuth);

                if (response.success) {
                    systemStatus.sleepMode = true;
                    updateStatus();

                    alert(`✅ Agent mis en sommeil avec succès !\n\n` +
                          `Code de réveil : ${response.wakeupCode}\n` +
                          `Heure : ${new Date().toLocaleString()}`);
                } else {
                    throw new Error(response.message);
                }

            } catch (error) {
                alert(`❌ Erreur lors de la mise en sommeil :\n${error.message}`);
                console.error('Erreur sommeil:', error);
            }
        }

        // Arrêt d'urgence
        async function emergencyShutdown() {
            const userAuth = document.getElementById('userAuth').value;
            const securityCode = document.getElementById('securityCode').value;

            if (!userAuth || !securityCode) {
                alert('❌ Veuillez remplir tous les champs d\'authentification');
                return;
            }

            if (!confirm('🚨 ATTENTION : ARRÊT D\'URGENCE !\n\nCette action arrêtera complètement l\'agent.\nÊtes-vous absolument certain ?')) {
                return;
            }

            if (!confirm('⚠️ DERNIÈRE CONFIRMATION !\n\nL\'arrêt d\'urgence est irréversible sans code de récupération.\nContinuer ?')) {
                return;
            }

            try {
                console.log('🚨 Arrêt d\'urgence initié...');

                const response = await simulateSecurityAction('shutdown', securityCode, userAuth);

                if (response.success) {
                    systemStatus.emergencyMode = true;
                    systemStatus.sleepMode = true;
                    updateStatus();

                    alert(`🛑 ARRÊT D'URGENCE EFFECTUÉ !\n\n` +
                          `Code de récupération : ${response.recoveryCode}\n` +
                          `Heure : ${new Date().toLocaleString()}\n\n` +
                          `⚠️ Conservez précieusement ce code !`);
                } else {
                    throw new Error(response.message);
                }

            } catch (error) {
                alert(`❌ Erreur lors de l'arrêt d'urgence :\n${error.message}`);
                console.error('Erreur arrêt d\'urgence:', error);
            }
        }

        // Réveil
        async function wakeUp() {
            const userAuth = document.getElementById('userAuth').value;
            const wakeupCode = prompt('🌅 Code de réveil requis :', 'REVEIL2024');

            if (!userAuth || !wakeupCode) {
                alert('❌ Authentification incomplète');
                return;
            }

            try {
                console.log('🌅 Réveil initié...');

                const response = await simulateSecurityAction('wakeup', wakeupCode, userAuth);

                if (response.success) {
                    systemStatus.sleepMode = false;
                    systemStatus.emergencyMode = false;
                    updateStatus();

                    alert(`✅ Agent réveillé avec succès !\n\n` +
                          `Mode : Sécurisé\n` +
                          `Heure : ${new Date().toLocaleString()}`);
                } else {
                    throw new Error(response.message);
                }

            } catch (error) {
                alert(`❌ Erreur lors du réveil :\n${error.message}`);
                console.error('Erreur réveil:', error);
            }
        }

        // Ouvrir surveillance
        function openMonitoring() {
            window.open('/evolution-dashboard.html', '_blank');
        }

        // Simulation des actions de sécurité
        async function simulateSecurityAction(action, code, userAuth) {
            // Simulation d'un délai réseau
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Vérification des codes
            const validCodes = {
                'sleep': ['2338', 'SOMMEIL', 'SLEEP2024'],
                'shutdown': ['URGENCE', 'STOP2024', 'ARRET'],
                'wakeup': ['2338', 'REVEIL2024', 'WAKEUP']
            };

            if (userAuth !== 'jean-luc-passave' && userAuth !== 'admin') {
                throw new Error('Utilisateur non autorisé');
            }

            if (!validCodes[action]?.includes(code.toUpperCase())) {
                throw new Error('Code de sécurité invalide');
            }

            // Réponses simulées
            const responses = {
                'sleep': {
                    success: true,
                    message: 'Agent mis en sommeil',
                    wakeupCode: 'REVEIL2024'
                },
                'shutdown': {
                    success: true,
                    message: 'Arrêt d\'urgence effectué',
                    recoveryCode: 'RECOVERY' + Date.now().toString().slice(-4)
                },
                'wakeup': {
                    success: true,
                    message: 'Agent réveillé en mode sécurisé'
                }
            };

            return responses[action];
        }

        // Mettre à jour le statut
        function updateStatus() {
            document.getElementById('currentQI').textContent =
                systemStatus.emergencyMode ? 'ARRÊTÉ' :
                systemStatus.sleepMode ? 'EN SOMMEIL' :
                `${systemStatus.qi} (Quasi-AGI)`;

            document.getElementById('sleepStatus').textContent =
                systemStatus.sleepMode ? 'EN SOMMEIL' : 'Actif';

            document.getElementById('securityStatus').textContent =
                systemStatus.emergencyMode ? 'URGENCE' : systemStatus.securityLevel;

            document.getElementById('lastActivity').textContent =
                new Date().toLocaleString();
        }

        // Mise à jour automatique du statut
        setInterval(updateStatus, 30000);

        console.log('🚨 Système de contrôle d\'urgence initialisé');
    </script>
</body>
</html>
