<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Intelligent - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <link rel="stylesheet" href="/css/chat-interface.css">
    <link rel="stylesheet" href="/css/right-panel.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Styles personnalisés pour cette page -->
    <style>
        /* Styles spécifiques pour l'intégration */
        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* === BARRE RÉTRACTABLE DES RÉFLEXIONS === */
        .reflections-sidebar {
            position: fixed;
            top: 0;
            right: -500px; /* Cachée par défaut */
            width: 500px;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            z-index: 10000;
            transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
            border-left: 2px solid rgba(255, 105, 180, 0.3);
            overflow: hidden;
        }

        .reflections-sidebar.open {
            right: 0; /* Visible quand ouverte */
        }

        .sidebar-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-header {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .sidebar-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .sidebar-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .sidebar-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .reflections-filter {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .filter-btn:hover {
            background: rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border-color: rgba(255, 105, 180, 0.8);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        .all-reflections-content {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 15px;
            min-height: 200px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .reflection-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .reflection-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(255, 105, 180, 0.2);
        }

        .reflection-item.expanded {
            background: rgba(255, 105, 180, 0.1);
            border-left-width: 6px;
        }

        .reflection-item.thinking { border-left-color: #ff69b4; }
        .reflection-item.analyzing { border-left-color: #00bcd4; }
        .reflection-item.learning { border-left-color: #4caf50; }
        .reflection-item.processing { border-left-color: #ff9800; }
        .reflection-item.decision { border-left-color: #9c27b0; }
        .reflection-item.memory { border-left-color: #f44336; }
        .reflection-item.connected { border-left-color: #2196f3; }
        .reflection-item.error { border-left-color: #e91e63; }

        .reflection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .reflection-type {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #ff69b4;
        }

        .reflection-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .reflection-content {
            color: white;
            line-height: 1.5;
            font-size: 0.95rem;
            position: relative;
        }

        .reflection-text {
            color: white;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .reflection-preview {
            display: block;
            max-height: 60px;
            overflow: hidden;
            position: relative;
        }

        .reflection-full {
            display: none;
            max-height: none;
            overflow: visible;
        }

        .reflection-item.expanded .reflection-preview {
            display: none;
        }

        .reflection-item.expanded .reflection-full {
            display: block;
        }

        .reflection-expand-hint {
            position: absolute;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            color: #ff69b4;
            font-weight: 600;
        }

        .reflection-item.expanded .reflection-expand-hint {
            display: none;
        }

        .reflection-collapse-btn {
            margin-top: 10px;
            padding: 5px 10px;
            background: rgba(255, 105, 180, 0.2);
            border: 1px solid rgba(255, 105, 180, 0.3);
            border-radius: 5px;
            color: #ff69b4;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .reflection-collapse-btn:hover {
            background: rgba(255, 105, 180, 0.3);
            transform: scale(1.05);
        }

        .sidebar-footer {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            flex-shrink: 0;
        }

        .sidebar-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85rem;
            flex: 1;
        }

        .sidebar-btn.primary {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            color: white;
        }

        .sidebar-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        /* Scrollbar personnalisée pour la sidebar */
        .sidebar-body::-webkit-scrollbar,
        .all-reflections-content::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-body::-webkit-scrollbar-track,
        .all-reflections-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .sidebar-body::-webkit-scrollbar-thumb,
        .all-reflections-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border-radius: 3px;
        }

        .sidebar-body::-webkit-scrollbar-thumb:hover,
        .all-reflections-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff1493, #c2185b);
        }

        /* Animation d'ouverture de la sidebar */
        .reflections-sidebar {
            animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                right: -500px;
                opacity: 0;
            }
            to {
                right: 0;
                opacity: 1;
            }
        }

        /* Responsive pour la barre rétractable */
        @media (max-width: 768px) {
            .reflections-sidebar {
                width: 100vw;
                right: -100vw;
            }

            .reflections-sidebar.open {
                right: 0;
            }

            .reflections-filter {
                justify-content: center;
            }

            .filter-btn {
                font-size: 0.75rem;
                padding: 5px 10px;
            }

            .sidebar-footer {
                flex-direction: column;
                gap: 8px;
            }

            .sidebar-btn {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 1024px) {
            .reflections-sidebar {
                width: 400px;
                right: -400px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-comments louna-header-icon"></i>
                <h1>Chat Intelligent avec Réflexions</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
                <a href="/brain-monitoring-complete.html" class="louna-nav-btn">
                    <i class="fas fa-brain"></i>
                    <span>Monitoring</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Agent connecté</span>
            </div>
        </div>
    </div>

    <!-- Sidebar gauche -->
    <div class="left-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">LOUNA</div>
            <div class="sidebar-subtitle">INTELLIGENCE ÉVOLUTIVE</div>
        </div>

        <div class="sidebar-nav">
            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">PRINCIPAL</div>
                <a href="/chat" class="sidebar-nav-item active">
                    <i class="fas fa-comments"></i>
                    <span>Chat Intelligent</span>
                </a>
                <a href="/generation-studio.html" class="sidebar-nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Génération</span>
                </a>
                <a href="/code-editor.html" class="sidebar-nav-item">
                    <i class="fas fa-code"></i>
                    <span>Développement</span>
                </a>
                <a href="/brain-visualization.html" class="sidebar-nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Analyse</span>
                </a>
            </div>

            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">SYSTÈME</div>
                <a href="/futuristic-interface.html" class="sidebar-nav-item">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire Thermique</span>
                </a>
                <a href="/qi-neuron-monitor.html" class="sidebar-nav-item">
                    <i class="fas fa-yin-yang"></i>
                    <span>Monitoring</span>
                </a>
                <a href="/agents" class="sidebar-nav-item">
                    <i class="fas fa-robot"></i>
                    <span>Agents</span>
                </a>
                <a href="/settings" class="sidebar-nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <!-- Chat -->
        <div class="chat-container">
            <div class="chat-header">
                <i class="fas fa-robot"></i>
                <div class="chat-title">Chat avec Louna</div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message agent">
                    <div class="message-content">Bonjour ! Je suis Louna, votre assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Posez-moi une question et vous pourrez voir mes réflexions internes !</div>
                    <div class="message-time">10:00</div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chat-input" placeholder="Salut comment vas-tu ?" autocomplete="off">
                <button class="chat-send-btn" id="chat-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>

        <!-- Panneau de droite -->
        <div class="right-panel">
            <!-- État du Système -->
            <div class="system-status">
                <div class="status-header">
                    <h3>État du Système</h3>
                </div>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">120</div>
                        <div class="status-label">QI</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">71</div>
                        <div class="status-label">Neurones</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">42°C</div>
                        <div class="status-label">Mémoire</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">84%</div>
                        <div class="status-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Réflexions Agent -->
            <div class="thoughts-panel" id="thoughts-panel">
                <div class="thoughts-header">
                    <i class="fas fa-brain"></i>
                    <h3>Réflexions Agent</h3>
                    <button class="view-reflections-btn" id="view-reflections-btn">
                        <i class="fas fa-eye"></i>
                        <span id="reflections-btn-text">Voir Réflexions</span>
                    </button>
                </div>
                <div class="thoughts-content" id="thoughts-content">
                    <div class="thought-section">
                        <div class="thought-title">
                            <i class="fas fa-lightbulb"></i>
                            En attente d'une question...
                        </div>
                        <div class="thought-detail">
                            Posez-moi une question et vous verrez ici toutes mes réflexions internes :
                            <ul>
                                <li>🔍 Analyse du type de question</li>
                                <li>🧠 Utilisation de la mémoire thermique</li>
                                <li>🌐 Recherche Internet si nécessaire</li>
                                <li>💭 Processus de réflexion étape par étape</li>
                                <li>📝 Construction du prompt enrichi</li>
                            </ul>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Actions Rapides -->
            <div class="quick-actions">
                <div class="actions-header">
                    <h3>Actions Rapides</h3>
                </div>
                <div class="actions-grid">
                    <button class="action-btn">
                        <i class="fas fa-robot"></i>
                        <span>Test Agent</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-sync"></i>
                        <span>Diagnostic</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-network-wired"></i>
                        <span>Net Reflexion</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-cogs"></i>
                        <span>Optimiser</span>
                    </button>
                </div>
            </div>

            <!-- Activité Récente -->
            <div class="recent-activity">
                <div class="activity-header">
                    <h3>Activité Récente</h3>
                </div>
                <div class="activity-content">
                    <div class="activity-item">
                        <div class="activity-text">Aucune activité récente</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 🧠 BARRE RÉTRACTABLE DES RÉFLEXIONS -->
    <div class="reflections-sidebar" id="reflections-sidebar">
        <div class="sidebar-content">
            <div class="sidebar-header">
                <h3><i class="fas fa-brain"></i> Toutes les Réflexions</h3>
                <button class="sidebar-close" id="close-reflections-sidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="sidebar-body">
                <div class="reflections-filter">
                    <button class="filter-btn active" data-filter="all">Toutes</button>
                    <button class="filter-btn" data-filter="thinking">Réflexion</button>
                    <button class="filter-btn" data-filter="analyzing">Analyse</button>
                    <button class="filter-btn" data-filter="learning">Apprentissage</button>
                    <button class="filter-btn" data-filter="processing">Traitement</button>
                    <button class="filter-btn" data-filter="decision">Décision</button>
                    <button class="filter-btn" data-filter="memory">Mémoire</button>
                </div>
                <div class="all-reflections-content" id="all-reflections-content">
                    <div class="reflection-item" data-reflection-id="demo">
                        <div class="reflection-header">
                            <div class="reflection-type">
                                <i class="fas fa-brain"></i>
                                <span>En attente</span>
                            </div>
                            <span class="reflection-time">--:--</span>
                        </div>
                        <div class="reflection-content">
                            <div class="reflection-preview">
                                Posez une question pour voir toutes mes réflexions détaillées ici. Cliquez sur cette zone pour voir le message complet...
                                <div class="reflection-expand-hint">Cliquer pour voir plus</div>
                            </div>
                            <div class="reflection-full">
                                Posez une question pour voir toutes mes réflexions détaillées ici. Cliquez sur cette zone pour voir le message complet.

                                Vous pourrez voir :
                                • Mon processus de réflexion complet
                                • L'analyse de votre question
                                • La recherche dans ma mémoire thermique
                                • Les connexions que j'établis
                                • Ma stratégie de réponse

                                Chaque réflexion est cliquable pour un affichage détaillé !
                                <button class="reflection-collapse-btn">Réduire</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sidebar-footer">
                <button class="sidebar-btn secondary" id="clear-reflections">
                    <i class="fas fa-trash"></i> Effacer
                </button>
                <button class="sidebar-btn primary" id="export-reflections">
                    <i class="fas fa-download"></i> Exporter
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Interface de chat avec réflexions initialisée');

            // Éléments du DOM
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const thoughtsContent = document.getElementById('thoughts-content');

            // Variables
            let messageHistory = [];
            let isConnected = false;
            let reflectionStream = null;
            let allReflections = []; // Stocker toutes les réflexions
            let currentFilter = 'all';

            // Initialiser la connexion aux réflexions
            initReflectionStream();

            // Vérifier le statut de l'agent au démarrage
            checkAgentStatus();

            // Fonction pour vérifier le statut de l'agent
            async function checkAgentStatus() {
                try {
                    const response = await fetch('/api/chat/status');
                    const data = await response.json();

                    if (data.success) {
                        isConnected = data.agent.isOnline;
                        updateThoughtsContent(`Agent ${isConnected ? 'en ligne' : 'hors ligne'} - ${data.agent.claudeAgent.name || 'Claude'}`, 'connected');

                        if (data.agent.capabilities) {
                            const capabilities = Object.keys(data.agent.capabilities).filter(cap => data.agent.capabilities[cap]);
                            updateThoughtsContent(`Capacités disponibles: ${capabilities.join(', ')}`, 'analyzing');
                        }
                    } else {
                        updateThoughtsContent('Erreur lors de la vérification du statut de l\'agent', 'error');
                    }
                } catch (error) {
                    console.error('Erreur statut agent:', error);
                    updateThoughtsContent('Impossible de vérifier le statut de l\'agent', 'error');
                }
            }

            // Fonction pour initialiser le stream de réflexions
            function initReflectionStream() {
                try {
                    reflectionStream = new EventSource('/api/agent/reflections/stream');

                    reflectionStream.onopen = () => {
                        console.log('🧠 Connexion aux réflexions établie');
                        updateThoughtsContent('Connexion aux réflexions établie...', 'connected');
                    };

                    reflectionStream.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            updateThoughtsContent(data.text, data.type);
                        } catch (error) {
                            console.error('Erreur parsing réflexion:', error);
                        }
                    };

                    reflectionStream.onerror = (error) => {
                        console.error('❌ Erreur stream réflexions:', error);
                        updateThoughtsContent('Erreur de connexion aux réflexions', 'error');
                    };
                } catch (error) {
                    console.error('❌ Erreur initialisation stream:', error);
                }
            }

            // Fonction pour mettre à jour les réflexions
            function updateThoughtsContent(text, type = 'thinking') {
                const iconMap = {
                    'thinking': 'fas fa-brain',
                    'analyzing': 'fas fa-search',
                    'learning': 'fas fa-graduation-cap',
                    'processing': 'fas fa-cogs',
                    'decision': 'fas fa-lightbulb',
                    'memory': 'fas fa-memory',
                    'connected': 'fas fa-wifi',
                    'error': 'fas fa-exclamation-triangle'
                };

                const icon = iconMap[type] || 'fas fa-brain';
                const timestamp = new Date().toLocaleTimeString();
                const fullTimestamp = new Date().toISOString();

                // Sauvegarder dans toutes les réflexions
                const reflection = {
                    id: Date.now() + Math.random(),
                    text: text,
                    type: type,
                    timestamp: fullTimestamp,
                    displayTime: timestamp,
                    icon: icon
                };
                allReflections.push(reflection);

                // Garder seulement les 100 dernières réflexions
                if (allReflections.length > 100) {
                    allReflections = allReflections.slice(-100);
                }

                const thoughtSection = document.createElement('div');
                thoughtSection.className = 'thought-section';
                thoughtSection.innerHTML = `
                    <div class="thought-title">
                        <i class="${icon}"></i>
                        ${type.charAt(0).toUpperCase() + type.slice(1)} - ${timestamp}
                    </div>
                    <div class="thought-detail">${text}</div>
                `;

                thoughtsContent.appendChild(thoughtSection);
                thoughtsContent.scrollTop = thoughtsContent.scrollHeight;

                // Garder seulement les 10 dernières réflexions dans l'affichage
                while (thoughtsContent.children.length > 10) {
                    thoughtsContent.removeChild(thoughtsContent.firstChild);
                }
            }

            // Fonction pour ajouter un message
            function addMessage(content, isUser = false, thoughts = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                const now = new Date();
                timeDiv.textContent = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);

                chatMessages.insertBefore(messageDiv, typingIndicator);
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Ajouter à l'historique
                messageHistory.push({
                    role: isUser ? 'user' : 'assistant',
                    content: content,
                    timestamp: new Date().toISOString()
                });

                // Garder seulement les 20 derniers messages
                if (messageHistory.length > 20) {
                    messageHistory = messageHistory.slice(-20);
                }

                return messageDiv;
            }

            // Fonction pour afficher l'indicateur de frappe
            function showTypingIndicator() {
                typingIndicator.style.display = 'flex';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Fonction pour cacher l'indicateur de frappe
            function hideTypingIndicator() {
                typingIndicator.style.display = 'none';
            }

            // Fonction pour envoyer un message à l'API
            async function sendToAgent(message) {
                try {
                    console.log('🚀 Envoi du message à l\'agent:', message);

                    // Afficher l'indicateur de frappe
                    showTypingIndicator();

                    // Ajouter une réflexion de traitement
                    updateThoughtsContent(`Traitement de votre message: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`, 'processing');

                    // Envoyer le message à l'API
                    const response = await fetch('/api/chat/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: message,
                            history: messageHistory.slice(-5), // Envoyer les 5 derniers messages
                            conversationId: 'chat-interface-' + Date.now()
                        })
                    });

                    const data = await response.json();
                    console.log('📦 Données reçues:', data);

                    // Cacher l'indicateur de frappe
                    hideTypingIndicator();

                    if (data.success && data.response) {
                        // Ajouter la réponse de l'agent
                        addMessage(data.response, false);

                        // Afficher les réflexions transmises par le serveur
                        if (data.reflections && data.reflections.length > 0) {
                            data.reflections.forEach(reflection => {
                                updateThoughtsContent(reflection.content, reflection.type);
                            });
                        }

                        // Afficher les métadonnées de traitement
                        if (data.metadata) {
                            updateThoughtsContent(`Réponse: ${data.metadata.responseLength} caractères`, 'memory');
                            if (data.metadata.hasReflections) {
                                updateThoughtsContent('Claude a utilisé ses capacités de réflexion avancées', 'thinking');
                            }
                            if (data.metadata.memoryUsed) {
                                updateThoughtsContent('🧠 Mémoire thermique consultée pour enrichir la réponse', 'memory');
                            }
                            if (data.metadata.memoriesSaved) {
                                updateThoughtsContent('💾 Conversation sauvegardée dans la mémoire thermique', 'memory');
                            }
                        }

                        // Afficher les informations de l'agent
                        if (data.agent) {
                            updateThoughtsContent(`Agent: ${data.agent.name} (${data.agent.model})`, 'memory');
                        }

                        // Ajouter une réflexion de succès
                        updateThoughtsContent('Réponse générée avec succès par Claude', 'decision');
                        updateRecentActivity(`Message traité par ${data.agent?.name || 'l\'agent'}`);

                        // Mettre à jour les statistiques si disponibles
                        if (data.usage) {
                            updateThoughtsContent(`Tokens utilisés: ${data.usage.total_tokens || 'N/A'}`, 'memory');
                        }

                        // Afficher si c'est un fallback
                        if (data.fallback) {
                            updateThoughtsContent('Mode fallback activé - Connexion Ollama temporairement indisponible', 'processing');
                        }
                    } else {
                        // Erreur de l'API
                        addMessage(`Erreur: ${data.error || 'Erreur inconnue'}`, false);
                        updateThoughtsContent(`Erreur lors du traitement: ${data.error || 'Erreur inconnue'}`, 'error');
                        showNotification(`Erreur: ${data.error || 'Erreur inconnue'}`, 'error');
                        updateRecentActivity('Erreur lors du traitement');
                    }
                } catch (error) {
                    console.error('💥 Erreur:', error);
                    hideTypingIndicator();
                    addMessage(`Erreur de connexion: ${error.message}`, false);
                    updateThoughtsContent(`Erreur de connexion: ${error.message}`, 'error');
                    showNotification('Erreur de connexion avec l\'agent', 'error');
                    updateRecentActivity('Erreur de connexion');
                }
            }

            // Fonction pour envoyer un message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Ajouter le message de l'utilisateur
                addMessage(message, true);

                // Effacer l'input
                chatInput.value = '';

                // Envoyer à l'agent
                sendToAgent(message);
            }

            // Fonction pour mettre à jour les statistiques système
            async function updateSystemStats() {
                try {
                    // Récupérer les métriques du chat
                    const response = await fetch('/api/chat/metrics');
                    const data = await response.json();

                    if (data) {
                        // Mettre à jour les statistiques affichées
                        const statusValues = document.querySelectorAll('.status-value');
                        const statusLabels = document.querySelectorAll('.status-label');

                        statusValues.forEach((element, index) => {
                            const label = statusLabels[index]?.textContent.toLowerCase();

                            switch (label) {
                                case 'qi':
                                    // Utiliser l'API centralisée du QI
                                    fetch('/api/qi/current')
                                        .then(response => response.json())
                                        .then(qiData => {
                                            element.textContent = qiData.qi || 148;
                                        })
                                        .catch(() => {
                                            element.textContent = 148; // Valeur de base de Jean-Luc Passave
                                        });
                                    break;
                                case 'neurones':
                                    // Simuler des neurones actifs
                                    element.textContent = Math.floor(50 + Math.random() * 50);
                                    break;
                                case 'mémoire':
                                    // Température de mémoire simulée
                                    element.textContent = Math.floor(35 + Math.random() * 15) + '°C';
                                    break;
                                case 'efficacité':
                                    // Efficacité basée sur les métriques
                                    const efficiency = (data.responses?.successRate || 0.95) * 100;
                                    element.textContent = Math.round(efficiency) + '%';
                                    break;
                            }
                        });

                        updateThoughtsContent('Statistiques système mises à jour', 'memory');
                    }
                } catch (error) {
                    console.error('Erreur mise à jour statistiques:', error);
                    // Fallback: utiliser l'API centralisée du QI
                    const qiElement = document.querySelector('.status-value');
                    if (qiElement) {
                        fetch('/api/qi/current')
                            .then(response => response.json())
                            .then(qiData => {
                                qiElement.textContent = qiData.qi || 148;
                            })
                            .catch(() => {
                                qiElement.textContent = 148; // Valeur de base de Jean-Luc Passave
                            });
                    }
                }
            }

            // Événements
            chatSendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // === 🧠 GESTION DE LA BARRE RÉTRACTABLE DES RÉFLEXIONS ===

            // Éléments de la barre rétractable
            const reflectionsSidebar = document.getElementById('reflections-sidebar');
            const closeReflectionsSidebar = document.getElementById('close-reflections-sidebar');
            const allReflectionsContent = document.getElementById('all-reflections-content');
            const clearReflectionsBtn = document.getElementById('clear-reflections');
            const exportReflectionsBtn = document.getElementById('export-reflections');
            const filterBtns = document.querySelectorAll('.filter-btn');

            // Bouton "Voir Réflexions" - NOUVELLE VERSION BARRE RÉTRACTABLE
            const viewReflectionsBtn = document.getElementById('view-reflections-btn');

            // 🧠 FONCTION POUR OUVRIR LA BARRE RÉTRACTABLE
            function openReflectionsSidebar() {
                console.log('📖 Ouverture de la barre rétractable des réflexions...');
                console.log(`📊 Nombre de réflexions disponibles: ${allReflections.length}`);

                // Ouvrir la barre rétractable
                reflectionsSidebar.classList.add('open');

                // Mettre à jour le contenu avec TOUTES les réflexions
                updateReflectionsSidebar();
                updateThoughtsContent(`Barre des réflexions ouverte - ${allReflections.length} réflexions disponibles`, 'decision');

                // Changer le texte du bouton
                const btnText = document.getElementById('reflections-btn-text');
                if (btnText) {
                    btnText.textContent = 'Fermer Réflexions';
                }

                // Ajouter une réflexion sur l'ouverture
                addReflection('Je vais maintenant afficher toutes mes réflexions détaillées dans la barre rétractable pour que vous puissiez comprendre mon processus de pensée complet.', 'analysis');
            }

            // 🧠 FONCTION POUR FERMER LA BARRE RÉTRACTABLE
            function closeReflectionsSidebarFunc() {
                reflectionsSidebar.classList.remove('open');
                updateThoughtsContent('Barre des réflexions fermée', 'decision');

                // Remettre le texte du bouton
                const btnText = document.getElementById('reflections-btn-text');
                if (btnText) {
                    btnText.textContent = 'Voir Réflexions';
                }
            }

            // Événements de fermeture
            if (closeReflectionsSidebar) {
                closeReflectionsSidebar.addEventListener('click', closeReflectionsSidebarFunc);
            }

            // Fermer avec Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && reflectionsSidebar.classList.contains('open')) {
                    closeReflectionsSidebarFunc();
                }
            });

            // Toggle avec le bouton principal
            if (viewReflectionsBtn) {
                viewReflectionsBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (reflectionsSidebar.classList.contains('open')) {
                        closeReflectionsSidebarFunc();
                    } else {
                        openReflectionsSidebar();
                    }
                });
            }

            // 🧠 FONCTION POUR METTRE À JOUR LE CONTENU DE LA BARRE RÉTRACTABLE
            function updateReflectionsSidebar() {
                if (!allReflectionsContent) return;

                // Filtrer les réflexions selon le filtre actuel
                const filteredReflections = currentFilter === 'all'
                    ? allReflections
                    : allReflections.filter(r => r.type === currentFilter);

                // Vider le contenu
                allReflectionsContent.innerHTML = '';

                if (filteredReflections.length === 0) {
                    allReflectionsContent.innerHTML = `
                        <div style="text-align: center; color: rgba(255,255,255,0.6); padding: 40px;">
                            <i class="fas fa-brain" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.3;"></i>
                            <p>Aucune réflexion ${currentFilter === 'all' ? '' : 'de type "' + currentFilter + '"'} disponible</p>
                            <p style="font-size: 0.9rem; opacity: 0.7;">Posez une question à l'agent pour voir ses réflexions !</p>
                        </div>
                    `;
                    return;
                }

                // Afficher les réflexions COMPLÈTES (les plus récentes en premier)
                const sortedReflections = [...filteredReflections].reverse();

                sortedReflections.forEach((reflection, index) => {
                    const reflectionElement = document.createElement('div');
                    reflectionElement.className = `reflection-item ${reflection.type}`;
                    reflectionElement.dataset.reflectionId = `reflection_${index}`;

                    // Couleurs par type de réflexion
                    const typeColors = {
                        'thinking': '#4ecdc4',
                        'analysis': '#45b7d1',
                        'processing': '#96ceb4',
                        'decision': '#feca57',
                        'learning': '#ff9ff3',
                        'memory': '#54a0ff',
                        'reflection': '#5f27cd',
                        'connected': '#00d2d3',
                        'error': '#ff6b6b'
                    };

                    const typeColor = typeColors[reflection.type] || '#ff6b9d';

                    // Tronquer le texte pour l'aperçu (150 caractères)
                    const previewText = reflection.text.length > 150
                        ? reflection.text.substring(0, 150) + '...'
                        : reflection.text;

                    // Style complet pour chaque réflexion
                    reflectionElement.style.cssText = `
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 15px;
                        padding: 20px;
                        margin-bottom: 15px;
                        border-left: 4px solid ${typeColor};
                        backdrop-filter: blur(10px);
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                        cursor: pointer;
                        position: relative;
                    `;

                    reflectionElement.innerHTML = `
                        <div class="reflection-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div class="reflection-type" style="display: flex; align-items: center; gap: 8px;">
                                <span style="
                                    background: ${typeColor};
                                    color: white;
                                    padding: 6px 12px;
                                    border-radius: 20px;
                                    font-size: 12px;
                                    font-weight: 700;
                                    text-transform: uppercase;
                                    letter-spacing: 1px;
                                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                                ">
                                    <i class="${reflection.icon || 'fas fa-brain'}"></i>
                                    ${reflection.type.charAt(0).toUpperCase() + reflection.type.slice(1)}
                                </span>
                            </div>
                            <span class="reflection-time" style="
                                color: rgba(255, 255, 255, 0.8);
                                font-size: 12px;
                                font-family: 'Courier New', monospace;
                                background: rgba(0, 0, 0, 0.3);
                                padding: 3px 6px;
                                border-radius: 6px;
                            ">${reflection.displayTime}</span>
                        </div>
                        <div class="reflection-content">
                            <div class="reflection-preview" style="
                                color: white;
                                line-height: 1.6;
                                font-size: 14px;
                                white-space: pre-wrap;
                                word-wrap: break-word;
                                max-height: 60px;
                                overflow: hidden;
                                position: relative;
                                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            ">
                                ${previewText}
                                ${reflection.text.length > 150 ? '<div class="reflection-expand-hint" style="position: absolute; bottom: 0; right: 0; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1)); padding: 2px 8px; border-radius: 4px; font-size: 11px; color: #ff69b4; font-weight: 600;">Cliquer pour voir plus</div>' : ''}
                            </div>
                            <div class="reflection-full" style="
                                display: none;
                                color: white;
                                line-height: 1.7;
                                font-size: 14px;
                                white-space: pre-wrap;
                                word-wrap: break-word;
                                max-height: none;
                                overflow: visible;
                                text-align: justify;
                                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            ">
                                ${reflection.text}
                                <button class="reflection-collapse-btn" style="
                                    margin-top: 15px;
                                    padding: 8px 15px;
                                    background: rgba(255, 105, 180, 0.2);
                                    border: 1px solid rgba(255, 105, 180, 0.3);
                                    border-radius: 8px;
                                    color: #ff69b4;
                                    font-size: 12px;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    display: flex;
                                    align-items: center;
                                    gap: 5px;
                                ">
                                    <i class="fas fa-chevron-up"></i>
                                    Réduire
                                </button>
                                <div style="
                                    margin-top: 15px;
                                    padding-top: 15px;
                                    border-top: 1px solid rgba(255, 255, 255, 0.2);
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    font-size: 11px;
                                    color: rgba(255, 255, 255, 0.6);
                                ">
                                    <span>Réflexion #${index + 1}</span>
                                    <span>${reflection.text.length} caractères</span>
                                    <span>Type: ${reflection.type}</span>
                                </div>
                            </div>
                        </div>
                    `;

                    // Effet hover amélioré
                    reflectionElement.addEventListener('mouseenter', () => {
                        if (!reflectionElement.classList.contains('expanded')) {
                            reflectionElement.style.background = 'rgba(255, 255, 255, 0.15)';
                            reflectionElement.style.transform = 'translateX(5px)';
                            reflectionElement.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.3)';
                        }
                    });

                    reflectionElement.addEventListener('mouseleave', () => {
                        if (!reflectionElement.classList.contains('expanded')) {
                            reflectionElement.style.background = 'rgba(255, 255, 255, 0.1)';
                            reflectionElement.style.transform = 'translateX(0)';
                            reflectionElement.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                        }
                    });

                    // 🎯 GESTION DU CLIC POUR EXPANSION/RÉDUCTION
                    reflectionElement.addEventListener('click', (e) => {
                        // Empêcher la propagation si on clique sur le bouton de réduction
                        if (e.target.classList.contains('reflection-collapse-btn') || e.target.closest('.reflection-collapse-btn')) {
                            return;
                        }

                        const isExpanded = reflectionElement.classList.contains('expanded');

                        if (isExpanded) {
                            // Réduire
                            collapseReflection(reflectionElement);
                        } else {
                            // Étendre
                            expandReflection(reflectionElement, typeColor);
                        }
                    });

                    // Gestion du bouton de réduction
                    const collapseBtn = reflectionElement.querySelector('.reflection-collapse-btn');
                    if (collapseBtn) {
                        collapseBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            collapseReflection(reflectionElement);
                        });

                        collapseBtn.addEventListener('mouseenter', () => {
                            collapseBtn.style.background = 'rgba(255, 105, 180, 0.3)';
                            collapseBtn.style.transform = 'scale(1.05)';
                        });

                        collapseBtn.addEventListener('mouseleave', () => {
                            collapseBtn.style.background = 'rgba(255, 105, 180, 0.2)';
                            collapseBtn.style.transform = 'scale(1)';
                        });
                    }

                    allReflectionsContent.appendChild(reflectionElement);
                });

                // 🎯 FONCTIONS D'EXPANSION ET DE RÉDUCTION
                function expandReflection(element, typeColor) {
                    element.classList.add('expanded');

                    // Changer l'apparence
                    element.style.background = 'rgba(255, 105, 180, 0.1)';
                    element.style.borderLeftWidth = '6px';
                    element.style.borderLeftColor = typeColor;
                    element.style.transform = 'scale(1.02)';
                    element.style.boxShadow = '0 8px 30px rgba(255, 105, 180, 0.3)';

                    // Masquer l'aperçu et afficher le contenu complet
                    const preview = element.querySelector('.reflection-preview');
                    const full = element.querySelector('.reflection-full');

                    if (preview && full) {
                        preview.style.display = 'none';
                        full.style.display = 'block';
                    }

                    // Notification
                    showNotification('Réflexion étendue - Cliquez à nouveau pour réduire', 'info');
                }

                function collapseReflection(element) {
                    element.classList.remove('expanded');

                    // Remettre l'apparence normale
                    element.style.background = 'rgba(255, 255, 255, 0.1)';
                    element.style.borderLeftWidth = '4px';
                    element.style.transform = 'scale(1)';
                    element.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';

                    // Afficher l'aperçu et masquer le contenu complet
                    const preview = element.querySelector('.reflection-preview');
                    const full = element.querySelector('.reflection-full');

                    if (preview && full) {
                        preview.style.display = 'block';
                        full.style.display = 'none';
                    }
                }
            }

            // Gestion des filtres
            filterBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Retirer la classe active des autres boutons
                    filterBtns.forEach(b => b.classList.remove('active'));
                    // Ajouter la classe active au bouton cliqué
                    btn.classList.add('active');

                    // Mettre à jour le filtre
                    currentFilter = btn.dataset.filter;

                    // Mettre à jour l'affichage
                    updateReflectionsSidebar();

                    updateThoughtsContent(`Filtre appliqué: ${currentFilter}`, 'decision');
                });
            });

            // Fonction pour effacer les réflexions
            if (clearReflectionsBtn) {
                clearReflectionsBtn.addEventListener('click', () => {
                    if (confirm('Êtes-vous sûr de vouloir effacer toutes les réflexions ?')) {
                        allReflections = [];
                        updateReflectionsSidebar();
                        updateThoughtsContent('Toutes les réflexions ont été effacées', 'memory');
                        showNotification('Réflexions effacées', 'success');
                    }
                });
            }

            // Fonction pour exporter les réflexions
            if (exportReflectionsBtn) {
                exportReflectionsBtn.addEventListener('click', () => {
                    exportReflections();
                });
            }

            // Fonction d'export
            function exportReflections() {
                if (allReflections.length === 0) {
                    showNotification('Aucune réflexion à exporter', 'error');
                    return;
                }

                const filteredReflections = currentFilter === 'all'
                    ? allReflections
                    : allReflections.filter(r => r.type === currentFilter);

                const exportData = {
                    exportDate: new Date().toISOString(),
                    filter: currentFilter,
                    totalReflections: filteredReflections.length,
                    reflections: filteredReflections.map(r => ({
                        type: r.type,
                        text: r.text,
                        timestamp: r.timestamp,
                        displayTime: r.displayTime
                    }))
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `louna-reflexions-${currentFilter}-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                updateThoughtsContent(`Export de ${filteredReflections.length} réflexions (${currentFilter})`, 'memory');
                showNotification(`${filteredReflections.length} réflexions exportées`, 'success');
            }

            // Boutons d'actions rapides
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.querySelector('span').textContent;
                    updateThoughtsContent(`Action rapide exécutée: ${action}`, 'processing');
                    showNotification(`Exécution de l'action: ${action}`, 'info');
                    updateRecentActivity(`Action: ${action}`);

                    // Simuler l'exécution de l'action
                    setTimeout(() => {
                        updateThoughtsContent(`Action "${action}" terminée avec succès`, 'decision');
                        showNotification(`Action "${action}" terminée`, 'success');
                        updateRecentActivity(`${action} terminé avec succès`);
                    }, 1000);
                });
            });

            // Mettre à jour les statistiques périodiquement
            setInterval(updateSystemStats, 30000); // Toutes les 30 secondes

            // Fonction pour afficher des notifications
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                `;

                // Styles pour la notification
                Object.assign(notification.style, {
                    position: 'fixed',
                    top: '80px',
                    right: '20px',
                    background: type === 'success' ? 'rgba(40, 167, 69, 0.9)' :
                               type === 'error' ? 'rgba(220, 53, 69, 0.9)' :
                               'rgba(23, 162, 184, 0.9)',
                    color: 'white',
                    padding: '12px 20px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    zIndex: '1001',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',
                    animation: 'slideInRight 0.3s ease-out'
                });

                document.body.appendChild(notification);

                // Supprimer après 3 secondes
                setTimeout(() => {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // Ajouter les animations CSS pour les notifications
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            // Fonction pour mettre à jour l'activité récente
            function updateRecentActivity(activity) {
                const activityContent = document.querySelector('.activity-content');
                if (activityContent) {
                    const activityItem = document.createElement('div');
                    activityItem.className = 'activity-item';
                    activityItem.innerHTML = `
                        <div class="activity-text">${activity}</div>
                        <div style="font-size: 10px; color: rgba(255, 255, 255, 0.5); margin-top: 5px;">
                            ${new Date().toLocaleTimeString()}
                        </div>
                    `;

                    // Ajouter en premier
                    activityContent.insertBefore(activityItem, activityContent.firstChild);

                    // Garder seulement les 5 dernières activités
                    while (activityContent.children.length > 5) {
                        activityContent.removeChild(activityContent.lastChild);
                    }
                }
            }

            // Fonction pour charger l'état de la mémoire thermique
            async function loadThermalMemoryStatus() {
                try {
                    const response = await fetch('/api/thermal/status');
                    const data = await response.json();

                    if (data.success) {
                        // Mettre à jour la température de mémoire dans les stats
                        const memoryElement = document.querySelector('.status-item .status-label');
                        if (memoryElement && memoryElement.textContent.toLowerCase() === 'mémoire') {
                            const tempValue = memoryElement.previousElementSibling;
                            if (tempValue) {
                                const temp = Math.round(data.globalTemp * 100);
                                tempValue.textContent = temp + '°C';
                            }
                        }

                        // Ajouter une réflexion sur l'état de la mémoire
                        const hotMemories = data.stats.hotMemories || 0;
                        const totalMemories = data.stats.totalMemories || 0;

                        updateThoughtsContent(`Mémoire thermique: ${totalMemories} mémoires, ${hotMemories} chaudes (temp: ${data.globalTemp.toFixed(2)})`, 'memory');

                        // Mettre à jour l'activité récente
                        updateRecentActivity(`Mémoire thermique synchronisée - ${totalMemories} mémoires`);
                    }
                } catch (error) {
                    console.log('⚠️ Impossible de charger l\'état de la mémoire thermique:', error);
                    updateThoughtsContent('Mémoire thermique non disponible', 'error');
                }
            }

            // Mettre à jour les statistiques périodiquement
            setInterval(updateSystemStats, 30000); // Toutes les 30 secondes

            // Charger l'état de la mémoire thermique
            loadThermalMemoryStatus();
            setInterval(loadThermalMemoryStatus, 60000); // Toutes les minutes

            // Ajouter des réflexions d'exemple pour tester le système
            addReflection('Interface de chat initialisée. Prêt à recevoir vos messages ! Je vais analyser chaque message avec attention et vous montrer mon processus de réflexion complet.', 'connected');
            addReflection('Analyse de l\'environnement système : Tous les modules sont opérationnels. La mémoire thermique fonctionne correctement avec une température globale optimale. Les accélérateurs KYBER sont actifs et prêts à traiter les requêtes complexes.', 'analysis');
            addReflection('Processus de démarrage terminé avec succès. Je suis maintenant en mesure de traiter vos demandes, de générer du contenu multimédia, et d\'accéder à Internet pour des recherches approfondies. Mon système de réflexion est pleinement fonctionnel.', 'processing');
            addReflection('Décision prise : Utiliser le mode de réflexion visible pour que l\'utilisateur puisse comprendre mon processus de pensée. Cela permettra une meilleure transparence et une collaboration plus efficace.', 'decision');
            addReflection('Apprentissage continu activé : Je vais mémoriser nos interactions dans ma mémoire thermique pour améliorer mes réponses futures. Chaque conversation enrichit ma base de connaissances et affine ma compréhension de vos besoins.', 'learning');

            updateThoughtsContent('Interface de chat initialisée. Prêt à recevoir vos messages !', 'connected');
            updateRecentActivity('Interface de chat démarrée');
            showNotification('Interface de chat connectée avec succès !', 'success');

            console.log('✅ Interface prête !');
        });

        // Fonction de notification pour les tests
        function showNotification(message, type = 'info') {
            // Créer l'élément de notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideIn 0.3s ease-out;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            // Supprimer après 3 secondes
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="/js/qi-manager.js"></script>
    <script src="/js/native-app.js"></script>
    <script src="/js/auto-init-fixes.js"></script>
</body>
</html>
