<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna v2.1.0 - Hub Central IA Avancée (100% Complet)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .app-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #667eea;
        }

        .app-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
            text-align: center;
        }

        .app-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
            color: #333;
        }

        .app-description {
            color: #666;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .app-status {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #28a745;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }

        .stats-title {
            text-align: center;
            font-size: 1.8rem;
            margin-bottom: 25px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: white;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .category-title {
            color: white;
            font-size: 1.5rem;
            margin: 40px 0 20px 0;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* Couleurs spécifiques par catégorie */
        .app-card.chat .app-icon { color: #e91e63; }
        .app-card.memory .app-icon { color: #ff5722; }
        .app-card.brain .app-icon { color: #9c27b0; }
        .app-card.generation .app-icon { color: #2196f3; }
        .app-card.monitoring .app-icon { color: #4caf50; }
        .app-card.development .app-icon { color: #ff9800; }
        .app-card.system .app-icon { color: #607d8b; }
        .app-card.analysis .app-icon { color: #ff6b9d; }

        /* Contrôles de sécurité en haut */
        .security-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .security-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .security-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn-sleep {
            background: rgba(33, 150, 243, 0.3);
            border-color: #2196f3;
            position: relative;
        }

        .btn-sleep:hover {
            background: rgba(33, 150, 243, 0.5);
        }

        .btn-sleep.active {
            background: rgba(33, 150, 243, 0.8);
            box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
            animation: pulseBlue 2s infinite;
        }

        .btn-sleep.active::before {
            content: "●";
            color: #00ff00;
            font-size: 8px;
            position: absolute;
            top: 2px;
            right: 2px;
            animation: blink 1s infinite;
        }

        .btn-wakeup {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4caf50;
            position: relative;
        }

        .btn-wakeup:hover {
            background: rgba(76, 175, 80, 0.5);
        }

        .btn-wakeup.active {
            background: rgba(76, 175, 80, 0.8);
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
            animation: pulseGreen 2s infinite;
        }

        .btn-wakeup.active::before {
            content: "●";
            color: #ffff00;
            font-size: 8px;
            position: absolute;
            top: 2px;
            right: 2px;
            animation: blink 1s infinite;
        }

        .btn-monitor {
            background: rgba(255, 152, 0, 0.3);
            border-color: #ff9800;
            position: relative;
        }

        .btn-monitor:hover {
            background: rgba(255, 152, 0, 0.5);
        }

        .btn-monitor.active {
            background: rgba(255, 152, 0, 0.8);
            box-shadow: 0 0 15px rgba(255, 152, 0, 0.6);
            animation: pulseOrange 2s infinite;
        }

        .btn-monitor.active::before {
            content: "●";
            color: #ff0000;
            font-size: 8px;
            position: absolute;
            top: 2px;
            right: 2px;
            animation: blink 1s infinite;
        }

        @keyframes pulseBlue {
            0%, 100% { box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
            50% { box-shadow: 0 0 25px rgba(33, 150, 243, 0.9); }
        }

        @keyframes pulseGreen {
            0%, 100% { box-shadow: 0 0 15px rgba(76, 175, 80, 0.6); }
            50% { box-shadow: 0 0 25px rgba(76, 175, 80, 0.9); }
        }

        @keyframes pulseOrange {
            0%, 100% { box-shadow: 0 0 15px rgba(255, 152, 0, 0.6); }
            50% { box-shadow: 0 0 25px rgba(255, 152, 0, 0.9); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .sleep-mode-banner {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: 600;
            animation: pulse 2s infinite;
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .sleep-mode-banner.active {
            display: block;
        }

        .hibernation-mode-banner {
            background: linear-gradient(135deg, #1a237e, #0d47a1);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: 700;
            animation: deepPulse 3s infinite;
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1001;
            box-shadow: 0 6px 20px rgba(26, 35, 126, 0.5);
            border-bottom: 3px solid #3f51b5;
        }

        .hibernation-mode-banner.active {
            display: block;
        }

        .hibernation-mode-banner .hibernation-icon {
            font-size: 24px;
            margin-right: 15px;
            animation: deepPulse 2s infinite;
        }

        @keyframes deepPulse {
            0%, 100% {
                opacity: 0.7;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        .btn-hibernation {
            background: rgba(26, 35, 126, 0.3);
            border-color: #1a237e;
            position: relative;
        }

        .btn-hibernation:hover {
            background: rgba(26, 35, 126, 0.5);
        }

        .btn-hibernation.active {
            background: rgba(26, 35, 126, 0.8);
            box-shadow: 0 0 20px rgba(26, 35, 126, 0.8);
            animation: deepPulse 3s infinite;
        }

        .btn-hibernation.active::before {
            content: "●";
            color: #00bcd4;
            font-size: 8px;
            position: absolute;
            top: 2px;
            right: 2px;
            animation: deepPulse 2s infinite;
        }

        /* Styles pour la modal de réveil */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
            color: white;
        }

        .security-warning {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .security-warning i {
            font-size: 2em;
            color: #ffc107;
            margin-bottom: 10px;
        }

        .code-input-section, .user-auth-section {
            margin-bottom: 20px;
        }

        .code-input-section label, .user-auth-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #e91e63;
        }

        .code-input-section input, .user-auth-section input {
            width: 100%;
            padding: 15px;
            border: 2px solid #34495e;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1.1em;
            transition: border-color 0.3s;
        }

        .code-input-section input:focus, .user-auth-section input:focus {
            outline: none;
            border-color: #e91e63;
            box-shadow: 0 0 10px rgba(233, 30, 99, 0.3);
        }

        .code-hint {
            margin-top: 8px;
            font-size: 0.9em;
            color: #bdc3c7;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #34495e;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .btn-cancel, .btn-wakeup-confirm {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-cancel {
            background: #95a5a6;
            color: white;
        }

        .btn-cancel:hover {
            background: #7f8c8d;
        }

        .btn-wakeup-confirm {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        .btn-wakeup-confirm:hover {
            background: linear-gradient(135deg, #ad1457, #880e4f);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .sleep-mode-banner .sleep-icon {
            font-size: 20px;
            margin-right: 10px;
            animation: pulse 1.5s infinite;
        }

        @media (max-width: 768px) {
            .apps-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .security-controls {
                position: relative;
                top: auto;
                right: auto;
                margin: 10px auto;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Bannière Mode Hibernation Profonde -->
    <div class="hibernation-mode-banner" id="hibernationBanner">
        <i class="fas fa-snowflake hibernation-icon"></i>
        <strong>MODE HIBERNATION PROFONDE ACTIVÉ</strong> - L'agent est en hibernation très profonde.
        <br>
        <strong>🔓 POUR RÉVEILLER :</strong> Cliquez sur le bouton "Réveil" ☀️ en haut à droite (Code 2338 automatique pour Jean-Luc)
    </div>

    <!-- Bannière Mode Sommeil -->
    <div class="sleep-mode-banner" id="sleepBanner">
        <i class="fas fa-moon sleep-icon"></i>
        <strong>MODE SOMMEIL ACTIVÉ</strong> - L'agent est en veille sécurisée. Utilisez le code 2338 pour le réveiller.
    </div>

    <!-- Modal de Réveil avec Saisie de Code -->
    <div class="modal-overlay" id="wakeupModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>
                    <i class="fas fa-sun"></i>
                    Réveil de l'Agent Louna
                </h2>
                <button class="modal-close" onclick="closeWakeupModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="security-warning">
                    <i class="fas fa-shield-alt"></i>
                    <p>L'agent est actuellement en <strong id="currentSleepMode">mode sommeil</strong>.</p>
                    <p>Veuillez saisir le code de sécurité pour le réveiller :</p>
                </div>

                <div class="code-input-section">
                    <label for="securityCodeInput">Code de Sécurité :</label>
                    <input type="password"
                           id="securityCodeInput"
                           placeholder="Saisissez le code..."
                           maxlength="10"
                           autocomplete="off">
                    <div class="code-hint">
                        <i class="fas fa-info-circle"></i>
                        Seul Jean-Luc Passave connaît ce code
                    </div>
                </div>

                <div class="user-auth-section">
                    <label for="userAuthInput">Nom d'utilisateur :</label>
                    <input type="text"
                           id="userAuthInput"
                           placeholder="jean-luc-passave"
                           value="jean-luc-passave"
                           readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="closeWakeupModal()">
                    <i class="fas fa-times"></i>
                    Annuler
                </button>
                <button class="btn-wakeup-confirm" onclick="confirmWakeup()">
                    <i class="fas fa-sun"></i>
                    Réveiller l'Agent
                </button>
            </div>
        </div>
    </div>

    <!-- Contrôles de Sécurité -->
    <div class="security-controls">
        <button class="security-btn btn-hibernation" onclick="enterDeepHibernation()" title="Hibernation profonde (Code: 2338)">
            <i class="fas fa-snowflake"></i>
            Hibernation
        </button>
        <button class="security-btn btn-sleep" onclick="putAgentToSleep()" title="Mettre en sommeil (Code: 2338)">
            <i class="fas fa-moon"></i>
            Sommeil
        </button>
        <button class="security-btn btn-wakeup" onclick="wakeUpAgent()" title="Réveiller l'agent (Code: 2338)">
            <i class="fas fa-sun"></i>
            Réveil
        </button>
        <button class="security-btn btn-monitor" onclick="openSecurityMonitor()" title="Surveillance sécurité">
            <i class="fas fa-shield-alt"></i>
            Surveillance
        </button>
    </div>

    <!-- Script des Directives Éthiques -->
    <script src="/modules/ethical-guidelines.js"></script>

    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-brain louna-header-icon"></i>
                <h1>Louna v2.1.0 - IA Avancée</h1>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>100% Complet - QI 203 (Quasi-AGI)</span>
            </div>
        </div>
    </div>

    <div class="louna-container">
        <!-- Statistiques en temps réel -->
        <div class="stats-section">
            <h2 class="stats-title">Statistiques du Système</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="qi-value">203</div>
                    <div class="stat-label">QI Système (Quasi-AGI)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memory-temp">37.0°C</div>
                    <div class="stat-label">Mémoire Thermique</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="neuron-count">89</div>
                    <div class="stat-label">Neurones Actifs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="kyber-count">8/16</div>
                    <div class="stat-label">Accélérateurs KYBER</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="completion-rate">100%</div>
                    <div class="stat-label">Fonctionnalités Complètes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="learning-rate">94.2%</div>
                    <div class="stat-label">Taux d'Apprentissage</div>
                </div>
            </div>
        </div>

        <!-- Applications principales -->
        <h2 class="category-title">🎯 Applications Principales</h2>
        <div class="apps-grid">
            <!-- Chat Intelligent avec Apprentissage -->
            <div class="app-card chat" onclick="openApp('/chat-agents.html')">
                <div class="app-icon"><i class="fas fa-robot"></i></div>
                <div class="app-title">🧠 Chat Intelligent Avancé</div>
                <div class="app-description">Conversations naturelles avec apprentissage par renforcement et analyse émotionnelle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>IA Évolutive - QI 203</span>
                </div>
            </div>

            <!-- Centre de Génération -->
            <div class="app-card generation" onclick="openApp('/generation-center.html')">
                <div class="app-icon"><i class="fas fa-magic"></i></div>
                <div class="app-title">🎨 Centre de Génération IA</div>
                <div class="app-description">Hub central pour génération d'images, vidéos LTX, musique et modèles 3D</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Création illimitée</span>
                </div>
            </div>

            <!-- Génération d'Images -->
            <div class="app-card generation" onclick="openApp('/image-generator-simple.html')">
                <div class="app-icon"><i class="fas fa-image"></i></div>
                <div class="app-title">🖼️ Génération d'Images</div>
                <div class="app-description">Création d'images illimitée avec styles artistiques variés et galerie intégrée</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Images HD illimitées</span>
                </div>
            </div>

            <!-- Génération Vidéo LTX -->
            <div class="app-card generation" onclick="openApp('/video-generator.html')">
                <div class="app-icon"><i class="fas fa-video"></i></div>
                <div class="app-title">🎥 Génération Vidéo LTX</div>
                <div class="app-description">Technologie LTX avancée pour créer des vidéos cinématiques jusqu'à 4K</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Technologie LTX</span>
                </div>
            </div>

            <!-- Centre de Sécurité Avancée -->
            <div class="app-card system" onclick="openApp('/security-center.html')">
                <div class="app-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="app-title">🔐 Centre de Sécurité Avancée</div>
                <div class="app-description">Protection complète : Firewall, Antivirus, Chiffrement, VPN et Biométrie</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Protection 95.8%</span>
                </div>
            </div>

            <!-- Générateur Musical IA -->
            <div class="app-card generation" onclick="openApp('/music-generator.html')">
                <div class="app-icon"><i class="fas fa-music"></i></div>
                <div class="app-title">🎵 Générateur Musical IA</div>
                <div class="app-description">Composition automatique et arrangements intelligents avec instruments virtuels</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Création musicale</span>
                </div>
            </div>

            <!-- CONTRÔLE D'URGENCE -->
            <div class="app-card system" onclick="openApp('/emergency-control.html')" style="border: 2px solid #f44336; background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(211, 47, 47, 0.1));">
                <div class="app-icon" style="color: #f44336;"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="app-title" style="color: #f44336;">🚨 CONTRÔLE D'URGENCE</div>
                <div class="app-description">Système de sécurité : Mise en sommeil, arrêt d'urgence et surveillance</div>
                <div class="app-status">
                    <div class="status-dot" style="background: #f44336;"></div>
                    <span style="color: #f44336;">Sécurité Maximum</span>
                </div>
            </div>

            <!-- Recherche Web Intelligente -->
            <div class="app-card analysis" onclick="openApp('/web-search.html')">
                <div class="app-icon"><i class="fas fa-search"></i></div>
                <div class="app-title">🔍 Recherche Web IA</div>
                <div class="app-description">Recherche intelligente avec analyse de contenu et synthèse automatique</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Recherche avancée</span>
                </div>
            </div>

            <!-- Reconnaissance Faciale -->
            <div class="app-card analysis" onclick="openApp('/face-recognition.html')">
                <div class="app-icon"><i class="fas fa-user-check"></i></div>
                <div class="app-title">👁️ Reconnaissance Faciale</div>
                <div class="app-description">Système de reconnaissance faciale avancé avec analyse émotionnelle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Détection temps réel</span>
                </div>
            </div>

            <!-- Dashboard Apprentissage -->
            <div class="app-card monitoring" onclick="openApp('/learning-dashboard.html')">
                <div class="app-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="app-title">📊 Dashboard Apprentissage</div>
                <div class="app-description">Suivi de l'apprentissage par renforcement et évolution du QI</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Apprentissage actif</span>
                </div>
            </div>

            <!-- Test Mémoire Biologique -->
            <div class="app-card memory" onclick="openApp('/biological-memory-test.html')">
                <div class="app-icon"><i class="fas fa-dna"></i></div>
                <div class="app-title">🧬 Test Mémoire Biologique</div>
                <div class="app-description">Interface de test pour la mémoire biologique artificielle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système biologique actif</span>
                </div>
            </div>

            <!-- Gestionnaire de Fonctionnalités Avancées -->
            <div class="app-card features" onclick="openApp('/advanced-features-manager.html')">
                <div class="app-icon"><i class="fas fa-rocket"></i></div>
                <div class="app-title">🚀 Fonctionnalités Avancées</div>
                <div class="app-description">Restauration et gestion de toutes les fonctionnalités avancées</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système de restauration prêt</span>
                </div>
            </div>

            <!-- Gestionnaire de Connexion Directe -->
            <div class="app-card system" onclick="openApp('/direct-connection-manager.html')">
                <div class="app-icon"><i class="fas fa-bolt"></i></div>
                <div class="app-title">⚡ Connexion Directe</div>
                <div class="app-description">Système de connexion directe sans Ollama - Vitesse optimisée</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Optimisation active</span>
                </div>
            </div>

            <!-- Mémoire Thermique -->
            <div class="app-card memory" onclick="openApp('/futuristic-interface.html')">
                <div class="app-icon"><i class="fas fa-fire"></i></div>
                <div class="app-title">Mémoire Thermique</div>
                <div class="app-description">Système de mémoire thermique avec 5 zones distinctes</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Actif - 42.3°C</span>
                </div>
            </div>

            <!-- Tableau de Bord Cérébral VIVANT -->
            <div class="app-card brain" onclick="openApp('/brain-dashboard-live.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">🧠 Tableau de Bord VIVANT</div>
                <div class="app-description">6 zones cérébrales en activité temps réel avec animations</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Activité en direct</span>
                </div>
            </div>

            <!-- Cerveau 3D VIVANT -->
            <div class="app-card brain" onclick="openApp('/brain-3d-live.html')">
                <div class="app-icon"><i class="fas fa-cube"></i></div>
                <div class="app-title">🧠 Cerveau 3D VIVANT</div>
                <div class="app-description">Visualisation 3D animée avec flux de données et particules</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Animations actives</span>
                </div>
            </div>

            <!-- Cerveau 3D (Original) -->
            <div class="app-card brain" onclick="openApp('/brain-visualization.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Visualisation Cerveau 3D</div>
                <div class="app-description">Visualisation 3D du cerveau artificiel en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>71 neurones actifs</span>
                </div>
            </div>

            <!-- QI Monitor -->
            <div class="app-card monitoring" onclick="openApp('/qi-neuron-monitor.html')">
                <div class="app-icon"><i class="fas fa-yin-yang"></i></div>
                <div class="app-title">Monitoring QI & Neurones</div>
                <div class="app-description">Surveillance en temps réel du QI et de l'activité neuronale</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>QI: 148</span>
                </div>
            </div>

            <!-- Monitoring Cérébral Complet -->
            <div class="app-card monitoring" onclick="openApp('/brain-monitoring-complete.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Monitoring Cérébral Complet</div>
                <div class="app-description">Surveillance complète de tous les aspects du cerveau artificiel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système optimal</span>
                </div>
            </div>

            <!-- Système de Persistance Mémoire -->
            <div class="app-card memory" onclick="openApp('/memory-persistence-system.html')">
                <div class="app-icon"><i class="fas fa-memory"></i></div>
                <div class="app-title">Persistance Mémoire Thermique</div>
                <div class="app-description">Protection contre la perte de données en mémoire instantanée</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Protection active</span>
                </div>
            </div>

            <!-- Transfert d'Informations -->
            <div class="app-card monitoring" onclick="openApp('/information-transfer-monitor.html')">
                <div class="app-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="app-title">Transfert d'Informations</div>
                <div class="app-description">Monitoring du flux et transfert d'informations neuronales</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Flux actif</span>
                </div>
            </div>

            <!-- Centre d'Évolution & Apprentissage -->
            <div class="app-card monitoring" onclick="openApp('/evolution-learning-center.html')">
                <div class="app-icon"><i class="fas fa-dna"></i></div>
                <div class="app-title">Centre d'Évolution & Apprentissage</div>
                <div class="app-description">Système d'évolution auto-adaptative et formation continue</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Auto-évolution active</span>
                </div>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="app-card system" onclick="openApp('/kyber-dashboard.html')">
                <div class="app-icon"><i class="fas fa-bolt"></i></div>
                <div class="app-title">Accélérateurs Kyber</div>
                <div class="app-description">Système d'accélération quantique pour boost de performance</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>8 accélérateurs actifs</span>
                </div>
            </div>

            <!-- Studio de Génération -->
            <div class="app-card generation" onclick="openApp('/generation-studio.html')">
                <div class="app-icon"><i class="fas fa-magic"></i></div>
                <div class="app-title">Studio de Génération</div>
                <div class="app-description">Création de contenu multimédia : vidéos, images, musique, 3D</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt à créer</span>
                </div>
            </div>

            <!-- Interface Vocale -->
            <div class="app-card chat" onclick="openApp('/voice-interface.html')">
                <div class="app-icon"><i class="fas fa-microphone"></i></div>
                <div class="app-title">Interface Vocale</div>
                <div class="app-description">Communication vocale avec Louna - Reconnaissance et synthèse</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt à parler</span>
                </div>
            </div>

            <!-- Paramètres Avancés -->
            <div class="app-card settings" onclick="openApp('/settings-advanced.html')">
                <div class="app-icon"><i class="fas fa-cogs"></i></div>
                <div class="app-title">Paramètres Avancés</div>
                <div class="app-description">Configuration complète de tous les modules Louna</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système configuré</span>
                </div>
            </div>

            <!-- Tableau de Bord Principal -->
            <div class="app-card dashboard" onclick="openApp('/dashboard-master.html')">
                <div class="app-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="app-title">Tableau de Bord Principal</div>
                <div class="app-description">Vue d'ensemble de tous les systèmes en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Monitoring actif</span>
                </div>
            </div>
        </div>

        <!-- Applications secondaires -->
        <h2 class="category-title">🛠️ Outils & Système</h2>
        <div class="apps-grid">
            <!-- Éditeur de Code -->
            <div class="app-card development" onclick="openApp('/code-editor.html')">
                <div class="app-icon"><i class="fas fa-code"></i></div>
                <div class="app-title">Éditeur de Code</div>
                <div class="app-description">Éditeur de code intelligent avec assistance IA</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt</span>
                </div>
            </div>

            <!-- Logs Système -->
            <div class="app-card system" onclick="openApp('/system-logs.html')">
                <div class="app-icon"><i class="fas fa-terminal"></i></div>
                <div class="app-title">Logs Système</div>
                <div class="app-description">Journal système en temps réel avec filtrage avancé</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Enregistrement actif</span>
                </div>
            </div>

            <!-- Analyses Comparatives -->
            <div class="app-card analysis" onclick="openApp('/brain-comparison-analysis.html')">
                <div class="app-icon"><i class="fas fa-balance-scale"></i></div>
                <div class="app-title">Analyses Comparatives</div>
                <div class="app-description">Tableaux cerveau humain vs mémoire thermique</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Comparaisons actives</span>
                </div>
            </div>

            <!-- Gestionnaire QI -->
            <div class="app-card monitoring" onclick="openApp('/test-qi-manager.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Gestionnaire QI</div>
                <div class="app-description">Tests et évaluation du QI centralisé</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>QI: <span id="qi-value">148</span></span>
                </div>
            </div>

            <!-- Test Comparatif Mémoire -->
            <div class="app-card analysis" onclick="openApp('/memory-comparison-test.html')">
                <div class="app-icon"><i class="fas fa-microscope"></i></div>
                <div class="app-title">Test Mémoire Thermique</div>
                <div class="app-description">Comparaison temps réel vs cerveau humain</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Fidélité: 95%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de test -->
    <div class="louna-container">
        <h2 class="category-title">🧪 Test de Navigation</h2>
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testNavigation()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 1.1rem; cursor: pointer; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                🧪 Tester la Navigation
            </button>
            <div id="test-results" style="margin-top: 20px; color: white; font-weight: bold;"></div>
        </div>
    </div>

    <div class="footer">
        <p>Louna - Agent IA Révolutionnaire &copy; 2025</p>
        <p>Créé par Jean-Luc Passave - Sainte-Anne, Guadeloupe</p>
    </div>

    <!-- Scripts Louna -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <script src="/js/qi-manager.js"></script>

    <script>
        // Configuration de la navigation Louna
        window.lounaNavConfig = {
            header: false, // On garde le header existant pour l'instant
            quickNav: false,
            breadcrumb: false,
            sidebar: false
        };

        // Fonction pour ouvrir une application
        function openApp(url) {
            console.log('🚀 Ouverture de:', url);

            // Afficher une notification
            showLoading('Chargement de l\'application...');

            // Vérifier que l'URL est accessible
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        showSuccess('Application chargée avec succès !');
                        setTimeout(() => {
                            window.location.href = url;
                        }, 500);
                    } else {
                        showError('Application non disponible');
                    }
                })
                .catch(error => {
                    showError('Erreur de connexion à l\'application');
                    console.error('Erreur:', error);
                });
        }

        // Fonction de test de navigation
        function testNavigation() {
            const testResults = document.getElementById('test-results');
            testResults.innerHTML = '🧪 Test en cours...';

            // Afficher une notification de début de test
            const testNotificationId = showLoading('Test de navigation en cours...');

            // Test des applications principales
            const apps = [
                { url: '/chat', name: 'Chat Intelligent' },
                { url: '/chat-agents.html', name: 'Chat avec Agents' },
                { url: '/biological-memory-test.html', name: 'Test Mémoire Biologique' },
                { url: '/advanced-features-manager.html', name: 'Fonctionnalités Avancées' },
                { url: '/futuristic-interface.html', name: 'Mémoire Thermique' },
                { url: '/brain-dashboard-live.html', name: 'Tableau de Bord VIVANT' },
                { url: '/brain-3d-live.html', name: 'Cerveau 3D VIVANT' },
                { url: '/brain-visualization.html', name: 'Visualisation 3D' },
                { url: '/qi-neuron-monitor.html', name: 'Monitoring QI' },
                { url: '/brain-monitoring-complete.html', name: 'Monitoring Cérébral Complet' },
                { url: '/information-transfer-monitor.html', name: 'Transfert d\'Informations' },
                { url: '/evolution-learning-center.html', name: 'Centre d\'Évolution & Apprentissage' },
                { url: '/kyber-dashboard.html', name: 'Accélérateurs Kyber' },
                { url: '/generation-studio.html', name: 'Studio de Génération' },
                { url: '/voice-interface.html', name: 'Interface Vocale' },
                { url: '/code-editor.html', name: 'Éditeur de Code' },
                { url: '/settings-advanced.html', name: 'Paramètres Avancés' },
                { url: '/dashboard-master.html', name: 'Tableau de Bord' },
                { url: '/system-logs.html', name: 'Logs Système' },
                { url: '/brain-comparison-analysis.html', name: 'Analyses Comparatives' },
                { url: '/test-qi-manager.html', name: 'Gestionnaire QI' },
                { url: '/memory-comparison-test.html', name: 'Test Mémoire Thermique' },
                { url: '/memory-persistence-system.html', name: 'Persistance Mémoire' }
            ];

            let testCount = 0;
            let successCount = 0;
            const results = [];

            apps.forEach((app, index) => {
                setTimeout(() => {
                    fetch(app.url)
                        .then(response => {
                            testCount++;
                            if (response.ok) {
                                successCount++;
                                results.push({ ...app, status: 'success' });
                                console.log('✅ Test réussi:', app.name);
                            } else {
                                results.push({ ...app, status: 'error' });
                                console.log('❌ Test échoué:', app.name);
                            }

                            // Mettre à jour la progression
                            const progress = Math.round((testCount / apps.length) * 100);
                            LounaNotify.update(testNotificationId, `Test en cours... ${progress}%`, 'loading');

                            if (testCount === apps.length) {
                                // Fermer la notification de test
                                LounaNotify.dismiss(testNotificationId);

                                // Afficher les résultats
                                testResults.innerHTML = `✅ Test terminé: ${successCount}/${apps.length} applications accessibles`;

                                if (successCount === apps.length) {
                                    testResults.innerHTML += '<br>🎉 Toutes les applications fonctionnent !';
                                    showSuccess('🎉 Tous les tests sont passés avec succès !');
                                } else {
                                    const failedApps = results.filter(r => r.status === 'error');
                                    showWarning(`⚠️ ${failedApps.length} application(s) non disponible(s)`);
                                }

                                // Afficher les détails
                                results.forEach(result => {
                                    if (result.status === 'success') {
                                        LounaNotify.system(`${result.name} : Opérationnel`, 'success');
                                    } else {
                                        LounaNotify.system(`${result.name} : Non disponible`, 'error');
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            testCount++;
                            results.push({ ...app, status: 'error' });
                            console.log('❌ Erreur test:', app.name, error);

                            const progress = Math.round((testCount / apps.length) * 100);
                            LounaNotify.update(testNotificationId, `Test en cours... ${progress}%`, 'loading');

                            if (testCount === apps.length) {
                                LounaNotify.dismiss(testNotificationId);
                                testResults.innerHTML = `⚠️ Test terminé: ${successCount}/${apps.length} applications accessibles`;
                                showWarning('⚠️ Certaines applications ne sont pas disponibles');
                            }
                        });
                }, index * 300); // Délai plus long pour éviter la surcharge
            });
        }

        // Mise à jour des statistiques (QI géré par qi-manager.js)
        function updateStats() {
            const memoryTemp = document.getElementById('memory-temp');
            const neuronCount = document.getElementById('neuron-count');
            const kyberCount = document.getElementById('kyber-count');

            // Fonction pour récupérer les statistiques (sauf QI)
            async function fetchStats() {
                try {
                    // Récupérer les stats de mémoire thermique
                    const memResponse = await fetch('/api/thermal/memory/stats');
                    if (memResponse.ok) {
                        const memData = await memResponse.json();

                        if (memData.temperature) {
                            memoryTemp.textContent = memData.temperature.toFixed(1) + '°C';
                        }
                    }

                    // Récupérer les stats du cerveau artificiel
                    const brainResponse = await fetch('/api/brain/status');
                    if (brainResponse.ok) {
                        const brainData = await brainResponse.json();
                        if (brainData.brain && brainData.brain.totalNeurons) {
                            neuronCount.textContent = brainData.brain.totalNeurons;
                        }
                    }

                    // Récupérer les accélérateurs Kyber
                    const kyberResponse = await fetch('/api/kyber/status');
                    if (kyberResponse.ok) {
                        const kyberData = await kyberResponse.json();
                        if (Array.isArray(kyberData)) {
                            const activeAccelerators = kyberData.filter(acc => acc.active).length;
                            kyberCount.textContent = activeAccelerators;
                        }
                    }

                } catch (error) {
                    console.warn('Erreur récupération stats:', error);
                }
            }

            // Récupérer les stats immédiatement
            fetchStats();

            // Puis toutes les 5 secondes
            setInterval(fetchStats, 5000);
        }

        // Démarrer les animations
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();

            // Notification de bienvenue
            setTimeout(() => {
                showSuccess('🎯 Hub Central Louna initialisé avec succès !');
                LounaNotify.system('Toutes les interfaces sont opérationnelles', 'success');
                LounaNotify.memory('Mémoire thermique connectée et fonctionnelle', 'success');
                LounaNotify.agent('Agent Claude 4GB prêt à vous assister', 'success');
            }, 1000);

            console.log('🎯 Hub Central Louna initialisé');
        });

        // ===== FONCTIONS DE CONTRÔLE DE SÉCURITÉ =====

        // Variables globales pour les modes de sommeil
        let isInSleepMode = false;
        let isInDeepHibernation = false;

        // Mettre l'agent en sommeil
        async function putAgentToSleep() {
            if (isInSleepMode) {
                showWarning('L\'agent est déjà en mode sommeil');
                return;
            }

            // Activer l'indicateur visuel immédiatement
            const sleepBtn = document.querySelector('.btn-sleep');
            sleepBtn.classList.add('active');

            try {
                const response = await fetch('/api/emergency/sleep', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        securityCode: '2338',
                        userAuth: 'jean-luc-passave'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    isInSleepMode = true;
                    document.getElementById('sleepBanner').classList.add('active');
                    showSuccess('🛌 Agent mis en sommeil sécurisé avec succès !');
                    LounaNotify.system('Agent en mode sommeil - Code 2338 requis pour réveil', 'info');

                    // Désactiver certaines fonctionnalités
                    disableNonEssentialFeatures();

                    // Désactiver le bouton réveil
                    document.querySelector('.btn-wakeup').classList.remove('active');
                } else {
                    // Retirer l'indicateur en cas d'erreur
                    sleepBtn.classList.remove('active');
                    throw new Error(result.error || 'Erreur inconnue');
                }
            } catch (error) {
                sleepBtn.classList.remove('active');
                console.error('Erreur mise en sommeil:', error);
                showError('❌ Erreur lors de la mise en sommeil: ' + error.message);
            }
        }

        // Réveiller l'agent - Ouvrir la modal de saisie
        function wakeUpAgent() {
            // Vérifier si l'agent est en sommeil ou hibernation
            if (!isInSleepMode && !isInDeepHibernation) {
                showWarning('L\'agent n\'est pas en mode sommeil');
                return;
            }

            // Déterminer le mode actuel pour la modal
            const currentMode = isInDeepHibernation ? 'hibernation profonde' : 'mode sommeil';
            document.getElementById('currentSleepMode').textContent = currentMode;

            // Ouvrir la modal de réveil
            document.getElementById('wakeupModal').style.display = 'flex';

            // Focus sur le champ de saisie du code
            setTimeout(() => {
                const codeInput = document.getElementById('securityCodeInput');
                codeInput.focus();

                // Gérer la touche Entrée pour valider
                codeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        confirmWakeup();
                    }
                });
            }, 300);
        }

        // Fermer la modal de réveil
        function closeWakeupModal() {
            document.getElementById('wakeupModal').style.display = 'none';
            // Réinitialiser le champ de saisie
            document.getElementById('securityCodeInput').value = '';
        }

        // Confirmer le réveil avec le code saisi
        async function confirmWakeup() {
            const securityCode = document.getElementById('securityCodeInput').value;
            const userAuth = document.getElementById('userAuthInput').value;

            // Vérifier que le code est saisi
            if (!securityCode) {
                showError('Veuillez saisir le code de sécurité');
                return;
            }

            // Fermer la modal
            closeWakeupModal();

            // Procéder au réveil selon le mode
            if (isInDeepHibernation) {
                await performDeepHibernationWakeup(securityCode, userAuth);
            } else if (isInSleepMode) {
                await performNormalWakeup(securityCode, userAuth);
            }
        }

        // Réveil de l'hibernation profonde
        async function performDeepHibernationWakeup(securityCode, userAuth) {
            const hibernationBtn = document.querySelector('.btn-hibernation');

            try {
                const response = await fetch('/api/hibernation/wake-up', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        wakeupCode: securityCode,
                        userAuth: userAuth
                    })
                });

                const result = await response.json();

                if (result.success) {
                    isInDeepHibernation = false;

                    // Masquer la bannière d'hibernation
                    document.getElementById('hibernationBanner').classList.remove('active');

                    showSuccess('🌅❄️ Agent réveillé de l\'hibernation profonde avec succès !');
                    LounaNotify.system('Agent réveillé de l\'hibernation profonde', 'success');

                    // Réactiver toutes les fonctionnalités
                    enableAllFeatures();

                    // Désactiver le bouton hibernation
                    hibernationBtn.classList.remove('active');
                    document.querySelector('.btn-wakeup').classList.remove('active');
                } else {
                    throw new Error(result.error || 'Code de sécurité incorrect');
                }
            } catch (error) {
                console.error('Erreur réveil hibernation:', error);
                showError('❌ Erreur lors du réveil de l\'hibernation: ' + error.message);
            }
        }

        // Réveil du mode sommeil normal
        async function performNormalWakeup(securityCode, userAuth) {
            const wakeupBtn = document.querySelector('.btn-wakeup');
            wakeupBtn.classList.add('active');

            try {
                const response = await fetch('/api/emergency/wakeup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        wakeupCode: securityCode,
                        userAuth: userAuth
                    })
                });

                const result = await response.json();

                if (result.success) {
                    isInSleepMode = false;

                    // Masquer la bannière de sommeil
                    document.getElementById('sleepBanner').classList.remove('active');

                    showSuccess('🌅 Agent réveillé avec succès !');
                    LounaNotify.system('Agent réveillé du mode sommeil', 'success');

                    // Réactiver toutes les fonctionnalités
                    enableAllFeatures();

                    // Désactiver les autres boutons
                    document.querySelector('.btn-sleep').classList.remove('active');
                    document.querySelector('.btn-hibernation').classList.remove('active');
                } else {
                    throw new Error(result.error || 'Code de sécurité incorrect');
                }
            } catch (error) {
                console.error('Erreur réveil:', error);
                showError('❌ Erreur lors du réveil: ' + error.message);
            } finally {
                wakeupBtn.classList.remove('active');
            }
        }

        // Entrer en hibernation profonde
        async function enterDeepHibernation() {
            if (isInDeepHibernation) {
                showWarning('L\'agent est déjà en hibernation profonde');
                return;
            }

            // Confirmation spéciale pour l'hibernation profonde
            const confirmed = confirm('⚠️ ATTENTION ⚠️\n\nVous allez mettre l\'agent en HIBERNATION PROFONDE.\n\nCet état persistera même après redémarrage et seul Jean-Luc Passave pourra le réveiller avec le code 2338.\n\nÊtes-vous sûr de vouloir continuer ?');

            if (!confirmed) {
                return;
            }

            // Activer l'indicateur visuel immédiatement
            const hibernationBtn = document.querySelector('.btn-hibernation');
            hibernationBtn.classList.add('active');

            try {
                const response = await fetch('/api/hibernation/deep-sleep', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        securityCode: '2338',
                        userAuth: 'jean-luc-passave'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    isInDeepHibernation = true;
                    isInSleepMode = false; // Sortir du mode sommeil normal

                    // Afficher la bannière d'hibernation
                    document.getElementById('hibernationBanner').classList.add('active');
                    document.getElementById('sleepBanner').classList.remove('active');

                    showSuccess('🛌❄️ Agent mis en hibernation profonde avec succès !');
                    LounaNotify.system('Agent en hibernation profonde - Persistant après redémarrage', 'info');

                    // Désactiver toutes les fonctionnalités
                    disableAllFeatures();

                    // Désactiver les autres boutons
                    document.querySelector('.btn-sleep').classList.remove('active');
                    document.querySelector('.btn-wakeup').classList.remove('active');
                } else {
                    // Retirer l'indicateur en cas d'erreur
                    hibernationBtn.classList.remove('active');
                    throw new Error(result.error || 'Erreur inconnue');
                }
            } catch (error) {
                hibernationBtn.classList.remove('active');
                console.error('Erreur hibernation profonde:', error);
                showError('❌ Erreur lors de l\'hibernation profonde: ' + error.message);
            }
        }

        // Réveiller de l'hibernation profonde
        async function wakeFromDeepHibernation() {
            if (!isInDeepHibernation) {
                return; // Fonction appelée automatiquement par wakeUpAgent
            }

            try {
                const response = await fetch('/api/hibernation/wake-up', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        wakeupCode: '2338',
                        userAuth: 'jean-luc-passave'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    isInDeepHibernation = false;

                    // Masquer la bannière d'hibernation
                    document.getElementById('hibernationBanner').classList.remove('active');

                    showSuccess('🌅❄️ Agent réveillé de l\'hibernation profonde avec succès !');
                    LounaNotify.system('Agent réveillé de l\'hibernation profonde', 'success');

                    // Réactiver toutes les fonctionnalités
                    enableAllFeatures();

                    // Désactiver le bouton hibernation
                    document.querySelector('.btn-hibernation').classList.remove('active');

                    return true;
                } else {
                    throw new Error(result.error || 'Erreur inconnue');
                }
            } catch (error) {
                console.error('Erreur réveil hibernation:', error);
                showError('❌ Erreur lors du réveil de l\'hibernation: ' + error.message);
                return false;
            }
        }

        // Ouvrir la surveillance de sécurité
        function openSecurityMonitor() {
            // Activer l'indicateur visuel
            const monitorBtn = document.querySelector('.btn-monitor');
            monitorBtn.classList.add('active');

            // Ouvrir la fenêtre de surveillance
            window.open('/emergency-control.html', '_blank');

            // Désactiver l'indicateur après 3 secondes
            setTimeout(() => {
                monitorBtn.classList.remove('active');
            }, 3000);
        }

        // Désactiver les fonctionnalités non essentielles en mode sommeil
        function disableNonEssentialFeatures() {
            // Ajouter une classe pour indiquer le mode sommeil
            document.body.classList.add('sleep-mode');

            // Optionnel : désactiver certains boutons ou fonctionnalités
            const appCards = document.querySelectorAll('.app-card');
            appCards.forEach(card => {
                if (!card.classList.contains('system')) {
                    card.style.opacity = '0.5';
                    card.style.pointerEvents = 'none';
                }
            });
        }

        // Désactiver TOUTES les fonctionnalités (hibernation profonde)
        function disableAllFeatures() {
            document.body.classList.add('hibernation-mode');

            const appCards = document.querySelectorAll('.app-card');
            appCards.forEach(card => {
                card.style.opacity = '0.2';
                card.style.pointerEvents = 'none';
                card.style.filter = 'grayscale(100%)';
            });

            // Désactiver les liens de navigation SAUF les boutons de sécurité
            const navLinks = document.querySelectorAll('a:not(.security-btn):not(.btn-wakeup):not(.btn-hibernation)');
            navLinks.forEach(link => {
                link.style.pointerEvents = 'none';
                link.style.opacity = '0.3';
            });

            // S'assurer que les boutons de sécurité restent visibles et fonctionnels
            const securityButtons = document.querySelectorAll('.security-btn');
            securityButtons.forEach(btn => {
                btn.style.pointerEvents = 'auto';
                btn.style.opacity = '1';
                btn.style.zIndex = '9999';
            });

            // Afficher un message d'hibernation
            console.log('💤 HIBERNATION PROFONDE - Toutes les fonctionnalités désactivées');
            console.log('🔓 Boutons de sécurité restent actifs pour le réveil');
        }

        // Réactiver toutes les fonctionnalités
        function enableAllFeatures() {
            document.body.classList.remove('sleep-mode');
            document.body.classList.remove('hibernation-mode');

            const appCards = document.querySelectorAll('.app-card');
            appCards.forEach(card => {
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
                card.style.filter = 'none';
            });

            // Réactiver les liens de navigation
            const navLinks = document.querySelectorAll('a');
            navLinks.forEach(link => {
                link.style.pointerEvents = 'auto';
                link.style.opacity = '1';
            });

            console.log('🌅 RÉVEIL COMPLET - Toutes les fonctionnalités réactivées');
        }

        // Vérifier le statut au chargement
        async function checkSecurityStatus() {
            try {
                // Vérifier d'abord l'hibernation profonde
                const hibernationResponse = await fetch('/api/hibernation/status');
                const hibernationStatus = await hibernationResponse.json();

                if (hibernationStatus.isInDeepHibernation) {
                    isInDeepHibernation = true;
                    document.getElementById('hibernationBanner').classList.add('active');
                    document.querySelector('.btn-hibernation').classList.add('active');
                    disableAllFeatures();
                    console.log('💤 Agent en hibernation profonde détecté au chargement');
                    return; // Pas besoin de vérifier le sommeil normal
                }

                // Vérifier le sommeil normal
                const response = await fetch('/api/emergency/status');
                const status = await response.json();

                if (status.sleepMode) {
                    isInSleepMode = true;
                    document.getElementById('sleepBanner').classList.add('active');
                    document.querySelector('.btn-sleep').classList.add('active');
                    disableNonEssentialFeatures();
                }
            } catch (error) {
                console.warn('Impossible de vérifier le statut de sécurité:', error);
            }
        }

        // Vérifier le statut au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            checkSecurityStatus();

            // Afficher le message éthique de l'agent
            if (window.lounaEthics) {
                setTimeout(() => {
                    showSuccess('🤖 Agent Louna initialisé avec directives éthiques - Prêt à vous servir, Jean-Luc !');
                    console.log(window.lounaEthics.displayWelcomeMessage());
                }, 2000);
            }
        });

        console.log('🔐 Système de contrôle de sécurité chargé - Code 2338 configuré');
    </script>
</body>
</html>
