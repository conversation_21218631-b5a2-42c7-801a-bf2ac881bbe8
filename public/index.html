<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna v2.1.0 - Hub Central IA Avancée (100% Complet)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <!-- Configuration globale Louna -->
    <script src="/js/global-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .app-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #667eea;
        }

        .app-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
            text-align: center;
        }

        .app-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
            color: #333;
        }

        .app-description {
            color: #666;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .app-status {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #28a745;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }

        .stats-title {
            text-align: center;
            font-size: 1.8rem;
            margin-bottom: 25px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: white;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .category-title {
            color: white;
            font-size: 1.5rem;
            margin: 40px 0 20px 0;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* Couleurs spécifiques par catégorie */
        .app-card.chat .app-icon { color: #e91e63; }
        .app-card.memory .app-icon { color: #ff5722; }
        .app-card.brain .app-icon { color: #9c27b0; }
        .app-card.generation .app-icon { color: #2196f3; }
        .app-card.monitoring .app-icon { color: #4caf50; }
        .app-card.development .app-icon { color: #ff9800; }
        .app-card.system .app-icon { color: #607d8b; }
        .app-card.analysis .app-icon { color: #ff6b9d; }

        @media (max-width: 768px) {
            .apps-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-brain louna-header-icon"></i>
                <h1>Louna v2.1.0 - IA Avancée</h1>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>100% Complet - QI 203 (Quasi-AGI)</span>
            </div>
        </div>
    </div>

    <div class="louna-container">
        <!-- Statistiques en temps réel -->
        <div class="stats-section">
            <h2 class="stats-title">Statistiques du Système</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="qi-value">203</div>
                    <div class="stat-label">QI Système (Quasi-AGI)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memory-temp">37.0°C</div>
                    <div class="stat-label">Mémoire Thermique</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="neuron-count">89</div>
                    <div class="stat-label">Neurones Actifs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="kyber-count">8/16</div>
                    <div class="stat-label">Accélérateurs KYBER</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="completion-rate">100%</div>
                    <div class="stat-label">Fonctionnalités Complètes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="learning-rate">94.2%</div>
                    <div class="stat-label">Taux d'Apprentissage</div>
                </div>
            </div>
        </div>

        <!-- Applications principales -->
        <h2 class="category-title">🎯 Applications Principales</h2>
        <div class="apps-grid">
            <!-- Chat Intelligent avec Apprentissage -->
            <div class="app-card chat" onclick="openApp('/chat-agents.html')">
                <div class="app-icon"><i class="fas fa-robot"></i></div>
                <div class="app-title">🧠 Chat Intelligent Avancé</div>
                <div class="app-description">Conversations naturelles avec apprentissage par renforcement et analyse émotionnelle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>IA Évolutive - QI 203</span>
                </div>
            </div>

            <!-- Centre de Génération -->
            <div class="app-card generation" onclick="openApp('/generation-center.html')">
                <div class="app-icon"><i class="fas fa-magic"></i></div>
                <div class="app-title">🎨 Centre de Génération IA</div>
                <div class="app-description">Hub central pour génération d'images, vidéos LTX, musique et modèles 3D</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Création illimitée</span>
                </div>
            </div>

            <!-- Génération d'Images -->
            <div class="app-card generation" onclick="openApp('/image-generator-simple.html')">
                <div class="app-icon"><i class="fas fa-image"></i></div>
                <div class="app-title">🖼️ Génération d'Images</div>
                <div class="app-description">Création d'images illimitée avec styles artistiques variés et galerie intégrée</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Images HD illimitées</span>
                </div>
            </div>

            <!-- Génération Vidéo LTX -->
            <div class="app-card generation" onclick="openApp('/video-generator.html')">
                <div class="app-icon"><i class="fas fa-video"></i></div>
                <div class="app-title">🎥 Génération Vidéo LTX</div>
                <div class="app-description">Technologie LTX avancée pour créer des vidéos cinématiques jusqu'à 4K</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Technologie LTX</span>
                </div>
            </div>

            <!-- Centre de Sécurité Avancée -->
            <div class="app-card system" onclick="openApp('/security-center.html')">
                <div class="app-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="app-title">🔐 Centre de Sécurité Avancée</div>
                <div class="app-description">Protection complète : Firewall, Antivirus, Chiffrement, VPN et Biométrie</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Protection 95.8%</span>
                </div>
            </div>

            <!-- Générateur Musical IA -->
            <div class="app-card generation" onclick="openApp('/music-generator.html')">
                <div class="app-icon"><i class="fas fa-music"></i></div>
                <div class="app-title">🎵 Générateur Musical IA</div>
                <div class="app-description">Composition automatique et arrangements intelligents avec instruments virtuels</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Création musicale</span>
                </div>
            </div>

            <!-- Recherche Web Intelligente -->
            <div class="app-card analysis" onclick="openApp('/web-search.html')">
                <div class="app-icon"><i class="fas fa-search"></i></div>
                <div class="app-title">🔍 Recherche Web IA</div>
                <div class="app-description">Recherche intelligente avec analyse de contenu et synthèse automatique</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Recherche avancée</span>
                </div>
            </div>

            <!-- Reconnaissance Faciale -->
            <div class="app-card analysis" onclick="openApp('/face-recognition.html')">
                <div class="app-icon"><i class="fas fa-user-check"></i></div>
                <div class="app-title">👁️ Reconnaissance Faciale</div>
                <div class="app-description">Système de reconnaissance faciale avancé avec analyse émotionnelle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Détection temps réel</span>
                </div>
            </div>

            <!-- Dashboard Apprentissage -->
            <div class="app-card monitoring" onclick="openApp('/learning-dashboard.html')">
                <div class="app-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="app-title">📊 Dashboard Apprentissage</div>
                <div class="app-description">Suivi de l'apprentissage par renforcement et évolution du QI</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Apprentissage actif</span>
                </div>
            </div>

            <!-- Test Mémoire Biologique -->
            <div class="app-card memory" onclick="openApp('/biological-memory-test.html')">
                <div class="app-icon"><i class="fas fa-dna"></i></div>
                <div class="app-title">🧬 Test Mémoire Biologique</div>
                <div class="app-description">Interface de test pour la mémoire biologique artificielle</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système biologique actif</span>
                </div>
            </div>

            <!-- Gestionnaire de Fonctionnalités Avancées -->
            <div class="app-card features" onclick="openApp('/advanced-features-manager.html')">
                <div class="app-icon"><i class="fas fa-rocket"></i></div>
                <div class="app-title">🚀 Fonctionnalités Avancées</div>
                <div class="app-description">Restauration et gestion de toutes les fonctionnalités avancées</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système de restauration prêt</span>
                </div>
            </div>

            <!-- Gestionnaire de Connexion Directe -->
            <div class="app-card system" onclick="openApp('/direct-connection-manager.html')">
                <div class="app-icon"><i class="fas fa-bolt"></i></div>
                <div class="app-title">⚡ Connexion Directe</div>
                <div class="app-description">Système de connexion directe sans Ollama - Vitesse optimisée</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Optimisation active</span>
                </div>
            </div>

            <!-- Mémoire Thermique -->
            <div class="app-card memory" onclick="openApp('/futuristic-interface.html')">
                <div class="app-icon"><i class="fas fa-fire"></i></div>
                <div class="app-title">Mémoire Thermique</div>
                <div class="app-description">Système de mémoire thermique avec 5 zones distinctes</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Actif - 42.3°C</span>
                </div>
            </div>

            <!-- Tableau de Bord Cérébral VIVANT -->
            <div class="app-card brain" onclick="openApp('/brain-dashboard-live.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">🧠 Tableau de Bord VIVANT</div>
                <div class="app-description">6 zones cérébrales en activité temps réel avec animations</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Activité en direct</span>
                </div>
            </div>

            <!-- Cerveau 3D VIVANT -->
            <div class="app-card brain" onclick="openApp('/brain-3d-live.html')">
                <div class="app-icon"><i class="fas fa-cube"></i></div>
                <div class="app-title">🧠 Cerveau 3D VIVANT</div>
                <div class="app-description">Visualisation 3D animée avec flux de données et particules</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Animations actives</span>
                </div>
            </div>

            <!-- Cerveau 3D (Original) -->
            <div class="app-card brain" onclick="openApp('/brain-visualization.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Visualisation Cerveau 3D</div>
                <div class="app-description">Visualisation 3D du cerveau artificiel en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>71 neurones actifs</span>
                </div>
            </div>

            <!-- QI Monitor -->
            <div class="app-card monitoring" onclick="openApp('/qi-neuron-monitor.html')">
                <div class="app-icon"><i class="fas fa-yin-yang"></i></div>
                <div class="app-title">Monitoring QI & Neurones</div>
                <div class="app-description">Surveillance en temps réel du QI et de l'activité neuronale</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>QI: 148</span>
                </div>
            </div>

            <!-- Monitoring Cérébral Complet -->
            <div class="app-card monitoring" onclick="openApp('/brain-monitoring-complete.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Monitoring Cérébral Complet</div>
                <div class="app-description">Surveillance complète de tous les aspects du cerveau artificiel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système optimal</span>
                </div>
            </div>

            <!-- Transfert d'Informations -->
            <div class="app-card monitoring" onclick="openApp('/information-transfer-monitor.html')">
                <div class="app-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="app-title">Transfert d'Informations</div>
                <div class="app-description">Monitoring du flux et transfert d'informations neuronales</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Flux actif</span>
                </div>
            </div>

            <!-- Centre d'Évolution & Apprentissage -->
            <div class="app-card monitoring" onclick="openApp('/evolution-learning-center.html')">
                <div class="app-icon"><i class="fas fa-dna"></i></div>
                <div class="app-title">Centre d'Évolution & Apprentissage</div>
                <div class="app-description">Système d'évolution auto-adaptative et formation continue</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Auto-évolution active</span>
                </div>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="app-card system" onclick="openApp('/kyber-dashboard.html')">
                <div class="app-icon"><i class="fas fa-bolt"></i></div>
                <div class="app-title">Accélérateurs Kyber</div>
                <div class="app-description">Système d'accélération quantique pour boost de performance</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>8 accélérateurs actifs</span>
                </div>
            </div>

            <!-- Studio de Génération -->
            <div class="app-card generation" onclick="openApp('/generation-studio.html')">
                <div class="app-icon"><i class="fas fa-magic"></i></div>
                <div class="app-title">Studio de Génération</div>
                <div class="app-description">Création de contenu multimédia : vidéos, images, musique, 3D</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt à créer</span>
                </div>
            </div>

            <!-- Interface Vocale -->
            <div class="app-card chat" onclick="openApp('/voice-interface.html')">
                <div class="app-icon"><i class="fas fa-microphone"></i></div>
                <div class="app-title">Interface Vocale</div>
                <div class="app-description">Communication vocale avec Louna - Reconnaissance et synthèse</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt à parler</span>
                </div>
            </div>

            <!-- Paramètres Avancés -->
            <div class="app-card settings" onclick="openApp('/settings-advanced.html')">
                <div class="app-icon"><i class="fas fa-cogs"></i></div>
                <div class="app-title">Paramètres Avancés</div>
                <div class="app-description">Configuration complète de tous les modules Louna</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Système configuré</span>
                </div>
            </div>

            <!-- Tableau de Bord Principal -->
            <div class="app-card dashboard" onclick="openApp('/dashboard-master.html')">
                <div class="app-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="app-title">Tableau de Bord Principal</div>
                <div class="app-description">Vue d'ensemble de tous les systèmes en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Monitoring actif</span>
                </div>
            </div>
        </div>

        <!-- Applications secondaires -->
        <h2 class="category-title">🛠️ Outils & Système</h2>
        <div class="apps-grid">
            <!-- Éditeur de Code -->
            <div class="app-card development" onclick="openApp('/code-editor.html')">
                <div class="app-icon"><i class="fas fa-code"></i></div>
                <div class="app-title">Éditeur de Code</div>
                <div class="app-description">Éditeur de code intelligent avec assistance IA</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt</span>
                </div>
            </div>

            <!-- Logs Système -->
            <div class="app-card system" onclick="openApp('/system-logs.html')">
                <div class="app-icon"><i class="fas fa-terminal"></i></div>
                <div class="app-title">Logs Système</div>
                <div class="app-description">Journal système en temps réel avec filtrage avancé</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Enregistrement actif</span>
                </div>
            </div>

            <!-- Analyses Comparatives -->
            <div class="app-card analysis" onclick="openApp('/brain-comparison-analysis.html')">
                <div class="app-icon"><i class="fas fa-balance-scale"></i></div>
                <div class="app-title">Analyses Comparatives</div>
                <div class="app-description">Tableaux cerveau humain vs mémoire thermique</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Comparaisons actives</span>
                </div>
            </div>

            <!-- Gestionnaire QI -->
            <div class="app-card monitoring" onclick="openApp('/test-qi-manager.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Gestionnaire QI</div>
                <div class="app-description">Tests et évaluation du QI centralisé</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>QI: <span id="qi-value">148</span></span>
                </div>
            </div>

            <!-- Test Comparatif Mémoire -->
            <div class="app-card analysis" onclick="openApp('/memory-comparison-test.html')">
                <div class="app-icon"><i class="fas fa-microscope"></i></div>
                <div class="app-title">Test Mémoire Thermique</div>
                <div class="app-description">Comparaison temps réel vs cerveau humain</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Fidélité: 95%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de test -->
    <div class="louna-container">
        <h2 class="category-title">🧪 Test de Navigation</h2>
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testNavigation()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 1.1rem; cursor: pointer; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                🧪 Tester la Navigation
            </button>
            <div id="test-results" style="margin-top: 20px; color: white; font-weight: bold;"></div>
        </div>
    </div>

    <div class="footer">
        <p>Louna - Agent IA Révolutionnaire &copy; 2025</p>
        <p>Créé par Jean-Luc Passave - Sainte-Anne, Guadeloupe</p>
    </div>

    <!-- Scripts Louna -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <script src="/js/qi-manager.js"></script>

    <script>
        // Configuration de la navigation Louna
        window.lounaNavConfig = {
            header: false, // On garde le header existant pour l'instant
            quickNav: false,
            breadcrumb: false,
            sidebar: false
        };

        // Fonction pour ouvrir une application
        function openApp(url) {
            console.log('🚀 Ouverture de:', url);

            // Afficher une notification
            showLoading('Chargement de l\'application...');

            // Vérifier que l'URL est accessible
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        showSuccess('Application chargée avec succès !');
                        setTimeout(() => {
                            window.location.href = url;
                        }, 500);
                    } else {
                        showError('Application non disponible');
                    }
                })
                .catch(error => {
                    showError('Erreur de connexion à l\'application');
                    console.error('Erreur:', error);
                });
        }

        // Fonction de test de navigation
        function testNavigation() {
            const testResults = document.getElementById('test-results');
            testResults.innerHTML = '🧪 Test en cours...';

            // Afficher une notification de début de test
            const testNotificationId = showLoading('Test de navigation en cours...');

            // Test des applications principales
            const apps = [
                { url: '/chat', name: 'Chat Intelligent' },
                { url: '/chat-agents.html', name: 'Chat avec Agents' },
                { url: '/biological-memory-test.html', name: 'Test Mémoire Biologique' },
                { url: '/advanced-features-manager.html', name: 'Fonctionnalités Avancées' },
                { url: '/futuristic-interface.html', name: 'Mémoire Thermique' },
                { url: '/brain-dashboard-live.html', name: 'Tableau de Bord VIVANT' },
                { url: '/brain-3d-live.html', name: 'Cerveau 3D VIVANT' },
                { url: '/brain-visualization.html', name: 'Visualisation 3D' },
                { url: '/qi-neuron-monitor.html', name: 'Monitoring QI' },
                { url: '/brain-monitoring-complete.html', name: 'Monitoring Cérébral Complet' },
                { url: '/information-transfer-monitor.html', name: 'Transfert d\'Informations' },
                { url: '/evolution-learning-center.html', name: 'Centre d\'Évolution & Apprentissage' },
                { url: '/kyber-dashboard.html', name: 'Accélérateurs Kyber' },
                { url: '/generation-studio.html', name: 'Studio de Génération' },
                { url: '/voice-interface.html', name: 'Interface Vocale' },
                { url: '/code-editor.html', name: 'Éditeur de Code' },
                { url: '/settings-advanced.html', name: 'Paramètres Avancés' },
                { url: '/dashboard-master.html', name: 'Tableau de Bord' },
                { url: '/system-logs.html', name: 'Logs Système' },
                { url: '/brain-comparison-analysis.html', name: 'Analyses Comparatives' },
                { url: '/test-qi-manager.html', name: 'Gestionnaire QI' },
                { url: '/memory-comparison-test.html', name: 'Test Mémoire Thermique' }
            ];

            let testCount = 0;
            let successCount = 0;
            const results = [];

            apps.forEach((app, index) => {
                setTimeout(() => {
                    fetch(app.url)
                        .then(response => {
                            testCount++;
                            if (response.ok) {
                                successCount++;
                                results.push({ ...app, status: 'success' });
                                console.log('✅ Test réussi:', app.name);
                            } else {
                                results.push({ ...app, status: 'error' });
                                console.log('❌ Test échoué:', app.name);
                            }

                            // Mettre à jour la progression
                            const progress = Math.round((testCount / apps.length) * 100);
                            LounaNotify.update(testNotificationId, `Test en cours... ${progress}%`, 'loading');

                            if (testCount === apps.length) {
                                // Fermer la notification de test
                                LounaNotify.dismiss(testNotificationId);

                                // Afficher les résultats
                                testResults.innerHTML = `✅ Test terminé: ${successCount}/${apps.length} applications accessibles`;

                                if (successCount === apps.length) {
                                    testResults.innerHTML += '<br>🎉 Toutes les applications fonctionnent !';
                                    showSuccess('🎉 Tous les tests sont passés avec succès !');
                                } else {
                                    const failedApps = results.filter(r => r.status === 'error');
                                    showWarning(`⚠️ ${failedApps.length} application(s) non disponible(s)`);
                                }

                                // Afficher les détails
                                results.forEach(result => {
                                    if (result.status === 'success') {
                                        LounaNotify.system(`${result.name} : Opérationnel`, 'success');
                                    } else {
                                        LounaNotify.system(`${result.name} : Non disponible`, 'error');
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            testCount++;
                            results.push({ ...app, status: 'error' });
                            console.log('❌ Erreur test:', app.name, error);

                            const progress = Math.round((testCount / apps.length) * 100);
                            LounaNotify.update(testNotificationId, `Test en cours... ${progress}%`, 'loading');

                            if (testCount === apps.length) {
                                LounaNotify.dismiss(testNotificationId);
                                testResults.innerHTML = `⚠️ Test terminé: ${successCount}/${apps.length} applications accessibles`;
                                showWarning('⚠️ Certaines applications ne sont pas disponibles');
                            }
                        });
                }, index * 300); // Délai plus long pour éviter la surcharge
            });
        }

        // Mise à jour des statistiques (QI géré par qi-manager.js)
        function updateStats() {
            const memoryTemp = document.getElementById('memory-temp');
            const neuronCount = document.getElementById('neuron-count');
            const kyberCount = document.getElementById('kyber-count');

            // Fonction pour récupérer les statistiques (sauf QI)
            async function fetchStats() {
                try {
                    // Récupérer les stats de mémoire thermique
                    const memResponse = await fetch('/api/thermal/memory/stats');
                    if (memResponse.ok) {
                        const memData = await memResponse.json();

                        if (memData.temperature) {
                            memoryTemp.textContent = memData.temperature.toFixed(1) + '°C';
                        }
                    }

                    // Récupérer les stats du cerveau artificiel
                    const brainResponse = await fetch('/api/brain/status');
                    if (brainResponse.ok) {
                        const brainData = await brainResponse.json();
                        if (brainData.brain && brainData.brain.totalNeurons) {
                            neuronCount.textContent = brainData.brain.totalNeurons;
                        }
                    }

                    // Récupérer les accélérateurs Kyber
                    const kyberResponse = await fetch('/api/kyber/status');
                    if (kyberResponse.ok) {
                        const kyberData = await kyberResponse.json();
                        if (Array.isArray(kyberData)) {
                            const activeAccelerators = kyberData.filter(acc => acc.active).length;
                            kyberCount.textContent = activeAccelerators;
                        }
                    }

                } catch (error) {
                    console.warn('Erreur récupération stats:', error);
                }
            }

            // Récupérer les stats immédiatement
            fetchStats();

            // Puis toutes les 5 secondes
            setInterval(fetchStats, 5000);
        }

        // Démarrer les animations
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();

            // Notification de bienvenue
            setTimeout(() => {
                showSuccess('🎯 Hub Central Louna initialisé avec succès !');
                LounaNotify.system('Toutes les interfaces sont opérationnelles', 'success');
                LounaNotify.memory('Mémoire thermique connectée et fonctionnelle', 'success');
                LounaNotify.agent('Agent Claude 4GB prêt à vous assister', 'success');
            }, 1000);

            console.log('🎯 Hub Central Louna initialisé');
        });
    </script>
</body>
</html>
