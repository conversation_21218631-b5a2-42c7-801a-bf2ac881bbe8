<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mémoire Thermique - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <link rel="stylesheet" href="/css/louna-thermal.css?v=2025">
    <link rel="stylesheet" href="/css/theme-switcher.css?v=2025">
    <link rel="stylesheet" href="/css/notifications.css?v=2025">
    <link rel="stylesheet" href="/css/native-app.css?v=2025">
    <link rel="stylesheet" href="/css/contrast-fixes.css?v=2025">
    <link rel="stylesheet" href="/css/home-button.css?v=2025">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        .thermal-container {
            padding: 15px;
            margin-top: 10px;
        }

        .thermal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .thermal-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .thermal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .thermal-header h3 {
            color: #ff69b4;
            margin: 0;
            font-size: 18px;
        }

        .thermal-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .thermal-status.active {
            background: rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .thermal-status.inactive {
            background: rgba(158, 158, 158, 0.3);
            color: #9e9e9e;
        }

        .memory-zones {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .memory-zone {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-zone:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .zone-temp {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .zone-temp.hot { color: #ff6b6b; }
        .zone-temp.warm { color: #ffa726; }
        .zone-temp.medium { color: #66bb6a; }
        .zone-temp.cool { color: #42a5f5; }

        .zone-label {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 5px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .zone-count {
            font-size: 16px;
            color: #ffffff;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #ffffff;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .memory-entries {
            max-height: 300px;
            overflow-y: auto;
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 3px solid;
            transition: all 0.3s ease;
        }

        .memory-entry:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(3px);
        }

        .memory-entry.hot { border-left-color: #ff6b6b; }
        .memory-entry.warm { border-left-color: #ffa726; }
        .memory-entry.medium { border-left-color: #66bb6a; }
        .memory-entry.cool { border-left-color: #42a5f5; }

        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .entry-type {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .entry-temp {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }

        .entry-content {
            font-size: 15px;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.5;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        }

        .entry-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .loading-indicator.hidden {
            display: none;
        }

        .thermal-visualization {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .temp-gauge {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            position: relative;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                #42a5f5 0deg 90deg,
                #66bb6a 90deg 180deg,
                #ffa726 180deg 270deg,
                #ff6b6b 270deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .temp-gauge::before {
            content: '';
            width: 160px;
            height: 160px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            position: absolute;
        }

        .temp-value {
            font-size: 32px;
            font-weight: bold;
            color: #ff69b4;
            z-index: 1;
        }

        .temp-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 10px;
        }
    </style>


</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-fire louna-header-icon"></i>
                <h1>Mémoire Thermique</h1>
            </div>
            <div class="louna-nav">
                <a href="/" class="louna-nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="louna-nav-btn">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/brain-monitoring-complete.html" class="louna-nav-btn">
                    <i class="fas fa-brain"></i>
                    <span>Monitoring</span>
                </a>
                <a href="/brain-visualization.html" class="louna-nav-btn">
                    <i class="fas fa-cube"></i>
                    <span>3D</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Mémoire active</span>
            </div>
        </div>
    </div>

    <!-- En-tête unifié -->
    <div class="unified-container">
        <div class="unified-header">
            <h1><i class="fas fa-fire"></i> Mémoire Thermique</h1>
            <p class="subtitle">Visualisation et gestion des zones de mémoire par température - Système évolutif</p>
        </div>

        <!-- Navigation unifiée -->
        <div class="unified-nav">
            <div class="nav-buttons">
                <a href="/chat" class="unified-button nav-button">
                    <i class="fas fa-comments"></i> Chat Intelligent
                </a>
                <a href="/brain-visualization.html" class="unified-button nav-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="unified-button nav-button">
                    <i class="fas fa-yin-yang"></i> Monitoring QI
                </a>
                <a href="/generation-studio.html" class="unified-button nav-button">
                    <i class="fas fa-magic"></i> Studio Génération
                </a>
                <a href="/kyber-dashboard.html" class="unified-button nav-button">
                    <i class="fas fa-bolt"></i> Accélérateurs Kyber
                </a>
            </div>
        </div>

        <!-- Indicateur de chargement -->
        <div class="loading-indicator" id="loading-indicator">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Chargement des données de mémoire thermique...</p>
        </div>

        <!-- Grille principale -->
        <div class="unified-grid cols-2" id="thermal-grid" style="display: none;">
            <!-- Visualisation thermique -->
            <div class="unified-card">
                <h2><i class="fas fa-thermometer-half"></i> Température Globale</h2>
                <div class="unified-status active">
                    <i class="fas fa-check-circle"></i> Actif
                </div>
                <div class="thermal-visualization">
                    <div class="temp-gauge">
                        <div class="temp-value" id="global-temp">0.65</div>
                    </div>
                    <div class="temp-label">Température moyenne du système</div>
                </div>
            </div>

            <!-- Zones de mémoire -->
            <div class="unified-card">
                <h2><i class="fas fa-layer-group"></i> Zones de Mémoire</h2>
                <div class="unified-status info">
                    <i class="fas fa-layer-group"></i> 6 Zones
                </div>
                <div class="memory-zones" id="memory-zones">
                    <!-- Les zones seront générées dynamiquement -->
                </div>
            </div>

            <!-- Statistiques -->
            <div class="unified-card">
                <h2><i class="fas fa-chart-bar"></i> Statistiques</h2>
                <div class="unified-status active">
                    <i class="fas fa-clock"></i> Temps réel
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-memories">0</div>
                        <div class="stat-label">Mémoires totales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="hot-memories">0</div>
                        <div class="stat-label">Mémoires chaudes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="cycles-count">0</div>
                        <div class="stat-label">Cycles effectués</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Mémoires récentes -->
            <div class="unified-card">
                <h2><i class="fas fa-history"></i> Mémoires Récentes</h2>
                <div class="unified-status active">
                    <i class="fas fa-pulse"></i> Actives
                </div>
                <div class="memory-entries" id="memory-entries">
                    <!-- Les entrées seront générées dynamiquement -->
                </div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="unified-card" id="control-buttons" style="display: none;">
            <h3>Contrôles de Mémoire</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
                <button class="unified-button" id="cycle-btn">
                    <i class="fas fa-sync"></i> Cycle de Mémoire
                </button>
                <button class="unified-button success" id="optimize-btn">
                    <i class="fas fa-magic"></i> Optimiser
                </button>
                <button class="unified-button info" id="consolidate-btn">
                    <i class="fas fa-compress"></i> Consolider
                </button>
                <button class="unified-button secondary" id="analyze-btn">
                    <i class="fas fa-search"></i> Analyser
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let thermalData = null;
        let updateInterval = null;
        let isSystemActive = true;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 Initialisation de la mémoire thermique...');

            // Démarrer le chargement des données
            setTimeout(() => {
                loadThermalData();
            }, 1000);

            // Configurer les événements
            setupEventListeners();

            console.log('✅ Mémoire thermique initialisée');
        });

        // Configuration des événements
        function setupEventListeners() {
            // Bouton actualiser
            document.getElementById('refresh-btn')?.addEventListener('click', function(e) {
                e.preventDefault();
                loadThermalData();
            });

            // Boutons de contrôle
            document.getElementById('cycle-btn')?.addEventListener('click', performMemoryCycle);
            document.getElementById('optimize-btn')?.addEventListener('click', optimizeMemory);
            document.getElementById('consolidate-btn')?.addEventListener('click', consolidateMemory);
            document.getElementById('analyze-btn')?.addEventListener('click', analyzeMemory);
        }

        // Charger les données thermiques
        async function loadThermalData() {
            const loadingIndicator = document.getElementById('loading-indicator');
            const thermalGrid = document.getElementById('thermal-grid');
            const controlButtons = document.getElementById('control-buttons');

            try {
                // Afficher l'indicateur de chargement
                loadingIndicator.style.display = 'block';
                thermalGrid.style.display = 'none';
                controlButtons.style.display = 'none';

                // Essayer de charger les vraies données
                const response = await fetch('/api/thermal/status');
                let data;

                if (response.ok) {
                    data = await response.json();
                } else {
                    throw new Error('API non disponible');
                }

                // Afficher les données
                displayThermalData(data);

            } catch (error) {
                console.log('API non disponible, utilisation de données simulées');
                // Utiliser des données simulées
                const simulatedData = generateSimulatedThermalData();
                displayThermalData(simulatedData);
            }
        }

        // Afficher les données thermiques
        function displayThermalData(data) {
            const loadingIndicator = document.getElementById('loading-indicator');
            const thermalGrid = document.getElementById('thermal-grid');
            const controlButtons = document.getElementById('control-buttons');

            // Masquer l'indicateur de chargement
            loadingIndicator.style.display = 'none';

            // Sauvegarder les données
            thermalData = data;

            // Mettre à jour l'interface
            updateGlobalTemperature(data.globalTemp);
            updateMemoryZones(data.zones);
            updateStatistics(data.stats);
            updateRecentMemories(data.recentMemories);

            // Afficher les sections
            thermalGrid.style.display = 'grid';
            controlButtons.style.display = 'flex';

            // Démarrer les mises à jour automatiques
            if (!updateInterval) {
                updateInterval = setInterval(() => {
                    if (isSystemActive) {
                        loadThermalData();
                    }
                }, 8000); // Mise à jour toutes les 8 secondes
            }
        }

        // Mettre à jour la température globale
        function updateGlobalTemperature(temp) {
            const tempElement = document.getElementById('global-temp');
            if (tempElement) {
                tempElement.textContent = temp.toFixed(2);

                // Changer la couleur selon la température
                if (temp > 0.8) {
                    tempElement.style.color = '#ff6b6b';
                } else if (temp > 0.6) {
                    tempElement.style.color = '#ffa726';
                } else if (temp > 0.4) {
                    tempElement.style.color = '#66bb6a';
                } else {
                    tempElement.style.color = '#42a5f5';
                }
            }
        }

        // Mettre à jour les zones de mémoire
        function updateMemoryZones(zones) {
            const zonesContainer = document.getElementById('memory-zones');
            if (!zonesContainer) return;

            zonesContainer.innerHTML = '';

            zones.forEach(zone => {
                const zoneElement = document.createElement('div');
                zoneElement.className = 'memory-zone';
                zoneElement.onclick = () => exploreZone(zone.name);

                const tempClass = getTempClass(zone.temperature);

                zoneElement.innerHTML = `
                    <div class="zone-temp ${tempClass}">${zone.temperature.toFixed(2)}</div>
                    <div class="zone-label">${zone.name}</div>
                    <div class="zone-count">${zone.count} mémoires</div>
                `;

                zonesContainer.appendChild(zoneElement);
            });
        }

        // Mettre à jour les statistiques
        function updateStatistics(stats) {
            document.getElementById('total-memories').textContent = stats.totalMemories || 0;
            document.getElementById('hot-memories').textContent = stats.hotMemories || 0;
            document.getElementById('cycles-count').textContent = stats.cyclesCount || 0;
            document.getElementById('efficiency').textContent = (stats.efficiency || 0) + '%';
        }

        // Mettre à jour les mémoires récentes
        function updateRecentMemories(memories) {
            const entriesContainer = document.getElementById('memory-entries');
            if (!entriesContainer) return;

            entriesContainer.innerHTML = '';

            if (!memories || memories.length === 0) {
                entriesContainer.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.6); padding: 20px;">Aucune mémoire récente</div>';
                return;
            }

            memories.forEach(memory => {
                const entryElement = document.createElement('div');
                const tempClass = getTempClass(memory.temperature);
                entryElement.className = `memory-entry ${tempClass}`;

                entryElement.innerHTML = `
                    <div class="entry-header">
                        <div class="entry-type">${memory.type}</div>
                        <div class="entry-temp ${tempClass}">${memory.temperature.toFixed(2)}°</div>
                    </div>
                    <div class="entry-content">${memory.content}</div>
                    <div class="entry-footer">
                        <span>${memory.tags || 'Aucun tag'}</span>
                        <span>${formatTime(memory.timestamp)}</span>
                    </div>
                `;

                entriesContainer.appendChild(entryElement);
            });
        }

        // Obtenir la classe de température
        function getTempClass(temp) {
            if (temp > 0.8) return 'hot';
            if (temp > 0.6) return 'warm';
            if (temp > 0.4) return 'medium';
            return 'cool';
        }

        // Formater le temps
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }

        // Explorer une zone
        function exploreZone(zoneName) {
            alert(`Exploration de la zone "${zoneName}"\n\nCette fonctionnalité permettra d'analyser en détail les mémoires de cette zone thermique.`);
        }

        // Effectuer un cycle de mémoire
        async function performMemoryCycle() {
            const button = document.getElementById('cycle-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cycle en cours...';

            try {
                // Simuler le cycle
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Recharger les données
                await loadThermalData();

                alert('Cycle de mémoire terminé avec succès!\n\nLes températures ont été recalculées et les connexions optimisées.');

            } catch (error) {
                console.error('Erreur lors du cycle:', error);
                alert('Erreur lors du cycle de mémoire: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync"></i> Cycle de Mémoire';
            }
        }

        // Optimiser la mémoire
        async function optimizeMemory() {
            const button = document.getElementById('optimize-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimisation...';

            try {
                // Simuler l'optimisation
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Recharger les données
                await loadThermalData();

                alert('Optimisation terminée!\n\nLes performances du système de mémoire ont été améliorées.');

            } catch (error) {
                console.error('Erreur lors de l\'optimisation:', error);
                alert('Erreur lors de l\'optimisation: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-magic"></i> Optimiser';
            }
        }

        // Consolider la mémoire
        async function consolidateMemory() {
            const button = document.getElementById('consolidate-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Consolidation...';

            try {
                // Simuler la consolidation
                await new Promise(resolve => setTimeout(resolve, 2500));

                // Recharger les données
                await loadThermalData();

                alert('Consolidation terminée!\n\nLes mémoires similaires ont été regroupées et les connexions renforcées.');

            } catch (error) {
                console.error('Erreur lors de la consolidation:', error);
                alert('Erreur lors de la consolidation: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-compress"></i> Consolider';
            }
        }

        // Analyser la mémoire
        async function analyzeMemory() {
            const button = document.getElementById('analyze-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyse...';

            try {
                // Simuler l'analyse
                await new Promise(resolve => setTimeout(resolve, 1500));

                if (thermalData) {
                    const analysis = generateAnalysis(thermalData);
                    alert(`Analyse du système de mémoire thermique:\n\n${analysis}`);
                }

            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);
                alert('Erreur lors de l\'analyse: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-search"></i> Analyser';
            }
        }

        // Générer une analyse
        function generateAnalysis(data) {
            const hotZones = data.zones.filter(z => z.temperature > 0.7).length;
            const coolZones = data.zones.filter(z => z.temperature < 0.3).length;
            const efficiency = data.stats.efficiency || 0;

            let analysis = `Température globale: ${data.globalTemp.toFixed(2)}\n`;
            analysis += `Zones chaudes: ${hotZones}\n`;
            analysis += `Zones froides: ${coolZones}\n`;
            analysis += `Efficacité: ${efficiency}%\n\n`;

            if (data.globalTemp > 0.8) {
                analysis += 'Recommandation: Système très actif, considérer une consolidation.';
            } else if (data.globalTemp < 0.3) {
                analysis += 'Recommandation: Système peu actif, stimuler avec de nouvelles données.';
            } else {
                analysis += 'Recommandation: Système équilibré, continuer le monitoring.';
            }

            return analysis;
        }

        // Générer des données simulées
        function generateSimulatedThermalData() {
            const zones = [
                { name: 'Sensorielle', temperature: 0.3 + Math.random() * 0.4, count: 15 + Math.floor(Math.random() * 10) },
                { name: 'Travail', temperature: 0.5 + Math.random() * 0.3, count: 8 + Math.floor(Math.random() * 7) },
                { name: 'Long Terme', temperature: 0.2 + Math.random() * 0.5, count: 25 + Math.floor(Math.random() * 15) },
                { name: 'Émotionnelle', temperature: 0.4 + Math.random() * 0.4, count: 12 + Math.floor(Math.random() * 8) },
                { name: 'Procédurale', temperature: 0.3 + Math.random() * 0.3, count: 18 + Math.floor(Math.random() * 12) },
                { name: 'Créative', temperature: 0.6 + Math.random() * 0.3, count: 7 + Math.floor(Math.random() * 5) }
            ];

            const globalTemp = zones.reduce((sum, zone) => sum + zone.temperature, 0) / zones.length;

            const recentMemories = [
                {
                    type: 'Conversation',
                    content: 'Discussion sur l\'intelligence artificielle et ses applications',
                    temperature: 0.8 + Math.random() * 0.2,
                    tags: 'IA, technologie',
                    timestamp: Date.now() - Math.random() * 3600000
                },
                {
                    type: 'Apprentissage',
                    content: 'Nouvelle technique de programmation apprise',
                    temperature: 0.7 + Math.random() * 0.2,
                    tags: 'code, apprentissage',
                    timestamp: Date.now() - Math.random() * 7200000
                },
                {
                    type: 'Réflexion',
                    content: 'Analyse des patterns de comportement utilisateur',
                    temperature: 0.6 + Math.random() * 0.3,
                    tags: 'analyse, comportement',
                    timestamp: Date.now() - Math.random() * 10800000
                },
                {
                    type: 'Créativité',
                    content: 'Génération d\'idées pour améliorer l\'interface',
                    temperature: 0.5 + Math.random() * 0.4,
                    tags: 'créativité, UI/UX',
                    timestamp: Date.now() - Math.random() * 14400000
                }
            ];

            const stats = {
                totalMemories: zones.reduce((sum, zone) => sum + zone.count, 0),
                hotMemories: recentMemories.filter(m => m.temperature > 0.7).length,
                cyclesCount: 15 + Math.floor(Math.random() * 10),
                efficiency: 75 + Math.floor(Math.random() * 20)
            };

            return {
                globalTemp,
                zones,
                recentMemories,
                stats
            };
        }

        // Gestion de la visibilité de la page
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                isSystemActive = false;
            } else {
                isSystemActive = true;
                loadThermalData();
            }
        });
    </script>

    <!-- Scripts de navigation -->
    <script src="/js/louna-navigation.js"></script>
    <script src="/js/louna-notifications.js"></script>
    <script src="js/native-app.js"></script>

</body>
</html>
