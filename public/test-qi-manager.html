<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Test du Gestionnaire QI Centralisé - Louna</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        h1 {
            text-align: center;
            color: #ff6b9d;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ff6b9d;
        }

        .test-title {
            color: #ff6b9d;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .qi-display {
            font-size: 3em;
            color: #00ff88;
            text-align: center;
            margin: 20px 0;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .test-result {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }

        .error {
            background: rgba(255, 107, 157, 0.1);
            border: 1px solid #ff6b9d;
            color: #ff6b9d;
        }

        button {
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 157, 0.4);
        }

        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin: 5px;
        }

        .status.success {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }

        .status.error {
            background: rgba(255, 107, 157, 0.2);
            color: #ff6b9d;
        }

        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .qi-element {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .qi-element h3 {
            color: #4ecdc4;
            margin-bottom: 10px;
        }

        .qi-value {
            font-size: 2em;
            color: #00ff88;
            font-weight: bold;
        }

        .log {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            margin: 20px 0;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #4ecdc4;
            padding-left: 10px;
        }

        .log-entry.error {
            border-left-color: #ff6b9d;
            color: #ff6b9d;
        }

        .log-entry.success {
            border-left-color: #00ff88;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Test du Gestionnaire QI Centralisé</h1>
        
        <div class="test-section">
            <div class="test-title">📊 QI Actuel (Source Centralisée)</div>
            <div class="qi-display" id="main-qi">Chargement...</div>
            <div id="qi-status"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 Actions de Test</div>
            <button onclick="testQIManager()">🧪 Tester le Gestionnaire QI</button>
            <button onclick="forceUpdate()">🔄 Forcer la Mise à Jour</button>
            <button onclick="reevaluateQI()">📊 Réévaluer le QI</button>
            <button onclick="testAllElements()">🎯 Tester Tous les Éléments</button>
            <button onclick="clearLog()">🗑️ Vider le Log</button>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 Éléments QI Détectés</div>
            <div class="grid" id="qi-elements-grid">
                <!-- Les éléments QI seront ajoutés ici -->
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 Log des Tests</div>
            <div class="log" id="test-log"></div>
        </div>
    </div>

    <script src="/js/qi-manager.js"></script>
    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            testLog.push({ message: entry, type });
            
            const logElement = document.getElementById('test-log');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = entry;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[QI-Test] ${entry}`);
        }

        function clearLog() {
            testLog = [];
            document.getElementById('test-log').innerHTML = '';
            log('Log vidé', 'success');
        }

        async function testQIManager() {
            log('🧪 Début des tests du gestionnaire QI...', 'info');
            
            try {
                // Test 1: Vérifier que le gestionnaire existe
                if (window.qiManager) {
                    log('✅ Gestionnaire QI trouvé', 'success');
                } else {
                    log('❌ Gestionnaire QI non trouvé', 'error');
                    return;
                }

                // Test 2: Obtenir le QI actuel
                const currentQI = window.qiManager.getCurrentQI();
                log(`📊 QI actuel: ${currentQI}`, 'success');
                document.getElementById('main-qi').textContent = currentQI;

                // Test 3: Tester l'API
                const response = await fetch('/api/qi/current');
                if (response.ok) {
                    const data = await response.json();
                    log(`🌐 API QI: ${JSON.stringify(data.qi || data)}`, 'success');
                } else {
                    log('❌ Erreur API QI', 'error');
                }

                // Test 4: Tester la mise à jour des éléments
                window.qiManager.updateAllQIElements();
                log('🔄 Mise à jour des éléments QI effectuée', 'success');

                // Test 5: Compter les éléments QI
                const qiElements = document.querySelectorAll('[id*="qi"], [class*="qi"], [id*="QI"], [class*="QI"]');
                log(`🎯 ${qiElements.length} éléments QI détectés`, 'info');

                updateQIElementsGrid();
                updateStatus();

            } catch (error) {
                log(`❌ Erreur lors des tests: ${error.message}`, 'error');
            }
        }

        async function forceUpdate() {
            log('🔄 Forçage de la mise à jour...', 'info');
            try {
                await window.qiManager.fetchRealQI();
                window.qiManager.updateAllQIElements();
                const newQI = window.qiManager.getCurrentQI();
                document.getElementById('main-qi').textContent = newQI;
                log(`✅ Mise à jour forcée - Nouveau QI: ${newQI}`, 'success');
                updateStatus();
            } catch (error) {
                log(`❌ Erreur mise à jour: ${error.message}`, 'error');
            }
        }

        async function reevaluateQI() {
            log('📊 Réévaluation du QI...', 'info');
            try {
                const result = await window.qiManager.forceReevaluation();
                if (result) {
                    log(`✅ Réévaluation effectuée: ${JSON.stringify(result)}`, 'success');
                } else {
                    log('⚠️ Réévaluation non disponible', 'warning');
                }
                await forceUpdate();
            } catch (error) {
                log(`❌ Erreur réévaluation: ${error.message}`, 'error');
            }
        }

        function testAllElements() {
            log('🎯 Test de tous les éléments QI...', 'info');
            
            const selectors = [
                '#qi-value', '#current-qi', '#qi-display', '#qi-score',
                '.qi-value', '.current-qi', '.qi-display', '.qi-score'
            ];

            let found = 0;
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    found += elements.length;
                    log(`✅ Trouvé ${elements.length} élément(s) pour ${selector}`, 'success');
                } else {
                    log(`⚠️ Aucun élément pour ${selector}`, 'warning');
                }
            });

            log(`📊 Total: ${found} éléments QI trouvés`, 'info');
            updateQIElementsGrid();
        }

        function updateQIElementsGrid() {
            const grid = document.getElementById('qi-elements-grid');
            grid.innerHTML = '';

            const allElements = document.querySelectorAll('*');
            const qiElements = [];

            allElements.forEach(el => {
                if (el.id && (el.id.includes('qi') || el.id.includes('QI')) ||
                    el.className && (el.className.includes('qi') || el.className.includes('QI')) ||
                    el.textContent && el.textContent.includes('QI:')) {
                    qiElements.push(el);
                }
            });

            qiElements.slice(0, 12).forEach((el, index) => {
                const elementDiv = document.createElement('div');
                elementDiv.className = 'qi-element';
                elementDiv.innerHTML = `
                    <h3>Élément ${index + 1}</h3>
                    <div class="qi-value">${el.textContent || 'Vide'}</div>
                    <small>ID: ${el.id || 'Aucun'}<br>Class: ${el.className || 'Aucune'}</small>
                `;
                grid.appendChild(elementDiv);
            });

            if (qiElements.length === 0) {
                grid.innerHTML = '<div class="qi-element"><h3>Aucun élément QI trouvé</h3></div>';
            }
        }

        function updateStatus() {
            const statusDiv = document.getElementById('qi-status');
            const qi = window.qiManager ? window.qiManager.getCurrentQI() : 'N/A';
            const isInitialized = window.qiManager ? window.qiManager.isInitialized : false;
            
            statusDiv.innerHTML = `
                <span class="status ${isInitialized ? 'success' : 'error'}">
                    ${isInitialized ? '✅ Initialisé' : '❌ Non initialisé'}
                </span>
                <span class="status success">QI: ${qi}</span>
                <span class="status success">Source: API Centralisée</span>
            `;
        }

        // Initialisation automatique
        window.addEventListener('load', () => {
            log('🚀 Page de test chargée', 'info');
            
            // Attendre que le gestionnaire QI soit prêt
            setTimeout(() => {
                testQIManager();
            }, 2000);

            // Mise à jour automatique toutes les 10 secondes
            setInterval(() => {
                if (window.qiManager) {
                    const qi = window.qiManager.getCurrentQI();
                    document.getElementById('main-qi').textContent = qi;
                    updateStatus();
                }
            }, 10000);
        });

        // Écouter les mises à jour du QI
        if (window.qiManager) {
            window.qiManager.onQIUpdate((newQI, oldQI) => {
                log(`🔄 QI mis à jour: ${oldQI} → ${newQI}`, 'success');
                document.getElementById('main-qi').textContent = newQI;
                updateStatus();
            });
        }
    </script>
</body>
</html>
