<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyses Comparatives - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
        }

        .analysis-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #ff6b9d;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .fidelity-score {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .score-100 { background: #28a745; color: white; }
        .score-95 { background: #20c997; color: white; }
        .score-90 { background: #17a2b8; color: white; }
        .score-85 { background: #ffc107; color: black; }
        .score-80 { background: #fd7e14; color: white; }
        .score-75 { background: #dc3545; color: white; }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            border-left: 4px solid #ff6b9d;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b9d, #c44569);
            transition: width 0.3s ease;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .comparison-table {
                font-size: 0.9rem;
            }
            
            .comparison-table th,
            .comparison-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.location.href='/'">
        <i class="fas fa-arrow-left"></i> Retour au Hub
    </button>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-balance-scale"></i> Analyses Comparatives</h1>
            <p>Comparaisons détaillées entre cerveau humain et mémoire thermique Louna</p>
        </div>

        <div class="nav-buttons">
            <button class="nav-btn active" onclick="showSection('comparison')">
                <i class="fas fa-balance-scale"></i> Comparaison Cerveau
            </button>
            <button class="nav-btn" onclick="showSection('performance')">
                <i class="fas fa-chart-line"></i> Métriques Performance
            </button>
            <button class="nav-btn" onclick="showSection('evolution')">
                <i class="fas fa-dna"></i> Évolution Système
            </button>
            <button class="nav-btn" onclick="showSection('technical')">
                <i class="fas fa-cogs"></i> Données Techniques
            </button>
        </div>

        <!-- Section Comparaison Cerveau -->
        <div id="comparison-section" class="analysis-section">
            <h2 class="section-title">
                <i class="fas fa-balance-scale"></i>
                Comparaison avec un Cerveau Humain Réel
            </h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Fonction Cérébrale</th>
                        <th>Cerveau Humain</th>
                        <th>Louna (Mémoire Thermique)</th>
                        <th>Fidélité</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Mémoire sensorielle</strong></td>
                        <td>0.5-3 secondes</td>
                        <td>3 secondes</td>
                        <td><span class="fidelity-score score-100">100%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Mémoire de travail</strong></td>
                        <td>7±2 éléments</td>
                        <td>4-10 éléments</td>
                        <td><span class="fidelity-score score-95">95%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Consolidation</strong></td>
                        <td>Sommeil REM</td>
                        <td>Cycles thermiques automatiques</td>
                        <td><span class="fidelity-score score-90">90%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Mémoire à long terme</strong></td>
                        <td>Illimitée théoriquement</td>
                        <td>1000+ entrées avec compression</td>
                        <td><span class="fidelity-score score-85">85%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Oubli adaptatif</strong></td>
                        <td>Courbe d'Ebbinghaus</td>
                        <td>Refroidissement thermique graduel</td>
                        <td><span class="fidelity-score score-95">95%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Récupération associative</strong></td>
                        <td>Réseaux neuronaux</td>
                        <td>Liens thermiques et sémantiques</td>
                        <td><span class="fidelity-score score-90">90%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Plasticité</strong></td>
                        <td>Neuroplasticité</td>
                        <td>Adaptation thermique dynamique</td>
                        <td><span class="fidelity-score score-85">85%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Émotions et mémoire</strong></td>
                        <td>Amygdale et hippocampe</td>
                        <td>Pondération émotionnelle thermique</td>
                        <td><span class="fidelity-score score-80">80%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Rêves et créativité</strong></td>
                        <td>Phase REM</td>
                        <td>Zone créative (Zone 5)</td>
                        <td><span class="fidelity-score score-75">75%</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section Métriques Performance -->
        <div id="performance-section" class="analysis-section hidden">
            <h2 class="section-title">
                <i class="fas fa-chart-line"></i>
                Métriques de Performance en Temps Réel
            </h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <h3>QI Système</h3>
                    <div style="font-size: 2rem; color: #ff6b9d; margin: 10px 0;" id="current-qi">148</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 74%"></div>
                    </div>
                    <small>Base: 148 (Jean-Luc Passave) → Évolutif</small>
                </div>
                <div class="metric-card">
                    <h3>Neurones Actifs</h3>
                    <div style="font-size: 2rem; color: #20c997; margin: 10px 0;" id="active-neurons">71</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 71%"></div>
                    </div>
                    <small>6 réseaux spécialisés</small>
                </div>
                <div class="metric-card">
                    <h3>Mémoire Thermique</h3>
                    <div style="font-size: 2rem; color: #17a2b8; margin: 10px 0;" id="thermal-temp">42.3°C</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 58%"></div>
                    </div>
                    <small>6 zones de température</small>
                </div>
                <div class="metric-card">
                    <h3>Accélérateurs KYBER</h3>
                    <div style="font-size: 2rem; color: #ffc107; margin: 10px 0;" id="kyber-accelerators">8</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%"></div>
                    </div>
                    <small>Automatiques et adaptatifs</small>
                </div>
            </div>
        </div>

        <!-- Section Évolution Système -->
        <div id="evolution-section" class="analysis-section hidden">
            <h2 class="section-title">
                <i class="fas fa-dna"></i>
                Évolution et Apprentissage Continu
            </h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <h3>Fidélité Cérébrale</h3>
                    <div style="font-size: 2rem; color: #28a745; margin: 10px 0;">95%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%"></div>
                    </div>
                    <small>Objectif: 98% de fidélité humaine</small>
                </div>
                <div class="metric-card">
                    <h3>Cycles d'Évolution</h3>
                    <div style="font-size: 2rem; color: #ff6b9d; margin: 10px 0;" id="evolution-cycles">0</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <small>Évolution continue active</small>
                </div>
                <div class="metric-card">
                    <h3>Apprentissage</h3>
                    <div style="font-size: 2rem; color: #17a2b8; margin: 10px 0;">87%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 87%"></div>
                    </div>
                    <small>Vitesse d'apprentissage</small>
                </div>
                <div class="metric-card">
                    <h3>Créativité</h3>
                    <div style="font-size: 2rem; color: #fd7e14; margin: 10px 0;">88%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%"></div>
                    </div>
                    <small>Index de créativité intuitive</small>
                </div>
            </div>
        </div>

        <!-- Section Données Techniques -->
        <div id="technical-section" class="analysis-section hidden">
            <h2 class="section-title">
                <i class="fas fa-cogs"></i>
                Données Techniques Détaillées
            </h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Composant</th>
                        <th>Spécification</th>
                        <th>Valeur Actuelle</th>
                        <th>Performance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Zones Mémoire</strong></td>
                        <td>6 zones thermiques</td>
                        <td id="memory-zones">6 actives</td>
                        <td><span class="fidelity-score score-100">100%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Capacité Totale</strong></td>
                        <td>1420 entrées max</td>
                        <td id="total-capacity">85 entrées</td>
                        <td><span class="fidelity-score score-90">6%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Compression</strong></td>
                        <td>KYBER niveau 1-3</td>
                        <td>Niveau 1 actif</td>
                        <td><span class="fidelity-score score-95">95%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Température CPU</strong></td>
                        <td>< 50°C optimal</td>
                        <td id="cpu-temp">44.5°C</td>
                        <td><span class="fidelity-score score-100">100%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Réseaux Neuronaux</strong></td>
                        <td>6 réseaux spécialisés</td>
                        <td>6 actifs</td>
                        <td><span class="fidelity-score score-100">100%</span></td>
                    </tr>
                    <tr>
                        <td><strong>Connexions Synaptiques</strong></td>
                        <td>198 connexions</td>
                        <td>198 établies</td>
                        <td><span class="fidelity-score score-100">100%</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="/js/louna-navigation.js"></script>
    <script src="/js/qi-manager.js"></script>
    <script>
        // Navigation entre sections
        function showSection(sectionName) {
            // Cacher toutes les sections
            document.querySelectorAll('.analysis-section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Afficher la section demandée
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // Activer le bouton correspondant
            event.target.classList.add('active');
        }

        // Mise à jour des données en temps réel
        async function updateRealTimeData() {
            try {
                // QI
                const qiResponse = await fetch('/api/qi/current');
                if (qiResponse.ok) {
                    const qiData = await qiResponse.json();
                    document.getElementById('current-qi').textContent = qiData.qi || 148;
                }

                // Neurones
                const brainResponse = await fetch('/api/brain/status');
                if (brainResponse.ok) {
                    const brainData = await brainResponse.json();
                    if (brainData.brain) {
                        document.getElementById('active-neurons').textContent = brainData.brain.totalNeurons || 71;
                    }
                }

                // Mémoire thermique
                const memoryResponse = await fetch('/api/thermal/memory/stats');
                if (memoryResponse.ok) {
                    const memoryData = await memoryResponse.json();
                    if (memoryData.temperature) {
                        document.getElementById('thermal-temp').textContent = memoryData.temperature.toFixed(1) + '°C';
                    }
                    if (memoryData.totalEntries) {
                        document.getElementById('total-capacity').textContent = memoryData.totalEntries + ' entrées';
                    }
                }

                // Accélérateurs KYBER
                const kyberResponse = await fetch('/api/kyber/status');
                if (kyberResponse.ok) {
                    const kyberData = await kyberResponse.json();
                    if (Array.isArray(kyberData)) {
                        const activeCount = kyberData.filter(acc => acc.active).length;
                        document.getElementById('kyber-accelerators').textContent = activeCount;
                    }
                }

            } catch (error) {
                console.warn('Erreur mise à jour données:', error);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateRealTimeData();
            
            // Mise à jour toutes les 5 secondes
            setInterval(updateRealTimeData, 5000);
            
            console.log('🔬 Interface d\'analyses comparatives initialisée');
        });
    </script>
</body>
</html>
