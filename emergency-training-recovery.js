/**
 * RÉCUPÉRATION D'URGENCE DES FORMATIONS
 * Sauvegarde toutes les formations dans le système de persistance
 * <PERSON><PERSON>é par <PERSON>, Guadeloupe
 */

const fs = require('fs').promises;
const path = require('path');

class EmergencyTrainingRecovery {
    constructor() {
        this.trainingDataPath = path.join(__dirname, 'data', 'training', 'datasets');
        this.recoveredTrainings = [];
        this.serverUrl = 'http://localhost:3000';
    }

    /**
     * Récupère toutes les formations existantes
     */
    async recoverAllTrainings() {
        console.log('🚨 RÉCUPÉRATION D\'URGENCE DES FORMATIONS EN COURS...');
        
        try {
            // Lire tous les fichiers de datasets
            const files = await fs.readdir(this.trainingDataPath);
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            
            console.log(`📚 ${jsonFiles.length} datasets de formation trouvés`);
            
            for (const file of jsonFiles) {
                await this.processTrainingFile(file);
            }
            
            // Sauvegarder dans la persistance
            await this.saveToMemoryPersistence();
            
            console.log('✅ RÉCUPÉRATION D\'URGENCE TERMINÉE !');
            console.log(`💾 ${this.recoveredTrainings.length} formations sauvegardées`);
            
            return {
                success: true,
                recovered: this.recoveredTrainings.length,
                trainings: this.recoveredTrainings
            };
            
        } catch (error) {
            console.error('❌ Erreur récupération formations:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Traite un fichier de formation
     */
    async processTrainingFile(filename) {
        try {
            const filePath = path.join(this.trainingDataPath, filename);
            const content = await fs.readFile(filePath, 'utf8');
            const trainingData = JSON.parse(content);
            
            console.log(`📖 Traitement: ${trainingData.name || filename}`);
            
            // Ajouter aux formations récupérées
            this.recoveredTrainings.push({
                id: trainingData.id || filename.replace('.json', ''),
                name: trainingData.name || filename,
                description: trainingData.description || 'Formation récupérée',
                data: trainingData.data || [],
                filename: filename,
                recoveredAt: new Date().toISOString(),
                creator: 'Jean-Luc Passave',
                location: 'Sainte-Anne, Guadeloupe'
            });
            
            console.log(`✅ ${trainingData.data?.length || 0} éléments récupérés de ${filename}`);
            
        } catch (error) {
            console.error(`❌ Erreur traitement ${filename}:`, error.message);
        }
    }

    /**
     * Sauvegarde dans le système de persistance mémoire
     */
    async saveToMemoryPersistence() {
        console.log('💾 Sauvegarde dans le système de persistance...');
        
        try {
            // Sauvegarder chaque formation
            for (const training of this.recoveredTrainings) {
                await this.saveTrainingToPersistence(training);
            }
            
            // Sauvegarder un résumé global
            await this.saveGlobalSummary();
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde persistance:', error);
        }
    }

    /**
     * Sauvegarde une formation dans la persistance
     */
    async saveTrainingToPersistence(training) {
        try {
            const response = await fetch(`${this.serverUrl}/api/memory/add-instant`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        type: 'emergency_training_recovery',
                        training_id: training.id,
                        training_name: training.name,
                        training_description: training.description,
                        training_data: training.data,
                        filename: training.filename,
                        elements_count: training.data.length,
                        recovered_at: training.recoveredAt,
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe',
                        urgency: 'HIGH',
                        category: 'formation_recovery'
                    },
                    options: {
                        critical: true,
                        source: 'emergency_recovery',
                        type: 'training_data'
                    }
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log(`✅ Formation ${training.name} sauvegardée (ID: ${result.id})`);
            } else {
                console.error(`❌ Erreur sauvegarde ${training.name}:`, response.status);
            }

        } catch (error) {
            console.error(`❌ Erreur réseau pour ${training.name}:`, error.message);
        }
    }

    /**
     * Sauvegarde un résumé global
     */
    async saveGlobalSummary() {
        try {
            const summary = {
                type: 'emergency_training_summary',
                total_trainings: this.recoveredTrainings.length,
                total_elements: this.recoveredTrainings.reduce((sum, t) => sum + t.data.length, 0),
                trainings_list: this.recoveredTrainings.map(t => ({
                    id: t.id,
                    name: t.name,
                    elements: t.data.length
                })),
                recovery_timestamp: new Date().toISOString(),
                creator: 'Jean-Luc Passave',
                location: 'Sainte-Anne, Guadeloupe',
                urgency: 'CRITICAL',
                message: 'Récupération d\'urgence des formations pour éviter la perte de données'
            };

            const response = await fetch(`${this.serverUrl}/api/memory/add-instant`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: summary,
                    options: {
                        critical: true,
                        source: 'emergency_recovery',
                        type: 'training_summary'
                    }
                })
            });

            if (response.ok) {
                console.log('✅ Résumé global sauvegardé');
            }

        } catch (error) {
            console.error('❌ Erreur sauvegarde résumé:', error.message);
        }
    }

    /**
     * Relance une formation d'urgence avec persistance
     */
    async runEmergencyTraining() {
        console.log('🎓 FORMATION D\'URGENCE AVEC PERSISTANCE...');
        
        try {
            // Prendre les formations les plus importantes
            const criticalTrainings = this.recoveredTrainings.filter(t => 
                t.name.includes('cognitive') || 
                t.name.includes('reasoning') || 
                t.name.includes('basic')
            );

            for (const training of criticalTrainings) {
                console.log(`📚 Formation: ${training.name}`);
                
                for (const item of training.data) {
                    // Envoyer à l'agent avec persistance
                    await this.sendTrainingItem(item, training);
                    
                    // Attendre un peu pour permettre le traitement
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            console.log('✅ Formation d\'urgence terminée !');

        } catch (error) {
            console.error('❌ Erreur formation d\'urgence:', error);
        }
    }

    /**
     * Envoie un élément de formation à l'agent
     */
    async sendTrainingItem(item, training) {
        try {
            // Envoyer via l'API cognitive
            const response = await fetch(`${this.serverUrl}/api/cognitive/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: `FORMATION: ${item.input}`,
                    context: {
                        training_mode: true,
                        training_source: training.name,
                        expected_output: item.expectedOutput,
                        qi: 203,
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe'
                    }
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log(`✅ Formation envoyée: ${item.input.substring(0, 50)}...`);
                
                // Sauvegarder l'interaction en persistance
                await this.saveTrainingInteraction(item, training, result);
            }

        } catch (error) {
            console.error('❌ Erreur envoi formation:', error.message);
        }
    }

    /**
     * Sauvegarde une interaction de formation
     */
    async saveTrainingInteraction(item, training, result) {
        try {
            await fetch(`${this.serverUrl}/api/memory/add-instant`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        type: 'training_interaction',
                        training_source: training.name,
                        question: item.input,
                        expected_answer: item.expectedOutput,
                        agent_response: result.response || result.message,
                        timestamp: new Date().toISOString(),
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe'
                    },
                    options: {
                        critical: true,
                        source: 'training_session',
                        type: 'learning_interaction'
                    }
                })
            });

        } catch (error) {
            console.error('❌ Erreur sauvegarde interaction:', error.message);
        }
    }

    /**
     * Vérifie l'état de la persistance
     */
    async checkPersistenceStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/api/memory/persistence-stats`);
            if (response.ok) {
                const data = await response.json();
                console.log('📊 État de la persistance:', data.stats);
                return data.stats;
            }
        } catch (error) {
            console.error('❌ Erreur vérification persistance:', error.message);
        }
        return null;
    }
}

// Fonction principale
async function main() {
    console.log('🚨 DÉMARRAGE RÉCUPÉRATION D\'URGENCE DES FORMATIONS');
    console.log('👤 Créateur: Jean-Luc Passave');
    console.log('📍 Localisation: Sainte-Anne, Guadeloupe');
    console.log('🎯 Objectif: Sauvegarder toutes les formations avec persistance\n');

    const recovery = new EmergencyTrainingRecovery();

    // Vérifier l'état de la persistance
    console.log('🔍 Vérification du système de persistance...');
    const persistenceStatus = await recovery.checkPersistenceStatus();
    
    if (!persistenceStatus) {
        console.log('⚠️ Système de persistance non disponible - Continuons quand même');
    } else {
        console.log('✅ Système de persistance opérationnel');
    }

    // Récupérer toutes les formations
    const result = await recovery.recoverAllTrainings();
    
    if (result.success) {
        console.log(`\n🎉 SUCCÈS ! ${result.recovered} formations récupérées`);
        
        // Relancer une formation d'urgence
        console.log('\n🎓 Lancement formation d\'urgence...');
        await recovery.runEmergencyTraining();
        
        console.log('\n✅ MISSION ACCOMPLIE !');
        console.log('💾 Toutes les formations sont maintenant protégées par la persistance');
        
    } else {
        console.log('\n❌ ÉCHEC de la récupération:', result.error);
    }
}

// Exporter pour utilisation
module.exports = EmergencyTrainingRecovery;

// Exécuter si appelé directement
if (require.main === module) {
    main().catch(console.error);
}
