#!/bin/bash

# Script d'installation des dépendances pour le système de caméra et analyse vidéo YouTube
# Créé pour Jean-Luc Passave - Louna Application

echo "🎥 Installation des dépendances pour la caméra et analyse vidéo..."

# Vérifier si Python3 est installé
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

echo "✅ Python3 trouvé: $(python3 --version)"

# Vérifier si pip est installé
if ! command -v pip3 &> /dev/null; then
    echo "📦 Installation de pip..."
    python3 -m ensurepip --upgrade
fi

echo "✅ pip trouvé: $(pip3 --version)"

# Installer les dépendances Python
echo "📦 Installation des dépendances Python..."

# Dépendances de base
pip3 install --upgrade pip

# OpenCV pour la vision par ordinateur
echo "📹 Installation d'OpenCV..."
pip3 install opencv-python

# Face recognition
echo "👤 Installation de face_recognition..."
pip3 install face_recognition

# NumPy pour les calculs numériques
echo "🔢 Installation de NumPy..."
pip3 install numpy

# Pillow pour le traitement d'images
echo "🖼️ Installation de Pillow..."
pip3 install Pillow

# Whisper pour la transcription audio
echo "🎤 Installation de Whisper..."
pip3 install openai-whisper

# PyTorch pour l'IA
echo "🧠 Installation de PyTorch..."
pip3 install torch torchvision torchaudio

# Transformers pour les modèles de langage
echo "🤖 Installation de Transformers..."
pip3 install transformers

# MoviePy pour le traitement vidéo
echo "🎬 Installation de MoviePy..."
pip3 install moviepy

# yt-dlp pour télécharger des vidéos YouTube
echo "📺 Installation de yt-dlp..."
pip3 install yt-dlp

# Vérifier si FFmpeg est installé
if ! command -v ffmpeg &> /dev/null; then
    echo "🎵 FFmpeg non trouvé. Installation via Homebrew..."
    if command -v brew &> /dev/null; then
        brew install ffmpeg
    else
        echo "⚠️ Homebrew non trouvé. Veuillez installer FFmpeg manuellement."
        echo "   Visitez: https://ffmpeg.org/download.html"
    fi
else
    echo "✅ FFmpeg trouvé: $(ffmpeg -version | head -n 1)"
fi

# Installer les dépendances Node.js
echo "📦 Installation des dépendances Node.js..."

# Socket.io pour les WebSockets
npm install socket.io --save

# Axios pour les requêtes HTTP
npm install axios --save

# Multer pour l'upload de fichiers
npm install multer --save

# Sharp pour le traitement d'images
npm install sharp --save

echo "✅ Installation terminée!"
echo ""
echo "🎯 Systèmes installés:"
echo "   📹 OpenCV - Vision par ordinateur"
echo "   👤 Face Recognition - Reconnaissance faciale"
echo "   🎤 Whisper - Transcription audio"
echo "   🎬 MoviePy - Traitement vidéo"
echo "   📺 yt-dlp - Téléchargement YouTube"
echo "   🎵 FFmpeg - Traitement multimédia"
echo ""
echo "🚀 Vous pouvez maintenant utiliser les fonctionnalités de caméra et d'analyse vidéo!"
