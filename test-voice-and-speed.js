#!/usr/bin/env node

/**
 * Test de la voix féminine et de la vitesse de Louna
 */

const axios = require('axios');

async function testVoiceAndSpeed() {
    console.log('🎤 Test de la voix féminine et de la vitesse de Louna...\n');
    
    // Test 1: Vérifier que le serveur répond
    console.log('1. Test de connectivité...');
    try {
        const healthCheck = await axios.get('http://localhost:3007/api/thermal/memory/stats', {
            timeout: 5000
        });
        console.log('✅ Serveur accessible');
    } catch (error) {
        console.log('❌ Serveur non accessible:', error.message);
        return;
    }
    
    // Test 2: Test de vitesse de réponse
    console.log('\n2. Test de vitesse de réponse...');
    const startTime = Date.now();
    
    try {
        const response = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour <PERSON> ! Peux-tu me parler avec ta voix féminine ?',
            history: []
        }, {
            timeout: 15000
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(`⏱️ Temps de réponse: ${responseTime}ms`);
        
        if (response.data.success) {
            console.log('✅ Réponse reçue avec succès');
            console.log(`📝 Réponse: ${response.data.response.substring(0, 100)}...`);
            
            // Afficher les informations des accélérateurs Kyber
            if (response.data.kyberAccelerators) {
                console.log('\n⚡ Accélérateurs Kyber:');
                console.log(`   • Processing Boost: ${response.data.kyberAccelerators.processingBoost}x`);
                console.log(`   • Memory Boost: ${response.data.kyberAccelerators.memoryBoost}x`);
                console.log(`   • Connection Boost: ${response.data.kyberAccelerators.connectionBoost}x`);
                console.log(`   • Total Boost: ${response.data.kyberAccelerators.totalBoost}x`);
                console.log(`   • Amélioration vitesse: ${response.data.kyberAccelerators.speedImprovement}`);
            }
            
            // Évaluer les performances
            if (responseTime < 2000) {
                console.log('🎉 Performance EXCELLENTE !');
            } else if (responseTime < 5000) {
                console.log('👍 Performance BONNE');
            } else {
                console.log('⚠️ Performance LENTE');
            }
        } else {
            console.log('❌ Erreur dans la réponse:', response.data.error);
        }
        
    } catch (error) {
        console.log('❌ Erreur lors du test:', error.message);
    }
    
    // Test 3: Test de la voix féminine macOS
    console.log('\n3. Test de la voix féminine macOS...');
    try {
        const { spawn } = require('child_process');
        
        const testPhrase = "Bonjour ! Je suis Louna, votre assistante IA féminine. Ma voix est maintenant optimisée et mes réponses sont plus rapides grâce aux accélérateurs Kyber.";
        
        console.log('🗣️ Test de la voix Amelie...');
        const sayProcess = spawn('say', ['-v', 'Amelie', testPhrase]);
        
        sayProcess.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Voix féminine Amelie fonctionne parfaitement !');
            } else {
                console.log('❌ Erreur avec la voix Amelie');
            }
        });
        
        sayProcess.on('error', (error) => {
            console.log('❌ Erreur système de synthèse vocale:', error.message);
        });
        
    } catch (error) {
        console.log('❌ Erreur test voix:', error.message);
    }
    
    // Test 4: Vérifier les accélérateurs Kyber
    console.log('\n4. Test des accélérateurs Kyber...');
    try {
        const KyberAccelerators = require('./kyber-accelerators');
        const kyber = new KyberAccelerators();
        
        // Attendre un peu pour l'initialisation
        setTimeout(() => {
            const stats = kyber.getAcceleratorStats();
            console.log('📊 Statistiques Kyber:');
            console.log(`   • Boost Réflexif: ${stats.reflexiveBoost}x`);
            console.log(`   • Boost Thermique: ${stats.thermalBoost}x`);
            console.log(`   • Boost Connecteur: ${stats.connectorBoost}x`);
            console.log(`   • Efficacité: ${stats.efficiency}`);
            
            if (parseFloat(stats.averageBoost) > 3.0) {
                console.log('🚀 Accélérateurs Kyber OPTIMAUX !');
            } else {
                console.log('⚠️ Accélérateurs Kyber peuvent être améliorés');
            }
        }, 2000);
        
    } catch (error) {
        console.log('❌ Erreur test Kyber:', error.message);
    }
    
    console.log('\n📋 Résumé des améliorations:');
    console.log('✅ Interface de chat avec boutons de contrôle audio');
    console.log('✅ Bouton haut-parleur pour couper/activer le son');
    console.log('✅ Bouton voix féminine pour activer Louna');
    console.log('✅ Messages cliquables pour écouter le texte');
    console.log('✅ Voix féminine Amelie (macOS)');
    console.log('✅ Accélérateurs Kyber pour la vitesse');
    console.log('✅ Optimisations de performance');
    
    console.log('\n🎯 Instructions d\'utilisation:');
    console.log('1. Ouvrez http://localhost:3007/chat dans votre navigateur');
    console.log('2. Cliquez sur le bouton haut-parleur (🔊) pour activer le son');
    console.log('3. Cliquez sur le bouton voix féminine (♀) pour activer Louna');
    console.log('4. Tapez votre message et envoyez-le');
    console.log('5. Cliquez sur les réponses de Louna pour les écouter');
    console.log('6. Profitez de la vitesse améliorée avec les accélérateurs Kyber !');
}

testVoiceAndSpeed();
