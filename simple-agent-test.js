const axios = require('axios');

async function testAgent() {
    console.log('🤖 Test simple de l\'agent...');

    try {
        // Test direct de l'API agents/status
        const response = await axios.get('http://localhost:3000/api/agents/status');
        console.log('✅ Réponse reçue:', response.data);

        // Test de chat si l'agent est disponible
        if (response.data.success) {
            console.log('\n💬 Test de chat...');
            const chatResponse = await axios.post('http://localhost:3000/api/chat/message', {
                message: 'Bonjour, comment allez-vous ?',
                conversationId: 'test_' + Date.now()
            });

            if (chatResponse.data.success) {
                console.log('✅ Chat fonctionne:', chatResponse.data.response);
            } else {
                console.log('❌ Chat échoué:', chatResponse.data.error);
            }
        }

    } catch (error) {
        console.log('❌ Erreur:', error.message);

        // Afficher plus de détails sur l'erreur
        if (error.response) {
            console.log('   Status:', error.response.status);
            console.log('   Data:', error.response.data);
        }
    }
}

testAgent();