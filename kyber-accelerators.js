/**
 * Système d'Accélérateurs Kyber pour l'agent Louna
 *
 * Ce système gère les accélérateurs qui optimisent les performances
 * de la mémoire thermique et d'autres fonctionnalités de l'agent.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, 'data/accelerators');
const ACCELERATORS_FILE = path.join(DATA_DIR, 'kyber_accelerators.json');

/**
 * Classe KyberAccelerators - Gère les accélérateurs Kyber de l'agent
 */
class KyberAccelerators {
  /**
   * Initialise les accélérateurs Kyber
   * @param {Object} config - Configuration des accélérateurs
   */
  constructor(config = {}) {
    // Configuration par défaut
    this.config = {
      updateInterval: config.updateInterval || 60, // secondes
      maxBoostFactor: config.maxBoostFactor || 5.0,
      minBoostFactor: config.minBoostFactor || 1.0,
      defaultBoostFactor: config.defaultBoostFactor || 1.5,
      stabilityThreshold: config.stabilityThreshold || 0.7,
      adaptationRate: config.adaptationRate || 0.05,
      energyConsumptionRate: config.energyConsumptionRate || 0.01,
      energyRegenerationRate: config.energyRegenerationRate || 0.005
    };

    // Initialiser les accélérateurs
    this.accelerators = {
      reflexive: {
        name: "Accélérateur Réflexif",
        description: "Améliore la vitesse de traitement des informations",
        boostFactor: config.reflexiveBoostFactor || 3.1,
        stability: config.reflexiveStability || 0.92,
        energy: config.reflexiveEnergy || 1.0,
        enabled: config.reflexiveEnabled !== undefined ? config.reflexiveEnabled : true,
        type: "processing"
      },
      thermal: {
        name: "Accélérateur Thermique",
        description: "Optimise les transferts entre zones de mémoire thermique",
        boostFactor: config.thermalBoostFactor || 2.7,
        stability: config.thermalStability || 0.88,
        energy: config.thermalEnergy || 1.0,
        enabled: config.thermalEnabled !== undefined ? config.thermalEnabled : true,
        type: "memory"
      },
      connector: {
        name: "Connecteur Thermique",
        description: "Facilite les connexions entre informations dans différentes zones",
        boostFactor: config.connectorBoostFactor || 2.1,
        stability: config.connectorStability || 0.95,
        energy: config.connectorEnergy || 1.0,
        enabled: config.connectorEnabled !== undefined ? config.connectorEnabled : true,
        type: "connection"
      }
    };

    // Statistiques
    this.stats = {
      totalBoostApplied: 0,
      energyConsumed: 0,
      stabilityEvents: 0,
      lastUpdateTime: null,
      averageBoostFactor: 0,
      efficiency: 0.92
    };

    // Initialiser le dossier de données
    this.initDataDir();

    // Charger les données existantes
    this.loadAccelerators();

    // Démarrer les mises à jour périodiques
    this.startUpdates();

    console.log('Accélérateurs Kyber initialisés');
  }

  /**
   * Initialise le dossier de données
   */
  async initDataDir() {
    try {
      if (!await existsAsync(DATA_DIR)) {
        await mkdirAsync(DATA_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du dossier de données:', error);
    }
  }

  /**
   * Charge les accélérateurs depuis le fichier
   */
  async loadAccelerators() {
    try {
      if (await existsAsync(ACCELERATORS_FILE)) {
        const data = await readFileAsync(ACCELERATORS_FILE, 'utf8');
        const acceleratorsData = JSON.parse(data);

        // Charger les accélérateurs
        this.accelerators = acceleratorsData.accelerators || this.accelerators;

        // Charger les statistiques
        this.stats = acceleratorsData.stats || this.stats;

        console.log('Accélérateurs chargés avec succès');
      } else {
        console.log('Aucun fichier d\'accélérateurs existant, création d\'un nouveau fichier');
        this.saveAccelerators();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des accélérateurs:', error);
      // Créer un nouveau fichier d'accélérateurs en cas d'erreur
      this.saveAccelerators();
    }
  }

  /**
   * Sauvegarde les accélérateurs dans le fichier
   */
  async saveAccelerators() {
    try {
      const acceleratorsData = {
        accelerators: this.accelerators,
        stats: this.stats
      };

      await writeFileAsync(ACCELERATORS_FILE, JSON.stringify(acceleratorsData, null, 2), 'utf8');

      console.log('Accélérateurs sauvegardés avec succès');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des accélérateurs:', error);
    }
  }

  /**
   * Applique un boost à une opération
   * @param {string} type - Type d'opération (processing, memory, connection)
   * @param {number} baseValue - Valeur de base à booster
   * @returns {number} - Valeur boostée
   */
  applyBoost(type, baseValue) {
    // Trouver les accélérateurs du type spécifié
    const applicableAccelerators = Object.values(this.accelerators).filter(
      acc => acc.type === type && acc.enabled && acc.energy > 0.1
    );

    if (applicableAccelerators.length === 0) {
      return baseValue; // Pas d'accélérateurs applicables
    }

    // Calculer le boost total
    let totalBoost = 1.0;
    applicableAccelerators.forEach(acc => {
      // Appliquer le boost en fonction de la stabilité
      const effectiveBoost = acc.boostFactor * (acc.stability ** 2);
      totalBoost *= effectiveBoost;

      // Consommer de l'énergie
      acc.energy = Math.max(0, acc.energy - this.config.energyConsumptionRate);
    });

    // Limiter le boost total
    totalBoost = Math.min(this.config.maxBoostFactor, Math.max(this.config.minBoostFactor, totalBoost));

    // Mettre à jour les statistiques
    this.stats.totalBoostApplied += totalBoost - 1.0;
    this.stats.energyConsumed += this.config.energyConsumptionRate * applicableAccelerators.length;

    // Appliquer le boost
    return baseValue * totalBoost;
  }

  /**
   * Effectue une mise à jour des accélérateurs
   * - Régénère l'énergie
   * - Ajuste la stabilité
   * - Met à jour les statistiques
   */
  performUpdate() {
    console.log('Mise à jour des accélérateurs Kyber...');

    // Régénérer l'énergie de tous les accélérateurs
    Object.values(this.accelerators).forEach(acc => {
      acc.energy = Math.min(1.0, acc.energy + this.config.energyRegenerationRate);
    });

    // Ajuster la stabilité en fonction de l'utilisation
    this.adjustStability();

    // Mettre à jour les statistiques
    this.updateStats();

    // Sauvegarder les accélérateurs
    this.saveAccelerators();

    // Mettre à jour le temps de dernière mise à jour
    this.stats.lastUpdateTime = Date.now();

    console.log('Mise à jour des accélérateurs terminée');
  }

  /**
   * Ajuste la stabilité des accélérateurs en fonction de leur utilisation
   */
  adjustStability() {
    Object.values(this.accelerators).forEach(acc => {
      // Simuler des fluctuations aléatoires de stabilité
      const randomFactor = 1.0 + (Math.random() * 0.1 - 0.05); // ±5%

      // Ajuster la stabilité
      acc.stability *= randomFactor;

      // Limiter la stabilité entre 0.5 et 1.0
      acc.stability = Math.min(1.0, Math.max(0.5, acc.stability));

      // Vérifier si un événement de stabilité s'est produit
      if (acc.stability < this.config.stabilityThreshold) {
        console.log(`Événement de stabilité détecté pour ${acc.name}`);
        this.stats.stabilityEvents++;

        // Réduire temporairement le facteur de boost
        acc.boostFactor *= 0.8;

        // Limiter le facteur de boost
        acc.boostFactor = Math.max(this.config.minBoostFactor, acc.boostFactor);
      } else {
        // Récupérer progressivement le facteur de boost
        acc.boostFactor += (this.config.defaultBoostFactor - acc.boostFactor) * this.config.adaptationRate;
      }
    });
  }

  /**
   * Met à jour les statistiques des accélérateurs
   */
  updateStats() {
    // Calculer le facteur de boost moyen
    let totalBoostFactor = 0;
    const accelerators = Object.values(this.accelerators);

    accelerators.forEach(acc => {
      totalBoostFactor += acc.boostFactor;
    });

    this.stats.averageBoostFactor = accelerators.length > 0
      ? totalBoostFactor / accelerators.length
      : 1.0;

    // Calculer l'efficacité
    const stabilityFactor = 1.0 - (this.stats.stabilityEvents / 100);
    const energyFactor = 1.0 - (this.stats.energyConsumed / 100);

    this.stats.efficiency = Math.min(1.0, Math.max(0.5,
      (stabilityFactor * 0.4) + (energyFactor * 0.3) + (this.stats.averageBoostFactor / this.config.maxBoostFactor * 0.3)
    ));
  }

  /**
   * Démarre les mises à jour périodiques des accélérateurs
   */
  startUpdates() {
    // Exécuter une mise à jour immédiatement
    this.performUpdate();

    // Planifier les mises à jour suivantes
    setInterval(() => {
      this.performUpdate();
    }, this.config.updateInterval * 1000);
  }

  /**
   * Récupère les statistiques des accélérateurs
   * @returns {Object} - Statistiques des accélérateurs
   */
  getAcceleratorStats() {
    return {
      reflexiveBoost: this.accelerators.reflexive.boostFactor.toFixed(1),
      thermalBoost: this.accelerators.thermal.boostFactor.toFixed(1),
      connectorBoost: this.accelerators.connector.boostFactor.toFixed(1),
      averageBoost: this.stats.averageBoostFactor.toFixed(1),
      efficiency: Math.round(this.stats.efficiency * 100) + '%',
      stabilityEvents: this.stats.stabilityEvents,
      lastUpdateTime: this.stats.lastUpdateTime ? new Date(this.stats.lastUpdateTime).toLocaleString() : 'Jamais'
    };
  }

  /**
   * Active ou désactive un accélérateur
   * @param {string} acceleratorId - ID de l'accélérateur (reflexive, thermal, connector)
   * @param {boolean} enabled - État d'activation
   * @returns {boolean} - Succès de l'opération
   */
  toggleAccelerator(acceleratorId, enabled) {
    if (this.accelerators[acceleratorId]) {
      this.accelerators[acceleratorId].enabled = enabled;
      this.saveAccelerators();
      return true;
    }
    return false;
  }

  /**
   * Ajuste manuellement le facteur de boost d'un accélérateur
   * @param {string} acceleratorId - ID de l'accélérateur (reflexive, thermal, connector)
   * @param {number} boostFactor - Nouveau facteur de boost
   * @returns {boolean} - Succès de l'opération
   */
  adjustBoostFactor(acceleratorId, boostFactor) {
    if (this.accelerators[acceleratorId]) {
      // Limiter le facteur de boost
      const newBoostFactor = Math.min(
        this.config.maxBoostFactor,
        Math.max(this.config.minBoostFactor, boostFactor)
      );

      this.accelerators[acceleratorId].boostFactor = newBoostFactor;
      this.saveAccelerators();
      return true;
    }
    return false;
  }

  /**
   * Réinitialise tous les accélérateurs
   */
  resetAccelerators() {
    // Réinitialiser les accélérateurs aux valeurs par défaut
    this.accelerators = {
      reflexive: {
        name: "Accélérateur Réflexif",
        description: "Améliore la vitesse de traitement des informations",
        boostFactor: this.config.defaultBoostFactor,
        stability: 0.9,
        energy: 1.0,
        enabled: true,
        type: "processing"
      },
      thermal: {
        name: "Accélérateur Thermique",
        description: "Optimise les transferts entre zones de mémoire thermique",
        boostFactor: this.config.defaultBoostFactor,
        stability: 0.9,
        energy: 1.0,
        enabled: true,
        type: "memory"
      },
      connector: {
        name: "Connecteur Thermique",
        description: "Facilite les connexions entre informations dans différentes zones",
        boostFactor: this.config.defaultBoostFactor,
        stability: 0.9,
        energy: 1.0,
        enabled: true,
        type: "connection"
      }
    };

    // Réinitialiser les statistiques
    this.stats = {
      totalBoostApplied: 0,
      energyConsumed: 0,
      stabilityEvents: 0,
      lastUpdateTime: Date.now(),
      averageBoostFactor: this.config.defaultBoostFactor,
      efficiency: 0.9
    };

    // Sauvegarder les accélérateurs réinitialisés
    this.saveAccelerators();
  }

  /**
   * Ajoute un nouvel accélérateur dynamiquement
   * @param {string} acceleratorId - ID unique de l'accélérateur
   * @param {Object} acceleratorData - Données de l'accélérateur
   * @returns {boolean} - Succès de l'opération
   */
  addAccelerator(acceleratorId, acceleratorData) {
    try {
      // Vérifier si l'accélérateur existe déjà
      if (this.accelerators[acceleratorId]) {
        console.log(`⚠️ Accélérateur ${acceleratorId} existe déjà, mise à jour...`);
      }

      // Ajouter l'accélérateur avec des valeurs par défaut
      this.accelerators[acceleratorId] = {
        name: acceleratorData.name || `Accélérateur ${acceleratorId}`,
        description: acceleratorData.description || 'Accélérateur automatique',
        boostFactor: acceleratorData.boostFactor || this.config.defaultBoostFactor,
        stability: acceleratorData.stability || 0.9,
        energy: acceleratorData.energy || 1.0,
        enabled: acceleratorData.enabled !== undefined ? acceleratorData.enabled : true,
        type: acceleratorData.type || 'processing',
        createdAt: Date.now(),
        expiresAt: acceleratorData.expiresAt || null
      };

      // Sauvegarder
      this.saveAccelerators();

      console.log(`✅ Accélérateur ${acceleratorId} ajouté avec succès`);
      return true;

    } catch (error) {
      console.error(`❌ Erreur ajout accélérateur ${acceleratorId}:`, error);
      return false;
    }
  }

  /**
   * Supprime un accélérateur
   * @param {string} acceleratorId - ID de l'accélérateur à supprimer
   * @returns {boolean} - Succès de l'opération
   */
  removeAccelerator(acceleratorId) {
    try {
      if (this.accelerators[acceleratorId]) {
        delete this.accelerators[acceleratorId];
        this.saveAccelerators();
        console.log(`✅ Accélérateur ${acceleratorId} supprimé avec succès`);
        return true;
      } else {
        console.log(`⚠️ Accélérateur ${acceleratorId} non trouvé`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Erreur suppression accélérateur ${acceleratorId}:`, error);
      return false;
    }
  }

  /**
   * Met à jour un accélérateur existant
   * @param {string} acceleratorId - ID de l'accélérateur
   * @param {Object} updates - Mises à jour à appliquer
   * @returns {boolean} - Succès de l'opération
   */
  updateAccelerator(acceleratorId, updates) {
    try {
      if (!this.accelerators[acceleratorId]) {
        console.log(`⚠️ Accélérateur ${acceleratorId} non trouvé pour mise à jour`);
        return false;
      }

      // Appliquer les mises à jour
      Object.keys(updates).forEach(key => {
        if (key === 'boostFactor') {
          // Limiter le facteur de boost
          this.accelerators[acceleratorId][key] = Math.min(
            this.config.maxBoostFactor,
            Math.max(this.config.minBoostFactor, updates[key])
          );
        } else if (key === 'stability') {
          // Limiter la stabilité
          this.accelerators[acceleratorId][key] = Math.min(1.0, Math.max(0.0, updates[key]));
        } else if (key === 'energy') {
          // Limiter l'énergie
          this.accelerators[acceleratorId][key] = Math.min(1.0, Math.max(0.0, updates[key]));
        } else {
          this.accelerators[acceleratorId][key] = updates[key];
        }
      });

      // Sauvegarder
      this.saveAccelerators();

      console.log(`✅ Accélérateur ${acceleratorId} mis à jour avec succès`);
      return true;

    } catch (error) {
      console.error(`❌ Erreur mise à jour accélérateur ${acceleratorId}:`, error);
      return false;
    }
  }

  /**
   * Obtient la liste de tous les accélérateurs
   * @returns {Object} - Liste des accélérateurs
   */
  getAllAccelerators() {
    return { ...this.accelerators };
  }

  /**
   * Obtient un accélérateur spécifique
   * @param {string} acceleratorId - ID de l'accélérateur
   * @returns {Object|null} - Données de l'accélérateur ou null
   */
  getAccelerator(acceleratorId) {
    return this.accelerators[acceleratorId] || null;
  }

  /**
   * Nettoie les accélérateurs expirés
   * @returns {number} - Nombre d'accélérateurs supprimés
   */
  cleanupExpiredAccelerators() {
    const now = Date.now();
    let removedCount = 0;

    Object.keys(this.accelerators).forEach(acceleratorId => {
      const accelerator = this.accelerators[acceleratorId];
      if (accelerator.expiresAt && accelerator.expiresAt < now) {
        this.removeAccelerator(acceleratorId);
        removedCount++;
      }
    });

    if (removedCount > 0) {
      console.log(`🧹 ${removedCount} accélérateurs expirés supprimés`);
    }

    return removedCount;
  }

  /**
   * Obtient les statistiques détaillées
   * @returns {Object} - Statistiques complètes
   */
  getDetailedStats() {
    return {
      accelerators: this.getAllAccelerators(),
      stats: { ...this.stats },
      config: { ...this.config },
      totalAccelerators: Object.keys(this.accelerators).length,
      activeAccelerators: Object.values(this.accelerators).filter(acc => acc.enabled).length,
      totalBoostPower: Object.values(this.accelerators)
        .filter(acc => acc.enabled)
        .reduce((total, acc) => total + acc.boostFactor, 0)
    };
  }

  /**
   * Obtient les statistiques générales (méthode manquante corrigée)
   * @returns {Object} - Statistiques générales
   */
  getStats() {
    const accelerators = Object.values(this.accelerators);
    const activeAccelerators = accelerators.filter(acc => acc.enabled);

    return {
      accelerators: this.accelerators,
      totalAccelerators: accelerators.length,
      activeAccelerators: activeAccelerators.length,
      averageBoostFactor: this.stats.averageBoostFactor,
      efficiency: this.stats.efficiency,
      totalBoostApplied: this.stats.totalBoostApplied,
      energyConsumed: this.stats.energyConsumed,
      stabilityEvents: this.stats.stabilityEvents,
      lastUpdateTime: this.stats.lastUpdateTime,
      totalBoostPower: activeAccelerators.reduce((total, acc) => total + acc.boostFactor, 0),
      averageEnergy: accelerators.length > 0 ?
        accelerators.reduce((total, acc) => total + acc.energy, 0) / accelerators.length : 0,
      averageStability: accelerators.length > 0 ?
        accelerators.reduce((total, acc) => total + acc.stability, 0) / accelerators.length : 0,
      totalOptimizations: this.stats.totalBoostApplied
    };
  }
}

module.exports = KyberAccelerators;
