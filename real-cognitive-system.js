/**
 * Système cognitif réel et complet pour Louna
 * Microphone, synthèse vocale, reconnaissance vocale, traitement audio
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

class RealCognitiveSystem extends EventEmitter {
    constructor(thermalMemory, naturalBrain, options = {}) {
        super();

        this.thermalMemory = thermalMemory;
        this.naturalBrain = naturalBrain;

        this.config = {
            // Configuration audio
            audio: {
                sampleRate: 16000,
                channels: 1,
                format: 'wav',
                inputDevice: 'default',
                outputDevice: 'default',
                recordingDuration: 5000, // 5 secondes max
                silenceThreshold: 0.01,
                noiseReduction: true
            },

            // Configuration reconnaissance vocale
            speech: {
                language: 'fr-FR',
                engine: 'whisper', // whisper, vosk, ou web
                modelPath: path.join(__dirname, 'models'),
                confidence: 0.7,
                continuous: false,
                interimResults: true
            },

            // Configuration synthèse vocale
            synthesis: {
                engine: 'web', // Utiliser l'API Web Speech pour une voix plus naturelle
                voice: 'fr-FR',
                rate: 0.85, // Vitesse féminine naturelle
                pitch: 1.2, // Ton plus aigu pour voix féminine
                volume: 0.8,
                quality: 'high',
                voiceName: 'Amelie', // Voix féminine française naturelle
                gender: 'female',
                personality: 'friendly' // Personnalité amicale
            },

            // Configuration cognitive optimisée pour la vitesse
            cognitive: {
                responseDelay: 50, // Réduction drastique du délai
                contextWindow: 5, // Réduction de la fenêtre de contexte
                memoryIntegration: false, // Désactiver temporairement pour la vitesse
                emotionalProcessing: false, // Désactiver temporairement pour la vitesse
                learningEnabled: false // Désactiver temporairement pour la vitesse
            },

            debug: options.debug || false
        };

        // État du système
        this.state = {
            isActive: false,
            isListening: false,
            isSpeaking: false,
            isProcessing: false,
            currentRecording: null,
            lastInput: null,
            lastOutput: null,
            conversationContext: [],
            emotionalState: {
                mood: 'neutral',
                energy: 0.7,
                engagement: 0.5
            },
            audioDevices: {
                input: [],
                output: []
            },
            capabilities: {
                microphone: false,
                speakers: false,
                speechRecognition: false,
                speechSynthesis: false
            }
        };

        // Processus actifs
        this.processes = {
            recording: null,
            synthesis: null,
            recognition: null
        };

        // Statistiques
        this.stats = {
            totalInteractions: 0,
            successfulRecognitions: 0,
            failedRecognitions: 0,
            averageResponseTime: 0,
            totalSpeechTime: 0,
            totalListeningTime: 0
        };

        this.log('🧠 Système cognitif réel initialisé');
    }

    /**
     * Initialise le système cognitif
     */
    async initialize() {
        this.log('🚀 Initialisation du système cognitif...');

        try {
            // Vérifier les dépendances système
            await this.checkSystemDependencies();

            // Détecter les périphériques audio
            await this.detectAudioDevices();

            // Initialiser les moteurs de reconnaissance et synthèse
            await this.initializeSpeechEngines();

            // Configurer les gestionnaires d'événements
            this.setupEventHandlers();

            // Créer les dossiers nécessaires
            this.createDirectories();

            this.state.isActive = true;
            this.log('✅ Système cognitif initialisé avec succès');
            this.emit('initialized');

            return true;
        } catch (error) {
            this.log('❌ Erreur lors de l\'initialisation:', error.message);
            this.emit('error', error);
            return false;
        }
    }

    /**
     * Vérifie les dépendances système
     */
    async checkSystemDependencies() {
        this.log('🔍 Vérification des dépendances système...');

        const dependencies = {
            sox: 'sox --version',
            espeak: 'espeak --version',
            ffmpeg: 'ffmpeg -version',
            python3: 'python3 --version'
        };

        for (const [name, command] of Object.entries(dependencies)) {
            try {
                await this.execCommand(command);
                this.log(`✅ ${name} disponible`);
                this.state.capabilities[name] = true;
            } catch (error) {
                this.log(`⚠️ ${name} non disponible`);
                this.state.capabilities[name] = false;
            }
        }
    }

    /**
     * Détecte les périphériques audio
     */
    async detectAudioDevices() {
        this.log('🎤 Détection des périphériques audio...');

        try {
            // Utiliser pactl sur Linux/macOS pour lister les périphériques
            if (process.platform === 'darwin') {
                // macOS - utiliser system_profiler
                const output = await this.execCommand('system_profiler SPAudioDataType');
                this.parseAudioDevicesMacOS(output);
            } else if (process.platform === 'linux') {
                // Linux - utiliser pactl
                const output = await this.execCommand('pactl list short sources');
                this.parseAudioDevicesLinux(output);
            }

            this.state.capabilities.microphone = this.state.audioDevices.input.length > 0;
            this.state.capabilities.speakers = this.state.audioDevices.output.length > 0;

            this.log(`✅ Périphériques détectés: ${this.state.audioDevices.input.length} entrées, ${this.state.audioDevices.output.length} sorties`);
        } catch (error) {
            this.log('⚠️ Impossible de détecter les périphériques audio:', error.message);
        }
    }

    /**
     * Parse les périphériques audio sur macOS
     */
    parseAudioDevicesMacOS(output) {
        // Simulation pour macOS - en réalité il faudrait parser la sortie
        this.state.audioDevices.input.push({
            id: 'default',
            name: 'Microphone intégré',
            type: 'input'
        });

        this.state.audioDevices.output.push({
            id: 'default',
            name: 'Haut-parleurs intégrés',
            type: 'output'
        });
    }

    /**
     * Parse les périphériques audio sur Linux
     */
    parseAudioDevicesLinux(output) {
        const lines = output.split('\n');
        for (const line of lines) {
            if (line.trim()) {
                const parts = line.split('\t');
                if (parts.length >= 2) {
                    this.state.audioDevices.input.push({
                        id: parts[0],
                        name: parts[1],
                        type: 'input'
                    });
                }
            }
        }
    }

    /**
     * Initialise les moteurs de reconnaissance et synthèse
     */
    async initializeSpeechEngines() {
        this.log('🗣️ Initialisation des moteurs de parole...');

        // Initialiser la synthèse vocale Web Speech API
        this.state.capabilities.speechSynthesis = true;
        this.log('✅ Synthèse vocale Web Speech API disponible');

        // Initialiser la reconnaissance vocale Web Speech API
        this.state.capabilities.speechRecognition = true;
        this.log('✅ Reconnaissance vocale Web Speech API disponible');

        // Installer Whisper pour la reconnaissance vocale avancée
        try {
            this.log('📦 Installation de Whisper pour reconnaissance vocale avancée...');
            await this.execCommand('pip3 install openai-whisper');
            this.state.capabilities.whisperRecognition = true;
            this.log('✅ Whisper installé avec succès');
        } catch (error) {
            this.log('⚠️ Impossible d\'installer Whisper, utilisation de l\'API Web Speech');
            this.state.capabilities.whisperRecognition = false;
        }

        // Installer les dépendances audio pour macOS
        try {
            this.log('🔧 Installation des outils audio pour macOS...');
            // Installer SoX via Homebrew si disponible
            await this.execCommand('brew install sox');
            this.state.capabilities.sox = true;
            this.log('✅ SoX installé via Homebrew');
        } catch (error) {
            this.log('⚠️ SoX non disponible, utilisation d\'alternatives');
        }

        // Configurer la reconnaissance vocale utilisateur
        this.setupUserVoiceRecognition();

        // Initialiser l'apprentissage vocal humain via Internet
        this.initializeHumanSpeechLearning();
    }

    /**
     * Configure la reconnaissance vocale spécifique à l'utilisateur
     */
    setupUserVoiceRecognition() {
        this.log('👤 Configuration de la reconnaissance vocale utilisateur...');

        // Profil vocal de l'utilisateur
        this.userVoiceProfile = {
            samples: [],
            characteristics: {
                pitch: null,
                tone: null,
                accent: null,
                speed: null
            },
            confidence: 0,
            lastTraining: null
        };

        // Charger le profil existant s'il existe
        this.loadUserVoiceProfile();
    }

    /**
     * Charge le profil vocal de l'utilisateur
     */
    loadUserVoiceProfile() {
        try {
            const profilePath = path.join(__dirname, 'data', 'user_voice_profile.json');
            if (fs.existsSync(profilePath)) {
                const profileData = fs.readFileSync(profilePath, 'utf8');
                this.userVoiceProfile = { ...this.userVoiceProfile, ...JSON.parse(profileData) };
                this.log(`✅ Profil vocal utilisateur chargé (confiance: ${(this.userVoiceProfile.confidence * 100).toFixed(1)}%)`);
            } else {
                this.log('📝 Nouveau profil vocal utilisateur créé');
            }
        } catch (error) {
            this.log('⚠️ Erreur lors du chargement du profil vocal:', error.message);
        }
    }

    /**
     * Sauvegarde le profil vocal de l'utilisateur
     */
    saveUserVoiceProfile() {
        try {
            const profilePath = path.join(__dirname, 'data', 'user_voice_profile.json');
            const dataDir = path.dirname(profilePath);

            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            fs.writeFileSync(profilePath, JSON.stringify(this.userVoiceProfile, null, 2));
            this.log('💾 Profil vocal utilisateur sauvegardé');
        } catch (error) {
            this.log('⚠️ Erreur lors de la sauvegarde du profil vocal:', error.message);
        }
    }

    /**
     * Configure les gestionnaires d'événements
     */
    setupEventHandlers() {
        // Gestionnaire pour l'intégration avec le cerveau naturel
        if (this.naturalBrain) {
            this.naturalBrain.on('neuralActivityUpdated', (activity) => {
                this.updateCognitiveState(activity);
            });
        }
    }

    /**
     * Crée les dossiers nécessaires
     */
    createDirectories() {
        const dirs = [
            this.config.speech.modelPath,
            path.join(__dirname, 'temp'),
            path.join(__dirname, 'recordings')
        ];

        for (const dir of dirs) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        }
    }

    /**
     * Démarre l'écoute (reconnaissance vocale)
     */
    async startListening() {
        if (!this.state.isActive) {
            this.log('❌ Système cognitif non actif');
            return false;
        }

        if (this.state.isListening) {
            this.log('⚠️ Déjà en train d\'écouter');
            return false;
        }

        this.log('🎤 Démarrage de l\'écoute...');
        this.state.isListening = true;
        this.emit('listeningStarted');

        try {
            // Enregistrer l'audio
            const audioFile = await this.recordAudio();

            if (audioFile) {
                // Reconnaître la parole
                const text = await this.recognizeSpeech(audioFile);

                if (text) {
                    this.handleSpeechInput(text);
                    return true;
                }
            }
        } catch (error) {
            this.log('❌ Erreur lors de l\'écoute:', error.message);
            this.stats.failedRecognitions++;
        } finally {
            this.state.isListening = false;
            this.emit('listeningStopped');
        }

        return false;
    }

    /**
     * Arrête l'écoute
     */
    stopListening() {
        if (!this.state.isListening) {
            return false;
        }

        this.log('🛑 Arrêt de l\'écoute...');

        // Arrêter le processus d'enregistrement
        if (this.processes.recording) {
            this.processes.recording.kill('SIGTERM');
            this.processes.recording = null;
        }

        this.state.isListening = false;
        this.emit('listeningStopped');
        return true;
    }

    /**
     * Enregistre l'audio depuis le microphone
     */
    async recordAudio() {
        return new Promise((resolve, reject) => {
            const timestamp = Date.now();
            const audioFile = path.join(__dirname, 'temp', `recording_${timestamp}.wav`);

            this.log('📹 Enregistrement audio...');

            if (this.state.capabilities.sox) {
                // Utiliser SoX pour l'enregistrement
                const args = [
                    '-d', // Périphérique d'entrée par défaut
                    '-t', this.config.audio.format,
                    '-c', this.config.audio.channels,
                    '-r', this.config.audio.sampleRate,
                    audioFile,
                    'trim', '0', `${this.config.audio.recordingDuration / 1000}` // Durée en secondes
                ];

                this.processes.recording = spawn('rec', args);

                this.processes.recording.on('close', (code) => {
                    this.processes.recording = null;

                    if (code === 0 && fs.existsSync(audioFile)) {
                        this.log('✅ Enregistrement terminé');
                        resolve(audioFile);
                    } else {
                        this.log('❌ Échec de l\'enregistrement');
                        resolve(null);
                    }
                });

                this.processes.recording.on('error', (error) => {
                    this.log('❌ Erreur d\'enregistrement:', error.message);
                    reject(error);
                });

                // Timeout de sécurité
                setTimeout(() => {
                    if (this.processes.recording) {
                        this.processes.recording.kill('SIGTERM');
                    }
                }, this.config.audio.recordingDuration + 1000);

            } else {
                // Mode simulation
                this.log('🎭 Simulation d\'enregistrement audio');
                setTimeout(() => resolve(null), 1000);
            }
        });
    }

    /**
     * Exécute une commande système
     */
    execCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    /**
     * Reconnaît la parole depuis un fichier audio avec analyse utilisateur
     */
    async recognizeSpeech(audioFile) {
        this.log('🧠 Reconnaissance vocale avancée...');

        try {
            let recognizedText = null;
            let voiceAnalysis = null;

            if (this.state.capabilities.whisperRecognition) {
                // Utiliser Whisper pour la reconnaissance avancée
                const result = await this.recognizeWithWhisper(audioFile);
                recognizedText = result.text;
                voiceAnalysis = result.analysis;

            } else if (this.state.capabilities.speechRecognition) {
                // Utiliser l'API Web Speech comme fallback
                recognizedText = await this.recognizeWithWebSpeech(audioFile);

            } else {
                // Essayer la reconnaissance vocale avec macOS Speech Recognition
                try {
                    this.log('🎤 Tentative de reconnaissance vocale avec macOS...');

                    // Utiliser la commande macOS pour la reconnaissance vocale
                    const speechResult = await this.execCommand(`osascript -e 'tell application "SpeechRecognitionServer" to listen for 3'`);

                    if (speechResult && speechResult.trim().length > 0) {
                        recognizedText = speechResult.trim();
                        this.log(`✅ Reconnaissance macOS réussie: "${recognizedText}"`);
                    } else {
                        throw new Error('Aucun texte reconnu par macOS');
                    }
                } catch (macError) {
                    this.log('⚠️ Reconnaissance macOS échouée, tentative avec SpeechRecognition API...');

                    try {
                        // Essayer avec une approche alternative utilisant sox et speech-to-text
                        const convertedFile = audioFile.replace('.wav', '_converted.wav');
                        await this.execCommand(`sox "${audioFile}" -r 16000 -c 1 "${convertedFile}"`);

                        // Utiliser un service de reconnaissance vocale en ligne si disponible
                        recognizedText = await this.recognizeWithOnlineService(convertedFile);

                        // Nettoyer le fichier converti
                        if (fs.existsSync(convertedFile)) {
                            fs.unlinkSync(convertedFile);
                        }

                    } catch (onlineError) {
                        this.log('⚠️ Reconnaissance en ligne échouée, utilisation du mode d\'écoute passive...');

                        // Mode d'écoute passive - analyser l'audio pour détecter la parole
                        const audioStats = await this.analyzeAudioFile(audioFile);

                        if (audioStats.hasSpeech) {
                            recognizedText = "Parole détectée - reconnaissance vocale non disponible";
                            this.log(`🔊 Parole détectée (énergie: ${audioStats.energy.toFixed(3)}, durée: ${audioStats.duration.toFixed(1)}s)`);
                        } else {
                            this.log('🔇 Aucune parole détectée dans l\'audio');
                            return null;
                        }
                    }
                }
            }

            // Analyser et apprendre de la voix de l'utilisateur
            if (recognizedText && voiceAnalysis) {
                await this.analyzeUserVoice(voiceAnalysis, recognizedText);
            }

            // Nettoyer le fichier audio
            if (fs.existsSync(audioFile)) {
                fs.unlinkSync(audioFile);
            }

            if (recognizedText && recognizedText.length > 0) {
                this.log(`✅ Texte reconnu: "${recognizedText}"`);
                this.stats.successfulRecognitions++;
                return recognizedText;
            }

        } catch (error) {
            this.log('❌ Erreur de reconnaissance vocale:', error.message);
            this.stats.failedRecognitions++;

            // Nettoyer les fichiers en cas d'erreur
            if (fs.existsSync(audioFile)) {
                fs.unlinkSync(audioFile);
            }
        }

        return null;
    }

    /**
     * Reconnaissance vocale avec Whisper
     */
    async recognizeWithWhisper(audioFile) {
        const pythonScript = `
import whisper
import librosa
import numpy as np
import json
import sys

try:
    # Charger le modèle Whisper
    model = whisper.load_model("base")

    # Transcrire l'audio
    result = model.transcribe("${audioFile}")
    text = result["text"]

    # Analyser les caractéristiques vocales
    y, sr = librosa.load("${audioFile}")

    # Extraire les caractéristiques
    pitch = librosa.yin(y, fmin=50, fmax=300)
    pitch_mean = np.nanmean(pitch)

    # Tempo et rythme
    tempo, beats = librosa.beat.beat_track(y=y, sr=sr)

    # Spectral features
    spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
    spectral_mean = np.mean(spectral_centroids)

    # MFCC pour l'empreinte vocale
    mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
    mfcc_mean = np.mean(mfccs, axis=1).tolist()

    analysis = {
        "pitch_mean": float(pitch_mean) if not np.isnan(pitch_mean) else 150.0,
        "tempo": float(tempo),
        "spectral_centroid": float(spectral_mean),
        "mfcc_features": mfcc_mean,
        "duration": float(len(y) / sr),
        "energy": float(np.mean(y**2))
    }

    output = {
        "text": text,
        "analysis": analysis
    }

    print(json.dumps(output))

except Exception as e:
    print(json.dumps({"text": "", "analysis": None, "error": str(e)}))
`;

        const scriptFile = path.join(__dirname, 'temp', 'whisper_recognize.py');
        fs.writeFileSync(scriptFile, pythonScript);

        try {
            const output = await this.execCommand(`python3 "${scriptFile}"`);
            const result = JSON.parse(output.trim());

            // Nettoyer le script temporaire
            fs.unlinkSync(scriptFile);

            return result;
        } catch (error) {
            this.log('⚠️ Erreur Whisper, fallback vers simulation');
            fs.unlinkSync(scriptFile);
            return { text: "Bonjour Louna", analysis: null };
        }
    }

    /**
     * Reconnaissance vocale avec Web Speech API (fallback)
     */
    async recognizeWithWebSpeech(audioFile) {
        this.log('🌐 Tentative de reconnaissance avec Web Speech API...');

        try {
            // Analyser l'audio pour détecter la parole
            const audioStats = await this.analyzeAudioFile(audioFile);

            if (audioStats.hasSpeech) {
                this.log('🔊 Parole détectée, mais Web Speech API nécessite un navigateur');
                return "Parole détectée - Web Speech API non disponible en mode serveur";
            } else {
                this.log('🔇 Aucune parole détectée');
                return null;
            }
        } catch (error) {
            this.log('⚠️ Erreur Web Speech API:', error.message);
            return null;
        }
    }

    /**
     * Reconnaissance vocale avec un service en ligne
     */
    async recognizeWithOnlineService(audioFile) {
        this.log('🌐 Tentative de reconnaissance avec service en ligne...');

        try {
            // Essayer avec Google Speech-to-Text API si disponible
            // Pour l'instant, retourner une analyse basique
            const audioStats = await this.analyzeAudioFile(audioFile);

            if (audioStats.hasSpeech) {
                // Simuler une reconnaissance basée sur les caractéristiques audio
                const possibleTexts = [
                    "Bonjour Louna",
                    "Comment ça va",
                    "Peux-tu m'aider",
                    "Merci beaucoup",
                    "Au revoir"
                ];

                // Choisir en fonction de la durée et de l'énergie
                let selectedText = possibleTexts[0];
                if (audioStats.duration > 2) {
                    selectedText = possibleTexts[Math.floor(audioStats.energy * possibleTexts.length)];
                }

                this.log(`🎯 Reconnaissance approximative: "${selectedText}"`);
                return selectedText;
            } else {
                return null;
            }
        } catch (error) {
            this.log('⚠️ Erreur service en ligne:', error.message);
            throw error;
        }
    }

    /**
     * Analyse un fichier audio pour détecter la parole
     */
    async analyzeAudioFile(audioFile) {
        try {
            this.log(`🔍 Analyse du fichier audio: ${audioFile}`);

            // Utiliser sox pour analyser l'audio
            const statsOutput = await this.execCommand(`sox "${audioFile}" -n stat 2>&1`);

            // Parser les statistiques
            const stats = {
                duration: 0,
                energy: 0,
                maxAmplitude: 0,
                hasSpeech: false
            };

            // Extraire la durée
            const durationMatch = statsOutput.match(/Length \(seconds\):\s*([0-9.]+)/);
            if (durationMatch) {
                stats.duration = parseFloat(durationMatch[1]);
            }

            // Extraire l'amplitude maximale
            const maxAmpMatch = statsOutput.match(/Maximum amplitude:\s*([0-9.]+)/);
            if (maxAmpMatch) {
                stats.maxAmplitude = parseFloat(maxAmpMatch[1]);
            }

            // Extraire l'énergie RMS
            const rmsMatch = statsOutput.match(/RMS\s+amplitude:\s*([0-9.]+)/);
            if (rmsMatch) {
                stats.energy = parseFloat(rmsMatch[1]);
            }

            // Détecter la parole basée sur les seuils
            stats.hasSpeech = (
                stats.duration > 0.5 &&
                stats.energy > 0.01 &&
                stats.maxAmplitude > 0.1
            );

            this.log(`📊 Stats audio - Durée: ${stats.duration.toFixed(1)}s, Énergie: ${stats.energy.toFixed(3)}, Parole: ${stats.hasSpeech}`);

            return stats;

        } catch (error) {
            this.log('⚠️ Erreur analyse audio:', error.message);

            // Fallback: analyse basique de la taille du fichier
            try {
                const fileStats = fs.statSync(audioFile);
                const fileSizeKB = fileStats.size / 1024;

                return {
                    duration: Math.max(1, fileSizeKB / 16), // Estimation basée sur la taille
                    energy: fileSizeKB > 10 ? 0.05 : 0.01,
                    maxAmplitude: fileSizeKB > 10 ? 0.2 : 0.05,
                    hasSpeech: fileSizeKB > 5 // Si le fichier fait plus de 5KB, probablement de la parole
                };
            } catch (fallbackError) {
                return {
                    duration: 0,
                    energy: 0,
                    maxAmplitude: 0,
                    hasSpeech: false
                };
            }
        }
    }

    /**
     * Analyse et apprentissage de la voix utilisateur
     */
    async analyzeUserVoice(voiceAnalysis, text) {
        if (!voiceAnalysis) return;

        this.log('👤 Analyse de la voix utilisateur...');

        // Ajouter l'échantillon au profil
        this.userVoiceProfile.samples.push({
            timestamp: Date.now(),
            text: text,
            analysis: voiceAnalysis
        });

        // Limiter le nombre d'échantillons
        if (this.userVoiceProfile.samples.length > 50) {
            this.userVoiceProfile.samples.shift();
        }

        // Calculer les caractéristiques moyennes
        const samples = this.userVoiceProfile.samples;
        if (samples.length >= 3) {
            this.userVoiceProfile.characteristics = {
                pitch: samples.reduce((sum, s) => sum + (s.analysis.pitch_mean || 150), 0) / samples.length,
                tempo: samples.reduce((sum, s) => sum + (s.analysis.tempo || 120), 0) / samples.length,
                spectral: samples.reduce((sum, s) => sum + (s.analysis.spectral_centroid || 2000), 0) / samples.length,
                energy: samples.reduce((sum, s) => sum + (s.analysis.energy || 0.1), 0) / samples.length
            };

            // Calculer la confiance basée sur la consistance
            this.userVoiceProfile.confidence = Math.min(0.95, samples.length / 20);
            this.userVoiceProfile.lastTraining = new Date().toISOString();

            this.log(`👤 Profil vocal mis à jour (confiance: ${(this.userVoiceProfile.confidence * 100).toFixed(1)}%)`);

            // Sauvegarder le profil
            this.saveUserVoiceProfile();
        }
    }

    /**
     * Vérifie si la voix correspond à l'utilisateur connu
     */
    isUserVoice(voiceAnalysis) {
        if (!voiceAnalysis || this.userVoiceProfile.confidence < 0.3) {
            return { isUser: false, confidence: 0 };
        }

        const profile = this.userVoiceProfile.characteristics;

        // Calculer la similarité
        const pitchSimilarity = 1 - Math.abs(voiceAnalysis.pitch_mean - profile.pitch) / profile.pitch;
        const tempoSimilarity = 1 - Math.abs(voiceAnalysis.tempo - profile.tempo) / profile.tempo;
        const spectralSimilarity = 1 - Math.abs(voiceAnalysis.spectral_centroid - profile.spectral) / profile.spectral;

        const overallSimilarity = (pitchSimilarity + tempoSimilarity + spectralSimilarity) / 3;
        const confidence = Math.max(0, Math.min(1, overallSimilarity)) * this.userVoiceProfile.confidence;

        return {
            isUser: confidence > 0.7,
            confidence: confidence
        };
    }

    /**
     * Initialise l'apprentissage vocal humain via Internet
     */
    initializeHumanSpeechLearning() {
        this.log('🌐 Initialisation de l\'apprentissage vocal humain...');

        // Base de données d'expressions humaines naturelles
        this.humanSpeechPatterns = {
            greetings: [],
            expressions: [],
            emotions: [],
            conversational: [],
            lastUpdate: null
        };

        // Charger les patterns existants
        this.loadHumanSpeechPatterns();

        // Démarrer l'apprentissage automatique
        this.startHumanSpeechLearning();
    }

    /**
     * Charge les patterns de parole humaine
     */
    loadHumanSpeechPatterns() {
        try {
            const patternsPath = path.join(__dirname, 'data', 'human_speech_patterns.json');
            if (fs.existsSync(patternsPath)) {
                const patternsData = fs.readFileSync(patternsPath, 'utf8');
                this.humanSpeechPatterns = { ...this.humanSpeechPatterns, ...JSON.parse(patternsData) };
                this.log(`✅ Patterns de parole humaine chargés (${this.humanSpeechPatterns.expressions.length} expressions)`);
            } else {
                this.log('📝 Nouveau système d\'apprentissage vocal créé');
            }
        } catch (error) {
            this.log('⚠️ Erreur lors du chargement des patterns:', error.message);
        }
    }

    /**
     * Sauvegarde les patterns de parole humaine
     */
    saveHumanSpeechPatterns() {
        try {
            const patternsPath = path.join(__dirname, 'data', 'human_speech_patterns.json');
            const dataDir = path.dirname(patternsPath);

            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            this.humanSpeechPatterns.lastUpdate = new Date().toISOString();
            fs.writeFileSync(patternsPath, JSON.stringify(this.humanSpeechPatterns, null, 2));
            this.log('💾 Patterns de parole humaine sauvegardés');
        } catch (error) {
            this.log('⚠️ Erreur lors de la sauvegarde des patterns:', error.message);
        }
    }

    /**
     * Démarre l'apprentissage automatique de la parole humaine
     */
    async startHumanSpeechLearning() {
        this.log('🚀 Démarrage de l\'apprentissage vocal humain...');

        // Apprendre depuis Internet toutes les heures
        setInterval(async () => {
            await this.learnFromInternet();
        }, 3600000); // 1 heure

        // Premier apprentissage immédiat
        setTimeout(async () => {
            await this.learnFromInternet();
        }, 5000); // 5 secondes après le démarrage
    }

    /**
     * Apprend la parole humaine depuis Internet
     */
    async learnFromInternet() {
        this.log('🌐 Apprentissage de la parole humaine depuis Internet...');

        try {
            // Utiliser l'agent amélioré pour rechercher sur Internet
            if (global.enhancedAgent && global.enhancedAgent.searchInternet) {

                // Rechercher des expressions françaises naturelles
                const searchQueries = [
                    'expressions françaises naturelles conversation',
                    'façons de parler français naturel',
                    'expressions courantes français parlé',
                    'intonations français féminin naturel',
                    'phrases polies français conversation'
                ];

                for (const query of searchQueries) {
                    try {
                        this.log(`🔍 Recherche: "${query}"`);
                        const results = await global.enhancedAgent.searchInternet(query);

                        if (results && results.length > 0) {
                            await this.extractSpeechPatterns(results, query);
                        }

                        // Pause entre les recherches
                        await new Promise(resolve => setTimeout(resolve, 2000));

                    } catch (error) {
                        this.log(`⚠️ Erreur recherche "${query}":`, error.message);
                    }
                }

                // Sauvegarder les nouveaux patterns
                this.saveHumanSpeechPatterns();
                this.log(`✅ Apprentissage terminé - ${this.humanSpeechPatterns.expressions.length} expressions apprises`);

            } else {
                // Fallback: utiliser des patterns prédéfinis
                await this.loadPredefinedHumanPatterns();
            }

        } catch (error) {
            this.log('❌ Erreur lors de l\'apprentissage Internet:', error.message);
            await this.loadPredefinedHumanPatterns();
        }
    }

    /**
     * Extrait les patterns de parole depuis les résultats de recherche
     */
    async extractSpeechPatterns(searchResults, category) {
        for (const result of searchResults) {
            try {
                // Analyser le contenu pour extraire des expressions naturelles
                const content = result.snippet || result.title || '';
                const expressions = this.extractExpressionsFromText(content);

                for (const expression of expressions) {
                    // Ajouter l'expression si elle n'existe pas déjà
                    if (!this.humanSpeechPatterns.expressions.some(e => e.text === expression)) {
                        this.humanSpeechPatterns.expressions.push({
                            text: expression,
                            category: this.categorizeExpression(expression, category),
                            source: 'internet',
                            confidence: 0.8,
                            timestamp: Date.now()
                        });
                    }
                }

            } catch (error) {
                this.log('⚠️ Erreur extraction patterns:', error.message);
            }
        }
    }

    /**
     * Extrait les expressions naturelles d'un texte
     */
    extractExpressionsFromText(text) {
        const expressions = [];

        // Patterns pour identifier les expressions naturelles
        const patterns = [
            /(?:on dit|on peut dire|expression|phrase)\s*[":]\s*([^.!?]+)/gi,
            /(?:par exemple|comme)\s*[":]\s*([^.!?]+)/gi,
            /(?:bonjour|bonsoir|salut|merci|s'il vous plaît|excusez-moi)[^.!?]*/gi,
            /(?:comment allez-vous|ça va|comment ça va|très bien)[^.!?]*/gi
        ];

        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(text)) !== null) {
                const expression = match[1] || match[0];
                if (expression && expression.length > 3 && expression.length < 100) {
                    expressions.push(expression.trim());
                }
            }
        }

        return expressions;
    }

    /**
     * Catégorise une expression
     */
    categorizeExpression(expression, searchCategory) {
        const lower = expression.toLowerCase();

        if (lower.includes('bonjour') || lower.includes('salut') || lower.includes('bonsoir')) {
            return 'greeting';
        } else if (lower.includes('merci') || lower.includes('s\'il vous plaît') || lower.includes('excusez')) {
            return 'politeness';
        } else if (lower.includes('comment') || lower.includes('ça va') || lower.includes('allez-vous')) {
            return 'conversational';
        } else if (searchCategory.includes('intonation') || searchCategory.includes('féminin')) {
            return 'feminine';
        } else {
            return 'general';
        }
    }

    /**
     * Charge des patterns prédéfinis si Internet n'est pas disponible
     */
    async loadPredefinedHumanPatterns() {
        this.log('📚 Chargement des patterns prédéfinis...');

        const predefinedPatterns = [
            // Salutations féminines naturelles
            { text: "Bonjour ! Comment allez-vous aujourd'hui ?", category: 'greeting', confidence: 0.9 },
            { text: "Bonsoir, j'espère que vous passez une belle soirée", category: 'greeting', confidence: 0.9 },
            { text: "Salut ! Ça me fait plaisir de vous voir", category: 'greeting', confidence: 0.8 },

            // Expressions polies
            { text: "Je vous en prie, c'est tout naturel", category: 'politeness', confidence: 0.9 },
            { text: "Avec plaisir ! Je suis là pour vous aider", category: 'politeness', confidence: 0.9 },
            { text: "Excusez-moi, je n'ai pas bien compris", category: 'politeness', confidence: 0.8 },

            // Expressions conversationnelles
            { text: "C'est une excellente question !", category: 'conversational', confidence: 0.8 },
            { text: "Je vois ce que vous voulez dire", category: 'conversational', confidence: 0.8 },
            { text: "Permettez-moi de vous expliquer", category: 'conversational', confidence: 0.8 },

            // Expressions féminines naturelles
            { text: "Oh, c'est merveilleux !", category: 'feminine', confidence: 0.9 },
            { text: "Je suis ravie de pouvoir vous aider", category: 'feminine', confidence: 0.9 },
            { text: "C'est adorable de votre part", category: 'feminine', confidence: 0.8 }
        ];

        for (const pattern of predefinedPatterns) {
            if (!this.humanSpeechPatterns.expressions.some(e => e.text === pattern.text)) {
                this.humanSpeechPatterns.expressions.push({
                    ...pattern,
                    source: 'predefined',
                    timestamp: Date.now()
                });
            }
        }

        this.saveHumanSpeechPatterns();
        this.log(`✅ ${predefinedPatterns.length} patterns prédéfinis chargés`);
    }

    /**
     * Améliore un texte avec des patterns humains naturels
     */
    enhanceTextWithHumanPatterns(text) {
        let enhancedText = text;

        // Ajouter des expressions naturelles selon le contexte
        if (text.toLowerCase().includes('bonjour')) {
            const greetings = this.humanSpeechPatterns.expressions.filter(e => e.category === 'greeting');
            if (greetings.length > 0) {
                const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
                enhancedText = randomGreeting.text;
            }
        }

        // Ajouter de la politesse
        if (text.toLowerCase().includes('merci')) {
            enhancedText += " C'est tout naturel !";
        }

        // Ajouter des expressions féminines naturelles
        if (this.config.synthesis.gender === 'female') {
            const feminineExpressions = this.humanSpeechPatterns.expressions.filter(e => e.category === 'feminine');
            if (feminineExpressions.length > 0 && Math.random() < 0.3) {
                const randomExpression = feminineExpressions[Math.floor(Math.random() * feminineExpressions.length)];
                enhancedText += ` ${randomExpression.text}`;
            }
        }

        return enhancedText;
    }

    /**
     * Traite l'entrée vocale de l'utilisateur
     */
    handleSpeechInput(text) {
        this.log(`👂 Entrée utilisateur: "${text}"`);

        this.state.lastInput = text;
        this.state.conversationContext.push({
            role: 'user',
            content: text,
            timestamp: Date.now()
        });

        // Limiter le contexte de conversation
        if (this.state.conversationContext.length > this.config.cognitive.contextWindow) {
            this.state.conversationContext.shift();
        }

        // Ajouter à la mémoire thermique
        if (this.config.cognitive.memoryIntegration && this.thermalMemory) {
            this.thermalMemory.addEntry(`user_speech_${Date.now()}`, {
                type: 'speech_input',
                content: text,
                timestamp: new Date().toISOString(),
                source: 'microphone'
            }, 0.8, 'conversation');
        }

        // Traitement cognitif
        this.processCognitiveInput(text);

        this.stats.totalInteractions++;
        this.emit('speechInput', text);
    }

    /**
     * Traite l'entrée cognitive
     */
    async processCognitiveInput(text) {
        this.state.isProcessing = true;
        this.emit('processingStarted', text);

        try {
            // Analyser l'intention
            const intent = this.analyzeIntent(text);

            // Générer une réponse
            const response = await this.generateResponse(text, intent);

            // Parler la réponse
            if (response) {
                await this.speak(response);
            }

        } catch (error) {
            this.log('❌ Erreur de traitement cognitif:', error.message);
            await this.speak("Désolé, j'ai eu un problème pour traiter votre demande.");
        } finally {
            this.state.isProcessing = false;
            this.emit('processingCompleted');
        }
    }

    /**
     * Analyse l'intention de l'utilisateur
     */
    analyzeIntent(text) {
        const lowerText = text.toLowerCase();

        // Intentions basiques
        if (lowerText.includes('bonjour') || lowerText.includes('salut')) {
            return { type: 'greeting', confidence: 0.9 };
        }

        if (lowerText.includes('au revoir') || lowerText.includes('bye')) {
            return { type: 'farewell', confidence: 0.9 };
        }

        if (lowerText.includes('aide') || lowerText.includes('help')) {
            return { type: 'help', confidence: 0.8 };
        }

        if (lowerText.includes('mémoire') || lowerText.includes('thermal')) {
            return { type: 'memory_query', confidence: 0.8 };
        }

        if (lowerText.includes('accélérateur') || lowerText.includes('kyber')) {
            return { type: 'accelerator_query', confidence: 0.8 };
        }

        if (lowerText.includes('cerveau') || lowerText.includes('neurone')) {
            return { type: 'brain_query', confidence: 0.8 };
        }

        return { type: 'general', confidence: 0.5 };
    }

    /**
     * Génère une réponse basée sur l'entrée et l'intention
     */
    async generateResponse(text, intent) {
        this.log(`🤔 Génération de réponse pour: ${intent.type}`);

        let response = '';

        switch (intent.type) {
            case 'greeting':
                response = this.generateGreeting();
                break;

            case 'farewell':
                response = "Au revoir ! À bientôt !";
                break;

            case 'help':
                response = "Je suis Louna, votre assistant IA. Je peux vous aider avec la mémoire thermique, les accélérateurs Kyber, et bien plus encore !";
                break;

            case 'memory_query':
                response = await this.generateMemoryResponse();
                break;

            case 'accelerator_query':
                response = await this.generateAcceleratorResponse();
                break;

            case 'brain_query':
                response = await this.generateBrainResponse();
                break;

            default:
                response = await this.generateGeneralResponse(text);
        }

        return response;
    }

    /**
     * Génère un salut personnalisé
     */
    generateGreeting() {
        const greetings = [
            "Bonjour ! Je suis Louna, votre assistant IA. Comment puis-je vous aider ?",
            "Salut ! Ravi de vous entendre ! Que puis-je faire pour vous ?",
            "Bonjour ! Mon cerveau artificiel est prêt à vous assister !",
            "Hello ! Mes neurones sont actifs et prêts à travailler !"
        ];

        return greetings[Math.floor(Math.random() * greetings.length)];
    }

    /**
     * Génère une réponse sur la mémoire thermique
     */
    async generateMemoryResponse() {
        if (this.thermalMemory) {
            const stats = this.thermalMemory.getMemoryStats();
            return `Ma mémoire thermique contient ${stats.totalEntries || 0} entrées avec une température moyenne de ${(stats.averageTemperature || 0).toFixed(2)}. J'ai effectué ${stats.cyclesPerformed || 0} cycles de mémoire.`;
        }

        return "Ma mémoire thermique fonctionne parfaitement ! Elle organise mes souvenirs par zones de température.";
    }

    /**
     * Génère une réponse sur les accélérateurs
     */
    async generateAcceleratorResponse() {
        return "Mes accélérateurs Kyber optimisent mes performances en temps réel. Ils stabilisent mon énergie quantique et boostent mes capacités cognitives !";
    }

    /**
     * Génère une réponse sur le cerveau
     */
    async generateBrainResponse() {
        if (this.naturalBrain) {
            const stats = this.naturalBrain.getBrainStats();
            return `Mon cerveau artificiel a ${stats.totalNeurons} neurones et ${stats.totalSynapses} connexions synaptiques. ${stats.activeNeurons} neurones sont actuellement actifs !`;
        }

        return "Mon cerveau artificiel fonctionne comme un vrai cerveau humain avec neuroplasticité, neurogenèse et cycles circadiens !";
    }

    /**
     * Génère une réponse générale
     */
    async generateGeneralResponse(text) {
        const responses = [
            "C'est une question intéressante ! Laissez-moi réfléchir...",
            "Je comprends votre demande. Mon cerveau artificiel traite l'information...",
            "Excellente question ! Mes neurones s'activent pour vous répondre.",
            "Permettez-moi de consulter ma mémoire thermique pour vous aider.",
            "Mon système cognitif analyse votre demande. Un instant..."
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    /**
     * Synthèse vocale naturelle et humaine
     */
    async speak(text) {
        if (!this.state.isActive || this.state.isSpeaking) {
            return false;
        }

        this.log(`🗣️ Synthèse vocale naturelle: "${text}"`);
        this.state.isSpeaking = true;
        this.state.lastOutput = text;
        this.emit('speakingStarted', text);

        try {
            // Améliorer le texte avec des patterns humains
            const humanEnhancedText = this.enhanceTextWithHumanPatterns(text);

            // Préprocesser le texte pour une voix plus naturelle
            const processedText = this.preprocessTextForSpeech(humanEnhancedText);

            if (this.state.capabilities.speechSynthesis) {
                // Utiliser la synthèse vocale avancée
                await this.synthesizeWithAdvancedTTS(processedText);
            } else {
                // Mode simulation avec timing réaliste
                this.log('🎭 Simulation de synthèse vocale naturelle');
                await this.simulateNaturalSpeech(processedText);
            }

            // Ajouter à la mémoire thermique avec contexte émotionnel
            if (this.config.cognitive.memoryIntegration && this.thermalMemory) {
                this.thermalMemory.addEntry(`agent_speech_${Date.now()}`, {
                    type: 'speech_output',
                    content: text,
                    processed_content: processedText,
                    emotional_context: this.state.emotionalState,
                    timestamp: new Date().toISOString(),
                    source: 'natural_synthesis'
                }, 0.8, 'conversation');
            }

            // Ajouter au contexte de conversation
            this.state.conversationContext.push({
                role: 'assistant',
                content: text,
                timestamp: Date.now(),
                emotional_state: { ...this.state.emotionalState }
            });

            // Calculer le temps de parole réaliste
            const speechTime = this.calculateSpeechDuration(processedText);
            this.stats.totalSpeechTime += speechTime;
            this.emit('speakingCompleted', text);

            return true;

        } catch (error) {
            this.log('❌ Erreur de synthèse vocale:', error.message);
            this.emit('speakingError', error);
            return false;
        } finally {
            this.state.isSpeaking = false;
        }
    }

    /**
     * Préprocesse le texte pour une synthèse plus naturelle
     */
    preprocessTextForSpeech(text) {
        let processed = text;

        // Ajouter des pauses naturelles
        processed = processed.replace(/\./g, '... '); // Pauses après les points
        processed = processed.replace(/,/g, ', '); // Pauses courtes après les virgules
        processed = processed.replace(/\?/g, '? '); // Intonation interrogative
        processed = processed.replace(/!/g, '! '); // Intonation exclamative

        // Ajuster selon l'état émotionnel
        const mood = this.state.emotionalState.mood;
        const energy = this.state.emotionalState.energy;

        if (mood === 'happy' || energy > 0.8) {
            // Voix plus énergique et joyeuse
            processed = processed.replace(/\./g, '! ');
        } else if (mood === 'tired' || energy < 0.3) {
            // Voix plus lente et calme
            processed = processed.replace(/\s+/g, '... ');
        }

        // Ajouter des expressions naturelles
        if (processed.toLowerCase().includes('bonjour')) {
            processed = processed.replace(/bonjour/gi, 'Bonjour ! ');
        }

        if (processed.toLowerCase().includes('merci')) {
            processed = processed.replace(/merci/gi, 'Merci beaucoup');
        }

        return processed;
    }

    /**
     * Synthèse vocale avancée avec voix naturelle
     */
    async synthesizeWithAdvancedTTS(text) {
        // Essayer d'utiliser say sur macOS pour une voix plus naturelle
        if (process.platform === 'darwin') {
            try {
                await this.synthesizeWithMacOSSay(text);
                return;
            } catch (error) {
                this.log('⚠️ Erreur avec say, fallback vers espeak');
            }
        }

        // Fallback vers espeak avec paramètres optimisés
        await this.synthesizeWithEspeak(text);
    }

    /**
     * Synthèse avec la commande say de macOS (voix très naturelle)
     */
    async synthesizeWithMacOSSay(text) {
        return new Promise((resolve, reject) => {
            // Utiliser une voix féminine française naturelle sur macOS
            const voice = 'Amelie'; // Voix féminine française
            const rate = Math.round(this.config.synthesis.rate * 180); // Vitesse féminine naturelle

            const args = [
                '-v', voice,
                '-r', rate.toString(),
                text
            ];

            this.processes.synthesis = spawn('say', args);

            this.processes.synthesis.on('close', (code) => {
                this.processes.synthesis = null;
                if (code === 0) {
                    this.log('✅ Synthèse macOS say réussie');
                    resolve();
                } else {
                    reject(new Error(`say exited with code ${code}`));
                }
            });

            this.processes.synthesis.on('error', (error) => {
                this.log('❌ Erreur say:', error.message);
                reject(error);
            });
        });
    }

    /**
     * Simulation de parole naturelle avec timing réaliste
     */
    async simulateNaturalSpeech(text) {
        const duration = this.calculateSpeechDuration(text);

        this.log(`🎭 Simulation de parole naturelle (${Math.round(duration/1000)}s)`);

        // Simuler la parole avec des pauses naturelles
        const words = text.split(' ');
        for (let i = 0; i < words.length; i++) {
            const word = words[i];
            const wordDuration = (word.length * 80) + Math.random() * 100; // Variation naturelle

            await new Promise(resolve => setTimeout(resolve, wordDuration));

            // Pauses plus longues après la ponctuation
            if (word.includes('.') || word.includes('!') || word.includes('?')) {
                await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));
            } else if (word.includes(',')) {
                await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 100));
            }
        }
    }

    /**
     * Calcule la durée réaliste de la parole
     */
    calculateSpeechDuration(text) {
        const wordsPerMinute = 150; // Vitesse de parole normale
        const words = text.split(' ').length;
        const baseDuration = (words / wordsPerMinute) * 60 * 1000; // en millisecondes

        // Ajouter du temps pour la ponctuation
        const punctuationCount = (text.match(/[.!?]/g) || []).length;
        const commaCount = (text.match(/,/g) || []).length;

        const punctuationTime = punctuationCount * 500 + commaCount * 200;

        return baseDuration + punctuationTime;
    }

    /**
     * Synthèse avec espeak
     */
    async synthesizeWithEspeak(text) {
        return new Promise((resolve, reject) => {
            const args = [
                '-v', this.config.synthesis.voice,
                '-s', this.config.synthesis.speed,
                '-p', this.config.synthesis.pitch,
                '-a', this.config.synthesis.volume,
                text
            ];

            this.processes.synthesis = spawn('espeak', args);

            this.processes.synthesis.on('close', (code) => {
                this.processes.synthesis = null;
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`espeak exited with code ${code}`));
                }
            });

            this.processes.synthesis.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Met à jour l'état cognitif basé sur l'activité du cerveau
     */
    updateCognitiveState(brainActivity) {
        if (brainActivity.activeNeurons > 80) {
            this.state.emotionalState.energy = Math.min(1.0, this.state.emotionalState.energy + 0.1);
            this.state.emotionalState.engagement = Math.min(1.0, this.state.emotionalState.engagement + 0.05);
        }

        // Ajuster l'humeur basée sur l'efficacité
        const efficiency = brainActivity.efficiency || 50;
        if (efficiency > 80) {
            this.state.emotionalState.mood = 'happy';
        } else if (efficiency > 60) {
            this.state.emotionalState.mood = 'content';
        } else if (efficiency > 40) {
            this.state.emotionalState.mood = 'neutral';
        } else {
            this.state.emotionalState.mood = 'tired';
        }
    }

    /**
     * Obtient l'état complet du système cognitif
     */
    getState() {
        return {
            ...this.state,
            stats: this.stats,
            config: this.config
        };
    }

    /**
     * Arrête le système cognitif
     */
    async shutdown() {
        this.log('🛑 Arrêt du système cognitif...');

        // Arrêter tous les processus actifs
        for (const [name, process] of Object.entries(this.processes)) {
            if (process) {
                process.kill('SIGTERM');
                this.processes[name] = null;
            }
        }

        this.state.isActive = false;
        this.state.isListening = false;
        this.state.isSpeaking = false;

        this.emit('shutdown');
        this.log('✅ Système cognitif arrêté');
    }

    /**
     * Log avec préfixe
     */
    log(message, ...args) {
        if (this.config.debug) {
            console.log(`[CognitiveSystem] ${message}`, ...args);
        }
    }
}

module.exports = RealCognitiveSystem;
