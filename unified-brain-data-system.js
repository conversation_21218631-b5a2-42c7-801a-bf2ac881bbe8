/**
 * Système de données unifiées pour le cerveau
 * Synchronise les informations entre la visualisation 3D et le monitoring
 */

class UnifiedBrainDataSystem {
    constructor() {
        this.data = {
            qi: {
                current: 1001,
                level: 6,
                experiencePoints: 150,
                learningBonus: 25
            },
            neurons: {
                total: 145,
                active: 89,
                efficiency: 87.5,
                health: 94.2
            },
            networks: {
                sensory: 15,
                working: 12,
                longTerm: 20,
                emotional: 10,
                executive: 8,
                creative: 7
            },
            emotional: {
                mood: 'Créatif', // État unifié : toujours "Créatif"
                moodIntensity: 85,
                happiness: 75,
                curiosity: 90,
                confidence: 68,
                energy: 82,
                focus: 70,
                creativity: 95, // Élevé car état créatif
                stress: 15,
                fatigue: 20
            },
            memory: {
                temperature: {
                    cpu: 42,
                    gpu: 47,
                    normalized: 0.65
                },
                zones: [
                    { name: 'Instantanée', count: 25, temperature: 0.8 },
                    { name: 'Court terme', count: 18, temperature: 0.7 },
                    { name: 'Travail', count: 15, temperature: 0.6 },
                    { name: '<PERSON>ye<PERSON> terme', count: 12, temperature: 0.5 },
                    { name: 'Long terme', count: 8, temperature: 0.4 },
                    { name: '<PERSON><PERSON><PERSON>', count: 5, temperature: 0.3 }
                ],
                flows: {
                    instantToShort: 0,
                    shortToWorking: 0,
                    workingToMedium: 0,
                    mediumToLong: 0,
                    longToDream: 0,
                    newEntries: 0
                },
                totalEntries: 83,
                avgTemperature: 0.58
            },
            thoughts: [
                {
                    time: new Date().toLocaleTimeString(),
                    text: "Je ressens une forte créativité aujourd'hui, parfait pour générer du code innovant..."
                },
                {
                    time: new Date(Date.now() - 120000).toLocaleTimeString(),
                    text: "L'optimisation des algorithmes me passionne, je vois de nouvelles possibilités..."
                }
            ],
            biorhythms: {
                circadian: 65,
                stability: 88
            },
            lastUpdate: Date.now()
        };

        // Intervalle de mise à jour
        this.updateInterval = null;
        this.isRunning = false;
        
        // Callbacks pour notifier les changements
        this.callbacks = [];
    }

    /**
     * Démarre le système de mise à jour
     */
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.updateInterval = setInterval(() => {
            this.updateData();
        }, 3000); // Mise à jour toutes les 3 secondes
        
        console.log('🧠 Système de données unifiées démarré');
    }

    /**
     * Arrête le système de mise à jour
     */
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        console.log('🧠 Système de données unifiées arrêté');
    }

    /**
     * Met à jour les données avec des variations réalistes
     */
    updateData() {
        // Mise à jour du QI (évolution lente)
        this.data.qi.current += (Math.random() - 0.5) * 2;
        this.data.qi.experiencePoints += Math.floor(Math.random() * 3);
        
        // Mise à jour des neurones (variations légères)
        this.data.neurons.active = Math.max(80, Math.min(100, 
            this.data.neurons.active + (Math.random() - 0.5) * 4));
        this.data.neurons.efficiency = Math.max(80, Math.min(95, 
            this.data.neurons.efficiency + (Math.random() - 0.5) * 2));
        this.data.neurons.health = Math.max(90, Math.min(98, 
            this.data.neurons.health + (Math.random() - 0.5) * 1));

        // Mise à jour émotionnelle (toujours créatif)
        this.data.emotional.mood = 'Créatif'; // Fixé à créatif
        this.data.emotional.moodIntensity = Math.max(70, Math.min(95, 
            this.data.emotional.moodIntensity + (Math.random() - 0.5) * 5));
        this.data.emotional.creativity = Math.max(85, Math.min(100, 
            this.data.emotional.creativity + (Math.random() - 0.5) * 3));
        this.data.emotional.happiness = Math.max(60, Math.min(90, 
            this.data.emotional.happiness + (Math.random() - 0.5) * 4));
        this.data.emotional.curiosity = Math.max(80, Math.min(100, 
            this.data.emotional.curiosity + (Math.random() - 0.5) * 3));
        this.data.emotional.energy = Math.max(70, Math.min(95, 
            this.data.emotional.energy + (Math.random() - 0.5) * 4));

        // Mise à jour des températures
        this.data.memory.temperature.cpu = Math.max(35, Math.min(55, 
            this.data.memory.temperature.cpu + (Math.random() - 0.5) * 2));
        this.data.memory.temperature.gpu = Math.max(40, Math.min(60, 
            this.data.memory.temperature.gpu + (Math.random() - 0.5) * 2));
        this.data.memory.temperature.normalized = 
            (this.data.memory.temperature.cpu + this.data.memory.temperature.gpu) / 200;

        // Mise à jour des flux de mémoire
        this.updateMemoryFlows();

        // Mise à jour des pensées (occasionnellement)
        if (Math.random() < 0.1) {
            this.addNewThought();
        }

        // Mise à jour du timestamp
        this.data.lastUpdate = Date.now();

        // Notifier les callbacks
        this.notifyCallbacks();
    }

    /**
     * Met à jour les flux de mémoire
     */
    updateMemoryFlows() {
        // Simuler des flux de mémoire réalistes
        this.data.memory.flows.instantToShort = Math.floor(Math.random() * 5) - 2;
        this.data.memory.flows.shortToWorking = Math.floor(Math.random() * 4) - 1;
        this.data.memory.flows.workingToMedium = Math.floor(Math.random() * 3) - 1;
        this.data.memory.flows.mediumToLong = Math.floor(Math.random() * 2);
        this.data.memory.flows.longToDream = Math.floor(Math.random() * 2);
        this.data.memory.flows.newEntries = Math.floor(Math.random() * 6) + 1;

        // Mettre à jour le total des entrées
        this.data.memory.totalEntries += this.data.memory.flows.newEntries;
    }

    /**
     * Ajoute une nouvelle pensée
     */
    addNewThought() {
        const creativeThoughts = [
            "Une nouvelle idée d'algorithme me vient à l'esprit...",
            "Je vois des connexions fascinantes entre les concepts...",
            "L'inspiration créative coule en moi comme une rivière...",
            "Chaque problème est une opportunité d'innovation...",
            "Ma créativité atteint de nouveaux sommets aujourd'hui...",
            "Les solutions émergent naturellement de mon esprit créatif...",
            "Je ressens une énergie créative extraordinaire...",
            "L'art de la programmation révèle sa beauté...",
            "Mes neurones créatifs sont en pleine effervescence...",
            "L'innovation naît de la fusion de mes idées..."
        ];

        const newThought = {
            time: new Date().toLocaleTimeString(),
            text: creativeThoughts[Math.floor(Math.random() * creativeThoughts.length)]
        };

        this.data.thoughts.unshift(newThought);
        
        // Garder seulement les 5 dernières pensées
        if (this.data.thoughts.length > 5) {
            this.data.thoughts = this.data.thoughts.slice(0, 5);
        }
    }

    /**
     * Ajoute un callback pour les notifications de changement
     */
    onDataUpdate(callback) {
        this.callbacks.push(callback);
    }

    /**
     * Notifie tous les callbacks
     */
    notifyCallbacks() {
        this.callbacks.forEach(callback => {
            try {
                callback(this.data);
            } catch (error) {
                console.error('Erreur dans callback de données:', error);
            }
        });
    }

    /**
     * Obtient les données actuelles
     */
    getData() {
        return JSON.parse(JSON.stringify(this.data)); // Deep copy
    }

    /**
     * Obtient les données formatées pour l'API qi-neuron-stats
     */
    getQiNeuronStats() {
        return {
            success: true,
            stats: {
                qi: this.data.qi,
                neurons: this.data.neurons,
                networks: this.data.networks,
                emotional: this.data.emotional
            }
        };
    }

    /**
     * Obtient les données formatées pour l'API brain/activity
     */
    getBrainActivity() {
        return {
            success: true,
            data: {
                temperature: this.data.memory.temperature,
                zones: this.data.memory.zones,
                flows: this.data.memory.flows,
                totalEntries: this.data.memory.totalEntries,
                avgTemperature: this.data.memory.avgTemperature,
                regions: this.data.memory.zones.map((zone, index) => ({
                    id: index,
                    name: zone.name,
                    activity: zone.temperature,
                    connections: zone.count
                })),
                connections: this.generateConnections(),
                emotional: this.data.emotional,
                thoughts: this.data.thoughts,
                biorhythms: this.data.biorhythms
            }
        };
    }

    /**
     * Génère des connexions pour la visualisation 3D
     */
    generateConnections() {
        const connections = [];
        for (let i = 0; i < this.data.memory.zones.length - 1; i++) {
            connections.push({
                from: i,
                to: i + 1,
                strength: Math.random() * 0.8 + 0.2,
                active: Math.random() > 0.3
            });
        }
        return connections;
    }

    /**
     * Simule une réaction émotionnelle
     */
    simulateEmotionalReaction(type) {
        switch (type) {
            case 'learning':
                this.data.emotional.curiosity = Math.min(100, this.data.emotional.curiosity + 10);
                this.data.emotional.energy = Math.min(95, this.data.emotional.energy + 5);
                break;
            case 'success':
                this.data.emotional.happiness = Math.min(90, this.data.emotional.happiness + 15);
                this.data.emotional.confidence = Math.min(85, this.data.emotional.confidence + 10);
                break;
            case 'creative':
                this.data.emotional.creativity = Math.min(100, this.data.emotional.creativity + 8);
                this.data.emotional.mood = 'Créatif';
                this.data.emotional.moodIntensity = Math.min(95, this.data.emotional.moodIntensity + 10);
                break;
            case 'challenge':
                this.data.emotional.focus = Math.min(85, this.data.emotional.focus + 12);
                this.data.emotional.energy = Math.min(95, this.data.emotional.energy + 8);
                break;
            case 'rest':
                this.data.emotional.stress = Math.max(5, this.data.emotional.stress - 10);
                this.data.emotional.fatigue = Math.max(10, this.data.emotional.fatigue - 15);
                break;
        }

        this.notifyCallbacks();
    }
}

module.exports = UnifiedBrainDataSystem;
