#!/bin/bash

# Script de démarrage pour le serveur Luna avec Vision Ultra
# Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "██╗   ██╗██╗███████╗██╗ ██████╗ ███╗   ██╗    ██╗   ██╗██╗  ████████╗██████╗  █████╗ "
echo "██║   ██║██║██╔════╝██║██╔═══██╗████╗  ██║    ██║   ██║██║  ╚══██╔══╝██╔══██╗██╔══██╗"
echo "██║   ██║██║███████╗██║██║   ██║██╔██╗ ██║    ██║   ██║██║     ██║   ██████╔╝███████║"
echo "╚██╗ ██╔╝██║╚════██║██║██║   ██║██║╚██╗██║    ██║   ██║██║     ██║   ██╔══██╗██╔══██║"
echo " ╚████╔╝ ██║███████║██║╚██████╔╝██║ ╚████║    ╚██████╔╝███████╗██║   ██║  ██║██║  ██║"
echo "  ╚═══╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝     ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝"
echo -e "${NC}"

echo -e "${CYAN}Créé par <PERSON>, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""

# Chemin vers le répertoire de travail
WORK_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter ce serveur.${NC}"
    exit 1
fi

# Vérifier si le fichier server-luna.js existe
if [ ! -f "$WORK_DIR/server-luna.js" ]; then
    echo -e "${RED}Le fichier server-luna.js n'existe pas dans le répertoire de travail.${NC}"
    exit 1
fi

# Vérifier si les modules Node.js sont installés
if [ ! -d "$WORK_DIR/node_modules" ]; then
    echo -e "${YELLOW}Installation des dépendances Node.js...${NC}"
    cd "$WORK_DIR" && npm install
fi

# Arrêter les serveurs existants
echo -e "${YELLOW}Arrêt des serveurs existants...${NC}"
pkill -f "node server-" || true

# Modifier le fichier server-luna.js pour corriger les problèmes
echo -e "${YELLOW}Correction des problèmes dans le fichier server-luna.js...${NC}"

# Vérifier s'il y a une double déclaration de brainPresence
BRAIN_PRESENCE_COUNT=$(grep -c "let brainPresence" "$WORK_DIR/server-luna.js")
if [ "$BRAIN_PRESENCE_COUNT" -gt 1 ]; then
    echo -e "${YELLOW}Correction de la double déclaration de brainPresence...${NC}"
    # Trouver la ligne de la deuxième déclaration et la supprimer
    SECOND_DECLARATION=$(grep -n "let brainPresence" "$WORK_DIR/server-luna.js" | tail -n 1 | cut -d':' -f1)
    if [ -n "$SECOND_DECLARATION" ]; then
        sed -i '' "${SECOND_DECLARATION}d" "$WORK_DIR/server-luna.js"
        echo -e "${GREEN}Double déclaration de brainPresence corrigée.${NC}"
    fi
fi

# Vérifier si le nom de l'agent est correct
if grep -q "name: 'DeepSeek Assistant'" "$WORK_DIR/server-luna.js"; then
    echo -e "${YELLOW}Mise à jour du nom de l'agent en Vision Ultra...${NC}"
    sed -i '' "s/name: 'DeepSeek Assistant'/name: 'Vision Ultra',\n  creator: 'Jean Passave',\n  location: 'Sainte-Anne, Guadeloupe (97180)'/g" "$WORK_DIR/server-luna.js"
    echo -e "${GREEN}Nom de l'agent mis à jour.${NC}"
fi

# Vérifier si les routes pour Louna et Lounas existent
if ! grep -q "app.get('/louna'" "$WORK_DIR/server-luna.js"; then
    echo -e "${YELLOW}Ajout des routes pour Louna et Lounas...${NC}"
    # Trouver la ligne de la route Luna
    LUNA_ROUTE_LINE=$(grep -n "app.get('/luna'" "$WORK_DIR/server-luna.js" | cut -d':' -f1)
    if [ -n "$LUNA_ROUTE_LINE" ]; then
        # Extraire la route Luna
        LUNA_ROUTE=$(sed -n "${LUNA_ROUTE_LINE},/});/p" "$WORK_DIR/server-luna.js")
        # Créer les routes Louna et Lounas
        LOUNA_ROUTE=$(echo "$LUNA_ROUTE" | sed "s/\/luna/\/louna/g" | sed "s/Luna/Louna/g")
        LOUNAS_ROUTE=$(echo "$LUNA_ROUTE" | sed "s/\/luna/\/lounas/g" | sed "s/Luna/Lounas/g")
        # Ajouter les nouvelles routes après la route Luna
        sed -i '' "${LUNA_ROUTE_LINE}s/});/});\n\n\/\/ Route pour Louna\n${LOUNA_ROUTE}\n\n\/\/ Route pour Lounas\n${LOUNAS_ROUTE}/g" "$WORK_DIR/server-luna.js"
        echo -e "${GREEN}Routes pour Louna et Lounas ajoutées.${NC}"
    fi
fi

# Démarrer le serveur Luna
echo -e "${GREEN}Démarrage du serveur Luna avec Vision Ultra...${NC}"
cd "$WORK_DIR" && node server-luna.js

# Ce code ne sera jamais atteint tant que le serveur est en cours d'exécution
echo -e "${RED}Le serveur Luna s'est arrêté.${NC}"
