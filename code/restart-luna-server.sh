#!/bin/bash
# Script pour redémarrer le serveur Luna avec les nouvelles fonctionnalités
# Créé le 16 mai 2025

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages avec un préfixe coloré
log() {
  echo -e "${BLUE}[LUNA]${NC} $1"
}

error() {
  echo -e "${RED}[ERREUR]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCÈS]${NC} $1"
}

warning() {
  echo -e "${YELLOW}[ATTENTION]${NC} $1"
}

# Vérifier si le disque externe est monté
if [ ! -d "/Volumes/seagate" ]; then
  error "Le disque externe 'seagate' n'est pas monté. Veuillez le connecter et réessayer."
  exit 1
fi

# Vérifier si le répertoire de travail existe
if [ ! -d "/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui" ]; then
  error "Le répertoire de travail n'existe pas sur le disque externe."
  exit 1
fi

# Définir le port pour Luna
PORT=3015

# Vérifier si le port est déjà utilisé
if lsof -i :$PORT > /dev/null; then
  warning "Le port $PORT est déjà utilisé. Tentative de libération du port..."
  lsof -i :$PORT | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null
  sleep 2
fi

# Vérifier si Ollama est en cours d'exécution
if ! curl -s http://localhost:11434/api/version > /dev/null; then
  warning "Ollama ne semble pas être en cours d'exécution. Tentative de démarrage..."
  open -a Ollama
  sleep 5
fi

# Se déplacer dans le répertoire de travail
cd "/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui" || {
  error "Impossible d'accéder au répertoire de travail."
  exit 1
}

# Arrêter tous les processus Luna en cours
log "Arrêt des processus Luna en cours..."
pkill -f "node server-luna.js" || true
sleep 2

# Démarrer le serveur Luna en arrière-plan
log "Démarrage du serveur Luna sur le port $PORT..."
PORT=$PORT node server-luna.js > /tmp/luna-server.log 2>&1 &
SERVER_PID=$!

# Attendre que le serveur soit prêt
log "Attente du démarrage du serveur..."
MAX_ATTEMPTS=30
ATTEMPT=0
while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
  if curl -s http://localhost:$PORT/luna > /dev/null; then
    success "Serveur Luna démarré avec succès sur le port $PORT."
    break
  fi
  ATTEMPT=$((ATTEMPT+1))
  sleep 1
  echo -n "."
done

if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
  error "Le serveur Luna n'a pas démarré dans le temps imparti."
  kill $SERVER_PID 2>/dev/null
  exit 1
fi

# Ouvrir Luna dans Opera
log "Ouverture de Luna dans Opera..."
open -a Opera "http://localhost:$PORT/luna"

success "Luna est maintenant accessible dans Opera à l'adresse: http://localhost:$PORT/luna"
log "PID du serveur: $SERVER_PID (utilisez 'kill $SERVER_PID' pour l'arrêter)"

# Enregistrer le PID pour pouvoir arrêter le serveur plus tard
echo $SERVER_PID > /tmp/luna-server.pid

# Afficher les instructions pour arrêter le serveur
log "Pour arrêter le serveur, exécutez: kill \$(cat /tmp/luna-server.pid)"
