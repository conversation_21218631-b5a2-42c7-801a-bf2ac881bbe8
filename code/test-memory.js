/**
 * Script de test pour la mémoire thermique
 * Ce script teste les fonctionnalités de la mémoire thermique pour s'assurer que les informations
 * sont correctement stockées et récupérées.
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le répertoire de mémoire
const MEMORY_DIR = path.join(__dirname, 'data', 'memory');
const THERMAL_MEMORY_FILE = path.join(MEMORY_DIR, 'thermal_memory.json');

console.log('=== TEST DE LA MÉMOIRE THERMIQUE ===');
console.log(`Répertoire de mémoire: ${MEMORY_DIR}`);
console.log(`Fichier de mémoire thermique: ${THERMAL_MEMORY_FILE}`);

// Vérifier si le répertoire de mémoire existe
if (!fs.existsSync(MEMORY_DIR)) {
  console.error(`Le répertoire de mémoire n'existe pas: ${MEMORY_DIR}`);
  process.exit(1);
}

// Vérifier si le fichier de mémoire thermique existe
if (!fs.existsSync(THERMAL_MEMORY_FILE)) {
  console.error(`Le fichier de mémoire thermique n'existe pas: ${THERMAL_MEMORY_FILE}`);
  process.exit(1);
}

// Lire le contenu du fichier de mémoire thermique
try {
  const data = fs.readFileSync(THERMAL_MEMORY_FILE, 'utf8');
  const memory = JSON.parse(data);

  console.log('\n=== STRUCTURE DE LA MÉMOIRE THERMIQUE ===');
  console.log(`Nombre de zones de température: ${memory.config?.temperatureZones?.length || 0}`);
  console.log(`Nombre d'entrées de mémoire: ${memory.memories?.length || 0}`);

  // Afficher les zones de température
  if (memory.config && memory.config.temperatureZones) {
    console.log('\n=== ZONES DE TEMPÉRATURE ===');
    memory.config.temperatureZones.forEach((zone, index) => {
      console.log(`Zone ${index + 1}: ${zone.name} (Température: ${zone.temp})`);
    });
  }

  // Compter les entrées par zone
  if (memory.memories && memory.memories.length > 0) {
    console.log('\n=== DISTRIBUTION DES ENTRÉES PAR ZONE ===');
    const zoneCount = {};
    memory.memories.forEach(entry => {
      const zone = entry.zone || 'undefined';
      zoneCount[zone] = (zoneCount[zone] || 0) + 1;
    });

    Object.keys(zoneCount).sort().forEach(zone => {
      console.log(`Zone ${zone}: ${zoneCount[zone]} entrées`);
    });

    // Afficher les types d'entrées
    console.log('\n=== TYPES D\'ENTRÉES ===');
    const typeCount = {};
    memory.memories.forEach(entry => {
      const type = entry.type || 'undefined';
      typeCount[type] = (typeCount[type] || 0) + 1;
    });

    Object.keys(typeCount).sort().forEach(type => {
      console.log(`Type ${type}: ${typeCount[type]} entrées`);
    });

    // Afficher quelques exemples d'entrées
    console.log('\n=== EXEMPLES D\'ENTRÉES ===');
    for (let zone = 1; zone <= 6; zone++) {
      const zoneEntries = memory.memories.filter(entry => entry.zone === zone);
      if (zoneEntries.length > 0) {
        const sample = zoneEntries[0];
        console.log(`\nZone ${zone} - Exemple (${sample.id}):`);
        console.log(`  Type: ${sample.type || 'non défini'}`);
        console.log(`  Titre: ${sample.title || 'non défini'}`);
        console.log(`  Contenu: ${sample.content ? sample.content.substring(0, 100) + '...' : 'non défini'}`);
        console.log(`  Date de création: ${sample.firstSaved || 'non définie'}`);
        console.log(`  Dernière consultation: ${sample.lastAccessed || 'non définie'}`);
      } else {
        console.log(`Zone ${zone}: Aucune entrée trouvée`);
      }
    }
  }

  // Ajouter une entrée de test
  console.log('\n=== AJOUT D\'UNE ENTRÉE DE TEST ===');
  const now = new Date();
  const testEntry = {
    id: `test_entry_${Date.now()}`,
    type: 'test',
    title: 'Test de mémoire thermique',
    content: `Ceci est un test de la mémoire thermique. Créé le ${now.toLocaleString()}`,
    messages: [{
      role: 'system',
      content: 'Test de la mémoire thermique',
      timestamp: now.toISOString()
    }],
    metadata: {
      timestamp: now.toISOString(),
      source: 'memory_test',
      importance: 'low'
    },
    temperature: 100,
    zone: 1,
    firstSaved: now.toISOString(),
    lastAccessed: now.toISOString(),
    isVisible: true,
    compressed: false,
    compressionRatio: 1.0
  };

  // Ajouter l'entrée à la mémoire
  memory.memories.push(testEntry);
  console.log(`Entrée de test ajoutée: ${testEntry.id}`);

  // Sauvegarder la mémoire thermique
  const backupPath = path.join(MEMORY_DIR, `thermal_memory_backup_${Date.now()}.json`);
  fs.writeFileSync(backupPath, JSON.stringify(memory, null, 2), 'utf8');
  console.log(`Sauvegarde créée: ${backupPath}`);

  // Sauvegarder la mémoire thermique modifiée
  fs.writeFileSync(THERMAL_MEMORY_FILE, JSON.stringify(memory, null, 2), 'utf8');
  console.log('Mémoire thermique mise à jour avec l\'entrée de test');

  console.log('\n=== TEST DE LA MÉMOIRE THERMIQUE TERMINÉ ===');
} catch (error) {
  console.error(`Erreur lors du test de la mémoire thermique: ${error.message}`);
  process.exit(1);
}
