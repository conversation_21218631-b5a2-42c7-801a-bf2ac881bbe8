/**
 * Script pour corriger les problèmes de mémoire thermique dans le serveur Luna
 * Ce script corrige les erreurs suivantes :
 * - [BrainPresence] Méthode de stockage dans la mémoire thermique non disponible
 * - [BrainPresence] Erreur lors de l'extraction de concepts de la mémoire: this.thermalMemory.getEntriesFromZone is not a function
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier de mémoire thermique
const MEMORY_FILE = path.join(__dirname, 'data/memory/thermal_memory.json');
const BRAIN_PRESENCE_FILE = path.join(__dirname, 'services/brain-presence.js');
const THERMAL_MEMORY_FILE = path.join(__dirname, 'services/thermal-memory.js');

// Fonction pour charger la mémoire thermique
function loadMemory() {
  try {
    if (fs.existsSync(MEMORY_FILE)) {
      const data = fs.readFileSync(MEMORY_FILE, 'utf8');
      const memory = JSON.parse(data);
      console.log(`Mémoire thermique chargée: ${memory.memories.length} entrées`);
      return memory;
    } else {
      console.log('Fichier de mémoire thermique non trouvé');
      return { memories: [] };
    }
  } catch (error) {
    console.error(`Erreur lors du chargement de la mémoire thermique: ${error.message}`);
    return { memories: [] };
  }
}

// Fonction pour sauvegarder la mémoire thermique
function saveMemory(memory) {
  try {
    fs.writeFileSync(MEMORY_FILE, JSON.stringify(memory, null, 2), 'utf8');
    console.log('Mémoire thermique sauvegardée');
    return true;
  } catch (error) {
    console.error(`Erreur lors de la sauvegarde de la mémoire thermique: ${error.message}`);
    return false;
  }
}

// Fonction pour corriger le service de mémoire thermique
function fixThermalMemoryService() {
  try {
    if (fs.existsSync(THERMAL_MEMORY_FILE)) {
      let content = fs.readFileSync(THERMAL_MEMORY_FILE, 'utf8');
      
      // Vérifier si la méthode getEntriesFromZone existe
      if (!content.includes('getEntriesFromZone')) {
        // Ajouter la méthode getEntriesFromZone
        const methodToAdd = `
  /**
   * Récupère les entrées d'une zone spécifique
   * @param {number} zone - Zone de mémoire (1-6)
   * @returns {Array} - Liste des entrées de la zone
   */
  getEntriesFromZone(zone) {
    return this.memory.memories.filter(entry => entry.zone === zone);
  }`;
        
        // Trouver la position pour insérer la méthode (avant la dernière accolade)
        const lastBraceIndex = content.lastIndexOf('}');
        if (lastBraceIndex !== -1) {
          content = content.slice(0, lastBraceIndex) + methodToAdd + content.slice(lastBraceIndex);
          fs.writeFileSync(THERMAL_MEMORY_FILE, content, 'utf8');
          console.log('Service de mémoire thermique corrigé: méthode getEntriesFromZone ajoutée');
        }
      } else {
        console.log('La méthode getEntriesFromZone existe déjà dans le service de mémoire thermique');
      }
    } else {
      console.error('Fichier du service de mémoire thermique non trouvé');
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du service de mémoire thermique: ${error.message}`);
  }
}

// Fonction pour corriger le service de présence cérébrale
function fixBrainPresenceService() {
  try {
    if (fs.existsSync(BRAIN_PRESENCE_FILE)) {
      let content = fs.readFileSync(BRAIN_PRESENCE_FILE, 'utf8');
      
      // Vérifier si le service utilise correctement la mémoire thermique
      if (content.includes('this.thermalMemory.getEntriesFromZone is not a function')) {
        // Corriger l'erreur
        content = content.replace('this.thermalMemory.getEntriesFromZone is not a function', 'this.thermalMemory.getEntriesFromZone(zone)');
        fs.writeFileSync(BRAIN_PRESENCE_FILE, content, 'utf8');
        console.log('Service de présence cérébrale corrigé: appel à getEntriesFromZone corrigé');
      }
      
      // Vérifier si le service initialise correctement la mémoire thermique
      if (!content.includes('this.thermalMemory = thermalMemory;')) {
        // Corriger l'initialisation
        content = content.replace('constructor(', 'constructor(thermalMemory, ');
        content = content.replace('constructor() {', 'constructor(thermalMemory) {\n    this.thermalMemory = thermalMemory;');
        fs.writeFileSync(BRAIN_PRESENCE_FILE, content, 'utf8');
        console.log('Service de présence cérébrale corrigé: initialisation de la mémoire thermique ajoutée');
      }
    } else {
      console.error('Fichier du service de présence cérébrale non trouvé');
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du service de présence cérébrale: ${error.message}`);
  }
}

// Fonction pour corriger le fichier server-luna.js
function fixServerLuna() {
  try {
    const SERVER_LUNA_FILE = path.join(__dirname, 'server-luna.js');
    if (fs.existsSync(SERVER_LUNA_FILE)) {
      let content = fs.readFileSync(SERVER_LUNA_FILE, 'utf8');
      
      // Vérifier si le serveur initialise correctement le service de présence cérébrale
      if (!content.includes('const brainPresence = new BrainPresence(thermalMemory);')) {
        // Corriger l'initialisation
        content = content.replace('const brainPresence = new BrainPresence();', 'const brainPresence = new BrainPresence(thermalMemory);');
        fs.writeFileSync(SERVER_LUNA_FILE, content, 'utf8');
        console.log('Serveur Luna corrigé: initialisation du service de présence cérébrale corrigée');
      } else {
        console.log('Le serveur Luna initialise déjà correctement le service de présence cérébrale');
      }
    } else {
      console.error('Fichier du serveur Luna non trouvé');
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du serveur Luna: ${error.message}`);
  }
}

// Fonction pour ajouter des entrées d'identité dans la mémoire thermique
function addIdentityEntries() {
  const memory = loadMemory();
  
  // Vérifier si les entrées d'identité existent déjà
  const userIdentityExists = memory.memories.some(entry => entry.type === 'user_identity');
  const assistantIdentityExists = memory.memories.some(entry => entry.type === 'assistant_identity');
  
  // Ajouter l'entrée d'identité de l'utilisateur si elle n'existe pas
  if (!userIdentityExists) {
    memory.memories.push({
      id: 'user_identity_1',
      type: 'user_identity',
      zone: 6, // Zone long terme
      content: "L'utilisateur s'appelle Jean-Luc Passave. Il habite à Sainte-Anne en Guadeloupe (97180). Il est d'origine africaine."
    });
    console.log('Entrée d\'identité de l\'utilisateur ajoutée à la mémoire thermique');
  }
  
  // Ajouter l'entrée d'identité de l'assistant si elle n'existe pas
  if (!assistantIdentityExists) {
    memory.memories.push({
      id: 'assistant_identity_1',
      type: 'assistant_identity',
      zone: 6, // Zone long terme
      content: "Je suis Vision Ultra, une assistante IA avancée. Je ne suis PAS Louna ni aucun autre nom."
    });
    console.log('Entrée d\'identité de l\'assistant ajoutée à la mémoire thermique');
  }
  
  // Ajouter les règles d'identité
  const identityRulesExists = memory.memories.some(entry => entry.type === 'identity_rules');
  if (!identityRulesExists) {
    memory.memories.push({
      id: 'identity_rules_1',
      type: 'identity_rules',
      zone: 6, // Zone long terme
      content: "RÈGLES D'IDENTITÉ:\n1. Je m'appelle Vision Ultra, PAS Louna.\n2. L'utilisateur s'appelle Jean-Luc Passave.\n3. L'utilisateur habite à Sainte-Anne en Guadeloupe (97180).\n4. L'utilisateur est d'origine africaine."
    });
    console.log('Entrée des règles d\'identité ajoutée à la mémoire thermique');
  }
  
  // Ajouter l'information sur le créateur
  const creatorInfoExists = memory.memories.some(entry => entry.type === 'creator_info');
  if (!creatorInfoExists) {
    memory.memories.push({
      id: 'creator_info_1',
      type: 'creator_info',
      zone: 6, // Zone long terme
      content: "Jean-Luc Passave est mon créateur. Il m'a créé pour l'aider dans ses tâches quotidiennes."
    });
    console.log('Entrée d\'information sur le créateur ajoutée à la mémoire thermique');
  }
  
  // Sauvegarder la mémoire
  saveMemory(memory);
}

// Fonction principale
function main() {
  console.log('=== CORRECTION DES PROBLÈMES DE MÉMOIRE THERMIQUE ===');
  
  // Corriger le service de mémoire thermique
  fixThermalMemoryService();
  
  // Corriger le service de présence cérébrale
  fixBrainPresenceService();
  
  // Corriger le serveur Luna
  fixServerLuna();
  
  // Ajouter des entrées d'identité dans la mémoire thermique
  addIdentityEntries();
  
  console.log('=== CORRECTION TERMINÉE ===');
  console.log('Veuillez redémarrer le serveur Luna pour appliquer les corrections');
}

// Exécuter la fonction principale
main();
