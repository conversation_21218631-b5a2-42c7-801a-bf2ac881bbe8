<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --louna-primary: #9c89b8;
      --louna-secondary: #f0a6ca;
      --louna-accent: #b8bedd;
      --louna-dark: #1a1a2e;
      --louna-light: #edf2fb;
      --louna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
      --sidebar-width: 250px;
    }
    
    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--louna-light);
      color: var(--louna-dark);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: var(--louna-gradient);
      color: white;
      height: var(--header-height);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      padding: 0 1rem;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .header h1 {
      font-size: 1.5rem;
      margin: 0;
      font-weight: 700;
    }
    
    .sidebar {
      background-color: white;
      width: var(--sidebar-width);
      position: fixed;
      top: var(--header-height);
      left: 0;
      bottom: 0;
      padding: 1rem 0;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
      overflow-y: auto;
      z-index: 900;
    }
    
    .sidebar-menu {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .sidebar-menu li {
      padding: 0.5rem 1rem;
    }
    
    .sidebar-menu a {
      color: var(--louna-dark);
      text-decoration: none;
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border-radius: 5px;
      transition: all 0.3s ease;
    }
    
    .sidebar-menu a:hover {
      background-color: rgba(156, 137, 184, 0.1);
    }
    
    .sidebar-menu a.active {
      background-color: var(--louna-primary);
      color: white;
    }
    
    .sidebar-menu i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }
    
    .main-content {
      margin-left: var(--sidebar-width);
      margin-top: var(--header-height);
      padding: 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .memory-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      padding: 2rem;
    }
    
    .memory-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }
    
    .memory-title {
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--louna-primary);
    }
    
    .memory-controls {
      display: flex;
      gap: 1rem;
    }
    
    .memory-btn {
      background: var(--louna-gradient);
      color: white;
      border: none;
      border-radius: 5px;
      padding: 0.5rem 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
    }
    
    .memory-btn i {
      margin-right: 0.5rem;
    }
    
    .memory-btn:hover {
      opacity: 0.9;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .memory-stats {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
    }
    
    .memory-stat {
      text-align: center;
    }
    
    .memory-stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--louna-primary);
    }
    
    .memory-stat-label {
      color: #666;
      font-size: 0.9rem;
    }
    
    .memory-zones {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .memory-zone {
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
    }
    
    .memory-zone-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #eee;
    }
    
    .memory-zone-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--louna-primary);
    }
    
    .memory-zone-count {
      background-color: var(--louna-primary);
      color: white;
      border-radius: 20px;
      padding: 0.2rem 0.8rem;
      font-size: 0.8rem;
      font-weight: 600;
    }
    
    .memory-items {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1rem;
    }
    
    .memory-item {
      background-color: white;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }
    
    .memory-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .memory-content {
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }
    
    .memory-meta {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: #666;
    }
    
    .memory-search {
      margin-bottom: 2rem;
    }
    
    .memory-search-input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: 'Quicksand', sans-serif;
    }
    
    .footer {
      background-color: var(--louna-dark);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: auto;
      margin-left: var(--sidebar-width);
    }
    
    .system-status {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.5rem;
    }
    
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    
    .status-indicator.active {
      background-color: #4CAF50;
    }
    
    .status-indicator.inactive {
      background-color: #F44336;
    }
    
    .status-text {
      font-size: 0.8rem;
      margin-right: 1rem;
    }
    
    .version {
      font-size: 0.8rem;
      opacity: 0.7;
    }
    
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content, .footer {
        margin-left: 0;
      }
      
      .menu-toggle {
        display: block;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <button class="menu-toggle btn btn-link text-white me-3 d-md-none">
      <i class="bi bi-list"></i>
    </button>
    <h1><%= title %></h1>
  </header>
  
  <nav class="sidebar">
    <ul class="sidebar-menu">
      <li><a href="/louna/home" class="<%= page === 'home' ? 'active' : '' %>"><i class="bi bi-house"></i> Accueil</a></li>
      <li><a href="/louna/chat" class="<%= page === 'chat' ? 'active' : '' %>"><i class="bi bi-chat"></i> Chat</a></li>
      <li><a href="/louna/memory" class="<%= page === 'memory' ? 'active' : '' %>"><i class="bi bi-brain"></i> Mémoire</a></li>
      <li><a href="/louna/training" class="<%= page === 'training' ? 'active' : '' %>"><i class="bi bi-mortarboard"></i> Formation</a></li>
      <li><a href="/louna/code" class="<%= page === 'code' ? 'active' : '' %>"><i class="bi bi-code-square"></i> Code</a></li>
      <li><a href="/louna/security" class="<%= page === 'security' ? 'active' : '' %>"><i class="bi bi-shield-lock"></i> Sécurité</a></li>
      <li><a href="/louna/backup" class="<%= page === 'backup' ? 'active' : '' %>"><i class="bi bi-cloud-arrow-up"></i> Sauvegarde</a></li>
      <li><a href="/louna/monitor" class="<%= page === 'monitor' ? 'active' : '' %>"><i class="bi bi-activity"></i> Surveillance</a></li>
      <li><a href="/louna/accelerators" class="<%= page === 'accelerators' ? 'active' : '' %>"><i class="bi bi-lightning"></i> Accélérateurs</a></li>
      <li><a href="/louna/stats" class="<%= page === 'stats' ? 'active' : '' %>"><i class="bi bi-bar-chart"></i> Statistiques</a></li>
      <li><a href="/louna/settings" class="<%= page === 'settings' ? 'active' : '' %>"><i class="bi bi-gear"></i> Paramètres</a></li>
      <li><a href="/louna/models" class="<%= page === 'models' ? 'active' : '' %>"><i class="bi bi-boxes"></i> Modèles</a></li>
      <li><a href="/louna/documents" class="<%= page === 'documents' ? 'active' : '' %>"><i class="bi bi-file-earmark-text"></i> Documents</a></li>
      <li><a href="/louna/prompts" class="<%= page === 'prompts' ? 'active' : '' %>"><i class="bi bi-chat-square-text"></i> Prompts</a></li>
      <li><a href="/louna/mcp" class="<%= page === 'mcp' ? 'active' : '' %>"><i class="bi bi-cpu"></i> MCP</a></li>
      <li><a href="/louna/internet" class="<%= page === 'internet' ? 'active' : '' %>"><i class="bi bi-globe"></i> Internet</a></li>
      <li><a href="/louna/vpn" class="<%= page === 'vpn' ? 'active' : '' %>"><i class="bi bi-shield"></i> VPN</a></li>
      <li><a href="/louna/antivirus" class="<%= page === 'antivirus' ? 'active' : '' %>"><i class="bi bi-virus"></i> Antivirus</a></li>
      <li><a href="/louna/cognitive" class="<%= page === 'cognitive' ? 'active' : '' %>"><i class="bi bi-lightbulb"></i> Cognitive</a></li>
    </ul>
  </nav>
  
  <main class="main-content">
    <div class="memory-container">
      <div class="memory-header">
        <h2 class="memory-title">Mémoire Thermique</h2>
        <div class="memory-controls">
          <button class="memory-btn" id="dream-mode-btn"><i class="bi bi-moon-stars"></i> Mode Rêve</button>
          <button class="memory-btn" id="consolidate-btn"><i class="bi bi-arrow-repeat"></i> Consolider</button>
          <button class="memory-btn" id="visualize-btn"><i class="bi bi-graph-up"></i> Visualiser</button>
        </div>
      </div>
      
      <div class="memory-search">
        <input type="text" class="memory-search-input" placeholder="Rechercher dans la mémoire...">
      </div>
      
      <div class="memory-stats">
        <div class="memory-stat">
          <div class="memory-stat-value" id="total-memories">0</div>
          <div class="memory-stat-label">Total des mémoires</div>
        </div>
        
        <div class="memory-stat">
          <div class="memory-stat-value" id="active-zones">0</div>
          <div class="memory-stat-label">Zones actives</div>
        </div>
        
        <div class="memory-stat">
          <div class="memory-stat-value" id="avg-importance">0%</div>
          <div class="memory-stat-label">Importance moyenne</div>
        </div>
        
        <div class="memory-stat">
          <div class="memory-stat-value" id="memory-health">0%</div>
          <div class="memory-stat-label">Santé de la mémoire</div>
        </div>
      </div>
      
      <div class="memory-zones" id="memory-zones">
        <!-- Les zones de mémoire seront ajoutées ici dynamiquement -->
      </div>
    </div>
  </main>
  
  <footer class="footer">
    <p>Louna - Interface Cognitive Avancée &copy; 2025</p>
    <div class="system-status">
      <span class="status-indicator active"></span>
      <span class="status-text">Système actif</span>
      <span class="version">v1.0.0</span>
    </div>
  </footer>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const menuToggle = document.querySelector('.menu-toggle');
      const sidebar = document.querySelector('.sidebar');
      const memoryZonesContainer = document.getElementById('memory-zones');
      const totalMemoriesElement = document.getElementById('total-memories');
      const activeZonesElement = document.getElementById('active-zones');
      const avgImportanceElement = document.getElementById('avg-importance');
      const memoryHealthElement = document.getElementById('memory-health');
      
      // Gérer le menu mobile
      if (menuToggle) {
        menuToggle.addEventListener('click', function() {
          sidebar.classList.toggle('show');
        });
      }
      
      // Connexion au serveur WebSocket
      const socket = io();
      
      // Demander les statistiques de mémoire
      socket.emit('get memory stats');
      
      // Recevoir les statistiques de mémoire
      socket.on('memory stats', function(stats) {
        totalMemoriesElement.textContent = stats.totalMemories;
        activeZonesElement.textContent = Object.keys(stats.zoneStats).length;
        
        // Calculer l'importance moyenne
        let totalImportance = 0;
        let totalCount = 0;
        
        for (const zone in stats.zoneStats) {
          totalImportance += stats.zoneStats[zone].avgImportance * stats.zoneStats[zone].count;
          totalCount += stats.zoneStats[zone].count;
        }
        
        const avgImportance = totalCount > 0 ? (totalImportance / totalCount) * 100 : 0;
        avgImportanceElement.textContent = Math.round(avgImportance) + '%';
        
        // Calculer la santé de la mémoire (simulée)
        memoryHealthElement.textContent = '95%';
        
        // Afficher les zones de mémoire
        memoryZonesContainer.innerHTML = '';
        
        const zoneNames = {
          instant: 'Mémoire Instantanée',
          short_term: 'Mémoire à Court Terme',
          working: 'Mémoire de Travail',
          medium_term: 'Mémoire à Moyen Terme',
          long_term: 'Mémoire à Long Terme',
          dream: 'Mémoire de Rêve',
          kyber: 'Mémoire Kyber',
          archive: 'Archives'
        };
        
        for (const zone in stats.zoneStats) {
          const zoneElement = document.createElement('div');
          zoneElement.className = 'memory-zone';
          
          zoneElement.innerHTML = `
            <div class="memory-zone-header">
              <h3 class="memory-zone-title">${zoneNames[zone] || zone}</h3>
              <span class="memory-zone-count">${stats.zoneStats[zone].count}</span>
            </div>
            <div class="memory-items" id="${zone}-items">
              <!-- Les éléments de mémoire seront ajoutés ici -->
            </div>
          `;
          
          memoryZonesContainer.appendChild(zoneElement);
        }
      });
      
      // Demander les mémoires
      socket.emit('get memories');
      
      // Recevoir les mémoires
      socket.on('memories', function(memories) {
        for (const zone in memories) {
          const zoneItemsContainer = document.getElementById(`${zone}-items`);
          
          if (zoneItemsContainer) {
            zoneItemsContainer.innerHTML = '';
            
            for (const memory of memories[zone]) {
              const memoryElement = document.createElement('div');
              memoryElement.className = 'memory-item';
              
              // Limiter le contenu à 100 caractères
              const contentPreview = memory.content.length > 100 
                ? memory.content.substring(0, 100) + '...' 
                : memory.content;
              
              // Formater la date
              const date = new Date(memory.timestamp);
              const formattedDate = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
              
              memoryElement.innerHTML = `
                <div class="memory-content">${contentPreview}</div>
                <div class="memory-meta">
                  <span>Importance: ${(memory.importance * 100).toFixed(0)}%</span>
                  <span>${formattedDate}</span>
                </div>
              `;
              
              zoneItemsContainer.appendChild(memoryElement);
            }
          }
        }
      });
      
      // Gérer le bouton de mode rêve
      document.getElementById('dream-mode-btn').addEventListener('click', function() {
        socket.emit('toggle dream mode');
      });
      
      // Gérer le bouton de consolidation
      document.getElementById('consolidate-btn').addEventListener('click', function() {
        socket.emit('consolidate memories');
      });
      
      // Gérer le bouton de visualisation
      document.getElementById('visualize-btn').addEventListener('click', function() {
        alert('Fonctionnalité de visualisation en cours de développement.');
      });
    });
  </script>
</body>
</html>
