/**
 * Script pour corriger les serveurs Louna et Lounas
 */

const fs = require('fs');
const path = require('path');

// Chemin vers les fichiers
const LOUNA_SERVER_FILE = path.join(__dirname, 'server-louna.js');
const LOUNAS_SERVER_FILE = path.join(__dirname, 'server-lounas.js');

// Fonction pour corriger le serveur Louna
function fixLounaServer() {
  try {
    if (fs.existsSync(LOUNA_SERVER_FILE)) {
      // Lire le contenu du fichier
      let content = fs.readFileSync(LOUNA_SERVER_FILE, 'utf8');
      
      // Créer une sauvegarde du fichier original
      fs.writeFileSync(`${LOUNA_SERVER_FILE}.bak`, content, 'utf8');
      console.log('Sauvegarde du fichier server-louna.js créée');
      
      // Remplacer le gestionnaire de socket pour les messages
      content = content.replace(
        /\/\/ Gérer les messages de l'utilisateur[\s\S]*?socket\.emit\('louna response', response\);[\s\S]*?}, 1000\);/g,
        `// Gérer les messages de l'utilisateur
  socket.on('louna message', (data) => {
    console.log('Message reçu:', data.message);
    
    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });
    
    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: \`Je suis Vision Ultra, votre assistant cognitif. Vous avez dit: "\${data.message}"\`,
        timestamp: new Date().toISOString()
      };
      
      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });
      
      // Envoyer la réponse au client
      socket.emit('louna response', response);
      console.log('Réponse envoyée:', response.message);
    }, 1000);`
      );
      
      // Écrire le contenu modifié dans le fichier
      fs.writeFileSync(LOUNA_SERVER_FILE, content, 'utf8');
      console.log('Fichier server-louna.js corrigé');
      
      return true;
    } else {
      console.error('Fichier server-louna.js non trouvé');
      return false;
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du fichier server-louna.js: ${error.message}`);
    return false;
  }
}

// Fonction pour corriger le serveur Lounas
function fixLounasServer() {
  try {
    if (fs.existsSync(LOUNAS_SERVER_FILE)) {
      // Lire le contenu du fichier
      let content = fs.readFileSync(LOUNAS_SERVER_FILE, 'utf8');
      
      // Créer une sauvegarde du fichier original
      fs.writeFileSync(`${LOUNAS_SERVER_FILE}.bak`, content, 'utf8');
      console.log('Sauvegarde du fichier server-lounas.js créée');
      
      // Remplacer le gestionnaire de socket pour les messages
      content = content.replace(
        /\/\/ Gérer les messages de l'utilisateur[\s\S]*?socket\.emit\('lounas response', response\);[\s\S]*?}, 1000\);/g,
        `// Gérer les messages de l'utilisateur
  socket.on('lounas message', (data) => {
    console.log('Message reçu:', data.message);
    
    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });
    
    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: \`Je suis Vision Ultra, votre assistant cognitif. Vous avez dit: "\${data.message}"\`,
        timestamp: new Date().toISOString()
      };
      
      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });
      
      // Envoyer la réponse au client
      socket.emit('lounas response', response);
      console.log('Réponse envoyée:', response.message);
    }, 1000);`
      );
      
      // Écrire le contenu modifié dans le fichier
      fs.writeFileSync(LOUNAS_SERVER_FILE, content, 'utf8');
      console.log('Fichier server-lounas.js corrigé');
      
      return true;
    } else {
      console.error('Fichier server-lounas.js non trouvé');
      return false;
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du fichier server-lounas.js: ${error.message}`);
    return false;
  }
}

// Fonction principale
function main() {
  console.log('=== CORRECTION DES SERVEURS LOUNA ET LOUNAS ===');
  
  // Corriger le serveur Louna
  const lounaSuccess = fixLounaServer();
  
  // Corriger le serveur Lounas
  const lounasSuccess = fixLounasServer();
  
  if (lounaSuccess && lounasSuccess) {
    console.log('=== CORRECTION TERMINÉE ===');
    console.log('Veuillez redémarrer les serveurs Louna et Lounas pour appliquer les corrections');
  } else {
    console.log('=== ÉCHEC DE LA CORRECTION ===');
    console.log('Veuillez vérifier les fichiers server-louna.js et server-lounas.js manuellement');
  }
}

// Exécuter la fonction principale
main();
