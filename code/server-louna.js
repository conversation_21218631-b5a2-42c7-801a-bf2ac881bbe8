/**
 * Serveur <PERSON>na - Interface cognitive avancée
 * Version simplifiée pour tester la mémoire thermique
 */

const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');
const socketIo = require('socket.io');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3002;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Charger les services
const ThermalMemory = require('./services/thermal-memory');
const BrainPresence = require('./services/brain-presence');

// Initialiser la mémoire thermique
const thermalMemory = new ThermalMemory(path.join(__dirname, 'data/memory/thermal_memory.json'));
console.log('Mémoire thermique initialisée');

// Initialiser le service de présence cérébrale
const brainPresence = new BrainPresence(thermalMemory);
console.log('Service de présence cérébrale initialisé');

// Activer le service de présence cérébrale
brainPresence.initialize();
console.log('Service de présence cérébrale activé');

// Route principale
app.get('/louna', (req, res) => {
  res.render('luna-chat', {
    title: 'Louna - Interface Cognitive',
    page: 'chat'
  });
});

// Route pour la page d'accueil
app.get('/louna/home', (req, res) => {
  res.render('luna-home', {
    title: 'Louna - Accueil',
    page: 'home'
  });
});

// Route pour la page de chat
app.get('/louna/chat', (req, res) => {
  res.render('luna-chat', {
    title: 'Louna - Chat',
    page: 'chat'
  });
});

// Route pour la page de mémoire
app.get('/louna/memory', (req, res) => {
  res.render('luna-memory', {
    title: 'Louna - Mémoire',
    page: 'memory'
  });
});

// Route pour la page de formation
app.get('/louna/training', (req, res) => {
  res.render('luna-training', {
    title: 'Louna - Formation',
    page: 'training'
  });
});

// Route pour la page de code
app.get('/louna/code', (req, res) => {
  res.render('luna-code', {
    title: 'Louna - Code',
    page: 'code'
  });
});

// Route pour la page de sécurité
app.get('/louna/security', (req, res) => {
  res.render('luna-security', {
    title: 'Louna - Sécurité',
    page: 'security'
  });
});

// Route pour la page de sauvegarde
app.get('/louna/backup', (req, res) => {
  res.render('luna-backup', {
    title: 'Louna - Sauvegarde',
    page: 'backup'
  });
});

// Route pour la page de surveillance
app.get('/louna/monitor', (req, res) => {
  res.render('luna-monitor', {
    title: 'Louna - Surveillance',
    page: 'monitor'
  });
});

// Route pour la page des accélérateurs
app.get('/louna/accelerators', (req, res) => {
  res.render('luna-accelerators', {
    title: 'Louna - Accélérateurs',
    page: 'accelerators'
  });
});

// Route pour la page des statistiques
app.get('/louna/stats', (req, res) => {
  res.render('luna-stats', {
    title: 'Louna - Statistiques',
    page: 'stats'
  });
});

// Route pour la page des paramètres
app.get('/louna/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Louna - Paramètres',
    page: 'settings'
  });
});

// Route pour la page des modèles
app.get('/louna/models', (req, res) => {
  res.render('luna-models', {
    title: 'Louna - Modèles',
    page: 'models'
  });
});

// Route pour la page des documents
app.get('/louna/documents', (req, res) => {
  res.render('luna-documents', {
    title: 'Louna - Documents',
    page: 'documents'
  });
});

// Route pour la page des prompts
app.get('/louna/prompts', (req, res) => {
  res.render('luna-prompts', {
    title: 'Louna - Prompts',
    page: 'prompts'
  });
});

// Route pour la page MCP
app.get('/louna/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Louna - MCP',
    page: 'mcp'
  });
});

// Route pour la page Internet
app.get('/louna/internet', (req, res) => {
  res.render('luna-internet', {
    title: 'Louna - Internet',
    page: 'internet'
  });
});

// Route pour la page VPN
app.get('/louna/vpn', (req, res) => {
  res.render('luna-vpn', {
    title: 'Louna - VPN',
    page: 'vpn'
  });
});

// Route pour la page Antivirus
app.get('/louna/antivirus', (req, res) => {
  res.render('luna-antivirus', {
    title: 'Louna - Antivirus',
    page: 'antivirus'
  });
});

// Route pour la page Cognitive
app.get('/louna/cognitive', (req, res) => {
  res.render('luna-cognitive', {
    title: 'Louna - Cognitive',
    page: 'cognitive'
  });
});

// Gestionnaire de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');

  // Gérer les messages de l'utilisateur
  socket.on('louna message', (data) => {
    console.log('Message reçu:', data.message);

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Simuler une réponse
    setTimeout(() => {
      const response = {
        message: `Je suis Louna, votre assistant cognitif. Vous avez dit: "${data.message}"`,
        timestamp: new Date().toISOString()
      };

      // Stocker la réponse dans la mémoire thermique
      thermalMemory.addEntry({
        type: 'output',
        content: response.message,
        timestamp: response.timestamp
      });

      // Envoyer la réponse au client
      socket.emit('louna response', response);
    }, 1000);
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Louna démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}/louna`);
});
