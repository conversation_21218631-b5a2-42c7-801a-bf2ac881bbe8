#!/bin/bash
# Script pour arrêter le serveur Luna
# Créé le 16 mai 2025

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages avec un préfixe coloré
log() {
  echo -e "${BLUE}[LUNA]${NC} $1"
}

error() {
  echo -e "${RED}[ERREUR]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCÈS]${NC} $1"
}

warning() {
  echo -e "${YELLOW}[ATTENTION]${NC} $1"
}

# Vérifier si le fichier PID existe
if [ ! -f "/tmp/luna-server.pid" ]; then
  warning "Aucun serveur Luna en cours d'exécution ou PID non enregistré."
  
  # Essayer de trouver le processus par le port
  for PORT in 3001 3002 3005 3010 3015; do
    PID=$(lsof -i :$PORT | grep LISTEN | awk '{print $2}')
    if [ ! -z "$PID" ]; then
      warning "Serveur trouvé sur le port $PORT avec PID $PID."
      break
    fi
  done
  
  if [ -z "$PID" ]; then
    error "Impossible de trouver un serveur Luna en cours d'exécution."
    exit 1
  fi
else
  PID=$(cat /tmp/luna-server.pid)
  log "Serveur Luna trouvé avec PID $PID."
fi

# Arrêter le serveur
log "Arrêt du serveur Luna..."
kill $PID 2>/dev/null

# Vérifier si le serveur a été arrêté
sleep 2
if ps -p $PID > /dev/null; then
  warning "Le serveur ne s'est pas arrêté normalement. Tentative de forcer l'arrêt..."
  kill -9 $PID 2>/dev/null
  sleep 1
fi

# Vérifier à nouveau
if ps -p $PID > /dev/null; then
  error "Impossible d'arrêter le serveur Luna."
  exit 1
else
  success "Serveur Luna arrêté avec succès."
  # Supprimer le fichier PID
  rm -f /tmp/luna-server.pid
fi
