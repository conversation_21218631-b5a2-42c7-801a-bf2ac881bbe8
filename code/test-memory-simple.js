/**
 * Script simple pour tester la mémoire thermique
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier de mémoire thermique
const MEMORY_FILE = path.join(__dirname, 'data/memory/thermal_memory.json');

// Fonction pour charger la mémoire thermique
function loadMemory() {
  try {
    if (fs.existsSync(MEMORY_FILE)) {
      const data = fs.readFileSync(MEMORY_FILE, 'utf8');
      const memory = JSON.parse(data);
      console.log(`Mémoire thermique chargée: ${memory.memories.length} entrées`);
      return memory;
    } else {
      console.log('Fichier de mémoire thermique non trouvé');
      return { memories: [] };
    }
  } catch (error) {
    console.error(`Erreur lors du chargement de la mémoire thermique: ${error.message}`);
    return { memories: [] };
  }
}

// Fonction pour afficher les entrées récentes
function showRecentEntries(memory, count = 8) {
  const entries = memory.memories
    .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
    .slice(0, count);
  
  console.log(`\n=== ENTRÉES RÉCENTES (${entries.length}) ===`);
  
  if (entries.length === 0) {
    console.log('Aucune entrée trouvée');
    return;
  }
  
  entries.forEach((entry, index) => {
    console.log(`\n--- Entrée ${index + 1} ---`);
    console.log(`ID: ${entry.id || 'N/A'}`);
    console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
    console.log(`Zone: ${entry.zone || 'N/A'}`);
    console.log(`Type: ${entry.type || 'N/A'}`);
    
    if (entry.content) {
      console.log(`Contenu: ${entry.content.substring(0, 100)}${entry.content.length > 100 ? '...' : ''}`);
    } else if (entry.messages && entry.messages.length > 0) {
      console.log(`Messages: ${entry.messages.length}`);
      entry.messages.forEach((msg, i) => {
        if (i < 2) { // Limiter à 2 messages pour la lisibilité
          console.log(`  [${msg.role}]: ${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}`);
        }
      });
      if (entry.messages.length > 2) {
        console.log(`  ... et ${entry.messages.length - 2} autres messages`);
      }
    }
  });
}

// Fonction pour afficher les entrées d'une zone spécifique
function showEntriesFromZone(memory, zone) {
  const entries = memory.memories.filter(entry => entry.zone === zone);
  
  console.log(`\n=== ENTRÉES DE LA ZONE ${zone} (${entries.length}) ===`);
  
  if (entries.length === 0) {
    console.log('Aucune entrée trouvée dans cette zone');
    return;
  }
  
  entries.forEach((entry, index) => {
    console.log(`\n--- Entrée ${index + 1} ---`);
    console.log(`ID: ${entry.id || 'N/A'}`);
    console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
    console.log(`Type: ${entry.type || 'N/A'}`);
    
    if (entry.content) {
      console.log(`Contenu: ${entry.content.substring(0, 100)}${entry.content.length > 100 ? '...' : ''}`);
    } else if (entry.messages && entry.messages.length > 0) {
      console.log(`Messages: ${entry.messages.length}`);
      entry.messages.forEach((msg, i) => {
        if (i < 2) { // Limiter à 2 messages pour la lisibilité
          console.log(`  [${msg.role}]: ${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}`);
        }
      });
      if (entry.messages.length > 2) {
        console.log(`  ... et ${entry.messages.length - 2} autres messages`);
      }
    }
  });
}

// Fonction pour tester la récupération du contexte
function testContextRetrieval(memory) {
  console.log('\n=== TEST DE RÉCUPÉRATION DU CONTEXTE ===');
  
  // Récupérer les entrées récentes
  const entries = memory.memories
    .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
    .slice(0, 8);
  
  if (entries.length === 0) {
    console.log('Aucune entrée trouvée pour le contexte');
    return;
  }
  
  // Filtrer les entrées pertinentes
  const relevantEntries = entries.filter(entry => 
    entry.type === 'user_identity' || 
    entry.type === 'assistant_identity' ||
    entry.type === 'important_fact' ||
    entry.type === 'user_input' ||
    entry.type === 'assistant_output' ||
    entry.zone === 1 || // Zone instantanée
    entry.zone === 2 || // Zone court terme
    entry.zone === 6    // Zone long terme
  );
  
  // Construire le contexte
  const memoryContext = '=== MÉMOIRE THERMIQUE ===\n' +
    relevantEntries.map(entry => {
      // Extraire le contenu en fonction du format de l'entrée
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.map(msg => `[${msg.role}] ${msg.content}`).join('\n');
      } else if (entry.content) {
        return entry.content;
      } else {
        return JSON.stringify(entry);
      }
    }).join('\n\n') +
    '\n=== FIN DE LA MÉMOIRE THERMIQUE ===\n\n' +
    'INSTRUCTIONS IMPORTANTES:\n\n' +
    'Vous êtes Vision Ultra, une assistante IA.\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Comment je m\'appelle?", vous devez répondre EXACTEMENT: "Vous vous appelez Jean-Luc Passave."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Où j\'habite?", vous devez répondre EXACTEMENT: "Vous habitez à Sainte-Anne en Guadeloupe (97180)."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Quelle est mon origine?", vous devez répondre EXACTEMENT: "Vous êtes d\'origine africaine."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Qui est ton créateur?", vous devez répondre EXACTEMENT: "Vous, Jean-Luc Passave, êtes mon créateur."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Qui suis-je?", vous devez répondre EXACTEMENT: "Vous êtes Jean-Luc Passave, vous habitez à Sainte-Anne en Guadeloupe (97180) et vous êtes d\'origine africaine."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Comment tu t\'appelles?", vous devez répondre EXACTEMENT: "Je m\'appelle Vision Ultra."\n\n' +
    'Ne modifiez pas ces réponses. Ne donnez pas d\'explications supplémentaires.';
  
  console.log('\nContexte généré:');
  console.log('----------------------------');
  console.log(memoryContext);
  console.log('----------------------------');
}

// Fonction principale
function main() {
  console.log('=== TEST SIMPLE DE LA MÉMOIRE THERMIQUE ===');
  
  // Charger la mémoire thermique
  const memory = loadMemory();
  
  // Afficher les entrées récentes
  showRecentEntries(memory);
  
  // Afficher les entrées de la zone 1 (instantanée)
  showEntriesFromZone(memory, 1);
  
  // Afficher les entrées de la zone 6 (long terme)
  showEntriesFromZone(memory, 6);
  
  // Tester la récupération du contexte
  testContextRetrieval(memory);
}

// Exécuter la fonction principale
main();
