/**
 * Script simple pour vérifier si l'interface Luna est accessible
 * Utilise http pour faire une requête à l'interface Luna
 */

const http = require('http');
const fs = require('fs');

// URL de l'interface Luna
const url = 'http://localhost:3010/luna';

console.log(`Vérification de l'interface Luna sur ${url}...`);

// Faire une requête HTTP GET
http.get(url, (res) => {
  const { statusCode } = res;
  const contentType = res.headers['content-type'];

  console.log(`Statut de la réponse: ${statusCode}`);
  console.log(`Type de contenu: ${contentType}`);

  let error;
  if (statusCode !== 200) {
    error = new Error(`Requête échouée avec le code ${statusCode}`);
  } else if (!/^text\/html/.test(contentType)) {
    error = new Error(`Type de contenu invalide: ${contentType}`);
  }

  if (error) {
    console.error(error.message);
    res.resume(); // Consommer la réponse pour libérer la mémoire
    return;
  }

  res.setEncoding('utf8');
  let rawData = '';
  res.on('data', (chunk) => { rawData += chunk; });
  res.on('end', () => {
    try {
      console.log(`Taille de la réponse: ${rawData.length} caractères`);
      
      // Vérifier si la réponse contient des éléments clés de l'interface Luna
      const hasLunaCard = rawData.includes('luna-card');
      const hasConversationContainer = rawData.includes('conversation-container');
      const hasUserInput = rawData.includes('user-input');
      const hasSendButton = rawData.includes('send-button');
      
      console.log(`Contient 'luna-card': ${hasLunaCard}`);
      console.log(`Contient 'conversation-container': ${hasConversationContainer}`);
      console.log(`Contient 'user-input': ${hasUserInput}`);
      console.log(`Contient 'send-button': ${hasSendButton}`);
      
      // Enregistrer la réponse dans un fichier pour analyse
      fs.writeFileSync('luna-response.html', rawData);
      console.log('Réponse enregistrée dans luna-response.html');
      
      // Résultat final
      if (hasLunaCard && hasConversationContainer && hasUserInput && hasSendButton) {
        console.log('✅ L\'interface Luna semble correctement chargée!');
      } else {
        console.log('❌ L\'interface Luna ne semble pas correctement chargée!');
      }
      
    } catch (e) {
      console.error(`Erreur lors de l'analyse de la réponse: ${e.message}`);
    }
  });
}).on('error', (e) => {
  console.error(`Erreur lors de la requête: ${e.message}`);
});
