<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --luna-primary: #9c89b8;
      --luna-secondary: #f0a6ca;
      --luna-accent: #b8bedd;
      --luna-dark: #1a1a2e;
      --luna-light: #edf2fb;
      --luna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
      --sidebar-width: 250px;
    }
    
    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--luna-light);
      color: var(--luna-dark);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: var(--luna-gradient);
      color: white;
      height: var(--header-height);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      padding: 0 1rem;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .header h1 {
      font-size: 1.5rem;
      margin: 0;
      font-weight: 700;
    }
    
    .sidebar {
      background-color: white;
      width: var(--sidebar-width);
      position: fixed;
      top: var(--header-height);
      left: 0;
      bottom: 0;
      padding: 1rem 0;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
      overflow-y: auto;
      z-index: 900;
    }
    
    .sidebar-menu {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .sidebar-menu li {
      padding: 0.5rem 1rem;
    }
    
    .sidebar-menu a {
      color: var(--luna-dark);
      text-decoration: none;
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border-radius: 5px;
      transition: all 0.3s ease;
    }
    
    .sidebar-menu a:hover {
      background-color: rgba(156, 137, 184, 0.1);
    }
    
    .sidebar-menu a.active {
      background-color: var(--luna-primary);
      color: white;
    }
    
    .sidebar-menu i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }
    
    .main-content {
      margin-left: var(--sidebar-width);
      margin-top: var(--header-height);
      padding: 2rem;
      flex: 1;
    }
    
    .card {
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      border: none;
    }
    
    .card-title {
      color: var(--luna-primary);
      font-weight: 700;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }
    
    .card-title i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }
    
    .btn-luna {
      background: var(--luna-gradient);
      color: white;
      border: none;
      border-radius: 5px;
      padding: 0.5rem 1rem;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    
    .btn-luna:hover {
      opacity: 0.9;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .footer {
      background-color: var(--luna-dark);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: auto;
      margin-left: var(--sidebar-width);
    }
    
    .system-status {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.5rem;
    }
    
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    
    .status-indicator.active {
      background-color: #4CAF50;
    }
    
    .status-indicator.inactive {
      background-color: #F44336;
    }
    
    .status-text {
      font-size: 0.8rem;
      margin-right: 1rem;
    }
    
    .version {
      font-size: 0.8rem;
      opacity: 0.7;
    }
    
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content, .footer {
        margin-left: 0;
      }
      
      .menu-toggle {
        display: block;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <button class="menu-toggle btn btn-link text-white me-3 d-md-none">
      <i class="bi bi-list"></i>
    </button>
    <h1><%= title %></h1>
  </header>
  
  <nav class="sidebar">
    <ul class="sidebar-menu">
      <li><a href="/luna/home" class="<%= page === 'home' ? 'active' : '' %>"><i class="bi bi-house"></i> Accueil</a></li>
      <li><a href="/luna/chat" class="<%= page === 'chat' ? 'active' : '' %>"><i class="bi bi-chat"></i> Chat</a></li>
      <li><a href="/luna/memory" class="<%= page === 'memory' ? 'active' : '' %>"><i class="bi bi-brain"></i> Mémoire</a></li>
      <li><a href="/luna/training" class="<%= page === 'training' ? 'active' : '' %>"><i class="bi bi-mortarboard"></i> Formation</a></li>
      <li><a href="/luna/code" class="<%= page === 'code' ? 'active' : '' %>"><i class="bi bi-code-square"></i> Code</a></li>
      <li><a href="/luna/security" class="<%= page === 'security' ? 'active' : '' %>"><i class="bi bi-shield-lock"></i> Sécurité</a></li>
      <li><a href="/luna/backup" class="<%= page === 'backup' ? 'active' : '' %>"><i class="bi bi-cloud-arrow-up"></i> Sauvegarde</a></li>
      <li><a href="/luna/monitor" class="<%= page === 'monitor' ? 'active' : '' %>"><i class="bi bi-activity"></i> Surveillance</a></li>
      <li><a href="/luna/accelerators" class="<%= page === 'accelerators' ? 'active' : '' %>"><i class="bi bi-lightning"></i> Accélérateurs</a></li>
      <li><a href="/luna/stats" class="<%= page === 'stats' ? 'active' : '' %>"><i class="bi bi-bar-chart"></i> Statistiques</a></li>
      <li><a href="/luna/settings" class="<%= page === 'settings' ? 'active' : '' %>"><i class="bi bi-gear"></i> Paramètres</a></li>
      <li><a href="/luna/models" class="<%= page === 'models' ? 'active' : '' %>"><i class="bi bi-boxes"></i> Modèles</a></li>
      <li><a href="/luna/documents" class="<%= page === 'documents' ? 'active' : '' %>"><i class="bi bi-file-earmark-text"></i> Documents</a></li>
      <li><a href="/luna/prompts" class="<%= page === 'prompts' ? 'active' : '' %>"><i class="bi bi-chat-square-text"></i> Prompts</a></li>
      <li><a href="/luna/mcp" class="<%= page === 'mcp' ? 'active' : '' %>"><i class="bi bi-cpu"></i> MCP</a></li>
      <li><a href="/luna/internet" class="<%= page === 'internet' ? 'active' : '' %>"><i class="bi bi-globe"></i> Internet</a></li>
      <li><a href="/luna/vpn" class="<%= page === 'vpn' ? 'active' : '' %>"><i class="bi bi-shield"></i> VPN</a></li>
      <li><a href="/luna/antivirus" class="<%= page === 'antivirus' ? 'active' : '' %>"><i class="bi bi-virus"></i> Antivirus</a></li>
      <li><a href="/luna/cognitive" class="<%= page === 'cognitive' ? 'active' : '' %>"><i class="bi bi-lightbulb"></i> Cognitive</a></li>
    </ul>
  </nav>
  
  <main class="main-content">
    <div class="card">
      <h2 class="card-title"><i class="bi bi-<%= page === 'home' ? 'house' : page === 'chat' ? 'chat' : page === 'memory' ? 'brain' : page === 'training' ? 'mortarboard' : page === 'code' ? 'code-square' : page === 'security' ? 'shield-lock' : page === 'backup' ? 'cloud-arrow-up' : page === 'monitor' ? 'activity' : page === 'accelerators' ? 'lightning' : page === 'stats' ? 'bar-chart' : page === 'settings' ? 'gear' : page === 'models' ? 'boxes' : page === 'documents' ? 'file-earmark-text' : page === 'prompts' ? 'chat-square-text' : page === 'mcp' ? 'cpu' : page === 'internet' ? 'globe' : page === 'vpn' ? 'shield' : page === 'antivirus' ? 'virus' : page === 'cognitive' ? 'lightbulb' : 'question-circle' %>"></i> <%= title %></h2>
      <p>Cette page est en cours de développement. Elle sera bientôt disponible.</p>
      <div class="alert alert-info">
        <i class="bi bi-info-circle"></i> Cette fonctionnalité sera disponible dans une prochaine mise à jour.
      </div>
      <button class="btn btn-luna mt-3">
        <i class="bi bi-arrow-clockwise"></i> Actualiser
      </button>
    </div>
  </main>
  
  <footer class="footer">
    <p>Luna - Interface Cognitive Avancée &copy; 2025</p>
    <div class="system-status">
      <span class="status-indicator active"></span>
      <span class="status-text">Système actif</span>
      <span class="version">v1.0.0</span>
    </div>
  </footer>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const menuToggle = document.querySelector('.menu-toggle');
      const sidebar = document.querySelector('.sidebar');
      
      if (menuToggle) {
        menuToggle.addEventListener('click', function() {
          sidebar.classList.toggle('show');
        });
      }
    });
  </script>
</body>
</html>
