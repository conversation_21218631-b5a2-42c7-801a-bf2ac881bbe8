
const EventEmitter = require('events');
class SpeechProcessor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    console.log('SpeechProcessor initialisé (mode de simulation)');
  }
  startListening() {
    console.log('Simulation d\'écoute démarrée');
    setTimeout(() => this.emit('recognitionResult', 'Commande simulée'), 1000);
    return true;
  }
  stopListening() { return true; }
  speak(text) {
    console.log('Simulation de synthèse vocale:', text);
    setTimeout(() => this.emit('speakingDone', text), 1000);
    return true;
  }
}
module.exports = SpeechProcessor;