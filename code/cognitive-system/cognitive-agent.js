
const EventEmitter = require('events');
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');

class CognitiveAgent extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    this.speech = new SpeechProcessor(options);
    this.sensory = new SensorySystem(options);
    this.cognitiveState = {
      isActive: false,
      isListening: false,
      isSpeaking: false,
      isObserving: false,
      lastUserInput: null,
      lastResponse: null,
      lastObservation: null,
      conversationContext: [],
      startTime: new Date(),
      shortTermMemory: []
    };
    console.log('CognitiveAgent initialisé (mode de simulation)');
  }
  activate() {
    this.cognitiveState.isActive = true;
    return true;
  }
  deactivate() {
    this.cognitiveState.isActive = false;
    return true;
  }
  speak(text) {
    this.cognitiveState.lastResponse = text;
    this.cognitiveState.conversationContext.push({role: 'assistant', content: text});
    return this.speech.speak(text);
  }
  startListening() { return this.speech.startListening(); }
  stopListening() { return this.speech.stopListening(); }
  listen() { return this.startListening(); }
  observe() {
    this.cognitiveState.isObserving = true;
    return this.sensory.observe();
  }
  getState() { return this.cognitiveState; }
  getCognitiveState() { return this.cognitiveState; }
}
module.exports = CognitiveAgent;