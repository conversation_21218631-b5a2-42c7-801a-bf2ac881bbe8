
const EventEmitter = require('events');
class SensorySystem extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = options;
    console.log('SensorySystem initialisé (mode de simulation)');
  }
  captureImage() {
    console.log('Simulation de capture d\'image');
    setTimeout(() => this.emit('analysisResult', {scene: 'bureau', objects: []}), 1000);
    return true;
  }
  observe() { return this.captureImage(); }
  describeEnvironment() { return "Simulation d'environnement de bureau"; }
}
module.exports = SensorySystem;