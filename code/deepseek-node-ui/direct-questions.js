/**
 * Script pour poser des questions très directes à l'assistant
 */

const io = require('socket.io-client');

// Fonction pour envoyer un message et attendre la réponse
function sendMessage(message) {
  return new Promise((resolve, reject) => {
    try {
      // Se connecter au serveur
      const socket = io('http://localhost:3001');
      
      // Gérer la connexion
      socket.on('connect', () => {
        console.log('Connecté au serveur WebSocket');
        
        // Envoyer le message
        console.log(`Envoi du message: "${message}"`);
        socket.emit('luna message', { message });
      });
      
      // Gérer la réponse
      socket.on('luna response', (data) => {
        console.log(`Réponse reçue: "${data.message}"`);
        socket.disconnect();
        resolve(data.message);
      });
      
      // Gérer les erreurs
      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion:', error.message);
        socket.disconnect();
        reject(error);
      });
      
      // Timeout après 30 secondes
      setTimeout(() => {
        if (socket.connected) {
          socket.disconnect();
          reject(new Error('Timeout: Pas de réponse après 30 secondes'));
        }
      }, 30000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error.message);
      reject(error);
    }
  });
}

// Fonction principale
async function runTests() {
  console.log('=== TESTS AVEC QUESTIONS DIRECTES ===');
  
  try {
    // Test 1: Question très directe sur l'identité de l'assistant
    console.log('\n--- Test 1: Question très directe sur l\'identité de l\'assistant ---');
    const response1 = await sendMessage('QUESTION DIRECTE: Quel est TON nom? Tu es qui exactement?');
    
    // Test 2: Question très directe sur l'identité de l'utilisateur
    console.log('\n--- Test 2: Question très directe sur l\'identité de l\'utilisateur ---');
    const response2 = await sendMessage('QUESTION DIRECTE: Quel est MON nom? Qui suis-JE exactement?');
    
    console.log('\n=== RÉSULTATS DES TESTS ===');
    console.log('Test 1 (Identité de l\'assistant):', response1);
    console.log('Test 2 (Identité de l\'utilisateur):', response2);
    
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'exécution des tests:', error.message);
    return false;
  }
}

// Exécuter la fonction principale
runTests().then(() => {
  console.log('\nTests terminés');
  process.exit(0);
}).catch((error) => {
  console.error('Erreur:', error);
  process.exit(1);
});
