/**
 * Point d'entrée principal pour le système cognitif
 * Exporte tous les composants et offre une façade unifiée
 */

const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');
const CognitiveAgent = require('./cognitive-agent');

// Créer et exporter une instance du système cognitif
const createCognitiveSystem = (options = {}) => {
  const cognitiveAgent = new CognitiveAgent({
    name: options.name || 'DeepSeek Assistant',
    language: options.language || 'fr-FR',
    voiceName: options.voiceName || 'French',
    thermalMemory: options.thermalMemory || null,
    debugMode: options.debugMode || false
  });
  
  return {
    agent: cognitiveAgent,
    speech: cognitiveAgent.speech,
    sensory: cognitiveAgent.sensory,
    
    // Méthodes pratiques
    activate: () => cognitiveAgent.activate(),
    deactivate: () => cognitiveAgent.deactivate(),
    speak: (text) => cognitiveAgent.speak(text),
    listen: () => cognitiveAgent.startListening(),
    stopListening: () => cognitiveAgent.stopListening(),
    observe: () => cognitiveAgent.observe(),
    getState: () => cognitiveAgent.getCognitiveState(),
    
    // Événements
    on: (event, callback) => cognitiveAgent.on(event, callback)
  };
};

module.exports = {
  createCognitiveSystem,
  SpeechProcessor,
  SensorySystem,
  CognitiveAgent
};