/**
 * Interface JavaScript pour le Master Control Program (MCP)
 * Gestion de l'accès Internet, bureau et commandes système
 */

class MCPInterface {
    constructor() {
        this.mcpBaseUrl = 'http://localhost:3002';
        this.desktopFiles = [];
        this.terminalHistory = [];
        this.terminalHistoryIndex = -1;

        this.init();
    }

    async init() {
        console.log('🚀 Initialisation de l\'interface MCP...');

        // Vérifier le statut du système
        await this.checkSystemStatus();

        // Charger le bureau
        await this.loadDesktop();

        // Configurer les gestionnaires d'événements
        this.setupEventHandlers();

        // Actualiser le statut périodiquement
        setInterval(() => this.checkSystemStatus(), 30000);

        console.log('✅ Interface MCP initialisée');
        this.showNotification('Interface MCP prête', 'success');
    }

    setupEventHandlers() {
        // Gestionnaire pour la recherche Internet
        document.getElementById('searchQuery').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchInternet();
            }
        });

        // Gestionnaire pour la récupération d'URL
        document.getElementById('urlFetch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.fetchUrl();
            }
        });

        // Gestionnaire pour le terminal
        const terminalInput = document.getElementById('terminalInput');
        terminalInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand(terminalInput.value);
                terminalInput.value = '';
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateTerminalHistory(-1);
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateTerminalHistory(1);
            }
        });
    }

    async checkSystemStatus() {
        try {
            // Vérifier le statut MCP
            const mcpResponse = await fetch(`${this.mcpBaseUrl}/mcp/status`);
            if (mcpResponse.ok) {
                const mcpData = await mcpResponse.json();
                this.updateStatus('mcp', true, 'Actif');
                this.updateStatus('internet', mcpData.capabilities.internet,
                    mcpData.capabilities.internet ? 'Disponible' : 'Indisponible');
            } else {
                this.updateStatus('mcp', false, 'Inactif');
                this.updateStatus('internet', false, 'Indisponible');
            }

            // Vérifier l'accès au bureau
            const desktopResponse = await fetch(`${this.mcpBaseUrl}/mcp/desktop/check`);
            if (desktopResponse.ok) {
                const desktopData = await desktopResponse.json();
                this.updateStatus('desktop', desktopData.success && desktopData.accessible,
                    desktopData.success && desktopData.accessible ? 'Accessible' : 'Inaccessible');
            } else {
                this.updateStatus('desktop', false, 'Erreur');
            }

        } catch (error) {
            console.error('❌ Erreur lors de la vérification du statut:', error);
            this.updateStatus('mcp', false, 'Erreur de connexion');
            this.updateStatus('internet', false, 'Erreur');
            this.updateStatus('desktop', false, 'Erreur');
        }
    }

    updateStatus(component, isActive, text) {
        const statusIndicator = document.getElementById(`${component}Status`);
        const statusText = document.getElementById(`${component}StatusText`);

        if (statusIndicator && statusText) {
            statusIndicator.className = `status-indicator ${isActive ? 'active' : 'inactive'}`;
            statusText.textContent = text;
        }
    }

    async searchInternet() {
        const query = document.getElementById('searchQuery').value.trim();
        if (!query) {
            this.showNotification('Veuillez entrer une requête de recherche', 'error');
            return;
        }

        this.showLoading('internet');

        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/internet/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query, limit: 5 })
            });

            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data.results, query);
                this.showNotification(`Recherche effectuée pour: ${query}`, 'success');
            } else {
                throw new Error(data.error || 'Erreur de recherche');
            }

        } catch (error) {
            console.error('❌ Erreur lors de la recherche:', error);
            this.showNotification('Erreur lors de la recherche: ' + error.message, 'error');
            this.displaySearchResults(null, query, error.message);
        } finally {
            this.hideLoading('internet');
        }
    }

    async fetchUrl() {
        const url = document.getElementById('urlFetch').value.trim();
        if (!url) {
            this.showNotification('Veuillez entrer une URL', 'error');
            return;
        }

        this.showLoading('internet');

        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/internet/fetch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ url })
            });

            const data = await response.json();

            if (data.success) {
                this.displayUrlContent(data, url);
                this.showNotification(`Contenu récupéré de: ${url}`, 'success');
            } else {
                throw new Error(data.error || 'Erreur de récupération');
            }

        } catch (error) {
            console.error('❌ Erreur lors de la récupération:', error);
            this.showNotification('Erreur lors de la récupération: ' + error.message, 'error');
            this.displayUrlContent(null, url, error.message);
        } finally {
            this.hideLoading('internet');
        }
    }

    displaySearchResults(results, query, error = null) {
        const resultsDiv = document.getElementById('internetResults');
        resultsDiv.style.display = 'block';

        if (error) {
            resultsDiv.innerHTML = `
                <div class="text-danger">
                    <h5><i class="bi bi-exclamation-triangle"></i> Erreur de recherche</h5>
                    <p>Impossible de rechercher "${query}": ${error}</p>
                </div>
            `;
            return;
        }

        if (!results || Object.keys(results).length === 0) {
            resultsDiv.innerHTML = `
                <div class="text-warning">
                    <h5><i class="bi bi-search"></i> Aucun résultat</h5>
                    <p>Aucun résultat trouvé pour "${query}"</p>
                </div>
            `;
            return;
        }

        let html = `<h5><i class="bi bi-search"></i> Résultats pour "${query}"</h5>`;

        // Afficher les résultats selon le format retourné
        if (results.RelatedTopics && Array.isArray(results.RelatedTopics)) {
            results.RelatedTopics.slice(0, 5).forEach((topic, index) => {
                if (topic.Text && topic.FirstURL) {
                    html += `
                        <div class="border-bottom pb-2 mb-2">
                            <h6>${index + 1}. ${topic.Text.split(' - ')[0]}</h6>
                            <p class="small">${topic.Text}</p>
                            <a href="${topic.FirstURL}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-link-45deg"></i> Voir plus
                            </a>
                        </div>
                    `;
                }
            });
        } else {
            html += `
                <div class="text-info">
                    <p>Recherche effectuée avec succès. Données brutes disponibles dans la console.</p>
                    <pre class="small">${JSON.stringify(results, null, 2).substring(0, 500)}...</pre>
                </div>
            `;
        }

        resultsDiv.innerHTML = html;
    }

    displayUrlContent(data, url, error = null) {
        const resultsDiv = document.getElementById('internetResults');
        resultsDiv.style.display = 'block';

        if (error) {
            resultsDiv.innerHTML = `
                <div class="text-danger">
                    <h5><i class="bi bi-exclamation-triangle"></i> Erreur de récupération</h5>
                    <p>Impossible de récupérer "${url}": ${error}</p>
                </div>
            `;
            return;
        }

        const contentPreview = typeof data.data === 'string'
            ? data.data.substring(0, 1000)
            : JSON.stringify(data.data).substring(0, 1000);

        resultsDiv.innerHTML = `
            <h5><i class="bi bi-download"></i> Contenu récupéré</h5>
            <p><strong>URL:</strong> ${url}</p>
            <p><strong>Statut:</strong> ${data.status}</p>
            <p><strong>Type de contenu:</strong> ${data.headers['content-type'] || 'Non spécifié'}</p>
            <div class="mt-3">
                <h6>Aperçu du contenu:</h6>
                <pre class="small bg-dark p-2 rounded">${contentPreview}${contentPreview.length >= 1000 ? '...' : ''}</pre>
            </div>
        `;
    }

    async loadDesktop() {
        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/desktop/files`);
            const data = await response.json();

            if (data.success) {
                this.desktopFiles = data.files;
                this.displayDesktopFiles();
                this.addTerminalOutput(`Bureau chargé: ${data.files.length} éléments trouvés`);
            } else {
                throw new Error(data.error || 'Erreur de chargement du bureau');
            }

        } catch (error) {
            console.error('❌ Erreur lors du chargement du bureau:', error);
            this.showNotification('Erreur lors du chargement du bureau: ' + error.message, 'error');
            document.getElementById('desktopBrowser').innerHTML = `
                <div class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i> Erreur: ${error.message}
                </div>
            `;
        }
    }

    displayDesktopFiles() {
        const browser = document.getElementById('desktopBrowser');

        if (this.desktopFiles.length === 0) {
            browser.innerHTML = '<p><i class="bi bi-folder"></i> Le bureau est vide</p>';
            return;
        }

        let html = '';
        this.desktopFiles.forEach(file => {
            const icon = file.isDirectory ? 'bi-folder-fill' : 'bi-file-earmark';
            const size = file.isDirectory ? 'Dossier' : this.formatFileSize(file.size);
            const date = new Date(file.modified).toLocaleDateString('fr-FR');

            html += `
                <div class="file-item" onclick="selectFile('${file.name}', ${file.isDirectory})">
                    <i class="bi ${icon} file-icon"></i>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-details">${size} • Modifié le ${date}</div>
                    </div>
                </div>
            `;
        });

        browser.innerHTML = html;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async executeCommand(command) {
        if (!command.trim()) return;

        this.terminalHistory.push(command);
        this.terminalHistoryIndex = this.terminalHistory.length;

        this.addTerminalOutput(`$ ${command}`);

        // Commandes locales
        switch (command.toLowerCase().trim()) {
            case 'help':
                this.addTerminalOutput(`Commandes disponibles:
  help          - Affiche cette aide
  status        - Statut du système MCP
  ls            - Liste les fichiers du bureau
  pwd           - Répertoire de travail actuel
  date          - Date et heure actuelles
  clear         - Efface le terminal
  desktop-info  - Informations sur le bureau`);
                return;

            case 'clear':
                document.getElementById('terminalOutput').innerHTML = '';
                return;

            case 'status':
                this.addTerminalOutput('Statut du système MCP:');
                this.addTerminalOutput('- MCP Server: En ligne');
                this.addTerminalOutput('- Accès Internet: Activé');
                this.addTerminalOutput('- Accès Bureau: Activé');
                this.addTerminalOutput('- Commandes Système: Activées');
                return;

            case 'ls':
                this.addTerminalOutput(`Contenu du bureau (${this.desktopFiles.length} éléments):`);
                this.desktopFiles.forEach(file => {
                    const type = file.isDirectory ? 'DIR' : 'FILE';
                    this.addTerminalOutput(`  ${type.padEnd(4)} ${file.name}`);
                });
                return;

            case 'pwd':
                this.addTerminalOutput('/Users/' + (navigator.userAgent.includes('Mac') ? 'user' : 'user') + '/Desktop');
                return;

            case 'date':
                this.addTerminalOutput(new Date().toString());
                return;

            case 'desktop-info':
                this.addTerminalOutput(`Informations sur le bureau:
  Nombre de fichiers: ${this.desktopFiles.filter(f => !f.isDirectory).length}
  Nombre de dossiers: ${this.desktopFiles.filter(f => f.isDirectory).length}
  Total: ${this.desktopFiles.length} éléments`);
                return;
        }

        // Commandes système via MCP
        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/system/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ command })
            });

            const data = await response.json();

            if (data.success) {
                if (data.stdout) {
                    this.addTerminalOutput(data.stdout);
                }
                if (data.stderr) {
                    this.addTerminalOutput(`STDERR: ${data.stderr}`, 'error');
                }
            } else {
                this.addTerminalOutput(`Erreur: ${data.error}`, 'error');
            }

        } catch (error) {
            this.addTerminalOutput(`Erreur de connexion: ${error.message}`, 'error');
        }
    }

    addTerminalOutput(text, type = 'normal') {
        const output = document.getElementById('terminalOutput');
        const div = document.createElement('div');

        if (type === 'error') {
            div.style.color = '#ff6b6b';
        } else if (type === 'success') {
            div.style.color = '#51cf66';
        }

        div.textContent = text;
        output.appendChild(div);

        // Faire défiler vers le bas
        const terminal = document.getElementById('terminal');
        terminal.scrollTop = terminal.scrollHeight;
    }

    navigateTerminalHistory(direction) {
        if (this.terminalHistory.length === 0) return;

        this.terminalHistoryIndex += direction;

        if (this.terminalHistoryIndex < 0) {
            this.terminalHistoryIndex = 0;
        } else if (this.terminalHistoryIndex >= this.terminalHistory.length) {
            this.terminalHistoryIndex = this.terminalHistory.length;
            document.getElementById('terminalInput').value = '';
            return;
        }

        document.getElementById('terminalInput').value = this.terminalHistory[this.terminalHistoryIndex];
    }

    showLoading(section) {
        const loading = document.getElementById(`${section}Loading`);
        if (loading) {
            loading.style.display = 'block';
        }
    }

    hideLoading(section) {
        const loading = document.getElementById(`${section}Loading`);
        if (loading) {
            loading.style.display = 'none';
        }
    }

    async createFolder(name) {
        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/desktop/createFolder`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ folderName: name })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(`Dossier "${name}" créé avec succès`, 'success');
                this.addTerminalOutput(`Dossier créé: ${name}`);
                await this.loadDesktop(); // Actualiser la liste
            } else {
                throw new Error(data.error || 'Erreur de création');
            }

        } catch (error) {
            console.error('❌ Erreur lors de la création du dossier:', error);
            this.showNotification('Erreur lors de la création: ' + error.message, 'error');
            this.addTerminalOutput(`Erreur: ${error.message}`, 'error');
        }
    }

    async createFile(name) {
        try {
            const response = await fetch(`${this.mcpBaseUrl}/mcp/desktop/createFile`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    fileName: name,
                    content: `# ${name}\n\nFichier créé le ${new Date().toLocaleString('fr-FR')}\n`
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(`Fichier "${name}" créé avec succès`, 'success');
                this.addTerminalOutput(`Fichier créé: ${name}`);
                await this.loadDesktop(); // Actualiser la liste
            } else {
                throw new Error(data.error || 'Erreur de création');
            }

        } catch (error) {
            console.error('❌ Erreur lors de la création du fichier:', error);
            this.showNotification('Erreur lors de la création: ' + error.message, 'error');
            this.addTerminalOutput(`Erreur: ${error.message}`, 'error');
        }
    }

    async openDesktopInSystem() {
        try {
            // Commande pour ouvrir le bureau selon l'OS
            const isMac = navigator.userAgent.includes('Mac');
            const command = isMac ? 'open ~/Desktop' : 'explorer %USERPROFILE%\\Desktop';

            const response = await fetch(`${this.mcpBaseUrl}/mcp/system/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ command })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Bureau ouvert dans le système', 'success');
                this.addTerminalOutput('Bureau ouvert dans le Finder/Explorateur');
            } else {
                throw new Error(data.error || 'Erreur d\'ouverture');
            }

        } catch (error) {
            console.error('❌ Erreur lors de l\'ouverture du bureau:', error);
            this.showNotification('Erreur lors de l\'ouverture: ' + error.message, 'error');
            this.addTerminalOutput(`Erreur: ${error.message}`, 'error');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification show ${type}`;

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// Fonctions globales pour les gestionnaires d'événements
function refreshDesktop() {
    mcpInterface.loadDesktop();
}

function selectFile(fileName, isDirectory) {
    mcpInterface.addTerminalOutput(`Sélectionné: ${fileName} (${isDirectory ? 'Dossier' : 'Fichier'})`);
}

function createFolder() {
    const name = prompt('Nom du nouveau dossier:');
    if (name) {
        mcpInterface.createFolder(name);
    }
}

function createFile() {
    const name = prompt('Nom du nouveau fichier:');
    if (name) {
        mcpInterface.createFile(name);
    }
}

function openDesktopInFinder() {
    mcpInterface.openDesktopInSystem();
}

function showDesktopInfo() {
    mcpInterface.executeCommand('desktop-info');
}

function searchInternet() {
    mcpInterface.searchInternet();
}

function fetchUrl() {
    mcpInterface.fetchUrl();
}

// Initialiser l'interface au chargement de la page
let mcpInterface;
document.addEventListener('DOMContentLoaded', () => {
    mcpInterface = new MCPInterface();
});
