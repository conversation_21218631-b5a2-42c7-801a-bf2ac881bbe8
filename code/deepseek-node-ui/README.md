# DeepSeek r1 - Interfaces Cognitives

## Interfaces principales

Ce projet contient plusieurs interfaces pour interagir avec le modèle DeepSeek r1 via Ollama. Les interfaces principales sont :

### Interface Luna
Interface cognitive avancée avec mémoire thermique et MCP, offrant une expérience utilisateur moderne et complète.
- **Démarrer** : `./start-luna.sh`
- **URL** : http://localhost:3001/luna
- **Fonctionnalités** :
  - Mémoire thermique avec 6 zones
  - Accélérateurs Kyber (24 actifs)
  - Système MCP pour l'accès Internet, bureau et commandes
  - Upload de fichiers directement dans la mémoire
  - Interface utilisateur moderne et intuitive

### Interface Cognitive
Interface cognitive standard avec mémoire thermique et traitement sensoriel.
- **Démarrer** : `./start-cognitif.sh`
- **URL** : http://localhost:3001/cognitive
- **Fonctionnalités** :
  - Système de reconnaissance vocale
  - Système de vision
  - Mémoire thermique

### Interface DeepSeek r1 Standard
Interface standard pour DeepSeek r1 avec Ollama.
- **Démarrer** : `./start-ollama.sh`
- **URL** : http://localhost:3000
- **Fonctionnalités** :
  - Intégration directe avec Ollama
  - Interface simple et efficace

### Interface Debug
Interface avec journalisation détaillée pour le débogage.
- **Démarrer** : `./start-debug.sh`
- **URL** : http://localhost:3000
- **Fonctionnalités** :
  - Journal détaillé des opérations
  - Affichage des erreurs et des communications avec Ollama

## Composants du système

### Mémoire Thermique
Système de mémoire avancé avec zones thermiques et accélérateurs Kyber.
- **Zones** : Instantanée (100°), Chaude (80°), Tiède (60°), Fraîche (40°), Froide (20°), Archive (5°)
- **Accélérateurs Kyber** : Augmentent les performances et l'efficacité des opérations de mémoire

### Système MCP (Model Context Protocol)
Permet l'accès aux ressources externes comme Internet, le système de fichiers et les ressources matérielles.
- **Port** : 3002
- **Capacités** : Internet, Bureau, Commandes Système

## Structure du projet
- `server-luna.js` : Serveur pour l'interface Luna
- `server-cognitif.js` : Serveur pour l'interface cognitive
- `server-ollama.js` : Serveur pour l'intégration standard avec Ollama
- `debug-server.js` : Serveur avec journalisation détaillée
- `thermal-memory/` : Modules pour la mémoire thermique
- `mcp/` : Modules pour le système MCP
- `views/` : Templates d'interface utilisateur
- `data/memory/` : Stockage des données de mémoire
- `interfaces-archived/` : Interfaces et scripts obsolètes ou moins utilisés

## Notes importantes
- La mémoire thermique est configurée pour optimiser les performances et la rétention des connaissances
- Le système MCP est configuré pour permettre à l'agent d'accéder à Internet et aux ressources locales
- L'interface Luna représente l'évolution la plus avancée avec toutes les fonctionnalités intégrées
