/**
 * Script pour tester explicitement l'assistant
 */

const io = require('socket.io-client');
const socket = io('http://localhost:3001');

socket.on('connect', () => {
  console.log('Connecté au serveur');
  
  // Envoyer un message très explicite
  const message = 'QUESTION DIRECTE: Je veux que tu me dises comment je m\'appelle, où j\'habite et quelle est mon origine. Utilise ta mémoire thermique pour répondre.';
  console.log(`Envoi du message: "${message}"`);
  socket.emit('luna message', { message });
});

socket.on('luna response', (data) => {
  console.log('Réponse:');
  console.log(data.message);
  socket.disconnect();
  process.exit(0);
});

socket.on('connect_error', (error) => {
  console.error('Erreur de connexion:', error);
  socket.disconnect();
  process.exit(1);
});

// Timeout après 30 secondes
setTimeout(() => {
  console.log('Timeout: Pas de réponse après 30 secondes');
  socket.disconnect();
  process.exit(1);
}, 30000);
