<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Chat avec Vision Ultra</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/louna-thermal.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@2.4.0/dist/purify.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
</head>
<body class="bg-dark text-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-moon-stars"></i> Louna
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Accueil</a>
                <a class="nav-link active" href="/chat">Chat</a>
                <a class="nav-link" href="/futuristic-interface.html">Interface Thermique</a>
            </div>
        </div>
    </nav>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h2><i class="bi bi-chat-dots"></i> Chat avec Louna</h2>
                        <span class="badge bg-success ms-3">Vision Ultra - QI 120</span>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- Section Caméra -->
                        <div class="camera-section d-flex align-items-center gap-2 p-2 rounded" style="background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.1);">
                            <div class="camera-preview" style="width: 80px; height: 45px; background: #000; border-radius: 4px; border: 1px solid rgba(255,255,255,0.2); overflow: hidden;">
                                <video id="cameraVideo" style="width: 100%; height: 100%; object-fit: cover;" autoplay muted></video>
                            </div>
                            <div class="camera-controls d-flex gap-1">
                                <button class="btn btn-sm btn-outline-success" id="cameraBtn" title="Activer/Désactiver la caméra">
                                    <i class="bi bi-camera-video"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" id="micBtn" title="Activer/Désactiver le micro">
                                    <i class="bi bi-mic"></i>
                                </button>
                            </div>
                        </div>

                        <div>
                            <button class="btn btn-outline-light" id="clearChatBtn">
                                <i class="bi bi-trash"></i> Effacer
                            </button>
                            <button class="btn btn-outline-light ms-2" id="exportChatBtn">
                                <i class="bi bi-download"></i> Exporter
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chat-container" id="chatContainer">
                                <div class="welcome-message">
                                    <h3>Bienvenue sur Louna</h3>
                                    <p>Je suis Vision Ultra, votre assistant cognitif avec mémoire thermique et accélérateurs Kyber. Comment puis-je vous aider aujourd'hui ?</p>
                                </div>
                                <div id="messagesContainer"></div>
                            </div>
                            <div class="input-group mt-3">
                                <textarea class="form-control" id="userInput" placeholder="Entrez votre message..." rows="2"></textarea>
                                <button class="btn btn-primary" type="button" id="sendButton">
                                    <i class="bi bi-send"></i> Envoyer
                                </button>
                            </div>
                        </div>

                        <!-- Panneau de Réflexions -->
                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5><i class="bi bi-brain"></i> Réflexions en Direct</h5>
                                </div>
                                <div class="card-body p-2">
                                    <div id="thoughtsContainer" style="height: 300px; overflow-y: auto;">
                                        <!-- Les réflexions apparaîtront ici -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Mémoire Thermique</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6>Température Globale</h6>
                                        <div class="progress" style="height: 20px;">
                                            <div id="memoryTempProgress" class="progress-bar bg-danger" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <h6>Mémoires Actives</h6>
                                        <p id="activeMemories">1,250 mémoires</p>
                                    </div>
                                    <a href="/luna/thermal" class="btn btn-outline-light btn-sm w-100">
                                        <i class="bi bi-thermometer-half"></i> Détails
                                    </a>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Accélérateurs Kyber</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6>Efficacité</h6>
                                        <div class="progress" style="height: 20px;">
                                            <div id="kyberEfficiencyProgress" class="progress-bar bg-info" role="progressbar" style="width: 87%;" aria-valuenow="87" aria-valuemin="0" aria-valuemax="100">87%</div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <h6>Accélérateurs Actifs</h6>
                                        <p id="activeAccelerators">128 accélérateurs</p>
                                    </div>
                                    <a href="/luna/thermal-accelerators" class="btn btn-outline-light btn-sm w-100">
                                        <i class="bi bi-lightning-charge"></i> Détails
                                    </a>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5>Système MCP</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6>État</h6>
                                        <span class="badge bg-success" id="mcpStatus">Actif</span>
                                    </div>
                                    <div class="mb-3">
                                        <h6>Mode</h6>
                                        <p id="mcpMode">Normal</p>
                                    </div>
                                    <a href="/luna/mcp" class="btn btn-outline-light btn-sm w-100">
                                        <i class="bi bi-cpu"></i> Détails
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .chat-container {
        height: 500px;
        overflow-y: auto;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
    }

    .welcome-message {
        background-color: rgba(74, 0, 224, 0.1);
        border-left: 4px solid var(--luna-accent);
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 10px;
        max-width: 80%;
    }

    .user-message {
        background-color: rgba(74, 0, 224, 0.2);
        margin-left: auto;
        border-top-right-radius: 0;
    }

    .assistant-message {
        background-color: rgba(0, 0, 0, 0.3);
        margin-right: auto;
        border-top-left-radius: 0;
    }

    .message-content {
        white-space: pre-wrap;
    }

    .message-time {
        font-size: 0.8rem;
        color: var(--luna-text-secondary);
        text-align: right;
        margin-top: 5px;
    }

    .typing-indicator {
        display: inline-block;
        width: 50px;
        height: 20px;
        position: relative;
    }

    .typing-indicator span {
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: var(--luna-text-secondary);
        border-radius: 50%;
        margin: 0 2px;
        animation: typing 1.5s infinite ease-in-out;
    }

    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes typing {
        0% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0); }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatContainer = document.getElementById('chatContainer');
        const messagesContainer = document.getElementById('messagesContainer');
        const userInput = document.getElementById('userInput');
        const sendButton = document.getElementById('sendButton');
        const clearChatBtn = document.getElementById('clearChatBtn');
        const exportChatBtn = document.getElementById('exportChatBtn');

        let conversationId = Date.now().toString();
        let isWaitingForResponse = false;
        let cameraStream = null;
        let isCameraActive = false;
        let isMicActive = false;

        // Fonction pour ajouter un message au chat
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            if (isUser) {
                contentDiv.textContent = content;
            } else {
                // Utiliser marked pour rendre le markdown
                contentDiv.innerHTML = DOMPurify.sanitize(marked.parse(content));

                // Appliquer highlight.js aux blocs de code
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                });
            }

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();

            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);

            // Faire défiler vers le bas
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Fonction pour ajouter un indicateur de frappe
        function addTypingIndicator() {
            const indicatorDiv = document.createElement('div');
            indicatorDiv.className = 'message assistant-message typing-indicator-container';
            indicatorDiv.id = 'typingIndicator';

            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.innerHTML = '<span></span><span></span><span></span>';

            indicatorDiv.appendChild(indicator);
            messagesContainer.appendChild(indicatorDiv);

            // Faire défiler vers le bas
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Fonction pour supprimer l'indicateur de frappe
        function removeTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // Fonction pour envoyer un message
        function sendMessage() {
            const message = userInput.value.trim();
            if (message === '' || isWaitingForResponse) return;

            // Ajouter le message de l'utilisateur
            addMessage(message, true);

            // Effacer l'entrée
            userInput.value = '';

            // Ajouter l'indicateur de frappe
            addTypingIndicator();

            // Marquer comme en attente de réponse
            isWaitingForResponse = true;

            // Envoyer le message au serveur
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    conversationId: conversationId
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de l\'envoi du message');
                }
                return response.json();
            })
            .then(data => {
                // Supprimer l'indicateur de frappe
                removeTypingIndicator();

                // Ajouter la réponse de l'assistant
                addMessage(data.response);

                // Marquer comme plus en attente de réponse
                isWaitingForResponse = false;

                // Mettre à jour les données de la mémoire thermique
                updateThermalMemoryData();

                // Mettre à jour les données des accélérateurs Kyber
                updateKyberAcceleratorsData();

                // Mettre à jour les données du système MCP
                updateMCPData();
            })
            .catch(error => {
                console.error('Erreur:', error);

                // Supprimer l'indicateur de frappe
                removeTypingIndicator();

                // Ajouter un message d'erreur
                addMessage('Désolé, une erreur est survenue lors du traitement de votre message. Veuillez réessayer.');

                // Marquer comme plus en attente de réponse
                isWaitingForResponse = false;
            });
        }

        // Fonction pour mettre à jour les données de la mémoire thermique
        function updateThermalMemoryData() {
            fetch('/api/thermal-memory/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur lors de la récupération des données de la mémoire thermique');
                    }
                    return response.json();
                })
                .then(data => {
                    // Mettre à jour les données de la mémoire thermique
                    document.getElementById('memoryTempProgress').style.width = `${data.temperature * 100}%`;
                    document.getElementById('memoryTempProgress').textContent = `${Math.round(data.temperature * 100)}%`;
                    document.getElementById('activeMemories').textContent = `${data.totalMemories.toLocaleString()} mémoires`;
                })
                .catch(error => {
                    console.error('Erreur:', error);
                });
        }

        // Fonction pour mettre à jour les données des accélérateurs Kyber
        function updateKyberAcceleratorsData() {
            fetch('/api/kyber-accelerators/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur lors de la récupération des données des accélérateurs Kyber');
                    }
                    return response.json();
                })
                .then(data => {
                    // Mettre à jour les données des accélérateurs Kyber
                    document.getElementById('kyberEfficiencyProgress').style.width = `${data.efficiency * 100}%`;
                    document.getElementById('kyberEfficiencyProgress').textContent = `${Math.round(data.efficiency * 100)}%`;
                    document.getElementById('activeAccelerators').textContent = `${data.totalCount.toLocaleString()} accélérateurs`;
                })
                .catch(error => {
                    console.error('Erreur:', error);
                });
        }

        // Fonction pour mettre à jour les données du système MCP
        function updateMCPData() {
            fetch('/api/mcp/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur lors de la récupération des données du système MCP');
                    }
                    return response.json();
                })
                .then(data => {
                    // Mettre à jour les données du système MCP
                    document.getElementById('mcpStatus').textContent = data.state === 'active' ? 'Actif' : 'Inactif';
                    document.getElementById('mcpStatus').className = data.state === 'active' ? 'badge bg-success' : 'badge bg-danger';
                    document.getElementById('mcpMode').textContent = data.mode.charAt(0).toUpperCase() + data.mode.slice(1);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                });
        }

        // Gestionnaire d'événements pour le bouton d'envoi
        sendButton.addEventListener('click', sendMessage);

        // Gestionnaire d'événements pour la touche Entrée
        userInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // Gestionnaire d'événements pour le bouton d'effacement
        clearChatBtn.addEventListener('click', function() {
            if (confirm('Êtes-vous sûr de vouloir effacer cette conversation ?')) {
                messagesContainer.innerHTML = '';
                conversationId = Date.now().toString();
            }
        });

        // Gestionnaire d'événements pour le bouton d'exportation
        exportChatBtn.addEventListener('click', function() {
            const messages = Array.from(messagesContainer.querySelectorAll('.message'));
            let exportText = 'Conversation Vision Ultra\n';
            exportText += '========================\n\n';

            messages.forEach(message => {
                const isUser = message.classList.contains('user-message');
                const content = message.querySelector('.message-content').textContent;
                const time = message.querySelector('.message-time').textContent;

                exportText += `[${time}] ${isUser ? 'Vous' : 'Vision Ultra'}: ${content}\n\n`;
            });

            const blob = new Blob([exportText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `vision-ultra-conversation-${new Date().toISOString().slice(0, 10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        });

        // ===== FONCTIONS CAMÉRA =====
        async function initCamera() {
            try {
                const video = document.getElementById('cameraVideo');
                cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720 },
                    audio: true
                });
                video.srcObject = cameraStream;
                isCameraActive = true;
                isMicActive = true;
                updateCameraButtons();
                console.log('Caméra initialisée avec succès');
            } catch (error) {
                console.error('Erreur lors de l\'initialisation de la caméra:', error);
            }
        }

        function toggleCamera() {
            if (isCameraActive) {
                // Désactiver la caméra
                if (cameraStream) {
                    const videoTracks = cameraStream.getVideoTracks();
                    videoTracks.forEach(track => track.stop());
                }
                document.getElementById('cameraVideo').srcObject = null;
                isCameraActive = false;
            } else {
                // Réactiver la caméra
                initCamera();
            }
            updateCameraButtons();
        }

        function toggleMic() {
            if (cameraStream) {
                const audioTracks = cameraStream.getAudioTracks();
                audioTracks.forEach(track => {
                    track.enabled = !track.enabled;
                });
                isMicActive = !isMicActive;
                updateCameraButtons();
            }
        }

        function updateCameraButtons() {
            const cameraBtn = document.getElementById('cameraBtn');
            const micBtn = document.getElementById('micBtn');

            if (isCameraActive) {
                cameraBtn.className = 'btn btn-sm btn-success';
                cameraBtn.innerHTML = '<i class="bi bi-camera-video-fill"></i>';
            } else {
                cameraBtn.className = 'btn btn-sm btn-outline-success';
                cameraBtn.innerHTML = '<i class="bi bi-camera-video-off"></i>';
            }

            if (isMicActive) {
                micBtn.className = 'btn btn-sm btn-primary';
                micBtn.innerHTML = '<i class="bi bi-mic-fill"></i>';
            } else {
                micBtn.className = 'btn btn-sm btn-outline-primary';
                micBtn.innerHTML = '<i class="bi bi-mic-mute"></i>';
            }
        }

        // ===== SYSTÈME DE RÉFLEXIONS =====
        const thoughtTypes = ['ANALYSE', 'VISION', 'MÉMOIRE', 'SYSTÈME', 'CRÉATIVITÉ', 'LOGIQUE'];
        let thoughtCounter = 0;

        function addThought(type, content) {
            const thoughtsContainer = document.getElementById('thoughtsContainer');
            const thoughtDiv = document.createElement('div');
            thoughtDiv.className = 'thought-item mb-2 p-2 rounded';
            thoughtDiv.style.cssText = 'background: rgba(74, 0, 224, 0.1); border-left: 3px solid var(--luna-accent); font-size: 0.85rem; animation: fadeIn 0.5s ease-in;';

            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'});
            thoughtDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="badge bg-primary" style="font-size: 0.7rem;">${type}</span>
                    <span style="font-size: 0.7rem; color: #888;">${time}</span>
                </div>
                <div style="color: #fff;">${content}</div>
            `;

            thoughtsContainer.appendChild(thoughtDiv);
            thoughtsContainer.scrollTop = thoughtsContainer.scrollHeight;

            // Limiter à 20 réflexions maximum
            while (thoughtsContainer.children.length > 20) {
                thoughtsContainer.removeChild(thoughtsContainer.firstChild);
            }
        }

        function generateRandomThought() {
            const thoughts = [
                'Analyse des patterns de conversation en cours...',
                'Optimisation de la mémoire thermique détectée',
                'Nouveau neurone créé dans la zone créative',
                'Accélérateur Kyber activé pour améliorer les performances',
                'Connexion avec la base de connaissances établie',
                'Traitement des émotions utilisateur en cours',
                'Mise à jour des algorithmes d\'apprentissage',
                'Synchronisation avec la mémoire à long terme',
                'Détection d\'un nouveau concept à mémoriser',
                'Optimisation des réponses basée sur l\'historique',
                'Analyse contextuelle approfondie en cours',
                'Activation du mode créatif pour cette conversation'
            ];

            const randomType = thoughtTypes[Math.floor(Math.random() * thoughtTypes.length)];
            const randomThought = thoughts[Math.floor(Math.random() * thoughts.length)];

            addThought(randomType, randomThought);
        }

        // Gestionnaires d'événements pour la caméra
        document.getElementById('cameraBtn').addEventListener('click', toggleCamera);
        document.getElementById('micBtn').addEventListener('click', toggleMic);

        // Démarrer les réflexions automatiques
        setInterval(generateRandomThought, 30000); // Toutes les 30 secondes

        // Initialiser la caméra au démarrage
        initCamera();

        // Charger les données initiales
        updateThermalMemoryData();
        updateKyberAcceleratorsData();
        updateMCPData();

        // Ajouter une réflexion initiale
        addThought('SYSTÈME', 'Interface Louna initialisée avec succès. Vision Ultra prêt à interagir.');

        // Force le rechargement des fonctionnalités - CACHE BUSTER
        console.log('🎬 Caméra et réflexions chargées - Version 4.0 - ' + new Date().toISOString());
    });
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
