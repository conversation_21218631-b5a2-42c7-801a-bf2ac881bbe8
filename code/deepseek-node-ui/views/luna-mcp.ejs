<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #c8a2c8;
            --secondary-color: #9d4edd;
            --accent-color: #7209b7;
            --background-dark: #1a1a1a;
            --surface-dark: #2d2d2d;
            --text-light: #ffffff;
            --text-muted: #b0b0b0;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            background: linear-gradient(135deg, var(--background-dark) 0%, var(--surface-dark) 100%);
            color: var(--text-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header h1 {
            margin: 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .sidebar {
            background: rgba(45, 45, 45, 0.95);
            backdrop-filter: blur(10px);
            border-right: 2px solid var(--primary-color);
            height: calc(100vh - 80px);
            position: fixed;
            width: 250px;
            overflow-y: auto;
            z-index: 999;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid rgba(200, 162, 200, 0.1);
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: var(--text-light);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .sidebar-menu a:hover {
            background: rgba(200, 162, 200, 0.1);
            border-left-color: var(--primary-color);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-left-color: var(--accent-color);
            font-weight: 600;
        }

        .sidebar-menu i {
            margin-right: 12px;
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 250px;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .mcp-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .status-card {
            background: rgba(200, 162, 200, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.active {
            background: var(--success-color);
            box-shadow: 0 0 10px var(--success-color);
        }

        .status-indicator.inactive {
            background: var(--danger-color);
            box-shadow: 0 0 10px var(--danger-color);
        }

        .control-section {
            background: rgba(45, 45, 45, 0.8);
            border: 1px solid var(--primary-color);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .control-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-mcp {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-mcp:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(200, 162, 200, 0.4);
            color: white;
        }

        .btn-mcp:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .desktop-browser {
            background: #000;
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin: 4px 0;
            background: rgba(255,255,255,0.05);
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .file-item:hover {
            background: rgba(200, 162, 200, 0.2);
        }

        .file-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-light);
        }

        .file-details {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .terminal {
            background: #000;
            color: #00ff00;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            min-height: 200px;
            border: 1px solid var(--primary-color);
            overflow-y: auto;
        }

        .terminal-input {
            background: transparent;
            border: none;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            width: 100%;
            outline: none;
        }

        .internet-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .search-box {
            background: var(--surface-dark);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            color: var(--text-light);
            padding: 10px 15px;
            width: 100%;
        }

        .search-results {
            background: rgba(0,0,0,0.5);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            border: 3px solid rgba(200, 162, 200, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 2000;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1><i class="bi bi-cpu"></i> Master Control Program (MCP)</h1>
                    <p class="mb-0">Contrôle total du système - Internet, Bureau et Commandes</p>
                </div>
            </div>
        </div>
    </div>

    <nav class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="/louna/home" class="<%= page === 'home' ? 'active' : '' %>"><i class="bi bi-house"></i> Accueil</a></li>
            <li><a href="/louna/chat" class="<%= page === 'chat' ? 'active' : '' %>"><i class="bi bi-chat"></i> Chat</a></li>
            <li><a href="/louna/memory" class="<%= page === 'memory' ? 'active' : '' %>"><i class="bi bi-brain"></i> Mémoire</a></li>
            <li><a href="/louna/training" class="<%= page === 'training' ? 'active' : '' %>"><i class="bi bi-mortarboard"></i> Formation</a></li>
            <li><a href="/louna/code" class="<%= page === 'code' ? 'active' : '' %>"><i class="bi bi-code-square"></i> Code</a></li>
            <li><a href="/louna/agents" class="<%= page === 'agents' ? 'active' : '' %>"><i class="bi bi-robot"></i> Agents</a></li>
            <li><a href="/louna/models" class="<%= page === 'models' ? 'active' : '' %>"><i class="bi bi-boxes"></i> Modèles</a></li>
            <li><a href="/louna/settings" class="<%= page === 'settings' ? 'active' : '' %>"><i class="bi bi-gear"></i> Paramètres</a></li>
            <li><a href="/louna/documents" class="<%= page === 'documents' ? 'active' : '' %>"><i class="bi bi-file-earmark-text"></i> Documents</a></li>
            <li><a href="/louna/prompts" class="<%= page === 'prompts' ? 'active' : '' %>"><i class="bi bi-chat-square-text"></i> Prompts</a></li>
            <li><a href="/louna/mcp" class="<%= page === 'mcp' ? 'active' : '' %>"><i class="bi bi-cpu"></i> MCP</a></li>
            <li><a href="/louna/internet" class="<%= page === 'internet' ? 'active' : '' %>"><i class="bi bi-globe"></i> Internet</a></li>
        </ul>
    </nav>

    <main class="main-content">
        <div class="mcp-container">
            <!-- État du système -->
            <div class="status-card">
                <h2><i class="bi bi-activity"></i> État du système MCP</h2>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator" id="mcpStatus"></span>
                            <strong>MCP: </strong><span id="mcpStatusText">Vérification...</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator" id="internetStatus"></span>
                            <strong>Internet: </strong><span id="internetStatusText">Vérification...</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator" id="desktopStatus"></span>
                            <strong>Bureau: </strong><span id="desktopStatusText">Vérification...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contrôles Internet -->
            <div class="control-section">
                <h3><i class="bi bi-globe"></i> Accès Internet</h3>
                <div class="internet-controls">
                    <div>
                        <label for="searchQuery" class="form-label">Recherche Web:</label>
                        <div class="input-group">
                            <input type="text" id="searchQuery" class="search-box" placeholder="Entrez votre recherche...">
                            <button class="btn-mcp" onclick="searchInternet()">
                                <i class="bi bi-search"></i> Rechercher
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="urlFetch" class="form-label">Récupérer URL:</label>
                        <div class="input-group">
                            <input type="text" id="urlFetch" class="search-box" placeholder="https://example.com">
                            <button class="btn-mcp" onclick="fetchUrl()">
                                <i class="bi bi-download"></i> Récupérer
                            </button>
                        </div>
                    </div>
                </div>
                <div id="internetResults" class="search-results mt-3" style="display: none;"></div>
                <div id="internetLoading" class="loading">
                    <div class="spinner"></div>
                    <p>Recherche en cours...</p>
                </div>
            </div>

            <!-- Explorateur de bureau -->
            <div class="control-section">
                <h3><i class="bi bi-folder"></i> Explorateur de Bureau</h3>
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex gap-2 mb-3">
                            <button class="btn-mcp" onclick="refreshDesktop()">
                                <i class="bi bi-arrow-clockwise"></i> Actualiser
                            </button>
                            <button class="btn-mcp" onclick="createFolder()">
                                <i class="bi bi-folder-plus"></i> Nouveau Dossier
                            </button>
                            <button class="btn-mcp" onclick="createFile()">
                                <i class="bi bi-file-plus"></i> Nouveau Fichier
                            </button>
                        </div>
                        <div id="desktopBrowser" class="desktop-browser">
                            <p><i class="bi bi-folder"></i> Chargement du bureau...</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h5>Actions rapides:</h5>
                        <div class="d-grid gap-2">
                            <button class="btn-mcp" onclick="openDesktopInFinder()">
                                <i class="bi bi-folder2-open"></i> Ouvrir dans Finder
                            </button>
                            <button class="btn-mcp" onclick="showDesktopInfo()">
                                <i class="bi bi-info-circle"></i> Informations Bureau
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terminal système -->
            <div class="control-section">
                <h3><i class="bi bi-terminal"></i> Terminal Système</h3>
                <div class="terminal" id="terminal">
                    <div>MCP Terminal v1.0 - Tapez 'help' pour l'aide</div>
                    <div id="terminalOutput"></div>
                    <div class="d-flex">
                        <span style="color: #00ff00;">$ </span>
                        <input type="text" class="terminal-input" id="terminalInput" placeholder="Entrez une commande...">
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        Commandes disponibles: help, status, ls, pwd, date, clear, desktop-info
                    </small>
                </div>
            </div>
        </div>
    </main>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/luna-mcp.js"></script>
</body>
</html>
