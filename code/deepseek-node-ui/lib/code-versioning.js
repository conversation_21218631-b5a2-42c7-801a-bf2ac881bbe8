/**
 * Module de versionnage des fichiers de code
 * Permet de gérer les versions des fichiers de code
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Répertoire de base pour les versions de code
const CODE_VERSIONS_DIR = path.join(__dirname, '../data/code-versions');

// S'assurer que le répertoire existe
if (!fs.existsSync(CODE_VERSIONS_DIR)) {
  fs.mkdirSync(CODE_VERSIONS_DIR, { recursive: true });
  console.log(`Répertoire de versions de code créé: ${CODE_VERSIONS_DIR}`);
}

/**
 * Classe pour gérer les versions des fichiers de code
 */
class CodeVersioning {
  constructor() {
    this.versions = {};
    this.loadVersions();
  }
  
  /**
   * Charge toutes les versions existantes
   */
  loadVersions() {
    try {
      // Lire tous les dossiers de projets
      const projectDirs = fs.readdirSync(CODE_VERSIONS_DIR);
      
      projectDirs.forEach(projectDir => {
        const projectPath = path.join(CODE_VERSIONS_DIR, projectDir);
        
        // Vérifier si c'est un dossier
        if (fs.statSync(projectPath).isDirectory()) {
          // Lire tous les fichiers du projet
          const fileDirs = fs.readdirSync(projectPath);
          
          fileDirs.forEach(fileDir => {
            const filePath = path.join(projectPath, fileDir);
            
            // Vérifier si c'est un dossier
            if (fs.statSync(filePath).isDirectory()) {
              // Lire toutes les versions du fichier
              const versionFiles = fs.readdirSync(filePath);
              
              // Initialiser le tableau des versions pour ce fichier
              if (!this.versions[projectDir]) {
                this.versions[projectDir] = {};
              }
              
              if (!this.versions[projectDir][fileDir]) {
                this.versions[projectDir][fileDir] = [];
              }
              
              versionFiles.forEach(versionFile => {
                const versionPath = path.join(filePath, versionFile);
                
                // Vérifier si c'est un fichier
                if (fs.statSync(versionPath).isFile()) {
                  try {
                    const versionInfo = JSON.parse(fs.readFileSync(versionPath, 'utf8'));
                    this.versions[projectDir][fileDir].push(versionInfo);
                  } catch (error) {
                    console.error(`Erreur lors du chargement de la version ${versionFile}:`, error);
                  }
                }
              });
              
              // Trier les versions par date
              if (this.versions[projectDir][fileDir].length > 0) {
                this.versions[projectDir][fileDir].sort((a, b) => {
                  return new Date(b.timestamp) - new Date(a.timestamp);
                });
              }
            }
          });
        }
      });
      
      console.log('Versions de code chargées');
    } catch (error) {
      console.error('Erreur lors du chargement des versions:', error);
    }
  }
  
  /**
   * Crée une nouvelle version d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} content - Contenu du fichier
   * @param {string} message - Message de commit
   * @returns {Object} - Informations de la version
   */
  createVersion(projectId, fileName, content, message = 'Sauvegarde automatique') {
    try {
      // Créer les répertoires s'ils n'existent pas
      const projectPath = path.join(CODE_VERSIONS_DIR, projectId);
      if (!fs.existsSync(projectPath)) {
        fs.mkdirSync(projectPath, { recursive: true });
      }
      
      const filePath = path.join(projectPath, fileName);
      if (!fs.existsSync(filePath)) {
        fs.mkdirSync(filePath, { recursive: true });
      }
      
      // Créer la version
      const versionId = uuidv4();
      const timestamp = new Date().toISOString();
      
      const versionInfo = {
        id: versionId,
        fileName: fileName,
        timestamp: timestamp,
        message: message,
        size: content.length
      };
      
      // Enregistrer les informations de la version
      fs.writeFileSync(
        path.join(filePath, `${versionId}.json`),
        JSON.stringify(versionInfo, null, 2)
      );
      
      // Enregistrer le contenu de la version
      fs.writeFileSync(
        path.join(filePath, `${versionId}.content`),
        content
      );
      
      // Ajouter la version à la liste
      if (!this.versions[projectId]) {
        this.versions[projectId] = {};
      }
      
      if (!this.versions[projectId][fileName]) {
        this.versions[projectId][fileName] = [];
      }
      
      this.versions[projectId][fileName].unshift(versionInfo);
      
      // Limiter le nombre de versions à 10
      if (this.versions[projectId][fileName].length > 10) {
        const oldestVersion = this.versions[projectId][fileName].pop();
        
        // Supprimer les fichiers de la version la plus ancienne
        try {
          fs.unlinkSync(path.join(filePath, `${oldestVersion.id}.json`));
          fs.unlinkSync(path.join(filePath, `${oldestVersion.id}.content`));
        } catch (error) {
          console.error(`Erreur lors de la suppression de la version ${oldestVersion.id}:`, error);
        }
      }
      
      console.log(`Nouvelle version créée pour ${fileName} dans le projet ${projectId}: ${versionId}`);
      
      return versionInfo;
    } catch (error) {
      console.error('Erreur lors de la création de la version:', error);
      return null;
    }
  }
  
  /**
   * Obtient toutes les versions d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @returns {Array} - Liste des versions
   */
  getVersions(projectId, fileName) {
    if (!this.versions[projectId] || !this.versions[projectId][fileName]) {
      return [];
    }
    
    return this.versions[projectId][fileName];
  }
  
  /**
   * Obtient le contenu d'une version
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} versionId - ID de la version
   * @returns {string} - Contenu de la version
   */
  getVersionContent(projectId, fileName, versionId) {
    try {
      const contentPath = path.join(CODE_VERSIONS_DIR, projectId, fileName, `${versionId}.content`);
      
      if (!fs.existsSync(contentPath)) {
        throw new Error(`Version non trouvée: ${versionId}`);
      }
      
      return fs.readFileSync(contentPath, 'utf8');
    } catch (error) {
      console.error('Erreur lors de la récupération du contenu de la version:', error);
      return null;
    }
  }
  
  /**
   * Restaure une version d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} versionId - ID de la version
   * @returns {string} - Contenu de la version
   */
  restoreVersion(projectId, fileName, versionId) {
    try {
      const content = this.getVersionContent(projectId, fileName, versionId);
      
      if (!content) {
        throw new Error(`Impossible de récupérer le contenu de la version ${versionId}`);
      }
      
      // Créer une nouvelle version avec le contenu restauré
      this.createVersion(projectId, fileName, content, `Restauration de la version ${versionId}`);
      
      return content;
    } catch (error) {
      console.error('Erreur lors de la restauration de la version:', error);
      return null;
    }
  }
}

module.exports = new CodeVersioning();
