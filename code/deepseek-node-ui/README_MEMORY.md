# Mémoire Thermique pour DeepSeek r1

Ce module ajoute une mémoire thermique à l'agent DeepSeek r1, permettant à l'agent de se souvenir des conversations passées et d'utiliser ces informations pour enrichir ses réponses futures.

## Concept de la Mémoire Thermique

La mémoire thermique est un système de gestion de la mémoire inspiré du fonctionnement du cerveau humain. Elle utilise le concept de "température" pour déterminer l'importance et la fraîcheur des informations.

### Niveaux de Mémoire

La mémoire thermique est organisée en plusieurs niveaux, chacun avec une température et une durée de rétention différentes :

1. **Mémoire instantanée (Niveau 1)** : Stockage ultra-volatile pour les informations immédiates. Température très élevée (0.9-1.0).
2. **Mémoire à court terme (Niveau 2)** : Stockage volatile pour les informations récentes. Température élevée (0.7-0.9).
3. **Mémoire de travail (Niveau 3)** : Stockage semi-volatile pour les informations en cours de traitement. Température moyenne à élevée (0.6-0.8).
4. **Mémoire à moyen terme (Niveau 4)** : Stockage semi-persistant pour les informations importantes. Température moyenne (0.4-0.7).
5. **Mémoire à long terme (Niveau 5)** : Stockage persistant pour les informations cruciales. Température basse (0.2-0.5).
6. **Mémoire des rêves (Niveau 6)** : Stockage spécial pour les rêves générés et leurs insights. Température variable (0.3-0.8).

### Fonctionnement

- Les informations sont initialement stockées dans les niveaux supérieurs (mémoire instantanée ou à court terme) en fonction de leur importance.
- Au fil du temps, la température des informations diminue, ce qui les fait "descendre" vers des niveaux de mémoire plus profonds.
- Les informations fréquemment accédées voient leur température augmenter, ce qui peut les faire "remonter" vers des niveaux de mémoire plus actifs.
- Des cycles de mémoire sont exécutés périodiquement pour mettre à jour les températures et déplacer les informations entre les niveaux.
- Des "rêves" peuvent être générés pour créer des connexions entre différentes informations stockées dans la mémoire.

## Utilisation

### Démarrage

Pour démarrer l'interface DeepSeek r1 avec la mémoire thermique activée, utilisez le script `start-with-memory.sh` :

```bash
./start-with-memory.sh
```

Ce script vérifie si Ollama est installé et en cours d'exécution, initialise les fichiers de mémoire nécessaires, et démarre le serveur Node.js.

### Interface Web

Une fois le serveur démarré, accédez à l'interface web à l'adresse http://localhost:3000.

#### Page de Chat

Sur la page de chat principale, vous pouvez activer ou désactiver l'utilisation de la mémoire thermique en utilisant l'option "Utiliser la mémoire thermique" dans les paramètres.

Lorsque la mémoire thermique est activée :
- Les conversations sont stockées dans la mémoire
- Les réponses de l'agent sont enrichies avec des informations pertinentes de la mémoire
- L'agent peut se souvenir des conversations passées et y faire référence

#### Page de Mémoire Thermique

Une page dédiée à la mémoire thermique est disponible à l'adresse http://localhost:3000/memory. Cette page vous permet de :

- Visualiser les différents niveaux de mémoire et leur contenu
- Rechercher des informations dans la mémoire
- Forcer un cycle de mémoire
- Générer un rêve
- Effacer la mémoire
- Exporter la mémoire

La page affiche également des statistiques sur la mémoire et une visualisation du "cerveau" de l'agent.

## Fonctionnalités Avancées

### Accélérateur Kyber

L'accélérateur Kyber est un système qui permet d'ajuster dynamiquement la température des informations en fonction de leur importance. Il utilise plusieurs paramètres :

- **Facteur d'accélération** : Multiplie la température des informations importantes
- **Température** : Contrôle la "chaleur" globale du système de mémoire
- **Stabilité** : Détermine la résistance du système aux fluctuations de température

### Génération de Rêves

La génération de rêves est un processus qui crée des connexions entre différentes informations stockées dans la mémoire. Ces rêves peuvent révéler des insights intéressants et aider l'agent à mieux comprendre les relations entre les informations.

### Cycles de Mémoire

Les cycles de mémoire sont exécutés périodiquement pour mettre à jour les températures des informations et les déplacer entre les différents niveaux de mémoire. Vous pouvez forcer l'exécution d'un cycle de mémoire depuis la page de mémoire thermique.

## API

### Endpoints

L'API de mémoire thermique expose plusieurs endpoints :

- `GET /api/memory/data` : Récupère toutes les données de mémoire
- `GET /api/memory/stats` : Récupère les statistiques de la mémoire
- `GET /api/memory/brain` : Récupère l'état du cerveau
- `GET /api/memory/search` : Recherche dans la mémoire
- `POST /api/memory/add` : Ajoute une information à la mémoire
- `GET /api/memory/item/:id` : Récupère un élément de mémoire
- `DELETE /api/memory/item/:id` : Supprime un élément de mémoire
- `POST /api/memory/cycle` : Force un cycle de mémoire
- `POST /api/memory/dream` : Génère un rêve
- `POST /api/memory/clear` : Efface toute la mémoire
- `POST /api/memory/chat` : Envoie un message à l'agent avec mémoire

### Intégration avec d'autres applications

Vous pouvez intégrer la mémoire thermique à d'autres applications en utilisant l'API. Par exemple, pour ajouter une information à la mémoire :

```javascript
fetch('http://localhost:3000/api/memory/add', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    key: 'Information importante',
    data: {
      content: 'Contenu de l\'information',
      source: 'Application externe'
    },
    importance: 0.8,
    category: 'externe'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Personnalisation

Vous pouvez personnaliser le comportement de la mémoire thermique en modifiant les paramètres dans le fichier `lib/memory/thermal_memory.js`. Les paramètres disponibles incluent :

- Capacités des différents niveaux de mémoire
- Taux de décroissance de la température
- Seuils de déplacement entre les niveaux
- Paramètres de recherche
- Facteurs d'importance

## Crédits

La mémoire thermique a été développée par Jean-Luc PASSAVE et adaptée pour l'agent DeepSeek r1.

---

Pour toute question ou suggestion, veuillez contacter Jean-Luc PASSAVE.
