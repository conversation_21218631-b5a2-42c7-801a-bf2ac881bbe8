/**
 * Script pour tester l'identité de l'assistant
 */

const io = require('socket.io-client');
const socket = io('http://localhost:3001');

socket.on('connect', () => {
  console.log('Connecté au serveur');
  
  // Envoyer un message sur l'identité de l'assistant
  const message = 'Comment tu t\'appelles?';
  console.log(`Envoi du message: "${message}"`);
  socket.emit('luna message', { message });
});

socket.on('luna response', (data) => {
  console.log('Réponse:');
  console.log(data.message);
  socket.disconnect();
  process.exit(0);
});

socket.on('connect_error', (error) => {
  console.error('Erreur de connexion:', error);
  socket.disconnect();
  process.exit(1);
});

// Timeout après 30 secondes
setTimeout(() => {
  console.log('Timeout: Pas de réponse après 30 secondes');
  socket.disconnect();
  process.exit(1);
}, 30000);
