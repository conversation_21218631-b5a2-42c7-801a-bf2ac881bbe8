const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Helpers pour les opérations de fichier
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434/api';

// Route principale
app.get('/', (req, res) => {
  res.render('index-ollama', {
    title: 'DeepSeek r1 - Interface Ollama'
  });
});

// Point de terminaison pour vérifier la disponibilité d'Ollama
app.get('/api/check-ollama', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/version`);
    res.json({ available: true, version: response.data.version });
  } catch (error) {
    console.error('Error checking Ollama availability:', error.message);
    res.json({ available: false, error: error.message });
  }
});

// Point de terminaison pour obtenir les modèles disponibles
app.get('/api/models', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/tags`);
    res.json(response.data.models || []);
  } catch (error) {
    console.error('Error getting models:', error.message);
    res.json({ error: error.message });
  }
});

// Gestion des connexions Socket.io
io.on('connection', (socket) => {
  console.log('New client connected');

  // Gestion de la déconnexion
  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });

  // Vérification de la disponibilité d'Ollama
  socket.on('check ollama', async () => {
    try {
      const response = await axios.get(`${OLLAMA_API_URL}/version`);
      socket.emit('ollama status', { available: true, version: response.data.version });
    } catch (error) {
      console.error('Error checking Ollama:', error.message);
      socket.emit('ollama status', { available: false, error: error.message });
    }
  });

  // Traitement des messages du chat
  socket.on('chat message', async (data) => {
    const { message, modelName, temperature, maxTokens } = data;
    console.log(`Received message: "${message.substring(0, 50)}..." for model ${modelName || 'default'}`);

    try {
      // Avertir le client que le message est en cours de traitement
      socket.emit('processing', true);

      // Préparer la requête pour l'API Ollama
      const requestData = {
        model: modelName || 'deepseek-r1:1.5b', // Modèle par défaut
        messages: [{ role: 'user', content: message }],
        options: {
          temperature: parseFloat(temperature || 0.7),
          num_predict: parseInt(maxTokens || 1000)
        }
      };

      console.log('Sending request to Ollama API:', JSON.stringify(requestData, null, 2));

      // Appeler l'API Ollama
      let response;
      try {
        // Essayer d'abord l'API de chat
        const result = await axios.post(`${OLLAMA_API_URL}/chat`, requestData);
        response = result.data;
      } catch (chatError) {
        console.error('Error with chat API, trying generate API:', chatError.message);
        
        // Fallback sur l'API de génération si l'API de chat échoue
        try {
          const generateRequestData = {
            model: modelName || 'deepseek-r1:1.5b',
            prompt: message,
            options: {
              temperature: parseFloat(temperature || 0.7),
              num_predict: parseInt(maxTokens || 1000)
            }
          };
          
          const generateResult = await axios.post(`${OLLAMA_API_URL}/generate`, generateRequestData);
          
          // Convertir la réponse de l'API de génération au format de l'API de chat
          response = {
            message: {
              role: 'assistant',
              content: generateResult.data.response
            }
          };
        } catch (generateError) {
          console.error('Error with generate API:', generateError.message);
          throw new Error(`Failed to communicate with Ollama: ${chatError.message}`);
        }
      }
      
      console.log('Response from Ollama API:', JSON.stringify(response, null, 2));
      
      // Formater la réponse si nécessaire
      const formattedResponse = formatOllamaResponse(response);
      
      // Envoyer la réponse au client
      socket.emit('chat response', formattedResponse);
      
      // Indiquer que le traitement est terminé
      socket.emit('processing', false);
    } catch (error) {
      console.error('Error processing message:', error.message);
      socket.emit('chat response', { 
        message: { 
          role: 'assistant', 
          content: `Désolé, une erreur s'est produite lors du traitement de votre message: ${error.message}` 
        } 
      });
      socket.emit('processing', false);
    }
  });
});

// Fonctions utilitaires
function formatOllamaResponse(response) {
  // Si la réponse est déjà au format attendu
  if (response && response.message && response.message.content) {
    return response;
  }
  
  // Sinon, tenter de la reformater
  const formattedResponse = {
    message: {
      role: 'assistant',
      content: ''
    }
  };
  
  if (typeof response === 'string') {
    formattedResponse.message.content = response;
  } else if (typeof response === 'object' && response !== null) {
    // Cas communs
    if (response.response) {
      formattedResponse.message.content = response.response;
    } else if (response.choices && response.choices.length > 0) {
      const choice = response.choices[0];
      if (choice.message && choice.message.content) {
        formattedResponse.message.content = choice.message.content;
      } else if (choice.text) {
        formattedResponse.message.content = choice.text;
      }
    } else if (response.generation || response.text || response.output) {
      formattedResponse.message.content = response.generation || response.text || response.output;
    } else {
      // Recherche récursive
      const content = findContentInObject(response);
      if (content) {
        formattedResponse.message.content = content;
      } else {
        formattedResponse.message.content = "Désolé, je n'ai pas pu générer une réponse. Veuillez réessayer.";
      }
    }
  }
  
  return formattedResponse;
}

function findContentInObject(obj, depth = 0) {
  if (depth > 3 || typeof obj !== 'object' || obj === null) {
    return null;
  }
  
  const contentKeys = ['content', 'text', 'message', 'response', 'output', 'generation', 'result'];
  
  // Chercher dans les clés probables
  for (const key of contentKeys) {
    if (obj[key] && typeof obj[key] === 'string') {
      return obj[key];
    }
  }
  
  // Recherche récursive
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      const found = findContentInObject(obj[key], depth + 1);
      if (found) return found;
    }
  }
  
  return null;
}

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('This is a simplified version of the DeepSeek r1 server');
});
