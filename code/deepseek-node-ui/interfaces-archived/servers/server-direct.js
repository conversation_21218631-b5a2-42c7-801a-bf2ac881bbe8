/**
 * Serveur Node.js pour l'interface DeepSeek r1 (version directe)
 * Cette version utilise directement l'API Ollama sans passer par la mémoire thermique
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434';

// Variables globales
let selectedModel = 'deepseek-r1:7b';
let isOllamaRunning = false;

// Agent simulé pour les tests
const simulatedAgent = {
  name: 'DeepSeek r1 (Simulé)',
  respond: (message) => {
    const responses = [
      `Je suis un agent DeepSeek r1 simulé. Votre message était : "${message}"`,
      `En tant qu'agent simulé, je peux vous dire que j'ai bien reçu votre message : "${message}"`,
      `Bonjour ! Je suis l'agent DeepSeek r1 en mode simulé. Je ne suis pas connecté à Ollama, mais je peux quand même vous répondre de manière basique.`,
      `Merci pour votre message. Je suis actuellement en mode simulé car Ollama n'est pas disponible.`,
      `Je suis désolé, mais je ne peux pas traiter votre demande de manière complète car je suis en mode simulé.`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }
};

// Fonction pour vérifier si Ollama est en cours d'exécution
async function checkOllama() {
  try {
    await axios.get(`${OLLAMA_API_URL}/api/version`);
    return true;
  } catch (error) {
    try {
      await axios.get(`${OLLAMA_API_URL}/version`);
      return true;
    } catch (error2) {
      return false;
    }
  }
}

// Fonction pour obtenir les modèles disponibles
async function getAvailableModels() {
  try {
    const response = await axios.get(`${OLLAMA_API_URL}/api/tags`);
    return response.data.models || [];
  } catch (error) {
    try {
      const response = await axios.get(`${OLLAMA_API_URL}/api/models`);
      return response.data.models || [];
    } catch (error2) {
      return [];
    }
  }
}

// Routes
app.get('/', (req, res) => {
  res.render('index', { title: 'DeepSeek r1 Interface (Direct)' });
});

// Route pour la mémoire thermique
app.get('/memory', (req, res) => {
  res.render('memory', { title: 'Mémoire Thermique - DeepSeek r1' });
});

// Route pour l'analyse de livres
app.get('/books', (req, res) => {
  res.render('book-analysis', { title: 'Analyse de Livres - DeepSeek r1' });
});

// Route pour la gestion des modèles
app.get('/models', (req, res) => {
  res.render('models', { title: 'Gestion des Modèles - DeepSeek r1' });
});

// Route pour les paramètres
app.get('/settings', (req, res) => {
  res.render('settings', { title: 'Paramètres - DeepSeek r1' });
});

// Socket.io pour les communications en temps réel
io.on('connection', async (socket) => {
  console.log('New client connected');

  // Vérifier si Ollama est en cours d'exécution
  isOllamaRunning = await checkOllama();
  socket.emit('ollama status', { isRunning: isOllamaRunning });

  // Récupérer les modèles disponibles
  if (isOllamaRunning) {
    const models = await getAvailableModels();
    socket.emit('models', { models });
  }

  // Gérer les messages de chat
  socket.on('chat message', async (data) => {
    try {
      console.log('Received chat message from client:', data);
      const { message, history, modelName, temperature, maxTokens } = data;

      if (!message) {
        console.error('Message is empty');
        return socket.emit('chat response', {
          error: 'Message cannot be empty'
        });
      }

      // Vérifier si Ollama est en cours d'exécution
      if (!isOllamaRunning) {
        isOllamaRunning = await checkOllama();
      }

      if (!isOllamaRunning) {
        // Si Ollama n'est pas en cours d'exécution, utiliser l'agent simulé
        console.log('Ollama is not running, using simulated agent');
        const simulatedResponse = simulatedAgent.respond(message);
        return socket.emit('chat response', {
          message: {
            role: 'assistant',
            content: simulatedResponse
          }
        });
      }

      console.log('Sending message to Ollama...');
      console.log('Model:', modelName || selectedModel);

      try {
        // Préparer les données pour l'API Ollama
        const requestData = {
          model: modelName || selectedModel,
          messages: [...(history || []), { role: 'user', content: message }],
          options: {
            temperature: parseFloat(temperature || 0.7),
            num_predict: parseInt(maxTokens || 1000)
          }
        };

        console.log('Request data:', JSON.stringify(requestData, null, 2));

        // Appeler l'API Ollama
        try {
          // Essayer d'abord l'API de chat
          const response = await axios.post(`${OLLAMA_API_URL}/api/chat`, requestData);

          // Extraire la réponse finale
          let finalResponse = {
            message: {
              role: 'assistant',
              content: ''
            }
          };

          if (response.data && response.data.message && response.data.message.content) {
            finalResponse.message.content = response.data.message.content;
          } else if (typeof response.data === 'string') {
            // Si la réponse est une chaîne JSON, la parser
            try {
              // Diviser la chaîne en lignes
              const lines = response.data.split('\n').filter(line => line.trim());

              // Extraire le contenu de chaque ligne
              let fullContent = '';
              for (const line of lines) {
                try {
                  const jsonObj = JSON.parse(line);
                  if (jsonObj.message && jsonObj.message.content) {
                    fullContent += jsonObj.message.content;
                  }
                } catch (e) {
                  // Ignorer les lignes qui ne sont pas du JSON valide
                }
              }

              // Nettoyer le contenu
              fullContent = fullContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

              finalResponse.message.content = fullContent;
            } catch (e) {
              finalResponse.message.content = "Erreur lors du traitement de la réponse.";
            }
          }

          console.log('Final response:', finalResponse);
          socket.emit('chat response', finalResponse);
        } catch (chatError) {
          console.error('Error with chat API, trying generate API:', chatError.message);

          // Si l'API de chat échoue, essayer l'API de génération
          try {
            const generateRequestData = {
              model: modelName || selectedModel,
              prompt: message,
              options: {
                temperature: parseFloat(temperature || 0.7),
                num_predict: parseInt(maxTokens || 1000)
              }
            };

            const generateResponse = await axios.post(`${OLLAMA_API_URL}/api/generate`, generateRequestData);

            // Convertir la réponse de l'API de génération au format de l'API de chat
            const finalResponse = {
              message: {
                role: 'assistant',
                content: generateResponse.data.response || "Pas de réponse"
              }
            };

            socket.emit('chat response', finalResponse);
          } catch (generateError) {
            console.error('Error with generate API:', generateError.message);

            // Si les deux APIs échouent, créer une réponse simulée
            socket.emit('chat response', {
              message: {
                role: 'assistant',
                content: `Je suis désolé, mais je ne peux pas traiter votre demande pour le moment. Erreur: ${chatError.message}`
              }
            });
          }
        }
      } catch (error) {
        console.error('Error calling Ollama API:', error.message);

        socket.emit('chat response', {
          message: {
            role: 'assistant',
            content: `Erreur lors de la communication avec Ollama: ${error.message}`
          }
        });
      }
    } catch (error) {
      console.error('Error handling chat message:', error);

      socket.emit('chat response', {
        message: {
          role: 'assistant',
          content: `Une erreur s'est produite: ${error.message}`
        }
      });
    }
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
