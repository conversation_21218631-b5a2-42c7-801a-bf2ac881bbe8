#!/bin/bash

# Script pour démarrer l'interface DeepSeek r1 avec la mémoire thermique
# Créé par Jean-<PERSON> PASSAVE

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Fonction pour afficher un message avec une couleur
print_message() {
  echo -e "${2}${1}${NC}"
}

# Fonction pour afficher un message d'erreur et quitter
error_exit() {
  print_message "ERREUR: $1" "$RED"
  exit 1
}

# Afficher le logo
print_message "
  ____                 ____            _      ____  _ 
 |  _ \  ___  ___ _ __/ ___|  ___  ___| | __ |  _ \\/ |
 | | | |/ _ \\/ _ \\ '_ \\___ \\ / _ \\/ _ \\ |/ / | |_) | |
 | |_| |  __/  __/ |_) |__) |  __/  __/   <  |  _ <| |
 |____/ \\___|\\___| .__/____/ \\___|\\___|_|\\_\\ |_| \\_\\_|
                 |_|                                  
 " "$PURPLE"
print_message "Avec Mémoire Thermique" "$YELLOW"
print_message "Développé par Jean-Luc PASSAVE" "$BLUE"
echo ""

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
  error_exit "Ollama n'est pas installé. Veuillez l'installer avant de continuer."
fi

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  error_exit "Node.js n'est pas installé. Veuillez l'installer avant de continuer."
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  error_exit "npm n'est pas installé. Veuillez l'installer avant de continuer."
fi

# Créer les dossiers nécessaires
print_message "Création des dossiers nécessaires..." "$BLUE"
mkdir -p data/memory

# Vérifier si Ollama est en cours d'exécution
print_message "Vérification si Ollama est en cours d'exécution..." "$BLUE"
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
  print_message "Ollama n'est pas en cours d'exécution. Démarrage d'Ollama..." "$YELLOW"
  
  # Démarrer Ollama en arrière-plan
  ollama serve &
  OLLAMA_PID=$!
  
  # Attendre que Ollama soit prêt
  print_message "Attente que Ollama soit prêt..." "$BLUE"
  while ! curl -s http://localhost:11434/api/version &> /dev/null; do
    sleep 1
  done
  
  print_message "Ollama est maintenant en cours d'exécution." "$GREEN"
else
  print_message "Ollama est déjà en cours d'exécution." "$GREEN"
fi

# Vérifier si le modèle DeepSeek r1 est disponible
print_message "Vérification si le modèle DeepSeek r1 est disponible..." "$BLUE"
if ! curl -s http://localhost:11434/api/tags | grep -q "deepseek-r1"; then
  print_message "Le modèle DeepSeek r1 n'est pas disponible. Voulez-vous le télécharger ? (o/n)" "$YELLOW"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Téléchargement du modèle DeepSeek r1..." "$BLUE"
    ollama pull deepseek-r1:7b
  else
    print_message "Vous devrez télécharger le modèle manuellement depuis l'interface." "$YELLOW"
  fi
else
  print_message "Le modèle DeepSeek r1 est disponible." "$GREEN"
fi

# Initialiser la mémoire thermique
print_message "Initialisation de la mémoire thermique..." "$BLUE"
if [ ! -f "data/memory/working_memory.json" ]; then
  print_message "Création des fichiers de mémoire initiaux..." "$YELLOW"
  echo "{}" > data/memory/working_memory.json
  echo "{}" > data/memory/medium_term_memory.json
  echo "{}" > data/memory/long_term_memory.json
  echo "{}" > data/memory/dream_memory.json
  print_message "Fichiers de mémoire créés avec succès." "$GREEN"
else
  print_message "Les fichiers de mémoire existent déjà." "$GREEN"
fi

# Démarrer le serveur Node.js
print_message "Démarrage du serveur Node.js avec la mémoire thermique activée..." "$BLUE"
print_message "L'interface sera disponible à l'adresse http://localhost:3000" "$GREEN"
print_message "Appuyez sur Ctrl+C pour arrêter le serveur." "$YELLOW"
echo ""
node server-ollama.js

# Si le serveur s'arrête, arrêter Ollama si nous l'avons démarré
if [ -n "$OLLAMA_PID" ]; then
  print_message "Arrêt d'Ollama..." "$BLUE"
  kill $OLLAMA_PID
fi

print_message "Serveur arrêté." "$YELLOW"
