#!/bin/bash

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${BLUE}"
echo "  ____                 ____            _      ____  _ "
echo " |  _ \  ___  ___ _ __/ ___|  ___  ___| | __ |  _ \/ |"
echo " | | | |/ _ \/ _ \ '_ \___ \ / _ \/ _ \ |/ / | |_) | |"
echo " | |_| |  __/  __/ |_) |__) |  __/  __/   <  |  _ <| |"
echo " |____/ \___|\___| .__/____/ \___|\___|_|\_\ |_| \_\_|"
echo "                 |_|                                  "
echo -e "${NC}"
echo -e "${CYAN}Version directe (sans mémoire thermique)${NC}"
echo -e "${YELLOW}Développé par Jean-<PERSON> PASSAVE${NC}"
echo ""

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application.${NC}"
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo -e "${RED}npm n'est pas installé. Veuillez installer npm pour exécuter cette application.${NC}"
    exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installation des dépendances...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}Erreur lors de l'installation des dépendances.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Dépendances installées avec succès.${NC}"
fi

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo -e "${YELLOW}Ollama n'est pas installé. L'application fonctionnera en mode simulé.${NC}"
else
    # Vérifier si Ollama est en cours d'exécution
    echo -e "${BLUE}Vérification si Ollama est en cours d'exécution...${NC}"
    if curl -s http://localhost:11434/api/version &> /dev/null || curl -s http://127.0.0.1:11434/api/version &> /dev/null || curl -s http://localhost:11434/version &> /dev/null || curl -s http://127.0.0.1:11434/version &> /dev/null; then
        echo -e "${GREEN}Ollama est en cours d'exécution.${NC}"
        
        # Vérifier si le modèle DeepSeek r1 est disponible
        echo -e "${BLUE}Vérification si le modèle DeepSeek r1 est disponible...${NC}"
        if ollama list | grep -q "deepseek-r1"; then
            echo -e "${GREEN}Le modèle DeepSeek r1 est disponible.${NC}"
        else
            echo -e "${YELLOW}Le modèle DeepSeek r1 n'est pas disponible. L'application fonctionnera en mode simulé.${NC}"
        fi
    else
        echo -e "${YELLOW}Ollama n'est pas en cours d'exécution. L'application fonctionnera en mode simulé.${NC}"
    fi
fi

# Démarrer le serveur Node.js
echo -e "${BLUE}Démarrage du serveur Node.js en mode direct...${NC}"
echo -e "${GREEN}L'interface sera disponible à l'adresse http://localhost:3000${NC}"
echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur.${NC}"
echo ""

# Démarrer le serveur
node server-direct.js
