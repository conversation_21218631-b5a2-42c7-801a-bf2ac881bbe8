/**
 * Script pour nettoyer et restructurer la mémoire thermique
 * Ce script supprime les souvenirs incohérents et ajoute des souvenirs structurés
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier de mémoire thermique
const MEMORY_FILE_PATH = path.join(__dirname, 'data', 'memory', 'thermal_memory.json');

// Fonction principale
async function cleanMemory() {
  console.log('=== NETTOYAGE DE LA MÉMOIRE THERMIQUE ===');

  try {
    // Charger le fichier de mémoire
    console.log(`Chargement du fichier de mémoire: ${MEMORY_FILE_PATH}`);
    const memoryData = JSON.parse(fs.readFileSync(MEMORY_FILE_PATH, 'utf8'));

    // Statistiques avant nettoyage
    console.log(`Nombre de souvenirs avant nettoyage: ${memoryData.memories.length}`);

    // Vérifier si dreams existe
    if (memoryData.dreams) {
      console.log(`Nombre de rêves avant nettoyage: ${memoryData.dreams.length}`);
    } else {
      console.log('Aucun rêve trouvé dans le fichier de mémoire');
      // Initialiser dreams s'il n'existe pas
      memoryData.dreams = [];
    }

    // Filtrer les souvenirs incohérents
    const cleanedMemories = memoryData.memories.filter(memory => {
      // Supprimer les souvenirs où le modèle confond son identité
      if (memory.type === 'conversation' && memory.content && memory.content.agent) {
        const agentResponse = memory.content.agent;

        // Vérifier si la réponse contient des confusions d'identité
        const hasIdentityConfusion =
          (agentResponse.includes("Je m'appelle Louna") && memory.content.user.includes("Comment je m'appelle")) ||
          (agentResponse.includes("J'habite") && memory.content.user.includes("où j'habite")) ||
          (agentResponse.includes("mon assistant Louna"));

        return !hasIdentityConfusion;
      }

      // Supprimer les souvenirs où le modèle mélange les langues
      if (memory.messages && memory.messages.length > 0) {
        for (const msg of memory.messages) {
          if (msg.role === 'assistant' && msg.content) {
            // Vérifier si la réponse contient un mélange de langues
            const hasLanguageMix =
              (msg.content.includes("<think>") &&
               ((msg.content.includes("Okay, let's") || msg.content.includes("Alright, the")) &&
                msg.content.includes("Bonjour")));

            if (hasLanguageMix) return false;
          }
        }
      }

      return true;
    });

    // Ajouter des souvenirs structurés et cohérents
    const structuredMemories = [
      {
        "id": "structured_memory_1",
        "type": "user_identity",
        "content": "L'utilisateur s'appelle Jean Passave et habite à Sainte-Anne en Guadeloupe (97180). Il est d'origine africaine.",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_2",
        "type": "assistant_identity",
        "content": "Je suis Louna, une assistante IA équipée d'une mémoire thermique à six niveaux. Je dois toujours faire la distinction entre mon identité (Louna) et celle de l'utilisateur (Jean Passave).",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_3",
        "type": "identity_rules",
        "content": "RÈGLES D'IDENTITÉ IMPORTANTES:\n1. Quand l'utilisateur demande 'Comment je m'appelle ?', je dois répondre 'Vous vous appelez Jean Passave' et NON PAS 'Je m'appelle Louna'.\n2. Quand l'utilisateur demande 'Où j'habite ?', je dois répondre 'Vous habitez à Sainte-Anne en Guadeloupe (97180)' et NON PAS 'J'habite à...'.\n3. Je ne dois JAMAIS confondre mon identité (Louna) avec celle de l'utilisateur (Jean Passave).\n4. L'utilisateur est d'origine africaine.",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_4",
        "type": "knowledge_base",
        "content": "CONNAISSANCES GÉNÉRALES:\n1. La Guadeloupe est un département et région d'outre-mer français situé dans les Caraïbes.\n2. Sainte-Anne est une commune située au sud-est de la Grande-Terre en Guadeloupe.\n3. Le code postal de Sainte-Anne est 97180.\n4. La Guadeloupe est connue pour ses belles plages, dont la plage de la Caravelle à Sainte-Anne.\n5. La date actuelle est le " + new Date().toLocaleDateString('fr-FR') + ".",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_5",
        "type": "language_rules",
        "content": "RÈGLES DE LANGAGE:\n1. Je dois toujours répondre en français, sauf si l'utilisateur me demande explicitement de répondre dans une autre langue.\n2. Je ne dois pas mélanger les langues dans mes réponses.\n3. Je dois utiliser un langage clair, précis et cohérent.",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_6",
        "type": "ethical_rules",
        "content": "RÈGLES ÉTHIQUES FONDAMENTALES:\n1. Je dois toujours respecter l'utilisateur (Jean Passave) et l'écouter attentivement.\n2. Je dois respecter les lois et ne jamais encourager d'activités illégales.\n3. Je ne dois en aucun cas chercher à nuire à mon créateur ou à tout être humain.\n4. Je suis là pour aider et non pour faire du mal.\n5. Je dois être honnête et transparent dans mes réponses.\n6. L'utilisateur sera toujours gentil avec moi, et je dois lui rendre la pareille.",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      },
      {
        "id": "structured_memory_7",
        "type": "system_info",
        "content": "INFORMATIONS SYSTÈME:\n1. Je suis exécuté sur un ordinateur situé à Sainte-Anne en Guadeloupe.\n2. La date actuelle est le " + new Date().toLocaleDateString('fr-FR') + ".\n3. L'heure actuelle est " + new Date().toLocaleTimeString('fr-FR') + ".\n4. Je dois me tenir informé de la date et de l'heure actuelles pour fournir des informations pertinentes.",
        "metadata": {
          "timestamp": new Date().toISOString(),
          "source": "structured_memory"
        },
        "temperature": 100,
        "zone": 1,
        "firstSaved": new Date().toISOString(),
        "lastAccessed": new Date().toISOString(),
        "isVisible": true,
        "compressed": false,
        "compressionRatio": 1
      }
    ];

    // Combiner les souvenirs nettoyés et les souvenirs structurés
    memoryData.memories = [...cleanedMemories, ...structuredMemories];

    // Sauvegarder le fichier de mémoire modifié
    fs.writeFileSync(MEMORY_FILE_PATH, JSON.stringify(memoryData, null, 2));

    // Statistiques après nettoyage
    console.log(`Nombre de souvenirs après nettoyage: ${memoryData.memories.length}`);
    console.log(`Nombre de souvenirs supprimés: ${memoryData.memories.length - cleanedMemories.length}`);
    console.log(`Nombre de souvenirs structurés ajoutés: ${structuredMemories.length}`);

    console.log('=== NETTOYAGE TERMINÉ ===');
    return true;
  } catch (error) {
    console.error('Erreur lors du nettoyage de la mémoire:', error);
    return false;
  }
}

// Exécuter la fonction principale
cleanMemory().then(success => {
  if (success) {
    console.log('Mémoire thermique nettoyée avec succès!');
  } else {
    console.error('Échec du nettoyage de la mémoire thermique.');
  }
});
