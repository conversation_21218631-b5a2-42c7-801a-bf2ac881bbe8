/**
 * Script pour afficher le contenu des différentes zones de la mémoire thermique
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier de mémoire thermique
const MEMORY_FILE_PATH = path.join(__dirname, 'data', 'memory', 'thermal_memory.json');

// Fonction principale
async function viewMemoryZones() {
  console.log('=== CONTENU DES ZONES DE LA MÉMOIRE THERMIQUE ===');
  
  try {
    // Charger le fichier de mémoire
    console.log(`Chargement du fichier de mémoire: ${MEMORY_FILE_PATH}`);
    const memoryData = JSON.parse(fs.readFileSync(MEMORY_FILE_PATH, 'utf8'));
    
    // Statistiques générales
    console.log(`Nombre total de mémoires: ${memoryData.memories.length}`);
    
    // Vérifier si les zones de température existent
    if (!memoryData.config || !memoryData.config.temperatureZones) {
      console.log('Aucune zone de température trouvée dans le fichier de mémoire');
      return false;
    }
    
    // Afficher les zones de température
    console.log(`Nombre de zones de température: ${memoryData.config.temperatureZones.length}`);
    console.log('\nZones de température:');
    memoryData.config.temperatureZones.forEach((zone, index) => {
      console.log(`Zone ${index + 1}: ${zone.name}, Température: ${zone.temp}`);
    });
    
    // Regrouper les souvenirs par zone
    const memoriesByZone = {};
    memoryData.memories.forEach(memory => {
      const zone = memory.zone || 1; // Par défaut, zone 1 si non spécifiée
      if (!memoriesByZone[zone]) {
        memoriesByZone[zone] = [];
      }
      memoriesByZone[zone].push(memory);
    });
    
    // Afficher le contenu de chaque zone
    console.log('\n=== CONTENU DES ZONES ===');
    for (let i = 1; i <= memoryData.config.temperatureZones.length; i++) {
      const zoneName = memoryData.config.temperatureZones[i - 1]?.name || `Zone ${i}`;
      const zoneMemories = memoriesByZone[i] || [];
      
      console.log(`\n--- ${zoneName} (${zoneMemories.length} mémoires) ---`);
      
      if (zoneMemories.length === 0) {
        console.log('Aucune mémoire dans cette zone');
        continue;
      }
      
      // Afficher un résumé des souvenirs dans cette zone
      zoneMemories.forEach((memory, index) => {
        // Limiter à 10 souvenirs par zone pour éviter de surcharger la console
        if (index < 10) {
          console.log(`\nMémoire ${index + 1}:`);
          console.log(`Type: ${memory.type}`);
          console.log(`Température: ${memory.temperature}`);
          
          // Afficher le contenu de manière appropriée selon le type
          if (memory.type === 'conversation') {
            if (memory.content && memory.content.user && memory.content.agent) {
              console.log(`Utilisateur: ${memory.content.user.substring(0, 100)}${memory.content.user.length > 100 ? '...' : ''}`);
              console.log(`Assistant: ${memory.content.agent.substring(0, 100)}${memory.content.agent.length > 100 ? '...' : ''}`);
            } else {
              console.log(`Contenu: ${JSON.stringify(memory.content).substring(0, 200)}...`);
            }
          } else if (memory.messages && memory.messages.length > 0) {
            memory.messages.forEach((msg, msgIndex) => {
              if (msgIndex < 2) { // Limiter à 2 messages pour éviter de surcharger la console
                console.log(`${msg.role}: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`);
              }
            });
            if (memory.messages.length > 2) {
              console.log(`... et ${memory.messages.length - 2} autres messages`);
            }
          } else if (typeof memory.content === 'string') {
            console.log(`Contenu: ${memory.content.substring(0, 200)}${memory.content.length > 200 ? '...' : ''}`);
          } else {
            console.log(`Contenu: ${JSON.stringify(memory.content).substring(0, 200)}...`);
          }
        } else if (index === 10) {
          console.log(`... et ${zoneMemories.length - 10} autres mémoires`);
        }
      });
    }
    
    // Afficher les rêves s'ils existent
    if (memoryData.dreams && memoryData.dreams.length > 0) {
      console.log('\n=== RÊVES NEURONAUX ===');
      console.log(`Nombre de rêves: ${memoryData.dreams.length}`);
      
      memoryData.dreams.forEach((dream, index) => {
        if (index < 5) { // Limiter à 5 rêves pour éviter de surcharger la console
          console.log(`\nRêve ${index + 1}:`);
          console.log(`ID: ${dream.id}`);
          console.log(`Créé le: ${dream.created}`);
          console.log(`Contenu: ${dream.content.substring(0, 200)}${dream.content.length > 200 ? '...' : ''}`);
        } else if (index === 5) {
          console.log(`... et ${memoryData.dreams.length - 5} autres rêves`);
        }
      });
    } else {
      console.log('\nAucun rêve neuronal trouvé');
    }
    
    console.log('\n=== FIN DU RAPPORT ===');
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'affichage des zones de mémoire:', error);
    return false;
  }
}

// Exécuter la fonction principale
viewMemoryZones().then(success => {
  if (success) {
    console.log('Affichage des zones de mémoire thermique terminé avec succès!');
  } else {
    console.error('Échec de l\'affichage des zones de mémoire thermique.');
  }
});
