const express = require('express');
const http = require('http');
const path = require('path');

// Configuration
const app = express();
const server = http.createServer(app);
const PORT = 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Route principale
app.get('/', (req, res) => {
  res.send('Serveur de test fonctionne correctement!');
});

// Route pour Luna
app.get('/luna', (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Interface Cognitive Avancée'
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`\n\n=== SERVEUR DE TEST DÉMARRÉ ===`);
  console.log(`Serveur de test démarré sur http://localhost:${PORT}`);
  console.log(`Accès à l'interface Luna: http://localhost:${PORT}/luna`);
  console.log(`=== SERVEUR DE TEST PRÊT ===\n\n`);
});
