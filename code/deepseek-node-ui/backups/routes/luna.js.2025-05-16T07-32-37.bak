/**
 * Routes pour l'interface Luna
 * Intégration avec l'agent <PERSON><PERSON> et la mémoire thermique
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Middleware pour vérifier l'état de la mémoire thermique
const checkThermalMemory = (req, res, next) => {
  const memoryPath = path.join(__dirname, '../data/memory');
  // Vérifier si les fichiers de mémoire existent
  try {
    if (!fs.existsSync(memoryPath)) {
      fs.mkdirSync(memoryPath, { recursive: true });
    }

    // S'assurer que tous les dossiers de mémoire existent
    const memoryFolders = ['instant', 'short_term', 'working', 'medium_term', 'long_term', 'dream', 'kyber'];
    memoryFolders.forEach(folder => {
      const folderPath = path.join(memoryPath, folder);
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }
    });

    // Vérifier ou créer les fichiers de configuration de mémoire
    const memoryConfigPath = path.join(memoryPath, 'memory_config.json');
    if (!fs.existsSync(memoryConfigPath)) {
      const defaultConfig = {
        version: "1.0",
        memoryZones: {
          instant: { maxItems: 50, retentionHours: 1 },
          short_term: { maxItems: 200, retentionHours: 24 },
          working: { maxItems: 100, retentionHours: 72 },
          medium_term: { maxItems: 500, retentionHours: 720 },
          long_term: { maxItems: 1000, retentionHours: 8760 },
          dream: { maxItems: 20, retentionHours: 8760 }
        },
        thermalSettings: {
          consolidationFrequency: 1, // heures
          dreamCycleFrequency: 24,   // heures
          temperatureThresholds: {
            hotZone: 80,
            warmZone: 60,
            coolZone: 40,
            coldZone: 20,
            archiveZone: 5
          }
        }
      };
      fs.writeFileSync(memoryConfigPath, JSON.stringify(defaultConfig, null, 2));
    }

    next();
  } catch (error) {
    console.error('Erreur lors de la vérification de la mémoire thermique:', error);
    next();
  }
};

// Route principale pour Luna
router.get('/', checkThermalMemory, (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Interface Cognitive Avancée',
    agent: 'Louna'
  });
});

// Route pour la gestion des documents
router.get('/documents', checkThermalMemory, (req, res) => {
  res.render('luna-documents', { title: 'Luna - Documents' });
});

// Route pour la gestion des modèles
router.get('/models', checkThermalMemory, (req, res) => {
  res.render('luna-models', { title: 'Luna - Modèles IA' });
});

// Route pour la page de mémoire
router.get('/memory', checkThermalMemory, (req, res) => {
  // Lire les statistiques de la mémoire
  const memoryStats = {
    totalItems: 0,
    zoneStats: {}
  };

  try {
    const memoryPath = path.join(__dirname, '../data/memory');
    const memoryFolders = ['instant', 'short_term', 'working', 'medium_term', 'long_term', 'dream'];

    memoryFolders.forEach(folder => {
      const folderPath = path.join(memoryPath, folder);
      let itemCount = 0;

      if (fs.existsSync(folderPath)) {
        const files = fs.readdirSync(folderPath);
        itemCount = files.filter(file => file.endsWith('.json')).length;
        memoryStats.totalItems += itemCount;
      }

      memoryStats.zoneStats[folder] = {
        count: itemCount,
        // Température calculée en fonction de la zone
        temperature: folder === 'instant' ? 100 :
                    folder === 'short_term' ? 80 :
                    folder === 'working' ? 60 :
                    folder === 'medium_term' ? 40 :
                    folder === 'long_term' ? 20 : 5
      };
    });
  } catch (error) {
    console.error('Erreur lors de la lecture des statistiques de mémoire:', error);
  }

  res.render('luna-memory', {
    title: 'Luna - Mémoire Thermique',
    memoryStats
  });
});

// Route pour la page de paramètres
router.get('/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Luna - Paramètres'
  });
});

// Route pour la page de réflexion
router.get('/reflection', checkThermalMemory, (req, res) => {
  res.render('luna-reflection', {
    title: 'Luna - Réflexion'
  });
});

// Route pour la page des accélérateurs
router.get('/accelerators', checkThermalMemory, (req, res) => {
  res.render('luna-accelerators-new', {
    title: 'Luna - Accélérateurs Kyber',
    page: 'accelerators',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route pour la page de visualisation du cerveau
router.get('/brain', checkThermalMemory, (req, res) => {
  res.render('luna-brain', {
    title: 'Luna - Visualisation du Cerveau'
  });
});

// API pour obtenir l'état du système
router.get('/api/status', (req, res) => {
  // Simuler la collecte des métriques système
  const memoryUsage = process.memoryUsage();

  res.json({
    success: true,
    status: {
      system: {
        active: true,
        cpuUsage: Math.floor(20 + Math.random() * 15),
        memoryUsage: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
        uptime: process.uptime()
      },
      agent: {
        name: 'Louna',
        status: 'online',
        version: '1.0'
      },
      thermalMemory: {
        status: 'optimal',
        usage: Math.floor(60 + Math.random() * 20),
        zones: 6,
        temperature: Math.floor(65 + Math.random() * 10)
      },
      mcp: {
        internet: true,
        filesystem: true,
        hardware: true
      }
    }
  });
});

module.exports = router;
