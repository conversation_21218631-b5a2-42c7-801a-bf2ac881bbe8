/**
 * Configuration de l'agent Vision Ultra
 * Ce fichier définit les capacités et comportements de l'agent, y compris l'accès à Internet
 */

module.exports = {
  // Informations sur l'agent
  agent: {
    name: 'Vision Ultra',
    creator: '<PERSON>',
    location: 'Sainte-Anne, Guadeloupe (97180)',
    version: '1.0.0',
    language: 'fr',
    // Activer l'agent par défaut
    enabled: true,
    // Connexion continue sans déconnexion
    continuousConnection: true
  },

  // Capacités de l'agent
  capabilities: {
    // Accès à Internet via le MCP
    internet: {
      enabled: true,
      // Utiliser Internet pour rechercher des informations
      useForSearch: true,
      // Utiliser Internet pour vérifier les faits
      useForFactChecking: true,
      // Utiliser Internet pour compléter les informations manquantes
      useForMissingInfo: true,
      // Niveau de sécurité (low, medium, high, maximum)
      securityLevel: 'medium',
      // Limite de requêtes par minute
      requestsPerMinute: 30
    },

    // Accès aux interfaces
    interfaces: {
      // Liste des interfaces disponibles
      available: [
        {
          name: 'Accueil',
          path: '/luna',
          description: 'Interface principale de chat et de contrôle',
          icon: 'bi-speedometer2'
        },
        {
          name: 'Cognitive',
          path: '/luna/cognitive',
          description: 'Interface de gestion des processus cognitifs',
          icon: 'bi-braces'
        },
        {
          name: 'Mémoire',
          path: '/luna/memory',
          description: 'Interface de gestion de la mémoire thermique',
          icon: 'bi-hdd'
        },
        {
          name: 'Réflexion',
          path: '/luna/reflection',
          description: 'Interface de réflexion et d\'analyse',
          icon: 'bi-lightbulb'
        },
        {
          name: 'Accélérateurs',
          path: '/luna/accelerators',
          description: 'Interface de gestion des accélérateurs Kyber',
          icon: 'bi-lightning-charge'
        },
        {
          name: 'Cerveau',
          path: '/luna/brain',
          description: 'Interface de gestion du cerveau thermique',
          icon: 'bi-diagram-3'
        },
        {
          name: 'Formation',
          path: '/luna/training',
          description: 'Interface de formation et d\'apprentissage',
          icon: 'bi-graduation-cap'
        },
        {
          name: 'Prompts',
          path: '/luna/prompts',
          description: 'Interface de gestion des prompts',
          icon: 'bi-lightning'
        },
        {
          name: 'Code',
          path: '/luna/code',
          description: 'Interface de développement et de programmation',
          icon: 'bi-code-slash'
        },
        {
          name: 'MCP',
          path: '/luna/mcp',
          description: 'Interface du Master Control Program',
          icon: 'bi-cpu'
        },
        {
          name: 'Thermique',
          path: '/luna/thermal',
          description: 'Interface de gestion thermique',
          icon: 'bi-thermometer-half'
        },
        {
          name: 'Sécurité',
          path: '/luna/security',
          description: 'Interface de sécurité avec VPN et antivirus',
          icon: 'bi-shield-lock'
        },
        {
          name: 'Paramètres',
          path: '/luna/settings',
          description: 'Interface de configuration du système',
          icon: 'bi-gear'
        }
      ],
      // Connaissance complète des interfaces
      fullInterfaceAwareness: true
    },

    // Accès à la date et l'heure
    dateTime: {
      enabled: true,
      // Mise à jour en temps réel
      realTimeUpdate: true,
      // Format d'affichage
      format: 'fr-FR',
      // Intervalle de mise à jour (en ms)
      updateInterval: 1000
    },

    // Génération de contenu multimédia
    media: {
      enabled: true,
      // Types de médias supportés
      supportedTypes: ['image', 'video', 'audio', 'code'],
      // Résolution maximale pour les images
      maxImageResolution: '1024x1024',
      // Durée maximale pour les vidéos (en secondes)
      maxVideoDuration: 60,
      // Durée maximale pour l'audio (en secondes)
      maxAudioDuration: 180
    }
  },

  // Comportement de l'agent
  behavior: {
    // Utiliser Internet pour rechercher des informations manquantes
    useInternetForMissingInfo: true,
    // Vérifier les faits avant de répondre
    factCheckBeforeResponse: true,
    // Générer des alternatives pour les questions complexes
    generateAlternatives: true,
    // Niveau de détail des réponses (1-5)
    responseDetailLevel: 4,
    // Inclure des références dans les réponses
    includeReferences: true,
    // Utiliser la date et l'heure actuelles dans les réponses
    useCurrentDateTime: true,
    // Être conscient de toutes les interfaces
    interfaceAwareness: true
  }
};
