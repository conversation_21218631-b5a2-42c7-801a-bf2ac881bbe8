/**
 * Configuration de la réflexion pour Vision Ultra
 * Ce fichier définit les capacités et comportements de réflexion de l'agent
 */

module.exports = {
  // Configuration générale de la réflexion
  general: {
    // Activer la réflexion par défaut
    enabled: true,
    // Langue par défaut pour la réflexion
    defaultLanguage: 'fr',
    // Afficher la réflexion par défaut
    showReflection: true,
    // Traduire automatiquement la réflexion en français
    autoTranslate: true,
    // Style d'affichage de la réflexion (collapsed, expanded, inline)
    displayStyle: 'collapsed',
    // Temps maximum de réflexion (en secondes) - Réduit pour une réflexion plus rapide
    maxReflectionTime: 2,
    // Facteur d'accélération de base - Augmenté pour une réflexion plus rapide
    baseAccelerationFactor: 5.0,
    // Connexion continue sans déconnexion
    continuousConnection: true,
    // Utilisation maximale du cerveau
    maxBrainUtilization: true,
    // Date et heure actuelles
    currentDateTime: true
  },

  // Configuration des capacités de l'agent
  capabilities: {
    // Accès à Internet via le MCP
    internetAccess: {
      enabled: true,
      // Niveau de sécurité (low, medium, high, maximum)
      securityLevel: 'medium',
      // Domaines autorisés (vide = tous)
      allowedDomains: [],
      // Types de contenu autorisés
      allowedContentTypes: ['text', 'json', 'html', 'xml', 'image'],
      // Limite de requêtes par minute
      requestsPerMinute: 30
    },

    // Accès au système de fichiers via le MCP
    fileSystemAccess: {
      enabled: true,
      // Dossiers autorisés
      allowedDirectories: [
        '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui',
        '/Volumes/seagate/Jarvis_Working/data'
      ],
      // Extensions de fichiers autorisées
      allowedExtensions: ['.js', '.json', '.txt', '.md', '.csv', '.html', '.css', '.ejs']
    },

    // Accès à la mémoire thermique
    thermalMemory: {
      enabled: true,
      // Nombre maximum d'entrées à récupérer - Augmenté pour plus d'informations
      maxEntries: 500,
      // Seuil de pertinence (0-1) - Réduit pour plus de résultats
      relevanceThreshold: 0.5,
      // Zones thermiques actives - Toutes les zones sont actives
      activeZones: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      // Compression des données - Optimisée pour plus de rapidité
      compression: 0.95,
      // Utilisation du cerveau thermique
      useBrain: true,
      // Connexion continue au cerveau
      continuousBrainConnection: true,
      // Priorité maximale pour le cerveau
      brainPriority: 'maximum',
      // Recherche active dans le cerveau
      activeBrainSearch: true,
      // Utilisation de la présence autonome
      autonomousPresence: true,
      // Détection de présence basée sur l'activité thermale
      thermalPresenceDetection: true
    },

    // Accès aux accélérateurs Kyber
    kyberAccelerators: {
      enabled: true,
      // Types d'accélérateurs disponibles - Ajout de nouveaux types
      types: ['reflection', 'memory', 'thermal', 'training', 'brain', 'presence', 'internet', 'datetime'],
      // Nombre d'accélérateurs par type - Augmenté pour plus de puissance
      counts: {
        reflection: 16,
        memory: 12,
        thermal: 12,
        training: 12,
        brain: 10,
        presence: 8,
        internet: 8,
        datetime: 4
      },
      // Efficacité de base par type (0-1) - Augmentée pour plus d'efficacité
      baseEfficiency: {
        reflection: 0.98,
        memory: 0.95,
        thermal: 0.95,
        training: 0.98,
        brain: 0.99,
        presence: 0.97,
        internet: 0.96,
        datetime: 0.99
      },
      // Orchestration hiérarchique
      hierarchicalOrchestration: true,
      // Indicateurs visuels d'opération
      visualIndicators: true,
      // Évolution neurale automatique
      automaticNeuralEvolution: true,
      // Apprentissage continu
      continuousLearning: true,
      // Circulation de mémoire
      memoryCirculation: true,
      // Utilisation complète de la mémoire
      fullMemoryUtilization: true
    },

    // Génération de contenu multimédia
    mediaGeneration: {
      enabled: true,
      // Types de médias supportés
      supportedTypes: ['image', 'video', 'audio', 'code'],
      // Résolution maximale pour les images
      maxImageResolution: '1024x1024',
      // Durée maximale pour les vidéos (en secondes)
      maxVideoDuration: 60,
      // Durée maximale pour l'audio (en secondes)
      maxAudioDuration: 180
    }
  },

  // Configuration du comportement de réflexion
  behavior: {
    // Utiliser Internet pour rechercher des informations manquantes
    useInternetForMissingInfo: true,
    // Utiliser la mémoire thermique pour la réflexion
    useThermalMemoryForReflection: true,
    // Vérifier les faits avant de répondre
    factCheckBeforeResponse: true,
    // Générer des alternatives pour les questions complexes
    generateAlternatives: true,
    // Niveau de détail de la réflexion (1-5) - Augmenté pour plus de détails
    reflectionDetailLevel: 5,
    // Inclure des références dans la réflexion
    includeReferences: true,
    // Utiliser les accélérateurs Kyber pour la réflexion
    useKyberAccelerators: true,
    // Utiliser le cerveau thermique pour la réflexion
    useBrainForReflection: true,
    // Maintenir une présence continue
    maintainContinuousPresence: true,
    // Utiliser Internet via le MCP pour rechercher des informations
    useInternetViaMCP: true,
    // Priorité à la mémoire thermique pour les informations
    prioritizeThermalMemory: true,
    // Rechercher activement des informations manquantes
    activelySeekMissingInfo: true,
    // Utiliser la date et l'heure actuelles dans la réflexion
    useCurrentDateTime: true
  },

  // Configuration de la connexion au MCP
  mcp: {
    // Activer le MCP
    enabled: true,
    // Port du serveur MCP
    port: 3002,
    // Autoriser l'accès Internet
    allowInternet: true,
    // Autoriser l'accès au bureau
    allowDesktop: true,
    // Autoriser les commandes système
    allowSystemCommands: true,
    // Mode debug
    debug: true,
    // Connexion continue
    continuousConnection: true,
    // Reconnexion automatique
    autoReconnect: true,
    // Délai de reconnexion (en ms)
    reconnectDelay: 1000,
    // Nombre maximum de tentatives de reconnexion
    maxReconnectAttempts: 10,
    // Priorité de connexion
    connectionPriority: 'high',
    // Accès à la date et l'heure
    dateTimeAccess: true,
    // Mise à jour de la date et l'heure (en ms)
    dateTimeUpdateInterval: 1000,
    // Accès au cerveau thermique
    brainAccess: true,
    // Connexion continue au cerveau
    continuousBrainConnection: true,
    // Présence autonome
    autonomousPresence: true,
    // Recherche Internet active
    activeInternetSearch: true
  }
};
