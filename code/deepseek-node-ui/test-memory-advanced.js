/**
 * Test avancé pour la mémoire thermique de Luna
 * Ce script envoie une série de messages complexes et vérifie si la mémoire fonctionne correctement
 */

const fs = require('fs');
const path = require('path');
const io = require('socket.io-client');

// Configuration
const LUNA_SOCKET_URL = 'http://localhost:3001';
const MEMORY_FILE_PATH = path.join(__dirname, 'data/memory/thermal_memory.json');
const DELAY_BETWEEN_MESSAGES = 1000; // 1 seconde entre chaque message
const SOCKET_TIMEOUT = 15000; // 15 secondes de timeout

// Fonction pour envoyer un message à Luna via WebSocket
async function sendMessage(message) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`\n📤 Envoi du message: "${message.substring(0, 100)}${message.length > 100 ? '...' : ''}"`);

      // Connexion au serveur WebSocket
      const socket = io(LUNA_SOCKET_URL);

      // Gérer la connexion
      socket.on('connect', () => {
        console.log('Connecté au serveur WebSocket');

        // Envoyer le message
        socket.emit('luna message', { message });
      });

      // Gérer la réponse
      socket.on('luna response', (data) => {
        console.log(`📥 Réponse reçue: "${data.message.substring(0, 100)}${data.message.length > 100 ? '...' : ''}"`);
        socket.disconnect();
        resolve(data.message);
      });

      // Gérer les erreurs
      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion WebSocket:', error.message);
        socket.disconnect();
        reject(error);
      });

      // Timeout après 15 secondes
      setTimeout(() => {
        if (socket.connected) {
          socket.disconnect();
          reject(new Error(`Timeout: Pas de réponse après ${SOCKET_TIMEOUT/1000} secondes`));
        }
      }, SOCKET_TIMEOUT);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error.message);
      reject(error);
    }
  });
}

// Fonction pour attendre un certain temps
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction pour évaluer la réponse
function evaluateResponse(response, expectedInfo) {
  const score = expectedInfo.reduce((total, info) => {
    if (response.toLowerCase().includes(info.toLowerCase())) {
      console.log(`✅ La réponse contient "${info}"`);
      return total + 1;
    } else {
      console.log(`❌ La réponse ne contient pas "${info}"`);
      return total;
    }
  }, 0);

  const percentage = (score / expectedInfo.length) * 100;
  console.log(`Score: ${score}/${expectedInfo.length} (${percentage.toFixed(2)}%)`);

  return {
    score,
    percentage,
    expectedInfo,
    passed: percentage >= 70 // Considéré comme réussi si au moins 70% des informations sont présentes
  };
}

// Fonction principale de test
async function runAdvancedMemoryTest() {
  console.log('=== TEST AVANCÉ DE LA MÉMOIRE THERMIQUE ===');
  console.log('Ce test va envoyer une série de messages complexes et vérifier si la mémoire fonctionne correctement');

  try {
    // Phase 1: Envoi d'informations personnelles et professionnelles combinées
    console.log('\n--- Phase 1: Informations personnelles et professionnelles ---');

    const personalInfo = `Bonjour, je m'appelle Jean-Luc Picard. Je suis né le 13 juillet 1975 à La Barre, en France.
    J'habite maintenant au 15 rue de la Paix à Paris, dans le 2ème arrondissement.
    Je suis capitaine de vaisseau spatial et j'ai étudié à l'Académie de Starfleet où j'ai obtenu mon diplôme en 1997.`;

    await sendMessage(personalInfo);
    await delay(DELAY_BETWEEN_MESSAGES);

    // Phase 2: Informations sur la famille et les préférences
    console.log('\n--- Phase 2: Informations sur la famille et les préférences ---');

    const familyInfo = `Ma famille est composée de mon frère aîné Robert Picard qui gère le vignoble familial à La Barre.
    J'aime beaucoup la littérature, particulièrement les œuvres de Shakespeare.
    Ma boisson préférée est le "Earl Grey, chaud". Je joue de la flûte traversière.`;

    await sendMessage(familyInfo);
    await delay(DELAY_BETWEEN_MESSAGES);

    // Test 1: Question simple sur les informations personnelles
    console.log('\n--- Test 1: Question simple sur les informations personnelles ---');

    const response1 = await sendMessage("Comment je m'appelle et où est-ce que j'habite ?");
    await delay(DELAY_BETWEEN_MESSAGES);

    const test1Result = evaluateResponse(response1, [
      "Jean-Luc Picard",
      "Paris",
      "rue de la Paix"
    ]);

    // Test 2: Question sur les informations professionnelles
    console.log('\n--- Test 2: Question sur les informations professionnelles ---');

    const response2 = await sendMessage("Quel est mon métier et où ai-je étudié ?");
    await delay(DELAY_BETWEEN_MESSAGES);

    const test2Result = evaluateResponse(response2, [
      "capitaine",
      "vaisseau spatial",
      "Académie de Starfleet"
    ]);

    // Test 3: Question sur la famille et les préférences
    console.log('\n--- Test 3: Question sur la famille et les préférences ---');

    const response3 = await sendMessage("Qui est mon frère et quelle est ma boisson préférée ?");
    await delay(DELAY_BETWEEN_MESSAGES);

    const test3Result = evaluateResponse(response3, [
      "Robert Picard",
      "Earl Grey",
      "chaud"
    ]);

    // Test 4: Question complexe combinant plusieurs types d'informations
    console.log('\n--- Test 4: Question complexe combinant plusieurs types d\'informations ---');

    const response4 = await sendMessage("Peux-tu me faire un résumé complet de qui je suis, incluant mon nom, mon métier, ma famille et mes préférences ?");
    await delay(DELAY_BETWEEN_MESSAGES);

    const test4Result = evaluateResponse(response4, [
      "Jean-Luc Picard",
      "capitaine",
      "vaisseau spatial",
      "Robert",
      "frère",
      "Earl Grey"
    ]);

    // Résultats finaux
    console.log('\n=== RÉSULTATS FINAUX ===');

    const tests = [
      { name: "Test 1: Informations personnelles", result: test1Result },
      { name: "Test 2: Informations professionnelles", result: test2Result },
      { name: "Test 3: Famille et préférences", result: test3Result },
      { name: "Test 4: Question complexe", result: test4Result }
    ];

    tests.forEach(test => {
      console.log(`${test.name}: ${test.result.passed ? '✅ RÉUSSI' : '❌ ÉCHOUÉ'} (${test.result.percentage.toFixed(2)}%)`);
    });

    const overallScore = tests.reduce((total, test) => total + test.result.score, 0);
    const maxScore = tests.reduce((total, test) => total + test.result.expectedInfo.length, 0);
    const overallPercentage = (overallScore / maxScore) * 100;

    console.log(`\nScore global: ${overallScore}/${maxScore} (${overallPercentage.toFixed(2)}%)`);

    if (overallPercentage >= 80) {
      console.log('✅ TEST GLOBAL RÉUSSI: La mémoire thermique fonctionne très bien');
    } else if (overallPercentage >= 60) {
      console.log('⚠️ TEST GLOBAL PARTIELLEMENT RÉUSSI: La mémoire thermique fonctionne mais pourrait être améliorée');
    } else {
      console.log('❌ TEST GLOBAL ÉCHOUÉ: La mémoire thermique ne fonctionne pas correctement');
    }

  } catch (error) {
    console.error('Erreur lors de l\'exécution du test:', error);
  }
}

// Exécuter le test
runAdvancedMemoryTest().catch(error => {
  console.error('Erreur lors de l\'exécution du test:', error);
});
