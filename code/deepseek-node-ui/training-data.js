/**
 * Script de formation accélérée pour l'agent Luna
 * Ce script envoie une série de questions et réponses à l'agent Luna
 * pour enrichir sa mémoire thermique et stimuler son réseau neuronal
 */

const axios = require('axios');

// Configuration
const LUNA_API_URL = 'http://localhost:3001/api/chat';
const DELAY_BETWEEN_MESSAGES = 5000; // 5 secondes entre chaque message

// Données de formation structurées par domaine
const trainingData = [
  // MATHÉMATIQUES
  {
    question: "Comment résoudre une équation du second degré ax² + bx + c = 0 ?",
    answer: "Pour résoudre une équation du second degré ax² + bx + c = 0, on calcule d'abord le discriminant Δ = b² - 4ac. Si Δ > 0, il y a deux solutions réelles distinctes : x₁ = (-b - √Δ) / 2a et x₂ = (-b + √Δ) / 2a. Si Δ = 0, il y a une solution réelle double : x = -b / 2a. Si Δ < 0, il n'y a pas de solution réelle, mais deux solutions complexes conjuguées."
  },
  {
    question: "Qu'est-ce que le théorème de Pythagore et comment l'appliquer ?",
    answer: "Le théorème de Pythagore établit que dans un triangle rectangle, le carré de la longueur de l'hypoténuse (le côté opposé à l'angle droit) est égal à la somme des carrés des longueurs des deux autres côtés. Si a et b sont les cathètes et c l'hypoténuse, alors c² = a² + b². Pour l'appliquer, il faut identifier le triangle rectangle, repérer l'hypoténuse (côté opposé à l'angle droit) et utiliser la formule pour trouver le côté manquant."
  },

  // PHYSIQUE
  {
    question: "Expliquez la théorie de la relativité restreinte d'Einstein.",
    answer: "La théorie de la relativité restreinte d'Einstein, publiée en 1905, repose sur deux postulats fondamentaux : 1) Les lois de la physique sont les mêmes dans tous les référentiels inertiels, et 2) La vitesse de la lumière dans le vide est constante et indépendante du mouvement de la source ou de l'observateur. Cette théorie introduit des concepts révolutionnaires comme la dilatation du temps (le temps s'écoule plus lentement pour un objet en mouvement), la contraction des longueurs, et l'équivalence masse-énergie exprimée par la célèbre équation E=mc²."
  },
  {
    question: "Comment fonctionne un réacteur nucléaire ?",
    answer: "Un réacteur nucléaire fonctionne sur le principe de la fission nucléaire contrôlée. Dans le cœur du réacteur, des noyaux d'uranium-235 ou de plutonium-239 sont bombardés par des neutrons, provoquant leur fission. Cette réaction libère de l'énergie sous forme de chaleur, ainsi que d'autres neutrons qui entretiennent la réaction en chaîne. Des barres de contrôle en matériau absorbant les neutrons (comme le bore ou le cadmium) régulent la réaction. La chaleur produite transforme l'eau en vapeur, qui fait tourner des turbines reliées à des générateurs électriques. Le modérateur (souvent de l'eau) ralentit les neutrons pour optimiser la probabilité de fission."
  },

  // INFORMATIQUE
  {
    question: "Expliquez le fonctionnement de l'algorithme de tri rapide (quicksort).",
    answer: "Le tri rapide (quicksort) est un algorithme de tri par division et conquête très efficace. Son fonctionnement : 1) Choisir un élément pivot dans le tableau. 2) Réorganiser le tableau de sorte que tous les éléments inférieurs au pivot soient placés avant lui, et tous les éléments supérieurs après. 3) Appliquer récursivement l'algorithme aux sous-tableaux formés à gauche et à droite du pivot. Sa complexité moyenne est de O(n log n), mais peut atteindre O(n²) dans le pire des cas. Quicksort est généralement plus rapide en pratique que d'autres algorithmes de tri de même complexité grâce à sa localité de référence et son faible surcoût."
  },
  {
    question: "Qu'est-ce que la programmation orientée objet et quels sont ses principes fondamentaux ?",
    answer: "La programmation orientée objet (POO) est un paradigme de programmation basé sur le concept d'objets contenant des données et des méthodes. Ses quatre principes fondamentaux sont : 1) L'encapsulation : regroupement des données et des méthodes qui les manipulent en une seule unité (classe) et restriction de l'accès direct aux données. 2) L'héritage : mécanisme permettant à une classe d'hériter des propriétés et méthodes d'une autre classe. 3) Le polymorphisme : capacité d'objets de différentes classes à répondre à la même interface. 4) L'abstraction : simplification de systèmes complexes en modélisant des classes appropriées."
  },

  // BIOLOGIE
  {
    question: "Comment fonctionne la réplication de l'ADN ?",
    answer: "La réplication de l'ADN est un processus semi-conservatif où chaque brin sert de modèle pour créer un nouveau brin complémentaire. Le processus commence par l'ouverture de la double hélice par l'hélicase, formant une fourche de réplication. L'ADN polymérase III ajoute des nucléotides complémentaires (A-T, G-C) dans la direction 5' vers 3'. Sur le brin continu, la synthèse est continue, tandis que sur le brin discontinu, elle se fait par fragments d'Okazaki qui sont ensuite liés par l'ADN ligase. L'ADN polymérase I remplace les amorces d'ARN par de l'ADN. Ce processus hautement fidèle inclut des mécanismes de correction d'erreurs pour maintenir l'intégrité du génome."
  },
  {
    question: "Expliquez le fonctionnement du système immunitaire humain.",
    answer: "Le système immunitaire humain est un réseau complexe de cellules, tissus et organes qui protège l'organisme contre les agents pathogènes. Il comprend deux lignes de défense principales : l'immunité innée et l'immunité adaptative. L'immunité innée, première ligne de défense, inclut les barrières physiques (peau, muqueuses), les cellules phagocytaires (neutrophiles, macrophages), les cellules NK et le système du complément. L'immunité adaptative, plus spécifique, implique les lymphocytes T et B. Les lymphocytes B produisent des anticorps spécifiques contre les antigènes, tandis que les lymphocytes T cytotoxiques détruisent les cellules infectées. Les lymphocytes T auxiliaires coordonnent la réponse immunitaire. Après une infection, des cellules mémoires persistent, permettant une réponse plus rapide lors d'une exposition ultérieure au même pathogène."
  },

  // CHIMIE
  {
    question: "Expliquez la théorie des orbitales moléculaires.",
    answer: "La théorie des orbitales moléculaires (TOM) explique la liaison chimique par la combinaison d'orbitales atomiques pour former des orbitales moléculaires délocalisées sur l'ensemble de la molécule. Lorsque deux orbitales atomiques se combinent, elles forment deux orbitales moléculaires : une liante (de plus basse énergie) et une anti-liante (de plus haute énergie). Les électrons occupent d'abord les orbitales de plus basse énergie. L'ordre de liaison, calculé comme (nombre d'électrons dans les orbitales liantes - nombre d'électrons dans les orbitales anti-liantes)/2, détermine la stabilité de la liaison. La TOM permet d'expliquer des propriétés comme le paramagnétisme de O₂ que la théorie de la liaison de valence ne peut pas justifier."
  },
  {
    question: "Comment fonctionne la spectroscopie RMN et quelles informations fournit-elle sur la structure moléculaire ?",
    answer: "La spectroscopie par Résonance Magnétique Nucléaire (RMN) analyse l'interaction entre les noyaux atomiques possédant un spin non nul (comme ¹H, ¹³C) et un champ magnétique externe. Lorsque ces noyaux sont placés dans un champ magnétique puissant, leurs spins s'alignent parallèlement ou antiparallèlement au champ. L'application d'une radiofréquence spécifique provoque une résonance, entraînant un changement d'orientation du spin. Le signal RMN résultant dépend de l'environnement électronique local du noyau (blindage). La RMN fournit des informations cruciales sur la structure moléculaire : le déplacement chimique révèle l'environnement électronique des atomes, les constantes de couplage indiquent les interactions entre noyaux voisins, et l'intégration des signaux quantifie les proportions relatives de différents types d'atomes."
  },

  // ÉCONOMIE
  {
    question: "Expliquez la théorie de l'offre et de la demande et son impact sur la formation des prix.",
    answer: "La théorie de l'offre et de la demande est un modèle économique fondamental qui explique comment le prix d'un bien ou service est déterminé sur un marché concurrentiel. La demande représente la quantité qu'acheteurs sont disposés à acquérir à différents prix (relation généralement inverse entre prix et quantité demandée). L'offre représente la quantité que vendeurs sont prêts à fournir à différents prix (relation généralement directe entre prix et quantité offerte). Le prix d'équilibre se forme au point d'intersection des courbes d'offre et de demande, où quantité offerte égale quantité demandée. Des facteurs externes peuvent déplacer ces courbes : changements de revenus, préférences, coûts de production, technologies, etc. Ces déplacements entraînent de nouveaux équilibres avec des prix et quantités différents. Ce mécanisme permet une allocation efficace des ressources dans une économie de marché."
  },
  {
    question: "Qu'est-ce que l'inflation et quelles sont ses causes et conséquences ?",
    answer: "L'inflation est l'augmentation générale et durable du niveau des prix des biens et services dans une économie. Ses principales causes incluent : 1) L'inflation par la demande (excès de demande globale par rapport à l'offre), 2) L'inflation par les coûts (augmentation des coûts de production comme salaires ou matières premières), 3) L'inflation monétaire (croissance excessive de la masse monétaire), et 4) L'inflation importée (hausse des prix des produits importés). Ses conséquences sont multiples : érosion du pouvoir d'achat, redistribution arbitraire des richesses entre créanciers et débiteurs, incertitude économique affectant l'investissement, distorsions fiscales, et potentiellement spirale inflationniste si non maîtrisée. Les banques centrales luttent contre l'inflation excessive principalement par des politiques monétaires restrictives (hausse des taux d'intérêt, réduction de la masse monétaire)."
  },

  // PHILOSOPHIE
  {
    question: "Expliquez le concept d'impératif catégorique de Kant.",
    answer: "L'impératif catégorique est le concept central de la philosophie morale d'Emmanuel Kant, développé dans sa 'Critique de la raison pratique' (1788). Contrairement aux impératifs hypothétiques (conditionnels), l'impératif catégorique est un commandement moral absolu, inconditionnel, qui s'applique universellement. Kant le formule principalement de trois façons : 1) 'Agis uniquement d'après la maxime qui fait que tu peux vouloir en même temps qu'elle devienne une loi universelle' - principe d'universalisation. 2) 'Agis de façon à traiter l'humanité, aussi bien dans ta personne que dans toute autre, toujours en même temps comme une fin, et jamais simplement comme un moyen' - principe de respect de la dignité humaine. 3) 'Agis selon les maximes d'un membre qui légifère universellement en vue d'un règne des fins simplement possible' - principe d'autonomie. Pour Kant, une action n'est moralement bonne que si elle est accomplie par devoir, conformément à l'impératif catégorique, et non par intérêt ou inclination."
  },
  {
    question: "Comparez les approches épistémologiques du rationalisme et de l'empirisme.",
    answer: "Le rationalisme et l'empirisme sont deux approches épistémologiques fondamentales qui divergent sur l'origine et la nature de la connaissance. Le rationalisme, représenté par Descartes, Spinoza et Leibniz, soutient que la raison est la source principale de connaissance. Il affirme l'existence de connaissances innées, indépendantes de l'expérience, et accessibles par déduction logique. La vérité, selon cette approche, doit être certaine et nécessaire. L'empirisme, défendu par Locke, Berkeley et Hume, considère l'expérience sensorielle comme la source primaire de connaissance. Il rejette l'idée de connaissances innées ('tabula rasa' de Locke) et privilégie l'induction à partir d'observations. Pour les empiristes, toute connaissance dérive des perceptions sensorielles. Kant a tenté une synthèse avec son 'idéalisme transcendantal', reconnaissant que si toute connaissance commence avec l'expérience, elle ne dérive pas nécessairement toute de l'expérience, car l'esprit structure activement les données sensorielles selon des catégories a priori."
  },

  // PROBLÈMES COMPLEXES
  {
    question: "Comment résoudre le problème du voyageur de commerce pour 10 villes ?",
    answer: "Le problème du voyageur de commerce (TSP) consiste à trouver le chemin le plus court permettant de visiter n villes une seule fois et revenir au point de départ. Pour 10 villes, une approche exacte serait d'évaluer toutes les permutations possibles ((10-1)!/2 = 181 440 chemins), mais c'est impraticable pour de grandes instances. Des méthodes heuristiques plus efficaces incluent : 1) L'algorithme glouton du plus proche voisin : partir d'une ville et toujours aller à la ville non visitée la plus proche. 2) L'heuristique d'insertion : commencer par un sous-tour et insérer progressivement les villes restantes de façon optimale. 3) Les algorithmes génétiques : simuler l'évolution en croisant et mutant des solutions. 4) Le recuit simulé : accepter occasionnellement des solutions moins bonnes pour éviter les optima locaux. 5) L'optimisation par colonies de fourmis : simuler le comportement des fourmis déposant des phéromones sur les chemins les plus courts. Pour 10 villes, ces méthodes peuvent trouver des solutions très proches de l'optimal en temps raisonnable."
  },
  {
    question: "Comment aborder la résolution d'équations différentielles non linéaires ?",
    answer: "La résolution d'équations différentielles non linéaires nécessite diverses approches car il n'existe pas de méthode générale. Voici les principales stratégies : 1) Méthodes analytiques : substitutions de variables, facteurs intégrants, séparation des variables pour certaines formes spécifiques. 2) Linéarisation : approximer localement l'équation non linéaire par une équation linéaire autour d'un point d'équilibre. 3) Méthodes qualitatives : analyse du plan de phase, théorie de la stabilité de Lyapunov pour comprendre le comportement global sans résolution explicite. 4) Méthodes numériques : Runge-Kutta, méthodes à pas multiples, méthodes adaptatives qui ajustent automatiquement le pas d'intégration. 5) Méthodes perturbatives : développements asymptotiques pour les systèmes avec petits paramètres. 6) Méthodes spectrales : décomposition en séries de Fourier ou polynômes orthogonaux. L'approche dépend fortement du type spécifique d'équation, du contexte du problème et de la précision requise. Pour les systèmes complexes, une combinaison de ces méthodes est souvent nécessaire."
  }
];

// Fonction pour envoyer un message à l'agent Luna
async function sendMessage(message) {
  try {
    const response = await axios.post(LUNA_API_URL, {
      message: message,
      model: 'deepseek-r1:7b' // Utiliser le modèle par défaut
    });

    console.log(`Message envoyé: "${message.substring(0, 50)}..."`);
    console.log(`Réponse reçue: "${response.data.response.substring(0, 50)}..."`);

    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message:', error.message);
    return null;
  }
}

// Fonction principale pour exécuter la formation
async function runTraining() {
  console.log('Démarrage de la formation accélérée pour l\'agent Luna...');

  for (const [index, item] of trainingData.entries()) {
    console.log(`\nTraitement de l'item de formation ${index + 1}/${trainingData.length}`);

    // Envoyer la question
    await sendMessage(item.question);

    // Attendre un peu pour simuler une conversation naturelle
    await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_MESSAGES));

    // Envoyer la réponse comme si c'était une correction ou un complément d'information
    await sendMessage(`Voici une réponse complète à cette question : ${item.answer}`);

    // Attendre avant de passer à la prochaine question
    await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_MESSAGES * 2));
  }

  console.log('\nFormation accélérée terminée !');
}

// Exécuter la formation
runTraining().catch(console.error);
