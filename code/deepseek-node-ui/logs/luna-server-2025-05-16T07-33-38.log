Configuration de mémoire thermique chargée
80 mémoires chargées
1 rêves neuronaux chargés
Réseau neuronal chargé: génération 94, 9 connexions
Accélérateurs Kyber chargés: 36 accélérateurs actifs
Réseau d'accélérateurs Kyber activé
Nettoyage automatique et archivage initialisés
💭 Cycle de rêve et évolution neuronale initialisés dans toutes les zones
Initialisation des accélérateurs Kyber en cascade
Initialisation des moniteurs pour les accélérateurs Kyber
Module de mémoire thermique et réseau d'accélérateurs Kyber initialisés
Mémoire thermique initialisée
Système d'accélérateurs Kyber initialisé
Initialisation des accélérateurs Kyber en cascade
Configuration de 8 accélérateurs de réflexion en série
Accélérateur de réflexion kyber-reflection-1747380818418-224 configuré en position 1 avec efficacité 149.7%
Accélérateur de réflexion kyber-reflection-1747380818418-827 configuré en position 2 avec efficacité 138.6%
Accélérateur de réflexion kyber-reflection-1747380818418-857 configuré en position 3 avec efficacité 131.1%
Accélérateur de réflexion kyber-reflection-1747380818418-863 configuré en position 4 avec efficacité 122.9%
Accélérateur de réflexion kyber-reflection-1747380818418-960 configuré en position 5 avec efficacité 115.1%
Accélérateur de réflexion kyber-reflection-1747380818418-481 configuré en position 6 avec efficacité 107.7%
Accélérateur de réflexion kyber-reflection-1747380818418-177 configuré en position 7 avec efficacité 100.3%
Accélérateur de réflexion kyber-reflection-1747380818418-112 configuré en position 8 avec efficacité 93.1%
Configuration en série des accélérateurs de réflexion terminée
Accélérateurs Kyber initialisés
[SlidingThermalZones] Système de zones thermiques glissantes démarré
🌡️ Système de zones thermiques glissantes activé
💡 Les 6 zones se déplacent comme un curseur en fonction de la température réelle
Serveur MCP initialisé avec les options: {
  port: 3002,
  allowInternet: true,
  allowDesktop: true,
  allowSystemCommands: true,
  debug: true
}
Configuration MCP chargée
Module de routes pour les accélérateurs de réflexion initialisé
Erreur lors du démarrage du serveur MCP: Error: listen EADDRINUSE: address already in use :::3002
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at /Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/mcp/mcp-server.js:393:19
    at new Promise (<anonymous>)
    at MCPServer.start (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/mcp/mcp-server.js:392:12)
    at Object.<anonymous> (/Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/server-luna.js:223:13)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
    at Object..js (node:internal/modules/cjs/loader:1899:10)
    at Module.load (node:internal/modules/cjs/loader:1469:32) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3002
}
❌ Erreur: Le port 3001 est déjà utilisé par une autre application.
Veuillez arrêter cette application ou utiliser un port différent.
