# Guide de sécurité pour le serveur Luna

Ce document explique les mesures de sécurité mises en place pour éviter les problèmes avec le serveur Luna.

## Mesures de sécurité implémentées

### 1. Validation de syntaxe automatique

Le script `validate-syntax.js` vérifie automatiquement la syntaxe des fichiers JavaScript critiques avant de démarrer le serveur. Il vérifie également que le nombre d'accolades ouvrantes et fermantes est équilibré dans le fichier `server-luna.js`.

```bash
# Pour exécuter la validation manuellement
npm run validate
```

### 2. Démarrage sécurisé

Le script `start-luna.js` offre un démarrage sécurisé du serveur avec les fonctionnalités suivantes :
- Validation de syntaxe avant le démarrage
- Redirection des logs vers un fichier
- Redémarrage automatique en cas d'erreur (jusqu'à 3 tentatives)
- Gestion des signaux d'arrêt

```bash
# Pour démarrer le serveur Luna de manière sécurisée
npm run start-luna
```

### 3. Sauvegarde automatique

Le script `backup-files.js` crée des sauvegardes des fichiers critiques avant chaque modification.

```bash
# Pour créer des sauvegardes manuellement
npm run backup
```

### 4. Restauration des sauvegardes

Le script `restore-backup.js` permet de restaurer un fichier à partir d'une sauvegarde spécifique ou de restaurer la dernière sauvegarde d'un fichier.

```bash
# Pour restaurer un fichier à partir d'une sauvegarde
npm run restore
```

### 5. Gestion globale des erreurs

Le serveur Luna inclut maintenant une gestion globale des erreurs non capturées et des rejets de promesses non gérés. Les erreurs sont enregistrées dans des fichiers de log dans le répertoire `logs`.

## Bonnes pratiques

### Avant de modifier le code

1. Créez une sauvegarde des fichiers que vous allez modifier :
   ```bash
   npm run backup
   ```

2. Vérifiez la syntaxe après vos modifications :
   ```bash
   npm run validate
   ```

3. Utilisez le démarrage sécurisé pour lancer le serveur :
   ```bash
   npm run start-luna
   ```

### En cas de problème

1. Arrêtez le serveur (Ctrl+C)

2. Restaurez la dernière sauvegarde fonctionnelle :
   ```bash
   npm run restore
   ```

3. Consultez les logs d'erreur dans le répertoire `logs`

## Structure des répertoires

- `logs/` : Contient les logs du serveur et les erreurs
- `backups/` : Contient les sauvegardes des fichiers critiques

## Conseils pour éviter les erreurs

1. **Indentation cohérente** : Utilisez une indentation cohérente dans tout le code (2 espaces recommandés)

2. **Accolades équilibrées** : Assurez-vous que chaque accolade ouvrante `{` a une accolade fermante `}` correspondante

3. **Tests progressifs** : Testez vos modifications par petites étapes plutôt que de faire de nombreux changements à la fois

4. **Commentaires clairs** : Ajoutez des commentaires pour expliquer le code complexe

5. **Gestion des erreurs** : Utilisez try/catch pour gérer les erreurs potentielles

## En cas d'urgence

Si le serveur ne démarre pas du tout, vous pouvez toujours restaurer manuellement les fichiers à partir des sauvegardes dans le répertoire `backups/`.
