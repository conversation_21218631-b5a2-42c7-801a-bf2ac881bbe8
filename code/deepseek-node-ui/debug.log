=== JOURNAL DE DÉBOGAGE DÉMARRÉ LE 2025-05-15T21:54:59.159Z ===

[2025-05-15T21:54:59.161Z] Journal de débogage initialisé
[2025-05-15T21:54:59.163Z] Serveur démarré sur http://localhost:3000
[2025-05-15T21:55:44.562Z] Accès à la page d'accueil
[2025-05-15T21:55:44.629Z] Nouveau client connecté
{ socketId: 'jKEfvUdf_uk0OeNgAAAC' }
[2025-05-15T21:55:44.630Z] Vérification si Ollama est en cours d'exécution...
[2025-05-15T21:55:44.630Z] Tentative de connexion à http://localhost:11434/api/version
[2025-05-15T21:55:44.637Z] Nouveau client connecté
{ socketId: '1HBpUcDrKoSpvxLKAAAD' }
[2025-05-15T21:55:44.637Z] Vérification si Ollama est en cours d'exécution...
[2025-05-15T21:55:44.637Z] Tentative de connexion à http://localhost:11434/api/version
[2025-05-15T21:55:44.641Z] Ollama est en cours d'exécution (API)
{ version: '0.6.6' }
[2025-05-15T21:55:44.642Z] Statut d'Ollama envoyé au client
{ isRunning: true }
[2025-05-15T21:55:44.642Z] Récupération des modèles disponibles...
[2025-05-15T21:55:44.642Z] Tentative de récupération des modèles via http://localhost:11434/api/tags
[2025-05-15T21:55:44.642Z] Ollama est en cours d'exécution (API)
{ version: '0.6.6' }
[2025-05-15T21:55:44.642Z] Tentative de récupération des modèles via http://localhost:11434/api/tags
[2025-05-15T21:55:44.642Z] Récupération des modèles disponibles...
[2025-05-15T21:55:44.642Z] Statut d'Ollama envoyé au client
{ isRunning: true }
[2025-05-15T21:55:44.644Z] Modèles récupérés (tags)
{
  models: [
    {
      name: 'incept5/llama3.1-claude:latest',
      model: 'incept5/llama3.1-claude:latest',
      modified_at: '2025-05-15T07:16:25.596999071-04:00',
      size: 4661237026,
      digest: '4ba850d59c62aec94b7725677b98342e84dc2d450a4ffa42e7b3ff60c16a729f',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'llama',
        families: [ 'llama' ],
        parameter_size: '8.0B',
        quantization_level: 'Q4_0'
      }
    },
    {
      name: 'deepseek-r1:7b',
      model: 'deepseek-r1:7b',
      modified_at: '2025-05-15T00:34:18.581141403-04:00',
      size: 4683075271,
      digest: '0a8c266910232fd3291e71e5ba1e058cc5af9d411192cf88b6d30e92b6e73163',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'qwen2',
        families: [ 'qwen2' ],
        parameter_size: '7.6B',
        quantization_level: 'Q4_K_M'
      }
    }
  ]
}
[2025-05-15T21:55:44.645Z] Modèles envoyés au client
{
  models: [
    {
      name: 'incept5/llama3.1-claude:latest',
      model: 'incept5/llama3.1-claude:latest',
      modified_at: '2025-05-15T07:16:25.596999071-04:00',
      size: 4661237026,
      digest: '4ba850d59c62aec94b7725677b98342e84dc2d450a4ffa42e7b3ff60c16a729f',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'llama',
        families: [ 'llama' ],
        parameter_size: '8.0B',
        quantization_level: 'Q4_0'
      }
    },
    {
      name: 'deepseek-r1:7b',
      model: 'deepseek-r1:7b',
      modified_at: '2025-05-15T00:34:18.581141403-04:00',
      size: 4683075271,
      digest: '0a8c266910232fd3291e71e5ba1e058cc5af9d411192cf88b6d30e92b6e73163',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'qwen2',
        families: [ 'qwen2' ],
        parameter_size: '7.6B',
        quantization_level: 'Q4_K_M'
      }
    }
  ]
}
[2025-05-15T21:55:44.645Z] Modèles envoyés au client
{
  models: [
    {
      name: 'incept5/llama3.1-claude:latest',
      model: 'incept5/llama3.1-claude:latest',
      modified_at: '2025-05-15T07:16:25.596999071-04:00',
      size: 4661237026,
      digest: '4ba850d59c62aec94b7725677b98342e84dc2d450a4ffa42e7b3ff60c16a729f',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'llama',
        families: [ 'llama' ],
        parameter_size: '8.0B',
        quantization_level: 'Q4_0'
      }
    },
    {
      name: 'deepseek-r1:7b',
      model: 'deepseek-r1:7b',
      modified_at: '2025-05-15T00:34:18.581141403-04:00',
      size: 4683075271,
      digest: '0a8c266910232fd3291e71e5ba1e058cc5af9d411192cf88b6d30e92b6e73163',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'qwen2',
        families: [ 'qwen2' ],
        parameter_size: '7.6B',
        quantization_level: 'Q4_K_M'
      }
    }
  ]
}
[2025-05-15T21:55:44.645Z] Modèles récupérés (tags)
{
  models: [
    {
      name: 'incept5/llama3.1-claude:latest',
      model: 'incept5/llama3.1-claude:latest',
      modified_at: '2025-05-15T07:16:25.596999071-04:00',
      size: 4661237026,
      digest: '4ba850d59c62aec94b7725677b98342e84dc2d450a4ffa42e7b3ff60c16a729f',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'llama',
        families: [ 'llama' ],
        parameter_size: '8.0B',
        quantization_level: 'Q4_0'
      }
    },
    {
      name: 'deepseek-r1:7b',
      model: 'deepseek-r1:7b',
      modified_at: '2025-05-15T00:34:18.581141403-04:00',
      size: 4683075271,
      digest: '0a8c266910232fd3291e71e5ba1e058cc5af9d411192cf88b6d30e92b6e73163',
      details: {
        parent_model: '',
        format: 'gguf',
        family: 'qwen2',
        families: [ 'qwen2' ],
        parameter_size: '7.6B',
        quantization_level: 'Q4_K_M'
      }
    }
  ]
}
