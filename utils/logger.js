/**
 * Système de logging avancé pour l'application Louna
 * Gestion des logs avec niveaux, rotation et formatage
 */

const fs = require('fs');
const path = require('path');
const { getConfig } = require('../config/app-config');

class Logger {
  constructor() {
    this.config = getConfig('logging');
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };
    
    this.colors = {
      error: '\x1b[31m', // Rouge
      warn: '\x1b[33m',  // Jaune
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[35m', // Magenta
      trace: '\x1b[37m', // Blanc
      reset: '\x1b[0m'
    };
    
    this.currentLevel = this.levels[this.config.level] || this.levels.info;
    this.logBuffer = [];
    this.maxBufferSize = 1000;
    
    // Créer le dossier de logs si nécessaire
    if (this.config.file.enabled) {
      this.ensureLogDirectory();
    }
    
    // Démarrer le flush périodique
    this.startPeriodicFlush();
  }

  /**
   * Crée le dossier de logs si nécessaire
   */
  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.config.file.path)) {
        fs.mkdirSync(this.config.file.path, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de la création du dossier de logs:', error.message);
    }
  }

  /**
   * Démarre le flush périodique des logs
   */
  startPeriodicFlush() {
    setInterval(() => {
      this.flushLogs();
    }, 5000); // Flush toutes les 5 secondes
  }

  /**
   * Formate un message de log
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const component = meta.component || 'APP';
    const userId = meta.userId || 'system';
    
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      component,
      userId,
      message,
      meta: Object.keys(meta).length > 0 ? meta : undefined
    };
    
    return logEntry;
  }

  /**
   * Formate pour la console
   */
  formatConsoleMessage(logEntry) {
    const color = this.colors[logEntry.level.toLowerCase()] || '';
    const reset = this.colors.reset;
    
    let formatted = '';
    
    if (this.config.console.timestamp) {
      formatted += `[${logEntry.timestamp}] `;
    }
    
    if (this.config.console.colorize) {
      formatted += `${color}[${logEntry.level}]${reset} `;
    } else {
      formatted += `[${logEntry.level}] `;
    }
    
    formatted += `[${logEntry.component}] ${logEntry.message}`;
    
    if (logEntry.meta) {
      formatted += ` ${JSON.stringify(logEntry.meta)}`;
    }
    
    return formatted;
  }

  /**
   * Écrit un log
   */
  log(level, message, meta = {}) {
    if (this.levels[level] > this.currentLevel) {
      return; // Niveau trop bas, ignorer
    }
    
    const logEntry = this.formatMessage(level, message, meta);
    
    // Ajouter au buffer
    this.logBuffer.push(logEntry);
    
    // Limiter la taille du buffer
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer = this.logBuffer.slice(-this.maxBufferSize);
    }
    
    // Affichage console
    if (this.config.console.enabled) {
      const consoleMessage = this.formatConsoleMessage(logEntry);
      
      switch (level) {
        case 'error':
          console.error(consoleMessage);
          break;
        case 'warn':
          console.warn(consoleMessage);
          break;
        default:
          console.log(consoleMessage);
      }
    }
    
    // Flush immédiat pour les erreurs
    if (level === 'error') {
      this.flushLogs();
    }
  }

  /**
   * Flush les logs vers le fichier
   */
  flushLogs() {
    if (!this.config.file.enabled || this.logBuffer.length === 0) {
      return;
    }
    
    try {
      const logFile = path.join(this.config.file.path, `louna-${new Date().toISOString().split('T')[0]}.log`);
      const logLines = this.logBuffer.map(entry => JSON.stringify(entry)).join('\n') + '\n';
      
      fs.appendFileSync(logFile, logLines);
      this.logBuffer = [];
      
      // Rotation des logs si nécessaire
      this.rotateLogs();
    } catch (error) {
      console.error('Erreur lors de l\'écriture des logs:', error.message);
    }
  }

  /**
   * Rotation des fichiers de logs
   */
  rotateLogs() {
    try {
      const logFiles = fs.readdirSync(this.config.file.path)
        .filter(file => file.startsWith('louna-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.config.file.path, file),
          stats: fs.statSync(path.join(this.config.file.path, file))
        }))
        .sort((a, b) => b.stats.mtime - a.stats.mtime);
      
      // Supprimer les anciens fichiers
      if (logFiles.length > this.config.file.maxFiles) {
        const filesToDelete = logFiles.slice(this.config.file.maxFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
        });
      }
      
      // Vérifier la taille des fichiers
      logFiles.forEach(file => {
        const maxSizeBytes = this.parseSize(this.config.file.maxSize);
        if (file.stats.size > maxSizeBytes) {
          // Archiver le fichier
          const archiveName = file.name.replace('.log', `-${Date.now()}.log`);
          const archivePath = path.join(this.config.file.path, 'archive', archiveName);
          
          // Créer le dossier archive si nécessaire
          const archiveDir = path.dirname(archivePath);
          if (!fs.existsSync(archiveDir)) {
            fs.mkdirSync(archiveDir, { recursive: true });
          }
          
          fs.renameSync(file.path, archivePath);
        }
      });
    } catch (error) {
      console.error('Erreur lors de la rotation des logs:', error.message);
    }
  }

  /**
   * Parse une taille (ex: "10MB" -> bytes)
   */
  parseSize(sizeStr) {
    const units = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };
    
    const match = sizeStr.match(/^(\d+)([A-Z]+)$/);
    if (!match) return 10 * 1024 * 1024; // 10MB par défaut
    
    const [, size, unit] = match;
    return parseInt(size) * (units[unit] || 1);
  }

  /**
   * Méthodes de logging par niveau
   */
  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  trace(message, meta = {}) {
    this.log('trace', message, meta);
  }

  /**
   * Log d'activité utilisateur
   */
  userActivity(userId, action, details = {}) {
    this.info(`User activity: ${action}`, {
      component: 'USER',
      userId,
      action,
      ...details
    });
  }

  /**
   * Log de performance
   */
  performance(operation, duration, details = {}) {
    this.info(`Performance: ${operation} took ${duration}ms`, {
      component: 'PERF',
      operation,
      duration,
      ...details
    });
  }

  /**
   * Log de sécurité
   */
  security(event, severity = 'info', details = {}) {
    this.log(severity, `Security event: ${event}`, {
      component: 'SECURITY',
      event,
      severity,
      ...details
    });
  }

  /**
   * Obtient les logs récents
   */
  getRecentLogs(limit = 100, level = null) {
    let logs = [...this.logBuffer];
    
    if (level) {
      logs = logs.filter(log => log.level.toLowerCase() === level.toLowerCase());
    }
    
    return logs.slice(-limit);
  }

  /**
   * Obtient les statistiques des logs
   */
  getStats() {
    const stats = {
      total: this.logBuffer.length,
      byLevel: {},
      byComponent: {},
      recentErrors: 0
    };
    
    const oneHourAgo = Date.now() - 3600000;
    
    this.logBuffer.forEach(log => {
      // Par niveau
      stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
      
      // Par composant
      stats.byComponent[log.component] = (stats.byComponent[log.component] || 0) + 1;
      
      // Erreurs récentes
      if (log.level === 'ERROR' && new Date(log.timestamp).getTime() > oneHourAgo) {
        stats.recentErrors++;
      }
    });
    
    return stats;
  }

  /**
   * Nettoie les logs anciens
   */
  cleanup() {
    this.flushLogs();
    
    // Nettoyer le buffer
    const oneHourAgo = Date.now() - 3600000;
    this.logBuffer = this.logBuffer.filter(log => 
      new Date(log.timestamp).getTime() > oneHourAgo
    );
  }
}

// Instance singleton
let loggerInstance = null;

/**
 * Obtient l'instance du logger
 */
function getLogger() {
  if (!loggerInstance) {
    loggerInstance = new Logger();
  }
  return loggerInstance;
}

module.exports = {
  Logger,
  getLogger
};
