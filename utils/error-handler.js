/**
 * Gestionnaire d'erreurs centralisé pour l'application Louna
 * Capture, traite et résout automatiquement les erreurs
 */

const { getLogger } = require('./logger');
const { getConfig } = require('../config/app-config');

class ErrorHandler {
  constructor() {
    this.logger = getLogger();
    this.config = getConfig('system.diagnostics');
    this.errorStats = {
      total: 0,
      byType: {},
      byComponent: {},
      resolved: 0,
      unresolved: 0
    };
    
    this.autoFixStrategies = new Map();
    this.errorHistory = [];
    this.maxHistorySize = 1000;
    
    // Initialiser les stratégies de correction automatique
    this.initializeAutoFixStrategies();
    
    // Configurer les gestionnaires d'erreurs globaux
    this.setupGlobalHandlers();
  }

  /**
   * Configure les gestionnaires d'erreurs globaux
   */
  setupGlobalHandlers() {
    // Erreurs non capturées
    process.on('uncaughtException', (error) => {
      this.handleCriticalError('uncaughtException', error);
    });

    // Promesses rejetées non gérées
    process.on('unhandledRejection', (reason, promise) => {
      this.handleCriticalError('unhandledRejection', reason, { promise });
    });

    // Avertissements
    process.on('warning', (warning) => {
      this.handleWarning(warning);
    });
  }

  /**
   * Initialise les stratégies de correction automatique
   */
  initializeAutoFixStrategies() {
    // Erreurs de mémoire
    this.autoFixStrategies.set('MEMORY_LEAK', async (error, context) => {
      this.logger.warn('Détection de fuite mémoire, nettoyage automatique', { component: 'ERROR_HANDLER' });
      
      // Forcer le garbage collection si disponible
      if (global.gc) {
        global.gc();
      }
      
      // Nettoyer les caches
      if (global.thermalMemory) {
        await global.thermalMemory.performMemoryCycle();
      }
      
      return { success: true, action: 'memory_cleanup' };
    });

    // Erreurs de connexion
    this.autoFixStrategies.set('CONNECTION_ERROR', async (error, context) => {
      this.logger.warn('Erreur de connexion détectée, tentative de reconnexion', { component: 'ERROR_HANDLER' });
      
      // Attendre avant de réessayer
      await this.delay(2000);
      
      // Tenter de redémarrer les services concernés
      if (context.service === 'ollama') {
        return await this.restartOllamaConnection();
      }
      
      return { success: false, action: 'manual_intervention_required' };
    });

    // Erreurs de fichier
    this.autoFixStrategies.set('FILE_ERROR', async (error, context) => {
      this.logger.warn('Erreur de fichier détectée, tentative de correction', { component: 'ERROR_HANDLER' });
      
      if (error.code === 'ENOENT') {
        // Créer le fichier/dossier manquant
        return await this.createMissingPath(context.path);
      }
      
      if (error.code === 'EACCES') {
        // Problème de permissions
        this.logger.error('Problème de permissions détecté', { path: context.path });
        return { success: false, action: 'permission_fix_required' };
      }
      
      return { success: false, action: 'unknown_file_error' };
    });

    // Erreurs de composant
    this.autoFixStrategies.set('COMPONENT_FAILURE', async (error, context) => {
      this.logger.warn(`Défaillance du composant ${context.component}`, { component: 'ERROR_HANDLER' });
      
      // Tenter de redémarrer le composant
      return await this.restartComponent(context.component);
    });
  }

  /**
   * Gère une erreur critique
   */
  handleCriticalError(type, error, context = {}) {
    const errorInfo = {
      type,
      message: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context,
      critical: true
    };

    this.logger.error(`Erreur critique: ${type}`, errorInfo);
    this.recordError(errorInfo);

    // Tenter une correction automatique si possible
    if (this.config.autoFix) {
      this.attemptAutoFix(errorInfo);
    }

    // Pour les erreurs vraiment critiques, on peut décider de redémarrer
    if (type === 'uncaughtException') {
      this.logger.error('Application en état critique, redémarrage recommandé');
      
      // Sauvegarder l'état avant de quitter
      this.saveErrorState();
      
      // Donner du temps pour les logs
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    }
  }

  /**
   * Gère un avertissement
   */
  handleWarning(warning) {
    this.logger.warn(`Avertissement système: ${warning.message}`, {
      component: 'SYSTEM',
      name: warning.name,
      code: warning.code
    });
  }

  /**
   * Gère une erreur standard
   */
  handleError(error, context = {}) {
    const errorInfo = {
      type: this.classifyError(error),
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context,
      critical: false
    };

    this.logger.error(`Erreur: ${errorInfo.type}`, errorInfo);
    this.recordError(errorInfo);

    // Tenter une correction automatique
    if (this.config.autoFix) {
      return this.attemptAutoFix(errorInfo);
    }

    return { success: false, action: 'logged_only' };
  }

  /**
   * Classifie une erreur
   */
  classifyError(error) {
    if (error.code === 'ENOENT' || error.code === 'EACCES') {
      return 'FILE_ERROR';
    }
    
    if (error.message.includes('connect') || error.message.includes('timeout')) {
      return 'CONNECTION_ERROR';
    }
    
    if (error.message.includes('memory') || error.message.includes('heap')) {
      return 'MEMORY_LEAK';
    }
    
    if (error.message.includes('not a function') || error.message.includes('undefined')) {
      return 'COMPONENT_FAILURE';
    }
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * Enregistre une erreur dans l'historique
   */
  recordError(errorInfo) {
    this.errorStats.total++;
    this.errorStats.byType[errorInfo.type] = (this.errorStats.byType[errorInfo.type] || 0) + 1;
    
    if (errorInfo.context.component) {
      this.errorStats.byComponent[errorInfo.context.component] = 
        (this.errorStats.byComponent[errorInfo.context.component] || 0) + 1;
    }

    this.errorHistory.push(errorInfo);
    
    // Limiter la taille de l'historique
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Tente une correction automatique
   */
  async attemptAutoFix(errorInfo) {
    const strategy = this.autoFixStrategies.get(errorInfo.type);
    
    if (!strategy) {
      this.logger.debug(`Aucune stratégie de correction pour ${errorInfo.type}`);
      this.errorStats.unresolved++;
      return { success: false, action: 'no_strategy' };
    }

    try {
      this.logger.info(`Tentative de correction automatique pour ${errorInfo.type}`);
      const result = await strategy(errorInfo, errorInfo.context);
      
      if (result.success) {
        this.errorStats.resolved++;
        this.logger.info(`Correction automatique réussie: ${result.action}`);
      } else {
        this.errorStats.unresolved++;
        this.logger.warn(`Correction automatique échouée: ${result.action}`);
      }
      
      return result;
    } catch (fixError) {
      this.errorStats.unresolved++;
      this.logger.error('Erreur lors de la correction automatique', {
        originalError: errorInfo.type,
        fixError: fixError.message
      });
      
      return { success: false, action: 'fix_failed', error: fixError.message };
    }
  }

  /**
   * Redémarre la connexion Ollama
   */
  async restartOllamaConnection() {
    try {
      // Vérifier si Ollama est disponible
      const response = await fetch('http://localhost:11434/api/tags');
      
      if (response.ok) {
        return { success: true, action: 'ollama_reconnected' };
      } else {
        return { success: false, action: 'ollama_unavailable' };
      }
    } catch (error) {
      return { success: false, action: 'ollama_connection_failed' };
    }
  }

  /**
   * Crée un chemin manquant
   */
  async createMissingPath(filePath) {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const dir = path.dirname(filePath);
      
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // Si c'est un fichier, créer un fichier vide
      if (path.extname(filePath)) {
        fs.writeFileSync(filePath, '');
      }
      
      return { success: true, action: 'path_created' };
    } catch (error) {
      return { success: false, action: 'path_creation_failed', error: error.message };
    }
  }

  /**
   * Redémarre un composant
   */
  async restartComponent(componentName) {
    try {
      switch (componentName) {
        case 'thermalMemory':
          if (global.thermalMemory && global.thermalMemory.restart) {
            await global.thermalMemory.restart();
            return { success: true, action: 'thermal_memory_restarted' };
          }
          break;
          
        case 'artificialBrain':
          if (global.artificialBrain && global.artificialBrain.restart) {
            await global.artificialBrain.restart();
            return { success: true, action: 'artificial_brain_restarted' };
          }
          break;
          
        case 'kyberAccelerators':
          if (global.kyberAccelerators && global.kyberAccelerators.restart) {
            await global.kyberAccelerators.restart();
            return { success: true, action: 'kyber_accelerators_restarted' };
          }
          break;
      }
      
      return { success: false, action: 'component_restart_not_supported' };
    } catch (error) {
      return { success: false, action: 'component_restart_failed', error: error.message };
    }
  }

  /**
   * Sauvegarde l'état des erreurs
   */
  saveErrorState() {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const errorState = {
        stats: this.errorStats,
        recentErrors: this.errorHistory.slice(-50),
        timestamp: new Date().toISOString()
      };
      
      const statePath = path.join(getConfig('paths.data'), 'error-state.json');
      fs.writeFileSync(statePath, JSON.stringify(errorState, null, 2));
      
      this.logger.info('État des erreurs sauvegardé');
    } catch (error) {
      console.error('Impossible de sauvegarder l\'état des erreurs:', error.message);
    }
  }

  /**
   * Utilitaire de délai
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtient les statistiques d'erreurs
   */
  getStats() {
    return {
      ...this.errorStats,
      recentErrors: this.errorHistory.slice(-10),
      successRate: this.errorStats.total > 0 ? 
        (this.errorStats.resolved / this.errorStats.total) * 100 : 100
    };
  }

  /**
   * Nettoie l'historique des erreurs
   */
  cleanup() {
    const oneHourAgo = Date.now() - 3600000;
    this.errorHistory = this.errorHistory.filter(error => 
      new Date(error.timestamp).getTime() > oneHourAgo
    );
  }
}

// Instance singleton
let errorHandlerInstance = null;

/**
 * Obtient l'instance du gestionnaire d'erreurs
 */
function getErrorHandler() {
  if (!errorHandlerInstance) {
    errorHandlerInstance = new ErrorHandler();
  }
  return errorHandlerInstance;
}

/**
 * Middleware Express pour la gestion d'erreurs
 */
function errorMiddleware(err, req, res, next) {
  const errorHandler = getErrorHandler();
  
  const context = {
    component: 'EXPRESS',
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  };
  
  errorHandler.handleError(err, context);
  
  res.status(500).json({
    success: false,
    error: 'Erreur interne du serveur',
    timestamp: new Date().toISOString()
  });
}

module.exports = {
  ErrorHandler,
  getErrorHandler,
  errorMiddleware
};
