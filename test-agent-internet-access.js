/**
 * Test complet de l'accès Internet de l'agent <PERSON><PERSON>
 * Ce script teste toutes les méthodes d'accès Internet disponibles
 */

const axios = require('axios');

async function testAgentInternetAccess() {
    console.log('🚀 Test complet de l\'accès Internet de l\'agent <PERSON><PERSON>...\n');
    
    const results = {
        mcpServer: false,
        enhancedAgent: false,
        chatRoute: false,
        internetSearch: false
    };

    // Test 1: Vérifier le serveur MCP
    console.log('📡 Test 1: Vérification du serveur MCP...');
    try {
        const mcpStatus = await axios.get('http://localhost:3002/mcp/status', {
            timeout: 5000
        });
        
        if (mcpStatus.data && mcpStatus.data.status === 'ok') {
            console.log('✅ Serveur MCP accessible');
            console.log('   Capacités:', mcpStatus.data.capabilities);
            results.mcpServer = true;
            
            // Test de recherche MCP
            try {
                const mcpSearch = await axios.post('http://localhost:3002/mcp/internet/search', {
                    query: 'test recherche internet',
                    maxResults: 2
                }, {
                    timeout: 10000,
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (mcpSearch.data && mcpSearch.data.success) {
                    console.log('✅ Recherche MCP fonctionnelle');
                    results.internetSearch = true;
                } else {
                    console.log('❌ Recherche MCP échouée:', mcpSearch.data);
                }
            } catch (searchError) {
                console.log('❌ Erreur recherche MCP:', searchError.message);
            }
        }
    } catch (error) {
        console.log('❌ Serveur MCP inaccessible:', error.message);
    }

    // Test 2: Vérifier l'agent amélioré
    console.log('\n🤖 Test 2: Vérification de l\'agent amélioré...');
    try {
        const agentTest = await axios.post('http://localhost:3007/api/chat/test-internet', {
            query: 'test agent internet'
        }, {
            timeout: 15000,
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (agentTest.data && agentTest.data.success) {
            console.log('✅ Agent amélioré accessible');
            console.log('   Internet Access:', agentTest.data.internetAccess);
            console.log('   Agent Status:', agentTest.data.agentStatus);
            results.enhancedAgent = true;
        }
    } catch (error) {
        console.log('❌ Agent amélioré inaccessible:', error.message);
    }

    // Test 3: Test de chat avec demande Internet explicite
    console.log('\n💬 Test 3: Test de chat avec demande Internet...');
    try {
        const chatResponse = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Peux-tu faire une recherche sur Internet pour les dernières nouvelles sur l\'intelligence artificielle ?'
        }, {
            timeout: 30000,
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (chatResponse.data && chatResponse.data.response) {
            console.log('✅ Chat accessible');
            console.log('   Réponse contient Internet:', chatResponse.data.response.includes('Internet') || chatResponse.data.response.includes('recherche'));
            
            if (chatResponse.data.internalThoughts && chatResponse.data.internalThoughts.internetSearch) {
                console.log('   Recherche Internet tentée:', chatResponse.data.internalThoughts.internetSearch.needed);
                console.log('   Succès recherche:', chatResponse.data.internalThoughts.internetSearch.success);
                results.chatRoute = true;
            }
        }
    } catch (error) {
        console.log('❌ Chat inaccessible:', error.message);
    }

    // Test 4: Test direct des APIs de recherche
    console.log('\n🔍 Test 4: Test direct des APIs de recherche...');
    try {
        // Test DuckDuckGo
        const duckDuckGoUrl = 'https://api.duckduckgo.com/?q=test&format=json&no_html=1&skip_disambig=1';
        const ddgResponse = await axios.get(duckDuckGoUrl, { timeout: 5000 });
        
        if (ddgResponse.data) {
            console.log('✅ DuckDuckGo API accessible');
        }
    } catch (error) {
        console.log('❌ DuckDuckGo API inaccessible:', error.message);
    }

    try {
        // Test Wikipedia
        const wikiUrl = 'https://fr.wikipedia.org/api/rest_v1/page/summary/intelligence_artificielle';
        const wikiResponse = await axios.get(wikiUrl, { timeout: 5000 });
        
        if (wikiResponse.data && wikiResponse.data.extract) {
            console.log('✅ Wikipedia API accessible');
        }
    } catch (error) {
        console.log('❌ Wikipedia API inaccessible:', error.message);
    }

    // Résumé des résultats
    console.log('\n📊 RÉSUMÉ DES TESTS:');
    console.log('===================');
    console.log(`Serveur MCP:        ${results.mcpServer ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`Agent amélioré:     ${results.enhancedAgent ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`Route de chat:      ${results.chatRoute ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`Recherche Internet: ${results.internetSearch ? '✅ OK' : '❌ ÉCHEC'}`);

    // Diagnostic et recommandations
    console.log('\n🔧 DIAGNOSTIC:');
    if (!results.mcpServer) {
        console.log('❌ Le serveur MCP n\'est pas accessible sur le port 3002');
        console.log('   Recommandation: Vérifier que le serveur MCP est démarré');
    }
    
    if (!results.enhancedAgent) {
        console.log('❌ L\'agent amélioré n\'a pas accès à Internet');
        console.log('   Recommandation: Vérifier la configuration de l\'agent amélioré');
    }
    
    if (!results.internetSearch) {
        console.log('❌ Aucune méthode de recherche Internet ne fonctionne');
        console.log('   Recommandation: Vérifier la connectivité réseau et les APIs');
    }

    if (results.mcpServer && results.enhancedAgent && results.internetSearch) {
        console.log('✅ Tous les systèmes d\'accès Internet fonctionnent correctement !');
    }

    return results;
}

// Exécuter le test si ce fichier est lancé directement
if (require.main === module) {
    testAgentInternetAccess()
        .then(results => {
            console.log('\n🎯 Test terminé.');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur lors du test:', error);
            process.exit(1);
        });
}

module.exports = { testAgentInternetAccess };
