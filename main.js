/**
 * Fichier principal pour l'application Electron Louna
 */

const { app, BrowserWindow, ipcMain, dialog, shell, Menu } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const fs = require('fs');
const http = require('http');

// Importer le système de persistance mémoire
const ThermalMemoryPersistence = require('./modules/memory-persistence');

// Garder une référence globale de l'objet window, sinon la fenêtre sera
// fermée automatiquement quand l'objet JavaScript sera garbage collected.
let mainWindow;
let serverProcess;
let serverPort = 3001; // Port synchronisé avec server.js
let serverReady = false;

// Système de persistance mémoire
let thermalMemoryPersistence;
let persistenceInitialized = false;

// Système de sécurité et hibernation
let isInDeepHibernation = false;
let isInSleepMode = false;
let securityCode = '2338';
let lastActivityTime = Date.now();

/**
 * Initialise le système de persistance mémoire
 */
async function initializePersistence() {
  try {
    console.log('🧠 Initialisation du système de persistance mémoire...');

    thermalMemoryPersistence = new ThermalMemoryPersistence();
    await thermalMemoryPersistence.initialize();

    persistenceInitialized = true;
    console.log('✅ Système de persistance mémoire initialisé');

    // Récupérer les données sauvegardées
    const recovery = await thermalMemoryPersistence.recoverSavedData();
    if (recovery.success && recovery.recovered > 0) {
      console.log(`🔄 ${recovery.recovered} éléments récupérés de la mémoire`);

      // Afficher une notification à l'utilisateur
      if (mainWindow) {
        mainWindow.webContents.executeJavaScript(`
          console.log('🧠 Système de persistance mémoire actif - ${recovery.recovered} éléments récupérés');
        `);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Erreur initialisation persistance:', error);
    persistenceInitialized = false;
    return false;
  }
}

/**
 * Sauvegarde d'urgence avant fermeture
 */
function emergencyPersistenceSave() {
  if (thermalMemoryPersistence && persistenceInitialized) {
    console.log('🚨 Sauvegarde d\'urgence de la persistance mémoire...');
    thermalMemoryPersistence.emergencySave();
  }
}

/**
 * Vérifie et charge l'état de sécurité au démarrage
 */
async function loadSecurityState() {
  try {
    const securityFile = path.join(__dirname, 'data', 'security-state.json');

    if (fs.existsSync(securityFile)) {
      const securityData = JSON.parse(fs.readFileSync(securityFile, 'utf8'));

      isInDeepHibernation = securityData.isInDeepHibernation || false;
      isInSleepMode = securityData.isInSleepMode || false;

      console.log('🔒 État de sécurité chargé:', {
        hibernation: isInDeepHibernation,
        sommeil: isInSleepMode
      });

      if (isInDeepHibernation) {
        console.log('❄️ Application en hibernation profonde - Fonctionnalités limitées');
      } else if (isInSleepMode) {
        console.log('😴 Application en mode sommeil - Fonctionnalités réduites');
      }
    }
  } catch (error) {
    console.error('❌ Erreur chargement état sécurité:', error);
  }
}

/**
 * Sauvegarde l'état de sécurité
 */
async function saveSecurityState() {
  try {
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const securityFile = path.join(dataDir, 'security-state.json');
    const securityData = {
      isInDeepHibernation,
      isInSleepMode,
      lastUpdate: new Date().toISOString(),
      version: '2.1.0'
    };

    fs.writeFileSync(securityFile, JSON.stringify(securityData, null, 2));
    console.log('💾 État de sécurité sauvegardé');
  } catch (error) {
    console.error('❌ Erreur sauvegarde état sécurité:', error);
  }
}

/**
 * Met l'agent en hibernation profonde
 */
async function enterDeepHibernation() {
  console.log('❄️ Entrée en hibernation profonde...');
  isInDeepHibernation = true;
  isInSleepMode = false;
  await saveSecurityState();

  // Notifier l'interface web
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.executeJavaScript(`
      if (typeof updateVisualState === 'function') {
        updateVisualState('hibernating');
      }
      console.log('❄️ Agent en hibernation profonde depuis Electron');
    `);
  }
}

/**
 * Met l'agent en mode sommeil
 */
async function putAgentToSleep() {
  console.log('😴 Entrée en mode sommeil...');
  isInSleepMode = true;
  isInDeepHibernation = false;
  await saveSecurityState();

  // Notifier l'interface web
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.executeJavaScript(`
      if (typeof updateVisualState === 'function') {
        updateVisualState('sleeping');
      }
      console.log('😴 Agent en mode sommeil depuis Electron');
    `);
  }
}

/**
 * Réveille l'agent
 */
async function wakeUpAgent() {
  console.log('🌅 Réveil de l\'agent...');
  isInDeepHibernation = false;
  isInSleepMode = false;
  await saveSecurityState();

  // Notifier l'interface web
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.executeJavaScript(`
      if (typeof updateVisualState === 'function') {
        updateVisualState('awake');
      }
      console.log('🌅 Agent réveillé depuis Electron');
    `);
  }
}

/**
 * Crée la fenêtre principale de l'application
 */
function createWindow() {
  // Créer la fenêtre du navigateur
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    backgroundColor: '#1a1a2e',
    show: false,
    movable: true, // Permettre le déplacement de la fenêtre
    resizable: true, // Permettre le redimensionnement
    minimizable: true, // Permettre la minimisation
    maximizable: true, // Permettre la maximisation
    closable: true, // Permettre la fermeture
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default', // Style de barre de titre par défaut pour permettre le déplacement
    frame: true, // Afficher le cadre de la fenêtre pour permettre le déplacement
    vibrancy: 'ultra-dark',
    visualEffectState: 'active'
  });

  // Afficher l'écran de chargement
  mainWindow.loadFile('public/loading.html');

  // Charger l'état de sécurité
  loadSecurityState();

  // Démarrer le serveur
  startServer();

  // Initialiser le système de persistance mémoire
  initializePersistence();

  // Ouvrir les DevTools en mode développement
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Émis lorsque la fenêtre est fermée
  mainWindow.on('closed', function () {
    // Sauvegarde d'urgence avant fermeture
    emergencyPersistenceSave();

    // Sauvegarder l'état de sécurité
    saveSecurityState();

    // Dé-référencer l'objet window, généralement vous stockeriez les fenêtres
    // dans un tableau si votre application supporte le multi-fenêtre, c'est le moment
    // où vous devriez supprimer l'élément correspondant.
    mainWindow = null;

    // Arrêter le serveur
    if (serverProcess) {
      serverProcess.kill();
      serverProcess = null;
    }
  });

  // Créer le menu de l'application
  createMenu();
}

/**
 * Démarre le serveur Node.js ou se connecte au serveur existant
 */
function startServer() {
  // Vérifier d'abord si un serveur est déjà en cours d'exécution
  checkServerAvailability()
    .then((isAvailable) => {
      if (isAvailable) {
        console.log('🔗 Serveur déjà disponible, connexion directe...');
        serverReady = true;
        loadApp();
      } else {
        console.log('🚀 Démarrage du serveur...');
        startNewServer();
      }
    })
    .catch(() => {
      console.log('🚀 Démarrage du serveur...');
      startNewServer();
    });
}

/**
 * Vérifie si le serveur est déjà disponible
 */
function checkServerAvailability() {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${serverPort}/api/monitoring/status`, (res) => {
      resolve(res.statusCode === 200);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.setTimeout(2000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

/**
 * Démarre un nouveau serveur
 */
function startNewServer() {
  // Démarrer le serveur Node.js complet avec mémoire thermique
  serverProcess = spawn('node', ['server.js'], {
    stdio: ['ignore', 'pipe', 'pipe']
  });

  let serverOutput = '';

  // Écouter la sortie standard du serveur
  serverProcess.stdout.on('data', (data) => {
    serverOutput += data.toString();
    console.log(`[Server]: ${data.toString()}`);

    // Vérifier si le serveur est prêt
    if (data.toString().includes('Serveur Louna Simple démarré sur le port') ||
        data.toString().includes('Serveur Louna démarré sur le port') ||
        data.toString().includes('Serveur démarré sur le port')) {
      serverReady = true;
      // Charger l'application une fois que le serveur est prêt
      loadApp();
    }
  });

  // Écouter la sortie d'erreur du serveur
  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error]: ${data.toString()}`);
  });

  // Gérer la fermeture du serveur
  serverProcess.on('close', (code) => {
    console.log(`Le serveur s'est arrêté avec le code ${code}`);
    serverReady = false;
  });

  // Définir un délai maximum pour le démarrage du serveur
  setTimeout(() => {
    if (!serverReady) {
      console.error('Le serveur n\'a pas démarré dans le délai imparti');
      // Essayer de se connecter au serveur existant
      checkServerAvailability()
        .then((isAvailable) => {
          if (isAvailable) {
            console.log('🔗 Connexion au serveur existant...');
            serverReady = true;
            loadApp();
          } else {
            dialog.showErrorBox(
              'Erreur de démarrage',
              'Le serveur n\'a pas démarré correctement. Veuillez redémarrer l\'application.'
            );
          }
        });
    }
  }, 10000);
}

/**
 * Charge l'application une fois que le serveur est prêt
 */
function loadApp() {
  // Charger la page d'accueil de Louna
  let appUrl = `http://localhost:${serverPort}/`;

  // Détecter le mode de rechargement forcé
  if (process.env.FORCE_RELOAD === 'true' || process.env.MODERN_INTERFACE === 'true') {
    console.log('🔄 Mode rechargement forcé détecté');
    appUrl += '?force=true&v=' + Date.now() + '&modern=true';
  }

  console.log('🚀 Chargement de l\'interface Louna:', appUrl);

  // Charger l'URL de l'application
  mainWindow.loadURL(appUrl);

  // Afficher la fenêtre une fois que tout est chargé
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // EMPÊCHER TOUTES LES NOUVELLES FENÊTRES - TOUT RESTE DANS LOUNA
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log("🚫 Tentative d'ouverture de nouvelle fenêtre bloquée:", url);
    // Bloquer TOUTES les nouvelles fenêtres
    return { action: 'deny' };
  });

  // EMPÊCHER LA NAVIGATION EXTERNE - TOUT RESTE DANS LOUNA
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    // Permettre seulement la navigation interne à Louna
    if (parsedUrl.hostname !== 'localhost' || parsedUrl.port !== '3001') {
      console.log("🚫 Navigation externe bloquée:", navigationUrl);
      event.preventDefault();
    } else {
      console.log("✅ Navigation interne autorisée:", navigationUrl);
    }
  });
}

/**
 * Crée le menu de l'application
 */
function createMenu() {
  const template = [
    {
      label: 'Louna',
      submenu: [
        { role: 'about', label: 'À propos de Louna' },
        { type: 'separator' },
        { role: 'services', label: 'Services' },
        { type: 'separator' },
        { role: 'hide', label: 'Masquer Louna' },
        { role: 'hideOthers', label: 'Masquer les autres' },
        { role: 'unhide', label: 'Tout afficher' },
        { type: 'separator' },
        { role: 'quit', label: 'Quitter Louna' }
      ]
    },
    {
      label: 'Édition',
      submenu: [
        { role: 'undo', label: 'Annuler' },
        { role: 'redo', label: 'Rétablir' },
        { type: 'separator' },
        { role: 'cut', label: 'Couper' },
        { role: 'copy', label: 'Copier' },
        { role: 'paste', label: 'Coller' },
        { role: 'selectAll', label: 'Tout sélectionner' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { role: 'reload', label: 'Actualiser' },
        { role: 'forceReload', label: 'Forcer l\'actualisation' },
        {
          label: '🔄 Forcer Interface Moderne',
          click: () => {
            const forceUrl = `http://localhost:${serverPort}/chat?force=true&v=${Date.now()}&modern=true`;
            console.log('🎨 Rechargement forcé de l\'interface moderne:', forceUrl);
            mainWindow.loadURL(forceUrl);
          }
        },
        { role: 'toggleDevTools', label: 'Outils de développement' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'Taille réelle' },
        { role: 'zoomIn', label: 'Zoom avant' },
        { role: 'zoomOut', label: 'Zoom arrière' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'Plein écran' }
      ]
    },
    {
      label: 'Navigation',
      submenu: [
        {
          label: '🏠 Accueil Principal',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/`);
          }
        },
        { type: 'separator' },
        {
          label: '💬 Chat Intelligent',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/chat`);
          }
        },
        {
          label: '🎤 Interface Vocale',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/voice-interface.html`);
          }
        },
        { type: 'separator' },
        {
          label: '🔥 Mémoire Thermique',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/futuristic-interface.html`);
          }
        },
        {
          label: '🧠 Visualisation Cerveau 3D',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/brain-visualization.html`);
          }
        },
        {
          label: '📊 Monitoring QI & Neurones',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/qi-neuron-monitor.html`);
          }
        },
        { type: 'separator' },
        {
          label: '⚡ Accélérateurs Kyber',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/kyber-dashboard.html`);
          }
        },
        {
          label: '🎨 Studio de Génération',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/generation-studio.html`);
          }
        },
        { type: 'separator' },
        {
          label: '💻 Éditeur de Code',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/code-editor.html`);
          }
        },
        {
          label: '👥 Gestion des Agents',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/agents.html`);
          }
        },
        {
          label: '🎓 Module de Formation',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/training.html`);
          }
        },
        { type: 'separator' },
        {
          label: '📊 Tableau de Bord Principal',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/dashboard-master.html`);
          }
        },
        {
          label: '📋 Logs Système',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/system-logs.html`);
          }
        },
        {
          label: '⚙️ Paramètres Avancés',
          click: () => {
            mainWindow.loadURL(`http://localhost:${serverPort}/settings-advanced.html`);
          }
        }
      ]
    },
    {
      label: 'Fenêtre',
      submenu: [
        { role: 'minimize', label: 'Réduire' },
        { role: 'zoom', label: 'Zoom' },
        { type: 'separator' },
        { role: 'front', label: 'Tout ramener au premier plan' }
      ]
    },
    {
      role: 'help',
      label: 'Aide',
      submenu: [
        {
          label: 'Documentation',
          click: async () => {
            await shell.openExternal('https://github.com/votre-repo/louna/wiki');
          }
        },
        {
          label: 'Signaler un problème',
          click: async () => {
            await shell.openExternal('https://github.com/votre-repo/louna/issues');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Cette méthode sera appelée quand Electron aura fini
// de s'initialiser et sera prêt à créer des fenêtres de navigateur.
// Certaines APIs peuvent être utilisées uniquement après cet événement.
app.on('ready', createWindow);

// Quitter quand toutes les fenêtres sont fermées.
app.on('window-all-closed', function () {
  // Sauvegarde d'urgence avant fermeture complète
  emergencyPersistenceSave();

  // Sauvegarder l'état de sécurité
  saveSecurityState();

  // Sur macOS, il est commun pour une application et leur barre de menu
  // de rester active tant que l'utilisateur ne quitte pas explicitement avec Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', function () {
  // Sur macOS, il est commun de re-créer une fenêtre de l'application quand
  // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
  if (mainWindow === null) {
    createWindow();
  }
});

// Dans ce fichier, vous pouvez inclure le reste du code spécifique du
// processus principal de votre application. Vous pouvez également le mettre dans des fichiers séparés
// et les inclure ici.

// Gérer les événements IPC (communication entre le processus principal et le renderer)
ipcMain.on('restart-server', () => {
  if (serverProcess) {
    serverProcess.kill();
    serverProcess = null;
  }
  startServer();
});

ipcMain.on('open-file-dialog', async (event) => {
  const { filePaths } = await dialog.showOpenDialog({
    properties: ['openFile', 'multiSelections']
  });
  event.reply('selected-files', filePaths);
});

// Gestionnaires IPC pour les contrôles de sécurité
ipcMain.handle('security-hibernation', async (event, code) => {
  if (code === securityCode) {
    await enterDeepHibernation();
    return { success: true, message: 'Agent mis en hibernation profonde' };
  }
  return { success: false, message: 'Code de sécurité incorrect' };
});

ipcMain.handle('security-sleep', async (event, code) => {
  if (code === securityCode) {
    await putAgentToSleep();
    return { success: true, message: 'Agent mis en mode sommeil' };
  }
  return { success: false, message: 'Code de sécurité incorrect' };
});

ipcMain.handle('security-wakeup', async (event, code) => {
  if (code === securityCode) {
    await wakeUpAgent();
    return { success: true, message: 'Agent réveillé avec succès' };
  }
  return { success: false, message: 'Code de sécurité incorrect' };
});

ipcMain.handle('security-status', async () => {
  return {
    isInDeepHibernation,
    isInSleepMode,
    isAwake: !isInDeepHibernation && !isInSleepMode,
    lastActivity: lastActivityTime
  };
});

// Événements IPC pour le système de persistance mémoire
ipcMain.on('persistence-save', async (event) => {
  if (thermalMemoryPersistence && persistenceInitialized) {
    const result = await thermalMemoryPersistence.saveInstantMemoryToStorage();
    event.reply('persistence-save-result', result);
  } else {
    event.reply('persistence-save-result', { success: false, error: 'Persistance non initialisée' });
  }
});

ipcMain.on('persistence-stats', async (event) => {
  if (thermalMemoryPersistence && persistenceInitialized) {
    const stats = thermalMemoryPersistence.getStats();
    event.reply('persistence-stats-result', { success: true, stats });
  } else {
    event.reply('persistence-stats-result', { success: false, error: 'Persistance non initialisée' });
  }
});

ipcMain.on('persistence-add-data', async (event, data) => {
  if (thermalMemoryPersistence && persistenceInitialized) {
    const result = thermalMemoryPersistence.addToInstantMemory(data.content, data.options || {});
    event.reply('persistence-add-data-result', { success: true, id: result });
  } else {
    event.reply('persistence-add-data-result', { success: false, error: 'Persistance non initialisée' });
  }
});

// Exporter l'application pour les tests
module.exports = app;
