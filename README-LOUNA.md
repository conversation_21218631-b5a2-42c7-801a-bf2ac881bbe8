# Louna - Système Cognitif Avancé avec Mémoire Thermique

Louna est une application cognitive avancée qui implémente un système de mémoire thermique sophistiqué. Cette application permet de stocker, organiser et récupérer des informations de manière intelligente, en utilisant un système de zones de température qui simule le fonctionnement de la mémoire humaine.

## Fonctionnalités principales

- **Mémoire thermique** : Système de mémoire à plusieurs niveaux de température
- **Analyse sémantique** : Compréhension du contenu et organisation intelligente
- **Adaptation automatique** : Ajustement des paramètres en fonction de l'utilisation
- **Consolidation et archivage** : Gestion intelligente des mémoires à long terme
- **Interface utilisateur avancée** : Visualisations et tableaux de bord interactifs
- **Intégration système** : Accélérateurs thermiques, système Kyber Thermal, etc.

## Correction des problèmes de navigation

Si vous rencontrez des problèmes de navigation entre les interfaces de Louna, vous pouvez utiliser le script `fix-louna-navigation.sh` pour les corriger. Ce script effectue les actions suivantes :

1. Corrige les liens incorrects dans la barre latérale (remplace `/louna/` par `/luna/`)
2. Crée un script de correction de navigation côté client
3. Ajoute un gestionnaire d'erreurs de navigation
4. Intercepte les clics sur les liens pour corriger les URL incorrectes
5. Redémarre le serveur pour appliquer les changements

Pour exécuter ce script, ouvrez un terminal et exécutez la commande suivante :

```bash
./fix-louna-navigation.sh
```

## Comment lancer l'application

### Méthode 1 : Utiliser le raccourci

1. Double-cliquez sur le fichier `Louna.command` sur votre bureau
2. L'application démarrera et sera accessible à l'adresse http://localhost:3000/louna

### Méthode 2 : Créer une application macOS

1. Ouvrez un terminal
2. Naviguez vers le répertoire où se trouve le script `create-louna-app.sh`
3. Exécutez la commande suivante :
   ```bash
   ./create-louna-app.sh
   ```
4. Une application macOS sera créée sur votre bureau
5. Double-cliquez sur l'application pour lancer Louna

## Interfaces principales

- **Tableau de bord système** : http://localhost:3000/louna/system-dashboard
- **Système cognitif** : http://localhost:3000/louna/cognitive-system
- **Kyber Thermal** : http://localhost:3000/louna/kyber-thermal
- **Cerveau** : http://localhost:3000/louna/brain
- **Accélérateurs thermiques** : http://localhost:3000/louna/thermal-accelerators
- **Test de mémoire thermique améliorée** : http://localhost:3000/louna/thermal-memory/enhanced-test
- **Exemple d'agent avec mémoire thermique** : http://localhost:3000/louna/thermal-memory/agent-example
- **Documentation de l'API** : http://localhost:3000/louna/thermal-memory/docs

## Structure des fichiers

- **/lib** : Bibliothèques principales du système
  - `thermal-memory-system.js` : Système de mémoire thermique
  - `thermal-memory-optimizer.js` : Optimiseur de mémoire thermique
  - `semantic-analyzer.js` : Analyseur sémantique
  - `adaptive-memory-manager.js` : Gestionnaire de mémoire adaptative
  - `long-term-memory-manager.js` : Gestionnaire de mémoire à long terme
- **/public** : Fichiers statiques pour l'interface utilisateur
  - `/js` : Scripts JavaScript pour l'interface utilisateur
  - `/css` : Feuilles de style CSS
  - `/images` : Images et icônes
- **/routes** : Routes de l'application
  - `luna.js` : Routes principales de Luna
  - `thermal-memory-enhanced.js` : Routes pour la mémoire thermique améliorée
- **/views** : Templates EJS pour l'interface utilisateur
- **/data** : Données de l'application
  - `/memory` : Fichiers de mémoire thermique

## Dépannage

Si l'application ne démarre pas correctement, vérifiez les points suivants :

1. **Port déjà utilisé** : Si le port 3000 est déjà utilisé, l'application essaiera de démarrer sur le port 3001. Vous pouvez également arrêter le processus existant en répondant "o" à la question correspondante.

2. **Dépendances manquantes** : Si certaines dépendances sont manquantes, le script de lancement essaiera de les installer automatiquement. Si cela échoue, vous pouvez les installer manuellement en exécutant `npm install` dans le répertoire de l'application.

3. **Répertoires de données manquants** : Si les répertoires de données sont manquants, le script de lancement les créera automatiquement.

4. **Erreurs de navigation** : Si vous rencontrez des erreurs de navigation entre les onglets, utilisez le script `fix-louna-navigation.sh` pour corriger ces problèmes.

## Support technique

Pour toute question ou problème, veuillez contacter le support technique.

---

Développé par Jean Passave, Sainte-Anne, Guadeloupe (97180)
