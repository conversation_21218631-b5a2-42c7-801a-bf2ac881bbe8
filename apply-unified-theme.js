#!/usr/bin/env node

/**
 * Script pour appliquer le thème unifié à toutes les interfaces Louna
 * Standardise le design et la navigation de toutes les pages HTML
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 APPLICATION DU THÈME UNIFIÉ LOUNA');
console.log('=====================================');

// Configuration
const publicDir = './public';
const backupDir = './public/backups-before-unification';

// Créer le dossier de sauvegarde
if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
}

// Template HTML unifié
const unifiedTemplate = {
    head: `
    <!-- Thème unifié Louna 2025 -->
    <link rel="stylesheet" href="/css/louna-unified-theme.css?v=2025">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="/js/louna-unified-nav.js?v=2025"></script>
    `,
    
    bodyStart: `
    <!-- Navigation sera injectée automatiquement par louna-unified-nav.js -->
    `,
    
    bodyEnd: `
    <script>
        // Auto-application du thème unifié
        document.addEventListener('DOMContentLoaded', function() {
            // Marquer comme page unifiée
            document.body.classList.add('louna-unified', 'theme-2025');
            
            // Ajouter des classes aux éléments existants pour la cohérence
            const cards = document.querySelectorAll('.card, .thermal-card, .kyber-card, .monitor-card, .studio-card');
            cards.forEach(card => {
                if (!card.classList.contains('louna-card')) {
                    card.classList.add('louna-card');
                }
            });
            
            const buttons = document.querySelectorAll('button:not(.louna-btn), .btn:not(.louna-btn)');
            buttons.forEach(btn => {
                if (!btn.classList.contains('louna-btn')) {
                    btn.classList.add('louna-btn', 'btn-primary');
                }
            });
            
            const grids = document.querySelectorAll('.grid, .main-grid, .content-grid');
            grids.forEach(grid => {
                if (!grid.classList.contains('louna-grid')) {
                    grid.classList.add('louna-grid', 'grid-2');
                }
            });
        });
    </script>
    `
};

// Fonction pour obtenir tous les fichiers HTML
function getAllHtmlFiles() {
    const files = [];
    
    function scanDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'backups-before-unification') {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                files.push(fullPath);
            }
        });
    }
    
    scanDirectory(publicDir);
    return files;
}

// Fonction pour sauvegarder un fichier
function backupFile(filePath) {
    const relativePath = path.relative(publicDir, filePath);
    const backupPath = path.join(backupDir, relativePath);
    const backupDirPath = path.dirname(backupPath);
    
    if (!fs.existsSync(backupDirPath)) {
        fs.mkdirSync(backupDirPath, { recursive: true });
    }
    
    fs.copyFileSync(filePath, backupPath);
}

// Fonction pour appliquer le thème unifié à un fichier
function applyUnifiedTheme(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier si déjà unifié
        if (content.includes('louna-unified-theme.css') || content.includes('theme-2025')) {
            return { success: true, action: 'already_unified' };
        }
        
        // Sauvegarder l'original
        backupFile(filePath);
        
        // Supprimer les anciennes navigations et styles
        content = content.replace(/<nav[^>]*class="[^"]*top-navbar[^"]*"[^>]*>[\s\S]*?<\/nav>/gi, '');
        content = content.replace(/<div[^>]*id="homeButton"[^>]*>[\s\S]*?<\/div>/gi, '');
        content = content.replace(/<link[^>]*href="[^"]*louna-thermal\.css[^"]*"[^>]*>/gi, '');
        content = content.replace(/<link[^>]*href="[^"]*theme-switcher\.css[^"]*"[^>]*>/gi, '');
        content = content.replace(/<link[^>]*href="[^"]*home-button\.css[^"]*"[^>]*>/gi, '');
        
        // Ajouter le thème unifié dans le head
        const headEndIndex = content.indexOf('</head>');
        if (headEndIndex !== -1) {
            content = content.slice(0, headEndIndex) + unifiedTemplate.head + content.slice(headEndIndex);
        }
        
        // Ajouter le script unifié avant la fermeture du body
        const bodyEndIndex = content.lastIndexOf('</body>');
        if (bodyEndIndex !== -1) {
            content = content.slice(0, bodyEndIndex) + unifiedTemplate.bodyEnd + content.slice(bodyEndIndex);
        }
        
        // Nettoyer les styles inline conflictuels
        content = content.replace(/style="[^"]*position:\s*fixed[^"]*"/gi, '');
        content = content.replace(/style="[^"]*top:\s*\d+px[^"]*"/gi, '');
        
        // Écrire le fichier modifié
        fs.writeFileSync(filePath, content, 'utf8');
        
        return { success: true, action: 'unified' };
        
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// Fonction principale
function unifyAllInterfaces() {
    const htmlFiles = getAllHtmlFiles();
    let unifiedCount = 0;
    let alreadyUnifiedCount = 0;
    let errorCount = 0;
    
    console.log(`\n📁 ${htmlFiles.length} fichiers HTML trouvés\n`);
    
    htmlFiles.forEach((filePath, index) => {
        const fileName = path.relative(publicDir, filePath);
        const result = applyUnifiedTheme(filePath);
        
        if (result.success) {
            if (result.action === 'unified') {
                console.log(`✅ [${index + 1}/${htmlFiles.length}] ${fileName} - UNIFIÉ`);
                unifiedCount++;
            } else {
                console.log(`⏭️  [${index + 1}/${htmlFiles.length}] ${fileName} - Déjà unifié`);
                alreadyUnifiedCount++;
            }
        } else {
            console.log(`❌ [${index + 1}/${htmlFiles.length}] ${fileName} - ERREUR: ${result.error}`);
            errorCount++;
        }
    });
    
    console.log('\n📊 RÉSUMÉ DE L\'UNIFICATION:');
    console.log(`✅ Fichiers unifiés: ${unifiedCount}`);
    console.log(`⏭️  Déjà unifiés: ${alreadyUnifiedCount}`);
    console.log(`❌ Erreurs: ${errorCount}`);
    console.log(`📈 Taux de réussite: ${Math.round(((unifiedCount + alreadyUnifiedCount) / htmlFiles.length) * 100)}%`);
    
    if (unifiedCount > 0) {
        console.log('\n🎉 UNIFICATION TERMINÉE AVEC SUCCÈS !');
        console.log('🔄 Redémarrez le serveur pour voir les changements');
        console.log('💾 Sauvegardes disponibles dans: ' + backupDir);
    }
    
    return {
        total: htmlFiles.length,
        unified: unifiedCount,
        alreadyUnified: alreadyUnifiedCount,
        errors: errorCount
    };
}

// Fonction pour créer des pages de test
function createTestPages() {
    console.log('\n🧪 Création de pages de test...');
    
    const testPages = [
        {
            name: 'test-unified-theme.html',
            title: 'Test du Thème Unifié',
            content: `
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-palette"></i>
                        Test du Thème Unifié
                    </h1>
                    <p class="page-subtitle">Vérification de la cohérence du design</p>
                </div>
                
                <div class="louna-grid grid-2">
                    <div class="louna-card">
                        <div class="card-header">
                            <i class="card-icon fas fa-check"></i>
                            <h3 class="card-title">Navigation</h3>
                        </div>
                        <p>La navigation unifiée est présente en haut de page</p>
                        <button class="louna-btn btn-primary">Bouton Test</button>
                    </div>
                    
                    <div class="louna-card">
                        <div class="card-header">
                            <i class="card-icon fas fa-palette"></i>
                            <h3 class="card-title">Thème</h3>
                        </div>
                        <p>Le thème unifié est appliqué avec les bonnes couleurs</p>
                        <button class="louna-btn btn-secondary">Bouton Secondaire</button>
                    </div>
                </div>
            `
        }
    ];
    
    testPages.forEach(page => {
        const fullContent = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.title} - Louna</title>
    ${unifiedTemplate.head}
</head>
<body>
    ${page.content}
    ${unifiedTemplate.bodyEnd}
</body>
</html>`;
        
        const filePath = path.join(publicDir, page.name);
        fs.writeFileSync(filePath, fullContent, 'utf8');
        console.log(`✅ Page de test créée: ${page.name}`);
    });
}

// Exécution
if (require.main === module) {
    console.log('🚀 Démarrage de l\'unification...\n');
    
    const results = unifyAllInterfaces();
    createTestPages();
    
    console.log('\n🎯 PROCHAINES ÉTAPES:');
    console.log('1. Redémarrez le serveur Louna');
    console.log('2. Testez les interfaces unifiées');
    console.log('3. Vérifiez la page de test: http://localhost:3000/test-unified-theme.html');
    console.log('4. Si problème, restaurez depuis: ' + backupDir);
}

module.exports = { unifyAllInterfaces, createTestPages };
