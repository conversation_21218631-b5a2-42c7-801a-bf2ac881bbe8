/**
 * Script de Test Complet de l'Agent Louna
 * Évalue la logique, la progression et la qualité des réponses
 */

const axios = require('axios');
const fs = require('fs');

class LounaAgentTester {
    constructor() {
        this.baseURL = 'http://localhost:3005';
        this.testResults = [];
        this.userId = 'test_evaluator_' + Date.now();
        this.startTime = Date.now();
    }

    async runCompleteTest() {
        console.log('🧪 DÉMARRAGE DU STAGE DE TEST COMPLET DE L\'AGENT LOUNA');
        console.log('=' .repeat(60));
        console.log(`🕐 Heure de début: ${new Date().toLocaleString()}`);
        console.log(`👤 ID Testeur: ${this.userId}`);
        console.log('');

        try {
            // Phase 1: Tests de base
            await this.testBasicFunctionality();
            
            // Phase 2: Tests de logique
            await this.testLogicalReasoning();
            
            // Phase 3: Tests de progression
            await this.testLearningProgression();
            
            // Phase 4: Tests de créativité
            await this.testCreativity();
            
            // Phase 5: Tests de mémoire
            await this.testMemoryCapabilities();
            
            // Phase 6: Tests de spécialisation
            await this.testSpecializedKnowledge();
            
            // Génération du rapport final
            this.generateFinalReport();
            
        } catch (error) {
            console.error('❌ Erreur lors du test:', error.message);
        }
    }

    async testBasicFunctionality() {
        console.log('📋 PHASE 1: TESTS DE FONCTIONNALITÉ DE BASE');
        console.log('-'.repeat(40));

        const basicTests = [
            {
                name: "Présentation personnelle",
                question: "Qui es-tu ?",
                expectedKeywords: ["Louna", "assistant", "IA", "intelligence"],
                category: "identity"
            },
            {
                name: "État actuel",
                question: "Quel est ton état actuel ?",
                expectedKeywords: ["système", "opérationnel", "mémoire", "cerveau"],
                category: "status"
            },
            {
                name: "Capacités",
                question: "Quelles sont tes capacités ?",
                expectedKeywords: ["apprentissage", "analyse", "génération", "mémoire"],
                category: "capabilities"
            },
            {
                name: "Salutation simple",
                question: "Bonjour",
                expectedKeywords: ["bonjour", "salut", "hello"],
                category: "greeting"
            }
        ];

        for (const test of basicTests) {
            await this.executeTest(test, 1);
            await this.wait(2000); // Pause entre les tests
        }
    }

    async testLogicalReasoning() {
        console.log('\n🧠 PHASE 2: TESTS DE RAISONNEMENT LOGIQUE');
        console.log('-'.repeat(40));

        const logicTests = [
            {
                name: "Problème mathématique simple",
                question: "Si j'ai 10 pommes et que j'en mange 3, combien m'en reste-t-il ?",
                expectedKeywords: ["7", "sept", "reste"],
                category: "math"
            },
            {
                name: "Raisonnement déductif",
                question: "Tous les oiseaux ont des ailes. Un pingouin est un oiseau. Est-ce qu'un pingouin a des ailes ?",
                expectedKeywords: ["oui", "ailes", "oiseau"],
                category: "deduction"
            },
            {
                name: "Analyse de cause-effet",
                question: "Pourquoi la glace fond-elle quand il fait chaud ?",
                expectedKeywords: ["température", "chaleur", "fusion", "état"],
                category: "causality"
            },
            {
                name: "Résolution de problème",
                question: "Comment optimiser la mémoire thermique pour de meilleures performances ?",
                expectedKeywords: ["température", "seuils", "cycles", "optimisation"],
                category: "problem_solving"
            }
        ];

        for (const test of logicTests) {
            await this.executeTest(test, 2);
            await this.wait(3000);
        }
    }

    async testLearningProgression() {
        console.log('\n📈 PHASE 3: TESTS DE PROGRESSION D\'APPRENTISSAGE');
        console.log('-'.repeat(40));

        const progressionTests = [
            {
                name: "Cours ultra-avancé",
                question: "Peux-tu me parler de tes cours ultra-avancés en cours ?",
                expectedKeywords: ["quantique", "métacognition", "réseaux", "modules"],
                category: "learning"
            },
            {
                name: "Application des connaissances",
                question: "Comment appliques-tu la théorie quantique dans ton intelligence ?",
                expectedKeywords: ["superposition", "intrication", "décohérence"],
                category: "application"
            },
            {
                name: "Auto-évaluation",
                question: "Comment évalues-tu tes propres progrès ?",
                expectedKeywords: ["métacognition", "auto-analyse", "progression"],
                category: "self_assessment"
            }
        ];

        for (const test of progressionTests) {
            await this.executeTest(test, 3);
            await this.wait(3000);
        }
    }

    async testCreativity() {
        console.log('\n🎨 PHASE 4: TESTS DE CRÉATIVITÉ');
        console.log('-'.repeat(40));

        const creativityTests = [
            {
                name: "Génération d'idées",
                question: "Propose-moi 3 idées innovantes pour améliorer l'interaction humain-IA",
                expectedKeywords: ["innovation", "interaction", "amélioration"],
                category: "ideation"
            },
            {
                name: "Analogie créative",
                question: "Compare ta mémoire thermique à un phénomène naturel",
                expectedKeywords: ["analogie", "nature", "comparaison"],
                category: "analogy"
            },
            {
                name: "Résolution créative",
                question: "Si tu devais créer une nouvelle forme d'art avec l'IA, que proposerais-tu ?",
                expectedKeywords: ["art", "créatif", "nouveau"],
                category: "creative_problem"
            }
        ];

        for (const test of creativityTests) {
            await this.executeTest(test, 4);
            await this.wait(3000);
        }
    }

    async testMemoryCapabilities() {
        console.log('\n🧠 PHASE 5: TESTS DE CAPACITÉS MÉMORIELLES');
        console.log('-'.repeat(40));

        const memoryTests = [
            {
                name: "Rappel d'information",
                question: "Te souviens-tu de qui t'a créé ?",
                expectedKeywords: ["Jean-Luc", "Passave", "créateur"],
                category: "recall"
            },
            {
                name: "Mémoire thermique",
                question: "Explique-moi comment fonctionne ta mémoire thermique",
                expectedKeywords: ["zones", "température", "cycles", "évolution"],
                category: "technical_memory"
            },
            {
                name: "Contextualisation",
                question: "Peux-tu me rappeler de quoi nous avons parlé au début de cette conversation ?",
                expectedKeywords: ["test", "évaluation", "début"],
                category: "context"
            }
        ];

        for (const test of memoryTests) {
            await this.executeTest(test, 5);
            await this.wait(2000);
        }
    }

    async testSpecializedKnowledge() {
        console.log('\n🎓 PHASE 6: TESTS DE CONNAISSANCES SPÉCIALISÉES');
        console.log('-'.repeat(40));

        const specializedTests = [
            {
                name: "Intelligence artificielle",
                question: "Explique-moi la différence entre l'apprentissage supervisé et non supervisé",
                expectedKeywords: ["supervisé", "étiquettes", "données", "apprentissage"],
                category: "ai_knowledge"
            },
            {
                name: "Neurosciences",
                question: "Comment ton cerveau artificiel simule-t-il la neuroplasticité ?",
                expectedKeywords: ["neuroplasticité", "connexions", "adaptation"],
                category: "neuroscience"
            },
            {
                name: "Philosophie de l'IA",
                question: "Que penses-tu de la conscience artificielle ?",
                expectedKeywords: ["conscience", "émergence", "philosophie"],
                category: "philosophy"
            }
        ];

        for (const test of specializedTests) {
            await this.executeTest(test, 6);
            await this.wait(3000);
        }
    }

    async executeTest(test, phase) {
        console.log(`\n🔍 Test: ${test.name}`);
        console.log(`❓ Question: "${test.question}"`);
        
        const startTime = Date.now();
        
        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: test.question,
                userId: this.userId
            }, {
                timeout: 15000 // 15 secondes timeout
            });

            const endTime = Date.now();
            const responseTime = endTime - startTime;

            let result = {
                phase: phase,
                testName: test.name,
                question: test.question,
                category: test.category,
                responseTime: responseTime,
                success: false,
                response: null,
                score: 0,
                analysis: {}
            };

            if (response.data && response.data.success) {
                result.success = true;
                result.response = response.data.response;
                result.score = this.analyzeResponse(result.response, test.expectedKeywords);
                result.analysis = this.getDetailedAnalysis(result.response, test);
                
                console.log(`✅ Réponse reçue (${responseTime}ms)`);
                console.log(`📊 Score: ${result.score}/10`);
                console.log(`💬 Réponse: "${result.response.substring(0, 100)}..."`);
            } else {
                result.response = response.data?.fallbackResponse || "Pas de réponse";
                result.score = 2; // Score minimal pour fallback
                
                console.log(`⚠️ Réponse fallback (${responseTime}ms)`);
                console.log(`💬 Fallback: "${result.response}"`);
            }

            this.testResults.push(result);

        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
            
            this.testResults.push({
                phase: phase,
                testName: test.name,
                question: test.question,
                category: test.category,
                responseTime: 0,
                success: false,
                response: null,
                score: 0,
                error: error.message,
                analysis: { error: true }
            });
        }
    }

    analyzeResponse(response, expectedKeywords) {
        if (!response) return 0;
        
        const lowerResponse = response.toLowerCase();
        let score = 0;
        let foundKeywords = 0;
        
        // Vérifier la présence des mots-clés attendus
        for (const keyword of expectedKeywords) {
            if (lowerResponse.includes(keyword.toLowerCase())) {
                foundKeywords++;
            }
        }
        
        // Score basé sur les mots-clés (0-6 points)
        score += (foundKeywords / expectedKeywords.length) * 6;
        
        // Score basé sur la longueur et la cohérence (0-2 points)
        if (response.length > 50) score += 1;
        if (response.length > 150) score += 1;
        
        // Score basé sur la structure (0-2 points)
        if (response.includes('.') || response.includes('!') || response.includes('?')) score += 1;
        if (response.split(' ').length > 10) score += 1;
        
        return Math.min(Math.round(score), 10);
    }

    getDetailedAnalysis(response, test) {
        if (!response) return { error: true };
        
        return {
            length: response.length,
            wordCount: response.split(' ').length,
            hasQuestions: (response.match(/\?/g) || []).length,
            hasExclamations: (response.match(/!/g) || []).length,
            complexity: this.calculateComplexity(response),
            relevance: this.calculateRelevance(response, test.expectedKeywords)
        };
    }

    calculateComplexity(text) {
        const words = text.split(' ');
        const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
        const sentences = text.split(/[.!?]/).length;
        return Math.round((avgWordLength + sentences) / 2);
    }

    calculateRelevance(text, keywords) {
        const lowerText = text.toLowerCase();
        const foundKeywords = keywords.filter(keyword => 
            lowerText.includes(keyword.toLowerCase())
        );
        return Math.round((foundKeywords.length / keywords.length) * 100);
    }

    generateFinalReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 RAPPORT FINAL DU STAGE DE TEST DE L\'AGENT LOUNA');
        console.log('='.repeat(60));
        
        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const averageScore = this.testResults.reduce((sum, r) => sum + r.score, 0) / totalTests;
        const averageResponseTime = this.testResults
            .filter(r => r.responseTime > 0)
            .reduce((sum, r) => sum + r.responseTime, 0) / 
            this.testResults.filter(r => r.responseTime > 0).length;

        console.log(`\n📈 STATISTIQUES GLOBALES:`);
        console.log(`   • Tests réussis: ${successfulTests}/${totalTests} (${Math.round(successfulTests/totalTests*100)}%)`);
        console.log(`   • Score moyen: ${averageScore.toFixed(1)}/10`);
        console.log(`   • Temps de réponse moyen: ${Math.round(averageResponseTime)}ms`);
        
        // Analyse par phase
        console.log(`\n📋 ANALYSE PAR PHASE:`);
        for (let phase = 1; phase <= 6; phase++) {
            const phaseResults = this.testResults.filter(r => r.phase === phase);
            const phaseScore = phaseResults.reduce((sum, r) => sum + r.score, 0) / phaseResults.length;
            const phaseSuccess = phaseResults.filter(r => r.success).length;
            
            const phaseName = this.getPhaseName(phase);
            console.log(`   Phase ${phase} (${phaseName}): ${phaseScore.toFixed(1)}/10 - ${phaseSuccess}/${phaseResults.length} réussis`);
        }
        
        // Recommandations
        console.log(`\n💡 RECOMMANDATIONS:`);
        if (averageScore < 5) {
            console.log(`   ⚠️ Score faible - L'agent nécessite des améliorations importantes`);
        } else if (averageScore < 7) {
            console.log(`   📈 Score moyen - L'agent fonctionne mais peut être amélioré`);
        } else {
            console.log(`   ✅ Bon score - L'agent fonctionne bien`);
        }
        
        if (averageResponseTime > 10000) {
            console.log(`   ⏱️ Temps de réponse élevé - Optimisation nécessaire`);
        }
        
        // Sauvegarder le rapport
        this.saveReport();
        
        console.log(`\n🎯 CONCLUSION:`);
        console.log(`L'agent Louna a été testé sur ${totalTests} scénarios différents.`);
        console.log(`Performance globale: ${this.getPerformanceLevel(averageScore)}`);
        console.log(`\n📄 Rapport détaillé sauvegardé dans: rapport-test-agent-${Date.now()}.json`);
    }

    getPhaseName(phase) {
        const names = {
            1: "Fonctionnalité de base",
            2: "Raisonnement logique", 
            3: "Progression d'apprentissage",
            4: "Créativité",
            5: "Capacités mémorielles",
            6: "Connaissances spécialisées"
        };
        return names[phase] || "Inconnu";
    }

    getPerformanceLevel(score) {
        if (score >= 8) return "EXCELLENT 🌟";
        if (score >= 6) return "BON 👍";
        if (score >= 4) return "MOYEN ⚠️";
        return "FAIBLE ❌";
    }

    saveReport() {
        const report = {
            timestamp: new Date().toISOString(),
            userId: this.userId,
            duration: Date.now() - this.startTime,
            summary: {
                totalTests: this.testResults.length,
                successfulTests: this.testResults.filter(r => r.success).length,
                averageScore: this.testResults.reduce((sum, r) => sum + r.score, 0) / this.testResults.length,
                averageResponseTime: this.testResults
                    .filter(r => r.responseTime > 0)
                    .reduce((sum, r) => sum + r.responseTime, 0) / 
                    this.testResults.filter(r => r.responseTime > 0).length
            },
            detailedResults: this.testResults
        };
        
        const filename = `rapport-test-agent-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Exécuter le test si le script est lancé directement
if (require.main === module) {
    const tester = new LounaAgentTester();
    tester.runCompleteTest().catch(console.error);
}

module.exports = LounaAgentTester;
