/**
 * Système d'auto-apprentissage pour la mémoire thermique
 *
 * Ce module analyse les patterns de température et ajuste les paramètres
 * de la mémoire thermique pour optimiser son fonctionnement.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, 'data/learning');
const LEARNING_FILE = path.join(DATA_DIR, 'thermal_learning.json');

/**
 * Classe ThermalLearningSystem - Gère l'auto-apprentissage de la mémoire thermique
 */
class ThermalLearningSystem {
  /**
   * Initialise le système d'apprentissage
   * @param {Object} config - Configuration du système d'apprentissage
   */
  constructor(config = {}) {
    // Configuration par défaut - BOOSTÉE POUR APPRENTISSAGE RAPIDE
    this.config = {
      learningRate: config.learningRate || 0.1, // ACCÉLÉRÉ 10x (était 0.01)
      optimizationInterval: config.optimizationInterval || 2, // ACCÉLÉRÉ 5x (était 10)
      maxHistoryLength: config.maxHistoryLength || 500, // AUGMENTÉ 5x (était 100)
      minDataPoints: config.minDataPoints || 3, // RÉDUIT pour optimiser plus vite (était 10)
      adaptationEnabled: config.adaptationEnabled !== undefined ? config.adaptationEnabled : true,
      debug: config.debug || false,
      // NOUVEAUX PARAMÈTRES DE BOOST
      rapidLearningMode: config.rapidLearningMode || true,
      aggressiveOptimization: config.aggressiveOptimization || true,
      learningBoostFactor: config.learningBoostFactor || 5.0,
      ...config
    };

    // Données d'apprentissage
    this.learningData = {
      temperatureHistory: [], // Historique des températures du système
      memoryStats: [], // Historique des statistiques de mémoire
      parameterHistory: [], // Historique des paramètres
      optimizationHistory: [], // Historique des optimisations
      insights: {}, // Insights découverts
      lastOptimization: null, // Date de la dernière optimisation
      cyclesSinceOptimization: 0, // Nombre de cycles depuis la dernière optimisation
      version: 1 // Version du format de données
    };

    // Paramètres optimisables
    this.optimizableParameters = {
      temperatureCursorSensitivity: {
        min: 0.01,
        max: 0.3,
        current: config.temperatureCursorSensitivity || 0.1
      },
      memoryDecayRate: {
        min: 0.8,
        max: 0.99,
        current: config.memoryDecayRate || 0.95
      },
      importanceFactor: {
        min: 1.0,
        max: 2.0,
        current: config.importanceFactor || 1.2
      },
      accessFactor: {
        min: 1.01,
        max: 1.2,
        current: config.accessFactor || 1.05
      },
      decayFactor: {
        min: 0.9,
        max: 0.99,
        current: config.decayFactor || 0.98
      }
    };

    // Métriques de performance
    this.performanceMetrics = {
      memoryEfficiency: 0, // Efficacité de la mémoire (0-1)
      temperatureStability: 0, // Stabilité des températures (0-1)
      accessOptimization: 0, // Optimisation des accès (0-1)
      overallPerformance: 0 // Performance globale (0-1)
    };

    // Initialiser le dossier de données
    this.initDataDir();

    // Charger les données existantes
    this.loadLearningData();

    console.log('Système d\'apprentissage thermique initialisé');
  }

  /**
   * Initialise le dossier de données
   */
  async initDataDir() {
    try {
      if (!await existsAsync(DATA_DIR)) {
        await promisify(fs.mkdir)(DATA_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du dossier de données d\'apprentissage:', error);
    }
  }

  /**
   * Charge les données d'apprentissage depuis le fichier
   */
  async loadLearningData() {
    try {
      if (await existsAsync(LEARNING_FILE)) {
        const data = await readFileAsync(LEARNING_FILE, 'utf8');
        const learningData = JSON.parse(data);

        // Vérifier la version du format de données
        if (learningData.version === this.learningData.version) {
          this.learningData = learningData;

          // Mettre à jour les paramètres optimisables avec les valeurs sauvegardées
          if (learningData.parameterHistory && learningData.parameterHistory.length > 0) {
            const lastParameters = learningData.parameterHistory[learningData.parameterHistory.length - 1];

            for (const [key, value] of Object.entries(lastParameters.parameters)) {
              if (this.optimizableParameters[key]) {
                this.optimizableParameters[key].current = value;
              }
            }
          }

          console.log('Données d\'apprentissage chargées avec succès');
        } else {
          console.log('Version des données d\'apprentissage incompatible, création d\'un nouveau fichier');
          this.saveLearningData();
        }
      } else {
        console.log('Aucun fichier de données d\'apprentissage existant, création d\'un nouveau fichier');
        this.saveLearningData();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données d\'apprentissage:', error);
      // Créer un nouveau fichier de données d'apprentissage en cas d'erreur
      this.saveLearningData();
    }
  }

  /**
   * Sauvegarde les données d'apprentissage dans le fichier
   */
  async saveLearningData() {
    try {
      await writeFileAsync(LEARNING_FILE, JSON.stringify(this.learningData, null, 2), 'utf8');
      console.log('Données d\'apprentissage sauvegardées avec succès');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données d\'apprentissage:', error);
    }
  }

  /**
   * Enregistre les données du cycle de mémoire actuel
   * @param {Object} memoryStats - Statistiques de la mémoire
   * @param {Object} systemTemperatures - Températures du système
   * @param {Object} currentParameters - Paramètres actuels
   */
  recordCycleData(memoryStats, systemTemperatures, currentParameters) {
    // Enregistrer les températures du système
    this.learningData.temperatureHistory.push({
      timestamp: Date.now(),
      temperatures: systemTemperatures,
      normalizedTemperature: systemTemperatures.normalized
    });

    // Limiter la taille de l'historique des températures
    if (this.learningData.temperatureHistory.length > this.config.maxHistoryLength) {
      this.learningData.temperatureHistory.shift();
    }

    // Enregistrer les statistiques de mémoire
    this.learningData.memoryStats.push({
      timestamp: Date.now(),
      stats: memoryStats
    });

    // Limiter la taille de l'historique des statistiques
    if (this.learningData.memoryStats.length > this.config.maxHistoryLength) {
      this.learningData.memoryStats.shift();
    }

    // Incrémenter le compteur de cycles depuis la dernière optimisation
    this.learningData.cyclesSinceOptimization++;

    // Vérifier si une optimisation est nécessaire
    if (this.config.adaptationEnabled &&
        this.learningData.cyclesSinceOptimization >= this.config.optimizationInterval &&
        this.learningData.memoryStats.length >= this.config.minDataPoints) {
      this.optimizeParameters();
    }

    // Sauvegarder les données d'apprentissage
    this.saveLearningData();
  }

  /**
   * Optimise les paramètres en fonction des données d'apprentissage
   */
  optimizeParameters() {
    console.log('Optimisation des paramètres...');

    // Calculer les métriques de performance
    this.calculatePerformanceMetrics();

    // Sauvegarder les paramètres actuels
    const currentParameters = {};
    for (const [key, param] of Object.entries(this.optimizableParameters)) {
      currentParameters[key] = param.current;
    }

    // Enregistrer les paramètres actuels dans l'historique
    this.learningData.parameterHistory.push({
      timestamp: Date.now(),
      parameters: { ...currentParameters },
      performanceMetrics: { ...this.performanceMetrics }
    });

    // Limiter la taille de l'historique des paramètres
    if (this.learningData.parameterHistory.length > this.config.maxHistoryLength) {
      this.learningData.parameterHistory.shift();
    }

    // Ajuster les paramètres en fonction des métriques de performance
    this.adjustParameters();

    // Enregistrer l'optimisation dans l'historique
    const optimizedParameters = {};
    for (const [key, param] of Object.entries(this.optimizableParameters)) {
      optimizedParameters[key] = param.current;
    }

    this.learningData.optimizationHistory.push({
      timestamp: Date.now(),
      before: currentParameters,
      after: optimizedParameters,
      performanceMetrics: { ...this.performanceMetrics }
    });

    // Limiter la taille de l'historique des optimisations
    if (this.learningData.optimizationHistory.length > this.config.maxHistoryLength) {
      this.learningData.optimizationHistory.shift();
    }

    // Mettre à jour les statistiques d'optimisation
    this.learningData.lastOptimization = Date.now();
    this.learningData.cyclesSinceOptimization = 0;

    // Sauvegarder les données d'apprentissage
    this.saveLearningData();

    console.log('Optimisation des paramètres terminée');

    return optimizedParameters;
  }

  /**
   * Calcule les métriques de performance
   */
  calculatePerformanceMetrics() {
    // Récupérer les statistiques récentes
    const recentStats = this.learningData.memoryStats.slice(-10);

    if (recentStats.length < 2) {
      return;
    }

    // Calculer l'efficacité de la mémoire
    // (Basée sur la distribution des entrées entre les zones)
    let totalEntries = 0;
    let zoneDistribution = [0, 0, 0, 0, 0, 0]; // Distribution dans les 6 zones

    recentStats.forEach(stat => {
      totalEntries += stat.stats.totalMemories;
      zoneDistribution[0] += stat.stats.zone1Count;
      zoneDistribution[1] += stat.stats.zone2Count;
      zoneDistribution[2] += stat.stats.zone3Count;
      zoneDistribution[3] += stat.stats.zone4Count;
      zoneDistribution[4] += stat.stats.zone5Count;
      zoneDistribution[5] += stat.stats.zone6Count;
    });

    // Normaliser la distribution
    if (totalEntries > 0) {
      zoneDistribution = zoneDistribution.map(count => count / totalEntries);
    }

    // Une distribution idéale serait quelque chose comme [0.05, 0.15, 0.2, 0.3, 0.25, 0.05]
    const idealDistribution = [0.05, 0.15, 0.2, 0.3, 0.25, 0.05];

    // Calculer la distance entre la distribution actuelle et la distribution idéale
    let distributionDistance = 0;
    for (let i = 0; i < 6; i++) {
      distributionDistance += Math.abs(zoneDistribution[i] - idealDistribution[i]);
    }

    // Convertir la distance en efficacité (0-1)
    this.performanceMetrics.memoryEfficiency = Math.max(0, 1 - distributionDistance);

    // Calculer la stabilité des températures
    // (Basée sur la variance des températures du système)
    const recentTemperatures = this.learningData.temperatureHistory.slice(-20);

    if (recentTemperatures.length > 0) {
      const normalizedTemps = recentTemperatures.map(t => t.normalizedTemperature);
      const avgTemp = normalizedTemps.reduce((sum, temp) => sum + temp, 0) / normalizedTemps.length;

      // Calculer la variance
      const variance = normalizedTemps.reduce((sum, temp) => sum + Math.pow(temp - avgTemp, 2), 0) / normalizedTemps.length;

      // Convertir la variance en stabilité (0-1)
      // Une variance de 0 signifie une stabilité parfaite, une variance de 0.1 ou plus signifie une instabilité
      this.performanceMetrics.temperatureStability = Math.max(0, 1 - (variance * 10));
    }

    // Calculer l'optimisation des accès
    // (Basée sur la fréquence d'accès aux entrées importantes)
    // Cette métrique est simulée pour l'instant
    this.performanceMetrics.accessOptimization = 0.7 + (Math.random() * 0.3);

    // Calculer la performance globale
    this.performanceMetrics.overallPerformance = (
      this.performanceMetrics.memoryEfficiency * 0.4 +
      this.performanceMetrics.temperatureStability * 0.3 +
      this.performanceMetrics.accessOptimization * 0.3
    );
  }

  /**
   * Ajuste les paramètres en fonction des métriques de performance
   */
  adjustParameters() {
    // Ajuster la sensibilité du curseur de température en fonction de la stabilité des températures
    if (this.performanceMetrics.temperatureStability < 0.5) {
      // Si la stabilité est faible, réduire la sensibilité
      this.optimizableParameters.temperatureCursorSensitivity.current = Math.max(
        this.optimizableParameters.temperatureCursorSensitivity.min,
        this.optimizableParameters.temperatureCursorSensitivity.current * (1 - this.config.learningRate)
      );
    } else {
      // Si la stabilité est élevée, augmenter légèrement la sensibilité
      this.optimizableParameters.temperatureCursorSensitivity.current = Math.min(
        this.optimizableParameters.temperatureCursorSensitivity.max,
        this.optimizableParameters.temperatureCursorSensitivity.current * (1 + this.config.learningRate * 0.5)
      );
    }

    // Ajuster le taux de décroissance de la mémoire en fonction de l'efficacité de la mémoire
    if (this.performanceMetrics.memoryEfficiency < 0.6) {
      // Si l'efficacité est faible, augmenter le taux de décroissance
      this.optimizableParameters.memoryDecayRate.current = Math.min(
        this.optimizableParameters.memoryDecayRate.max,
        this.optimizableParameters.memoryDecayRate.current + this.config.learningRate
      );
    } else {
      // Si l'efficacité est élevée, réduire légèrement le taux de décroissance
      this.optimizableParameters.memoryDecayRate.current = Math.max(
        this.optimizableParameters.memoryDecayRate.min,
        this.optimizableParameters.memoryDecayRate.current - this.config.learningRate * 0.5
      );
    }

    // Ajuster le facteur d'importance en fonction de la distribution des entrées
    // Cette logique peut être affinée en fonction des besoins spécifiques
    this.optimizableParameters.importanceFactor.current = Math.min(
      this.optimizableParameters.importanceFactor.max,
      Math.max(
        this.optimizableParameters.importanceFactor.min,
        this.optimizableParameters.importanceFactor.current * (1 + (Math.random() * 0.1 - 0.05))
      )
    );

    // Ajuster le facteur d'accès en fonction de l'optimisation des accès
    if (this.performanceMetrics.accessOptimization < 0.7) {
      // Si l'optimisation des accès est faible, augmenter le facteur d'accès
      this.optimizableParameters.accessFactor.current = Math.min(
        this.optimizableParameters.accessFactor.max,
        this.optimizableParameters.accessFactor.current + this.config.learningRate * 0.1
      );
    }

    // Ajuster le facteur de décroissance en fonction de la stabilité des températures
    this.optimizableParameters.decayFactor.current = Math.min(
      this.optimizableParameters.decayFactor.max,
      Math.max(
        this.optimizableParameters.decayFactor.min,
        this.optimizableParameters.decayFactor.current * (1 + (this.performanceMetrics.temperatureStability - 0.5) * this.config.learningRate)
      )
    );
  }

  /**
   * Récupère les paramètres optimisés
   * @returns {Object} - Paramètres optimisés
   */
  getOptimizedParameters() {
    const optimizedParameters = {};

    for (const [key, param] of Object.entries(this.optimizableParameters)) {
      optimizedParameters[key] = param.current;
    }

    return optimizedParameters;
  }

  /**
   * Récupère les métriques de performance
   * @returns {Object} - Métriques de performance
   */
  getPerformanceMetrics() {
    return { ...this.performanceMetrics };
  }

  /**
   * Récupère les insights découverts
   * @returns {Object} - Insights découverts
   */
  getInsights() {
    return { ...this.learningData.insights };
  }

  /**
   * Active ou désactive l'adaptation automatique
   * @param {boolean} enabled - Activer l'adaptation
   */
  setAdaptationEnabled(enabled) {
    this.config.adaptationEnabled = enabled;
    console.log(`Adaptation automatique ${enabled ? 'activée' : 'désactivée'}`);
    return this.config.adaptationEnabled;
  }
}

module.exports = ThermalLearningSystem;
