#!/bin/bash

# Script pour renommer Luna en Louna dans tous les fichiers de présentation
# Ce script remplace toutes les occurrences de "Luna" par "Louna" dans les fichiers spécifiés

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██████╗ ███████╗███╗   ██╗ ██████╗ ███╗   ███╗███╗   ███╗ █████╗  ██████╗ ███████╗"
echo "██╔══██╗██╔════╝████╗  ██║██╔═══██╗████╗ ████║████╗ ████║██╔══██╗██╔════╝ ██╔════╝"
echo "██████╔╝█████╗  ██╔██╗ ██║██║   ██║██╔████╔██║██╔████╔██║███████║██║  ███╗█████╗  "
echo "██╔══██╗██╔══╝  ██║╚██╗██║██║   ██║██║╚██╔╝██║██║╚██╔╝██║██╔══██║██║   ██║██╔══╝  "
echo "██║  ██║███████╗██║ ╚████║╚██████╔╝██║ ╚═╝ ██║██║ ╚═╝ ██║██║  ██║╚██████╔╝███████╗"
echo "╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚═╝     ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝"
echo -e "${NC}"
echo -e "${CYAN}Renommage de Luna en Louna${NC}"
echo ""
print_message "Début du renommage de Luna en Louna..."
sleep 1

# Étape 1 : Modifier les fichiers de vue (EJS)
print_message "Étape 1 : Modification des fichiers de vue (EJS)..."

# Trouver tous les fichiers EJS
EJS_FILES=$(find "$APP_DIR/views" -name "*.ejs" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier EJS
for file in $EJS_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers de vue (EJS) modifiés."

# Étape 2 : Modifier les fichiers JavaScript
print_message "Étape 2 : Modification des fichiers JavaScript..."

# Trouver tous les fichiers JavaScript
JS_FILES=$(find "$APP_DIR/public/js" -name "*.js" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier JavaScript
for file in $JS_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers JavaScript modifiés."

# Étape 3 : Modifier les fichiers CSS
print_message "Étape 3 : Modification des fichiers CSS..."

# Trouver tous les fichiers CSS
CSS_FILES=$(find "$APP_DIR/public/css" -name "*.css" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier CSS
for file in $CSS_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers CSS modifiés."

# Étape 4 : Modifier les fichiers HTML
print_message "Étape 4 : Modification des fichiers HTML..."

# Trouver tous les fichiers HTML
HTML_FILES=$(find "$APP_DIR/public" -name "*.html" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier HTML
for file in $HTML_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers HTML modifiés."

# Étape 5 : Modifier les fichiers Markdown
print_message "Étape 5 : Modification des fichiers Markdown..."

# Trouver tous les fichiers Markdown
MD_FILES=$(find "$APP_DIR" -name "*.md" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier Markdown
for file in $MD_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers Markdown modifiés."

# Étape 6 : Modifier le fichier server.js
print_message "Étape 6 : Modification du fichier server.js..."

# Sauvegarder une copie du fichier original
cp "$APP_DIR/server.js" "$APP_DIR/server.js.bak"

# Remplacer "Luna" par "Louna" en préservant la casse
sed -i '' 's/Luna/Louna/g' "$APP_DIR/server.js"
sed -i '' 's/LUNA/LOUNA/g' "$APP_DIR/server.js"

print_success "Fichier server.js modifié."

# Étape 7 : Modifier les fichiers de routes
print_message "Étape 7 : Modification des fichiers de routes..."

# Trouver tous les fichiers de routes
ROUTE_FILES=$(find "$APP_DIR/routes" -name "*.js" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier de routes
for file in $ROUTE_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers de routes modifiés."

# Étape 8 : Modifier les fichiers de middleware
print_message "Étape 8 : Modification des fichiers de middleware..."

# Trouver tous les fichiers de middleware
MIDDLEWARE_FILES=$(find "$APP_DIR/middleware" -name "*.js" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier de middleware
for file in $MIDDLEWARE_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers de middleware modifiés."

# Étape 9 : Modifier les fichiers de bibliothèque
print_message "Étape 9 : Modification des fichiers de bibliothèque..."

# Trouver tous les fichiers de bibliothèque
LIB_FILES=$(find "$APP_DIR/lib" -name "*.js" -not -path "*/node_modules/*" -not -path "*/backups/*")

# Remplacer Luna par Louna dans chaque fichier de bibliothèque
for file in $LIB_FILES; do
  # Sauvegarder une copie du fichier original
  cp "$file" "$file.bak"
  
  # Remplacer "Luna" par "Louna" en préservant la casse
  sed -i '' 's/Luna/Louna/g' "$file"
  sed -i '' 's/LUNA/LOUNA/g' "$file"
  
  print_message "Fichier modifié: $file"
done

print_success "Fichiers de bibliothèque modifiés."

print_success "Renommage de Luna en Louna terminé !"
print_message "Tous les fichiers ont été modifiés pour utiliser le nom Louna au lieu de Luna."
print_message "Vous pouvez maintenant lancer l'application avec le nom Louna."
