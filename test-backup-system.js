/**
 * Script de test pour le système de sauvegarde d'urgence
 * Ce script teste la protection contre les coupures et la récupération des données
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3005';

async function testBackupSystem() {
    console.log('🧪 Test du système de sauvegarde d\'urgence...\n');

    try {
        // 1. Vérifier l'état du système de sauvegarde
        console.log('1. Vérification de l\'état du système de sauvegarde...');
        const statusResponse = await axios.get(`${BASE_URL}/api/emergency-backup/status`);
        
        if (statusResponse.data.success) {
            const stats = statusResponse.data.stats;
            console.log('✅ Système de sauvegarde actif');
            console.log(`   - En cours d'exécution: ${stats.isRunning ? 'Oui' : 'Non'}`);
            console.log(`   - Nombre de sauvegardes: ${stats.backupCount}`);
            console.log(`   - Der<PERSON><PERSON> sauvegarde: ${new Date(stats.lastBackupTime).toLocaleString()}`);
            console.log(`   - Répertoire: ${stats.backupDirectory}`);
        } else {
            console.log('❌ Erreur lors de la vérification du statut:', statusResponse.data.error);
            return;
        }

        // 2. Forcer une sauvegarde d'urgence
        console.log('\n2. Test de sauvegarde d\'urgence forcée...');
        const forceResponse = await axios.post(`${BASE_URL}/api/emergency-backup/force`);
        
        if (forceResponse.data.success) {
            console.log('✅ Sauvegarde d\'urgence forcée réussie');
        } else {
            console.log('❌ Erreur lors de la sauvegarde forcée:', forceResponse.data.error);
        }

        // 3. Lister les sauvegardes disponibles
        console.log('\n3. Liste des sauvegardes disponibles...');
        const listResponse = await axios.get(`${BASE_URL}/api/emergency-backup/list`);
        
        if (listResponse.data.success) {
            const backups = listResponse.data.backups;
            console.log(`✅ ${backups.length} sauvegarde(s) trouvée(s):`);
            
            backups.slice(0, 5).forEach((backup, index) => {
                const sizeKB = (backup.size / 1024).toFixed(2);
                console.log(`   ${index + 1}. ${backup.name} (${sizeKB} KB) - ${new Date(backup.created).toLocaleString()}`);
            });
            
            if (backups.length > 5) {
                console.log(`   ... et ${backups.length - 5} autre(s) sauvegarde(s)`);
            }
        } else {
            console.log('❌ Erreur lors de la récupération de la liste:', listResponse.data.error);
        }

        // 4. Vérifier l'état de la mémoire thermique
        console.log('\n4. Vérification de l\'état de la mémoire thermique...');
        const memoryResponse = await axios.get(`${BASE_URL}/api/thermal/memory/stats`);
        
        if (memoryResponse.data.success) {
            const stats = memoryResponse.data.stats;
            console.log('✅ Mémoire thermique active');
            console.log(`   - Entrées totales: ${stats.totalEntries || 0}`);
            console.log(`   - Cycles effectués: ${stats.cyclesPerformed || 0}`);
            console.log(`   - Température moyenne: ${(stats.averageTemperature || 0).toFixed(3)}`);
        } else {
            console.log('❌ Erreur lors de la vérification de la mémoire:', memoryResponse.data.error);
        }

        // 5. Vérifier l'état des accélérateurs Kyber
        console.log('\n5. Vérification de l\'état des accélérateurs Kyber...');
        try {
            const kyberResponse = await axios.get(`${BASE_URL}/api/kyber/stats`);
            
            if (kyberResponse.data.success) {
                console.log('✅ Accélérateurs Kyber actifs');
                console.log(`   - Nombre d'accélérateurs: ${Object.keys(kyberResponse.data.stats.accelerators || {}).length}`);
            } else {
                console.log('⚠️ Accélérateurs Kyber non disponibles');
            }
        } catch (error) {
            console.log('⚠️ Accélérateurs Kyber non accessibles');
        }

        // 6. Test de simulation de coupure (ajouter des données puis vérifier la sauvegarde)
        console.log('\n6. Test de simulation de données critiques...');
        
        // Ajouter une entrée de test à la mémoire thermique
        const testData = {
            key: `test_backup_${Date.now()}`,
            data: {
                type: 'test_backup',
                message: 'Données de test pour vérifier la sauvegarde d\'urgence',
                timestamp: new Date().toISOString(),
                importance: 'critique'
            },
            importance: 0.9,
            category: 'test',
            metadata: {
                testBackup: true,
                agentId: 'test_agent'
            }
        };

        try {
            const addResponse = await axios.post(`${BASE_URL}/api/thermal/memory/add`, testData);
            
            if (addResponse.data.success) {
                console.log('✅ Données de test ajoutées à la mémoire thermique');
                console.log(`   - ID: ${addResponse.data.id}`);
                
                // Attendre un peu pour que la sauvegarde automatique se déclenche
                console.log('   - Attente de la sauvegarde automatique...');
                await new Promise(resolve => setTimeout(resolve, 6000)); // 6 secondes
                
                // Vérifier que les données sont toujours là
                const verifyResponse = await axios.get(`${BASE_URL}/api/thermal/memory/entry/${addResponse.data.id}`);
                
                if (verifyResponse.data.success) {
                    console.log('✅ Données de test récupérées avec succès');
                } else {
                    console.log('❌ Impossible de récupérer les données de test');
                }
            } else {
                console.log('❌ Erreur lors de l\'ajout des données de test:', addResponse.data.error);
            }
        } catch (error) {
            console.log('❌ Erreur lors du test de données:', error.message);
        }

        // 7. Vérifier la taille et l'intégrité des sauvegardes
        console.log('\n7. Vérification de l\'intégrité des sauvegardes...');
        
        const backupDir = path.join(__dirname, 'data', 'emergency_backups');
        if (fs.existsSync(backupDir)) {
            const files = fs.readdirSync(backupDir).filter(file => file.endsWith('.json'));
            
            if (files.length > 0) {
                // Vérifier le fichier de sauvegarde le plus récent
                const latestFile = files
                    .map(file => ({
                        name: file,
                        path: path.join(backupDir, file),
                        time: fs.statSync(path.join(backupDir, file)).mtime
                    }))
                    .sort((a, b) => b.time - a.time)[0];

                try {
                    const backupContent = JSON.parse(fs.readFileSync(latestFile.path, 'utf8'));
                    console.log('✅ Sauvegarde la plus récente vérifiée');
                    console.log(`   - Fichier: ${latestFile.name}`);
                    console.log(`   - Type: ${backupContent.backupType || 'inconnu'}`);
                    console.log(`   - Timestamp: ${backupContent.timestamp || 'inconnu'}`);
                    console.log(`   - Systèmes sauvegardés: ${Object.keys(backupContent.systems || {}).length}`);
                    
                    // Vérifier la présence des systèmes critiques
                    const systems = backupContent.systems || {};
                    const criticalSystems = ['thermalMemory', 'artificialBrain', 'kyberAccelerators'];
                    
                    criticalSystems.forEach(system => {
                        if (systems[system]) {
                            console.log(`   ✅ ${system}: sauvegardé`);
                        } else {
                            console.log(`   ❌ ${system}: manquant`);
                        }
                    });
                    
                } catch (error) {
                    console.log('❌ Erreur lors de la lecture de la sauvegarde:', error.message);
                }
            } else {
                console.log('⚠️ Aucun fichier de sauvegarde trouvé');
            }
        } else {
            console.log('⚠️ Répertoire de sauvegarde non trouvé');
        }

        console.log('\n🎉 Test du système de sauvegarde terminé !');
        console.log('\n📋 Résumé de la protection:');
        console.log('   - Sauvegarde automatique toutes les 30 secondes');
        console.log('   - Sauvegarde critique toutes les 5 secondes');
        console.log('   - Protection contre les coupures (SIGINT, SIGTERM)');
        console.log('   - Sauvegarde d\'urgence en cas d\'exception');
        console.log('   - Conservation de 100 sauvegardes maximum');
        console.log('   - Sauvegarde de tous les systèmes critiques');

    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Assurez-vous que le serveur Louna est démarré sur le port 3005');
        }
    }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
    testBackupSystem();
}

module.exports = { testBackupSystem };
