/**
 * Module de formation des agents
 * Permet de former les agents avec la mémoire thermique
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const axios = require('axios');

// Promisifier les fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

/**
 * Classe pour la formation des agents
 */
class AgentTrainer extends EventEmitter {
  /**
   * Initialise le formateur d'agents
   * @param {Object} options - Options de configuration
   */
  constructor(options = {}) {
    super();

    this.options = {
      dataPath: options.dataPath || path.join(__dirname, 'data', 'training'),
      thermalMemory: options.thermalMemory || null,
      agentManager: options.agentManager || null,
      debug: options.debug || false
    };

    // Créer les dossiers nécessaires
    if (!fs.existsSync(this.options.dataPath)) {
      fs.mkdirSync(this.options.dataPath, { recursive: true });
    }

    // État de la formation
    this.trainingState = {
      isTraining: false,
      progress: 0,
      currentAgent: null,
      currentDataset: null,
      startTime: null,
      endTime: null,
      results: null,
      error: null
    };

    // Historique des formations
    this.trainingHistory = [];

    // Charger l'historique des formations
    this.loadTrainingHistory();

    this.log('Formateur d\'agents initialisé');
  }

  /**
   * Définit la mémoire thermique à utiliser
   * @param {Object} thermalMemory - Instance de la mémoire thermique
   */
  setThermalMemory(thermalMemory) {
    this.options.thermalMemory = thermalMemory;
    this.log('Mémoire thermique définie');
  }

  /**
   * Définit le gestionnaire d'agents à utiliser
   * @param {Object} agentManager - Instance du gestionnaire d'agents
   */
  setAgentManager(agentManager) {
    this.options.agentManager = agentManager;
    this.log('Gestionnaire d\'agents défini');
  }

  /**
   * Charge l'historique des formations
   */
  async loadTrainingHistory() {
    try {
      const historyPath = path.join(this.options.dataPath, 'training-history.json');

      if (await existsAsync(historyPath)) {
        const historyData = await readFileAsync(historyPath, 'utf8');
        this.trainingHistory = JSON.parse(historyData);
        this.log(`Historique des formations chargé: ${this.trainingHistory.length} entrées`);
      } else {
        this.trainingHistory = [];
        this.log('Aucun historique de formation trouvé, création d\'un nouvel historique');
        await this.saveTrainingHistory();
      }
    } catch (error) {
      this.log(`Erreur lors du chargement de l'historique des formations: ${error.message}`, 'error');
      this.trainingHistory = [];
    }
  }

  /**
   * Sauvegarde l'historique des formations
   */
  async saveTrainingHistory() {
    try {
      const historyPath = path.join(this.options.dataPath, 'training-history.json');
      await writeFileAsync(historyPath, JSON.stringify(this.trainingHistory, null, 2), 'utf8');
      this.log('Historique des formations sauvegardé');
    } catch (error) {
      this.log(`Erreur lors de la sauvegarde de l'historique des formations: ${error.message}`, 'error');
    }
  }

  /**
   * Forme un agent avec un ensemble de données
   * @param {string} agentId - ID de l'agent à former
   * @param {string} datasetId - ID de l'ensemble de données à utiliser
   * @param {Object} options - Options de formation
   * @returns {Promise<Object>} - Résultats de la formation
   */
  async trainAgent(agentId, datasetId, options = {}) {
    try {
      // Vérifier si une formation est déjà en cours
      if (this.trainingState.isTraining) {
        throw new Error('Une formation est déjà en cours');
      }

      // Vérifier si le gestionnaire d'agents est disponible
      if (!this.options.agentManager) {
        throw new Error('Gestionnaire d\'agents non disponible');
      }

      // Vérifier si la mémoire thermique est disponible
      if (!this.options.thermalMemory) {
        throw new Error('Mémoire thermique non disponible');
      }

      // Récupérer l'agent principal
      const agent = this.options.agentManager.getAgent(agentId);
      if (!agent) {
        throw new Error(`Agent ${agentId} non trouvé`);
      }

      // Récupérer l'agent de formation
      const trainingAgentId = options.trainingAgentId || 'agent_training';
      const trainingAgent = this.options.agentManager.getAgent(trainingAgentId);
      if (!trainingAgent) {
        throw new Error(`Agent de formation ${trainingAgentId} non trouvé`);
      }

      // Récupérer l'ensemble de données
      const dataset = await this.getDataset(datasetId);
      if (!dataset) {
        throw new Error(`Ensemble de données ${datasetId} non trouvé`);
      }

      // Initialiser l'état de la formation
      this.trainingState = {
        isTraining: true,
        progress: 0,
        currentAgent: agent,
        trainingAgent: trainingAgent,
        currentDataset: dataset,
        startTime: Date.now(),
        endTime: null,
        results: null,
        error: null
      };

      // Émettre un événement de début de formation
      this.emit('trainingStarted', {
        agentId,
        trainingAgentId,
        datasetId,
        startTime: this.trainingState.startTime
      });

      // Paramètres de formation
      const trainingOptions = {
        epochs: options.epochs || 1,
        batchSize: options.batchSize || 10,
        learningRate: options.learningRate || 0.001,
        useMemory: options.useMemory !== false,
        saveToMemory: options.saveToMemory !== false
      };

      this.log(`Début de la formation de l'agent ${agent.name} avec l'agent de formation ${trainingAgent.name} et l'ensemble de données ${dataset.name}`);

      // Résultats de la formation
      const results = {
        agentId,
        trainingAgentId,
        datasetId,
        options: trainingOptions,
        startTime: this.trainingState.startTime,
        endTime: null,
        duration: null,
        samplesProcessed: 0,
        accuracy: 0,
        loss: 0,
        memoryEntries: 0
      };

      // Traiter les échantillons de données
      const samples = dataset.data || [];
      const totalSamples = samples.length;

      for (let epoch = 0; epoch < trainingOptions.epochs; epoch++) {
        this.log(`Époque ${epoch + 1}/${trainingOptions.epochs}`);

        // Traiter les échantillons par lots
        for (let i = 0; i < totalSamples; i += trainingOptions.batchSize) {
          const batch = samples.slice(i, i + trainingOptions.batchSize);

          // Traiter chaque échantillon du lot
          for (const sample of batch) {
            // Vérifier si la formation a été annulée
            if (!this.trainingState.isTraining) {
              this.log('Formation annulée');
              throw new Error('Formation annulée');
            }

            // Utiliser la mémoire thermique si demandé
            let context = [];
            if (trainingOptions.useMemory) {
              const relevantMemories = this.options.thermalMemory.getRecentMemoriesForContext(sample.input, 5);
              if (relevantMemories.length > 0) {
                context = relevantMemories.map(memory => memory.data);
              }
            }

            // Utiliser l'agent de formation pour générer une réponse
            const trainingPrompt = `
Entrée: ${sample.input}

Sortie attendue: ${sample.expectedOutput || sample.output}

Génère une réponse similaire à la sortie attendue:`;

            const trainingResponse = await this.options.agentManager.sendMessage(
              trainingPrompt,
              [],
              {
                agentId: trainingAgentId,
                temperature: 0.5,
                maxTokens: 1000,
                context
              }
            );

            // Calculer la similarité entre la réponse générée et la sortie attendue
            const expectedOutput = sample.expectedOutput || sample.output;
            const similarity = this.calculateSimilarity(trainingResponse.content, expectedOutput);

            // Mettre à jour les statistiques
            results.accuracy += similarity;
            results.loss += (1 - similarity);

            // Sauvegarder dans la mémoire thermique si demandé
            if (trainingOptions.saveToMemory) {
              // Créer une entrée pour la paire question-réponse
              const memoryEntry = {
                key: `training_${Date.now()}`,
                data: `Q: ${sample.input}\nR: ${expectedOutput}`,
                importance: 0.6 + (similarity * 0.3), // Plus la similarité est élevée, plus l'importance est grande
                category: 'training'
              };

              // Ajouter l'entrée à la mémoire thermique
              const entryId = this.options.thermalMemory.add(
                memoryEntry.key,
                memoryEntry.data,
                memoryEntry.importance,
                memoryEntry.category
              );

              results.memoryEntries++;
            }

            // Mettre à jour les résultats
            results.samplesProcessed++;

            // Calculer la progression
            this.trainingState.progress = (results.samplesProcessed / (totalSamples * trainingOptions.epochs)) * 100;

            // Émettre un événement de progression
            this.emit('trainingProgress', {
              progress: this.trainingState.progress,
              samplesProcessed: results.samplesProcessed,
              totalSamples: totalSamples * trainingOptions.epochs,
              currentAccuracy: results.accuracy / results.samplesProcessed,
              currentLoss: results.loss / results.samplesProcessed
            });
          }
        }
      }

      // Finaliser les résultats
      results.endTime = Date.now();
      results.duration = results.endTime - results.startTime;

      // Mettre à jour l'état de la formation
      this.trainingState.isTraining = false;
      this.trainingState.progress = 100;
      this.trainingState.endTime = results.endTime;
      this.trainingState.results = results;

      // Ajouter à l'historique des formations
      this.trainingHistory.push(results);
      await this.saveTrainingHistory();

      // Émettre un événement de fin de formation
      this.emit('trainingCompleted', results);

      this.log(`Formation terminée: ${results.samplesProcessed} échantillons traités en ${results.duration / 1000} secondes`);

      return results;
    } catch (error) {
      this.log(`Erreur lors de la formation: ${error.message}`, 'error');

      // Mettre à jour l'état de la formation
      this.trainingState.isTraining = false;
      this.trainingState.error = error.message;

      // Émettre un événement d'erreur
      this.emit('trainingError', {
        agentId,
        datasetId,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Récupère un ensemble de données
   * @param {string} datasetId - ID de l'ensemble de données
   * @returns {Promise<Object>} - Ensemble de données
   */
  async getDataset(datasetId) {
    try {
      const datasetPath = path.join(this.options.dataPath, `${datasetId}.json`);

      if (await existsAsync(datasetPath)) {
        const datasetData = await readFileAsync(datasetPath, 'utf8');
        return JSON.parse(datasetData);
      }

      return null;
    } catch (error) {
      this.log(`Erreur lors de la récupération de l'ensemble de données: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Récupère la liste des ensembles de données disponibles
   * @returns {Promise<Array>} - Liste des ensembles de données
   */
  async getDatasets() {
    try {
      const files = fs.readdirSync(this.options.dataPath);
      const datasets = [];

      for (const file of files) {
        if (file.endsWith('.json') && file !== 'training-history.json') {
          const datasetPath = path.join(this.options.dataPath, file);
          const datasetData = await readFileAsync(datasetPath, 'utf8');
          const dataset = JSON.parse(datasetData);

          datasets.push({
            id: path.basename(file, '.json'),
            name: dataset.name,
            description: dataset.description,
            samples: dataset.data ? dataset.data.length : 0,
            createdAt: dataset.createdAt
          });
        }
      }

      return datasets;
    } catch (error) {
      this.log(`Erreur lors de la récupération des ensembles de données: ${error.message}`, 'error');
      return [];
    }
  }

  /**
   * Crée un nouvel ensemble de données
   * @param {Object} dataset - Ensemble de données à créer
   * @returns {Promise<Object>} - Ensemble de données créé
   */
  async createDataset(dataset) {
    try {
      if (!dataset.id) {
        dataset.id = `dataset_${Date.now()}`;
      }

      if (!dataset.createdAt) {
        dataset.createdAt = new Date().toISOString();
      }

      const datasetPath = path.join(this.options.dataPath, `${dataset.id}.json`);

      await writeFileAsync(datasetPath, JSON.stringify(dataset, null, 2), 'utf8');

      this.log(`Ensemble de données ${dataset.name} créé`);

      return dataset;
    } catch (error) {
      this.log(`Erreur lors de la création de l'ensemble de données: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Récupère l'état actuel de la formation
   * @returns {Object} - État de la formation
   */
  getTrainingState() {
    return this.trainingState;
  }

  /**
   * Récupère l'historique des formations
   * @returns {Array} - Historique des formations
   */
  getTrainingHistory() {
    return this.trainingHistory;
  }

  /**
   * Calcule la similarité entre deux chaînes de caractères
   * @param {string} str1 - Première chaîne
   * @param {string} str2 - Deuxième chaîne
   * @returns {number} - Score de similarité entre 0 et 1
   */
  calculateSimilarity(str1, str2) {
    if (!str1 || !str2) {
      return 0;
    }

    // Convertir les chaînes en ensembles de mots
    const words1 = str1.toLowerCase().split(/\W+/).filter(word => word.length > 0);
    const words2 = str2.toLowerCase().split(/\W+/).filter(word => word.length > 0);

    // Créer un ensemble de tous les mots uniques
    const uniqueWords = new Set([...words1, ...words2]);

    // Créer des vecteurs pour chaque chaîne
    const vector1 = Array.from(uniqueWords).map(word => words1.filter(w => w === word).length);
    const vector2 = Array.from(uniqueWords).map(word => words2.filter(w => w === word).length);

    // Calculer le produit scalaire
    let dotProduct = 0;
    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
    }

    // Calculer les normes
    const norm1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
    const norm2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

    // Éviter la division par zéro
    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }

    // Calculer la similarité cosinus
    return dotProduct / (norm1 * norm2);
  }

  /**
   * Journalise un message
   * @param {string} message - Message à journaliser
   * @param {string} level - Niveau de journalisation (log, warn, error)
   */
  log(message, level = 'log') {
    if (!this.options.debug && level === 'log') {
      return;
    }

    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [AgentTrainer] ${message}`;

    switch (level) {
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }

    // Émettre un événement de journalisation
    this.emit('log', { timestamp, level, message });
  }
}

module.exports = AgentTrainer;
