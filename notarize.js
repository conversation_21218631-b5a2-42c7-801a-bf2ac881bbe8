/**
 * Script pour notariser l'application Louna pour macOS
 * Ce script est appelé automatiquement par electron-builder après la signature de l'application
 */

const { notarize } = require('@electron/notarize');
const path = require('path');
const fs = require('fs');

// Fonction pour notariser l'application
module.exports = async function (params) {
  // Ne notariser que pour macOS
  if (process.platform !== 'darwin') {
    return;
  }

  console.log('Notarisation de l\'application...');

  // Récupérer les informations de construction
  const appId = params.packager.appInfo.id;
  const appPath = path.join(params.appOutDir, `${params.packager.appInfo.productFilename}.app`);

  if (!fs.existsSync(appPath)) {
    console.log(`Application introuvable à ${appPath}`);
    return;
  }

  // Vérifier si les variables d'environnement sont définies
  if (!process.env.APPLE_ID || !process.env.APPLE_ID_PASSWORD) {
    console.log('Les variables d\'environnement APPLE_ID et APPLE_ID_PASSWORD doivent être définies pour la notarisation');
    return;
  }

  try {
    // Notariser l'application
    await notarize({
      appBundleId: appId,
      appPath: appPath,
      appleId: process.env.APPLE_ID,
      appleIdPassword: process.env.APPLE_ID_PASSWORD,
      teamId: process.env.APPLE_TEAM_ID
    });

    console.log('Notarisation terminée avec succès !');
  } catch (error) {
    console.error('Erreur lors de la notarisation :', error);
    throw error;
  }
};
