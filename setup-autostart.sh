#!/bin/bash

# Script pour configurer le démarrage automatique de l'application Louna avec Ollama

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Obtenir le chemin absolu du répertoire de l'application
APP_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# Créer le dossier LaunchAgents s'il n'existe pas
LAUNCH_AGENTS_DIR="$HOME/Library/LaunchAgents"
if [ ! -d "$LAUNCH_AGENTS_DIR" ]; then
  print_message "Création du dossier LaunchAgents..."
  mkdir -p "$LAUNCH_AGENTS_DIR"
fi

# Créer le fichier plist pour le démarrage automatique
PLIST_FILE="$LAUNCH_AGENTS_DIR/com.louna.app.plist"

print_message "Création du fichier de démarrage automatique..."
cat > "$PLIST_FILE" <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.louna.app</string>
    <key>ProgramArguments</key>
    <array>
        <string>$APP_DIR/launch-louna-with-ollama.sh</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
    <key>StandardOutPath</key>
    <string>$APP_DIR/logs/louna-autostart.log</string>
    <key>StandardErrorPath</key>
    <string>$APP_DIR/logs/louna-autostart-error.log</string>
    <key>WorkingDirectory</key>
    <string>$APP_DIR</string>
</dict>
</plist>
EOF

# Créer le dossier logs s'il n'existe pas
if [ ! -d "$APP_DIR/logs" ]; then
  print_message "Création du dossier logs..."
  mkdir -p "$APP_DIR/logs"
fi

# Charger le fichier plist
print_message "Chargement du fichier de démarrage automatique..."
launchctl load "$PLIST_FILE"

# Vérifier si le chargement a réussi
if [ $? -eq 0 ]; then
  print_success "L'application Louna démarrera automatiquement au démarrage de votre Mac."
  print_message "Pour désactiver le démarrage automatique, exécutez la commande suivante :"
  echo "launchctl unload $PLIST_FILE"
else
  print_error "Erreur lors du chargement du fichier de démarrage automatique."
  print_message "Veuillez vérifier les permissions et réessayer."
fi
