/**
 * GESTIONNAIRE DE CONNEXION MCP ROBUSTE
 * Gère la connexion, reconnexion automatique et surveillance du serveur MCP
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

class MCPConnectionManager {
    constructor(options = {}) {
        this.options = {
            port: options.port || 3002,
            maxRetries: options.maxRetries || 5,
            retryDelay: options.retryDelay || 3000,
            healthCheckInterval: options.healthCheckInterval || 10000,
            debug: options.debug || true,
            ...options
        };
        
        this.mcpServer = null;
        this.mcpProcess = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.healthCheckTimer = null;
        this.lastHealthCheck = null;
        
        this.stats = {
            connectionAttempts: 0,
            successfulConnections: 0,
            failedConnections: 0,
            lastConnectionTime: null,
            uptime: 0,
            healthChecks: 0,
            failedHealthChecks: 0
        };
        
        console.log('🔗 Gestionnaire de connexion MCP initialisé');
    }

    /**
     * Démarre le gestionnaire de connexion MCP
     */
    async start() {
        console.log('🚀 Démarrage du gestionnaire de connexion MCP...');
        
        try {
            // Vérifier si un serveur MCP existe déjà
            const existingConnection = await this.checkExistingConnection();
            
            if (existingConnection) {
                console.log('✅ Serveur MCP existant détecté et fonctionnel');
                this.isConnected = true;
                this.stats.successfulConnections++;
                this.startHealthCheck();
                return true;
            }
            
            // Démarrer un nouveau serveur MCP
            const started = await this.startMCPServer();
            
            if (started) {
                this.startHealthCheck();
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('❌ Erreur lors du démarrage du gestionnaire MCP:', error);
            return false;
        }
    }

    /**
     * Vérifie si un serveur MCP existe déjà
     */
    async checkExistingConnection() {
        try {
            this.stats.connectionAttempts++;
            
            const response = await axios.get(`http://localhost:${this.options.port}/mcp/status`, {
                timeout: 5000
            });
            
            if (response.status === 200 && response.data.status === 'ok') {
                console.log('🔍 Serveur MCP existant trouvé et opérationnel');
                this.lastHealthCheck = Date.now();
                return true;
            }
            
            return false;
            
        } catch (error) {
            if (this.options.debug) {
                console.log('🔍 Aucun serveur MCP existant trouvé');
            }
            return false;
        }
    }

    /**
     * Démarre un nouveau serveur MCP
     */
    async startMCPServer() {
        try {
            console.log('🚀 Démarrage d\'un nouveau serveur MCP...');
            
            // Importer et créer le serveur MCP
            const MCPServer = require('./code/deepseek-node-ui/mcp/mcp-server');
            this.mcpServer = new MCPServer({
                port: this.options.port,
                allowInternet: true,
                allowDesktop: true,
                allowSystemCommands: true,
                debug: this.options.debug
            });
            
            // Démarrer le serveur
            await this.mcpServer.start();
            
            // Attendre un peu pour que le serveur soit prêt
            await this.delay(2000);
            
            // Vérifier que le serveur répond
            const isReady = await this.verifyConnection();
            
            if (isReady) {
                console.log('✅ Serveur MCP démarré avec succès');
                this.isConnected = true;
                this.stats.successfulConnections++;
                this.stats.lastConnectionTime = Date.now();
                
                // Rendre disponible globalement
                global.mcpServer = this.mcpServer;
                
                return true;
            } else {
                console.error('❌ Le serveur MCP ne répond pas après le démarrage');
                return false;
            }
            
        } catch (error) {
            console.error('❌ Erreur lors du démarrage du serveur MCP:', error);
            this.stats.failedConnections++;
            
            // Si c'est une erreur de port déjà utilisé, essayer de se connecter au serveur existant
            if (error.code === 'EADDRINUSE') {
                console.log('🔄 Port déjà utilisé, tentative de connexion au serveur existant...');
                return await this.checkExistingConnection();
            }
            
            return false;
        }
    }

    /**
     * Vérifie la connexion au serveur MCP
     */
    async verifyConnection() {
        try {
            const response = await axios.get(`http://localhost:${this.options.port}/mcp/status`, {
                timeout: 5000
            });
            
            return response.status === 200 && response.data.status === 'ok';
            
        } catch (error) {
            return false;
        }
    }

    /**
     * Démarre la surveillance de santé
     */
    startHealthCheck() {
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        
        console.log(`🔍 Démarrage de la surveillance MCP (toutes les ${this.options.healthCheckInterval/1000}s)`);
        
        this.healthCheckTimer = setInterval(async () => {
            await this.performHealthCheck();
        }, this.options.healthCheckInterval);
        
        // Premier check immédiat
        this.performHealthCheck();
    }

    /**
     * Effectue un contrôle de santé
     */
    async performHealthCheck() {
        try {
            this.stats.healthChecks++;
            
            const isHealthy = await this.verifyConnection();
            
            if (isHealthy) {
                if (!this.isConnected) {
                    console.log('✅ Connexion MCP rétablie');
                    this.isConnected = true;
                    this.retryCount = 0;
                }
                
                this.lastHealthCheck = Date.now();
                
                if (this.options.debug && this.stats.healthChecks % 6 === 0) { // Log toutes les minutes
                    console.log(`🔍 MCP Health Check: OK (${this.stats.healthChecks} checks)`);
                }
                
            } else {
                this.stats.failedHealthChecks++;
                
                if (this.isConnected) {
                    console.log('⚠️ Connexion MCP perdue, tentative de reconnexion...');
                    this.isConnected = false;
                }
                
                await this.attemptReconnection();
            }
            
        } catch (error) {
            this.stats.failedHealthChecks++;
            console.error('❌ Erreur lors du contrôle de santé MCP:', error);
            
            if (this.isConnected) {
                this.isConnected = false;
                await this.attemptReconnection();
            }
        }
    }

    /**
     * Tente une reconnexion
     */
    async attemptReconnection() {
        if (this.retryCount >= this.options.maxRetries) {
            console.error(`❌ Nombre maximum de tentatives de reconnexion atteint (${this.options.maxRetries})`);
            return false;
        }
        
        this.retryCount++;
        console.log(`🔄 Tentative de reconnexion MCP ${this.retryCount}/${this.options.maxRetries}...`);
        
        // Attendre avant de réessayer
        await this.delay(this.options.retryDelay);
        
        // Vérifier d'abord s'il y a un serveur existant
        const existingConnection = await this.checkExistingConnection();
        
        if (existingConnection) {
            console.log('✅ Reconnexion réussie au serveur MCP existant');
            this.isConnected = true;
            this.retryCount = 0;
            return true;
        }
        
        // Sinon, essayer de redémarrer le serveur
        const restarted = await this.startMCPServer();
        
        if (restarted) {
            console.log('✅ Serveur MCP redémarré avec succès');
            this.retryCount = 0;
            return true;
        }
        
        return false;
    }

    /**
     * Teste les fonctionnalités MCP
     */
    async testCapabilities() {
        if (!this.isConnected) {
            return {
                success: false,
                error: 'MCP non connecté'
            };
        }
        
        const tests = {
            status: false,
            internet: false,
            desktop: false,
            system: false
        };
        
        try {
            // Test du statut
            const statusResponse = await axios.get(`http://localhost:${this.options.port}/mcp/status`, {
                timeout: 5000
            });
            tests.status = statusResponse.status === 200;
            
            // Test d'accès Internet
            try {
                const internetResponse = await axios.post(`http://localhost:${this.options.port}/mcp/internet/search`, {
                    query: 'test',
                    maxResults: 1
                }, { timeout: 10000 });
                tests.internet = internetResponse.status === 200;
            } catch (error) {
                console.log('⚠️ Test Internet échoué:', error.message);
            }
            
            // Test d'accès bureau
            try {
                const desktopResponse = await axios.get(`http://localhost:${this.options.port}/mcp/desktop/check`, {
                    timeout: 5000
                });
                tests.desktop = desktopResponse.status === 200;
            } catch (error) {
                console.log('⚠️ Test Bureau échoué:', error.message);
            }
            
            // Test commandes système
            try {
                const systemResponse = await axios.post(`http://localhost:${this.options.port}/mcp/system/execute`, {
                    command: 'echo "test"',
                    timeout: 5000
                }, { timeout: 10000 });
                tests.system = systemResponse.status === 200;
            } catch (error) {
                console.log('⚠️ Test Système échoué:', error.message);
            }
            
            return {
                success: true,
                tests: tests,
                allPassed: Object.values(tests).every(t => t)
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                tests: tests
            };
        }
    }

    /**
     * Obtient les statistiques
     */
    getStats() {
        const now = Date.now();
        const uptime = this.stats.lastConnectionTime ? now - this.stats.lastConnectionTime : 0;
        
        return {
            isConnected: this.isConnected,
            port: this.options.port,
            retryCount: this.retryCount,
            maxRetries: this.options.maxRetries,
            lastHealthCheck: this.lastHealthCheck,
            timeSinceLastCheck: this.lastHealthCheck ? now - this.lastHealthCheck : null,
            uptime: uptime,
            stats: {
                ...this.stats,
                uptime: uptime,
                successRate: this.stats.connectionAttempts > 0 ? 
                    (this.stats.successfulConnections / this.stats.connectionAttempts * 100).toFixed(2) + '%' : '0%',
                healthCheckSuccessRate: this.stats.healthChecks > 0 ? 
                    ((this.stats.healthChecks - this.stats.failedHealthChecks) / this.stats.healthChecks * 100).toFixed(2) + '%' : '0%'
            }
        };
    }

    /**
     * Arrête le gestionnaire
     */
    async stop() {
        console.log('🛑 Arrêt du gestionnaire de connexion MCP...');
        
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }
        
        if (this.mcpServer) {
            try {
                await this.mcpServer.stop();
                this.mcpServer = null;
            } catch (error) {
                console.error('❌ Erreur lors de l\'arrêt du serveur MCP:', error);
            }
        }
        
        this.isConnected = false;
        console.log('✅ Gestionnaire de connexion MCP arrêté');
    }

    /**
     * Utilitaire pour attendre
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = MCPConnectionManager;
