#!/usr/bin/env node

/**
 * Script de test pour vérifier toutes les routes et identifier les erreurs 404
 */

const http = require('http');

const routes = [
    '/',
    '/louna',
    '/unified-hub.html',
    '/agi-dashboard.html',
    '/futuristic-interface.html',
    '/brain-visualization.html',
    '/qi-neuron-monitor.html',
    '/kyber-dashboard.html',
    '/memory-fusion.html',
    '/memory-sync.html',
    '/performance.html',
    '/chat-simple',
    '/chat-complet',
    '/chat-ultra-complet',
    '/settings',
    '/agents',
    '/training',
    '/camera',
    '/video-analysis',
    '/ltx-video',
    '/code-editor',
    '/code-extensions',
    '/generation-studio',
    '/advanced-course-monitor.html',
    '/coding-evolution.html',
    '/security-dashboard.html',
    '/agent-navigation.html',
    '/real-time-brain-monitor.html',
    '/presentation.html',
    '/thermal-memory',
    '/brain-visualization',
    '/qi-neuron-monitor',
    '/memory-fusion',
    '/cinema-3d',
    '/dreams.html',
    '/settings-new.html'
];

function testRoute(route) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: route,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            resolve({
                route: route,
                status: res.statusCode,
                success: res.statusCode < 400
            });
        });

        req.on('error', (err) => {
            resolve({
                route: route,
                status: 'ERROR',
                success: false,
                error: err.message
            });
        });

        req.setTimeout(5000, () => {
            req.destroy();
            resolve({
                route: route,
                status: 'TIMEOUT',
                success: false,
                error: 'Timeout'
            });
        });

        req.end();
    });
}

async function testAllRoutes() {
    console.log('🧪 Test de toutes les routes de l\'application Louna...\n');
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const route of routes) {
        const result = await testRoute(route);
        results.push(result);
        
        if (result.success) {
            console.log(`✅ ${route} - ${result.status}`);
            successCount++;
        } else {
            console.log(`❌ ${route} - ${result.status} ${result.error ? '(' + result.error + ')' : ''}`);
            errorCount++;
        }
    }

    console.log('\n📊 Résumé des tests:');
    console.log(`✅ Routes fonctionnelles: ${successCount}`);
    console.log(`❌ Routes en erreur: ${errorCount}`);
    console.log(`📈 Taux de réussite: ${Math.round((successCount / routes.length) * 100)}%`);

    if (errorCount > 0) {
        console.log('\n🔧 Routes à corriger:');
        results.filter(r => !r.success).forEach(r => {
            console.log(`   - ${r.route} (${r.status})`);
        });
    }
}

testAllRoutes().catch(console.error);
