/**
 * SYSTÈME D'ACCÉLÉRATEURS AUTOMATIQUES POUR LOUNA
 * Ajoute automatiquement des accélérateurs selon les besoins détectés
 */

class AutoAcceleratorSystem {
    constructor(kyberAccelerators, thermalMemory, artificialBrain) {
        this.kyberAccelerators = kyberAccelerators;
        this.thermalMemory = thermalMemory;
        this.artificialBrain = artificialBrain;

        this.isActive = false;
        this.monitoringInterval = null;
        this.continuousMonitoringInterval = null;
        this.realTimeMonitoringInterval = null;
        this.acceleratorCounter = 0;
        this.lastMetrics = null;
        this.metricsHistory = [];

        // Seuils de déclenchement
        this.thresholds = {
            cpuLoad: 0.8,           // 80% charge CPU
            memoryUsage: 0.85,      // 85% usage mémoire
            responseTime: 5000,     // 5 secondes de temps de réponse
            thermalTemp: 0.8,       // 80% température thermique
            neuronActivity: 0.9,    // 90% activité neuronale
            qiLevel: 300           // QI en dessous de 300
        };

        // Types d'accélérateurs disponibles
        this.acceleratorTypes = {
            cpu_booster: {
                name: 'CPU Booster',
                description: 'Accél<PERSON> le traitement CPU',
                boostFactor: 2.5,
                energyCost: 100,
                trigger: 'high_cpu_load'
            },
            memory_optimizer: {
                name: 'Memory Optimizer',
                description: 'Optimise l\'usage mémoire',
                boostFactor: 2.0,
                energyCost: 80,
                trigger: 'high_memory_usage'
            },
            response_accelerator: {
                name: 'Response Accelerator',
                description: 'Accélère les temps de réponse',
                boostFactor: 3.0,
                energyCost: 120,
                trigger: 'slow_response'
            },
            thermal_cooler: {
                name: 'Thermal Cooler',
                description: 'Refroidit la mémoire thermique',
                boostFactor: 1.8,
                energyCost: 90,
                trigger: 'high_thermal_temp'
            },
            neural_stimulator: {
                name: 'Neural Stimulator',
                description: 'Stimule l\'activité neuronale',
                boostFactor: 2.2,
                energyCost: 110,
                trigger: 'low_neural_activity'
            },
            qi_enhancer: {
                name: 'QI Enhancer',
                description: 'Améliore le niveau de QI',
                boostFactor: 2.8,
                energyCost: 150,
                trigger: 'low_qi'
            }
        };

        this.activeAccelerators = new Map();

        console.log('🚀 Système d\'accélérateurs automatiques initialisé');
    }

    /**
     * Démarre le système d'accélérateurs automatiques
     */
    start() {
        if (this.isActive) {
            console.log('⚠️ Système d\'accélérateurs automatiques déjà actif');
            return;
        }

        this.isActive = true;
        console.log('🚀 DÉMARRAGE DU SYSTÈME D\'ACCÉLÉRATEURS AUTOMATIQUES');

        // Surveillance temps réel toutes les 2 secondes (réduit de 500ms)
        this.realTimeMonitoringInterval = setInterval(() => {
            this.realTimeMonitoring();
        }, 2000);

        // Surveillance continue toutes les 5 secondes pour ajout d'accélérateurs (réduit de 2s)
        this.monitoringInterval = setInterval(() => {
            this.monitorSystemAndAddAccelerators();
        }, 5000);

        // Surveillance continue des métriques toutes les 1 seconde (réduit de 100ms)
        this.continuousMonitoringInterval = setInterval(() => {
            this.continuousMetricsMonitoring();
        }, 1000);

        // Première vérification immédiate
        this.monitorSystemAndAddAccelerators();

        console.log('✅ Système d\'accélérateurs automatiques démarré avec surveillance continue');
    }

    /**
     * Arrête le système d'accélérateurs automatiques
     */
    stop() {
        if (!this.isActive) return;

        this.isActive = false;

        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        if (this.continuousMonitoringInterval) {
            clearInterval(this.continuousMonitoringInterval);
            this.continuousMonitoringInterval = null;
        }

        if (this.realTimeMonitoringInterval) {
            clearInterval(this.realTimeMonitoringInterval);
            this.realTimeMonitoringInterval = null;
        }

        console.log('🛑 Système d\'accélérateurs automatiques arrêté');
    }

    /**
     * Surveille le système et ajoute des accélérateurs selon les besoins
     */
    async monitorSystemAndAddAccelerators() {
        try {
            console.log('🔍 Surveillance du système pour détection des besoins...');

            const systemMetrics = await this.gatherSystemMetrics();
            const needs = this.analyzeNeeds(systemMetrics);

            if (needs.length > 0) {
                console.log(`⚡ ${needs.length} besoins détectés:`, needs);

                for (const need of needs) {
                    await this.addAcceleratorForNeed(need, systemMetrics);
                }
            } else {
                console.log('✅ Système optimal - Aucun accélérateur supplémentaire nécessaire');
            }

            // Nettoyer les accélérateurs expirés
            this.cleanupExpiredAccelerators();

        } catch (error) {
            console.error('❌ Erreur surveillance système:', error);
        }
    }

    /**
     * Collecte les métriques système
     */
    async gatherSystemMetrics() {
        const metrics = {
            timestamp: Date.now(),
            cpu: await this.getCPUMetrics(),
            memory: await this.getMemoryMetrics(),
            thermal: await this.getThermalMetrics(),
            neural: await this.getNeuralMetrics(),
            response: await this.getResponseMetrics()
        };

        return metrics;
    }

    /**
     * Obtient les métriques CPU
     */
    async getCPUMetrics() {
        try {
            const os = require('os');
            const cpus = os.cpus();
            const loadAvg = os.loadavg();

            return {
                load: loadAvg[0] / cpus.length,
                cores: cpus.length,
                usage: Math.min(loadAvg[0] / cpus.length, 1.0)
            };
        } catch (error) {
            return { load: 0.5, cores: 4, usage: 0.5 };
        }
    }

    /**
     * Obtient les métriques mémoire
     */
    async getMemoryMetrics() {
        try {
            const os = require('os');
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;

            return {
                total: totalMem,
                used: usedMem,
                free: freeMem,
                usage: usedMem / totalMem
            };
        } catch (error) {
            return { total: 8000000000, used: 6000000000, free: 2000000000, usage: 0.75 };
        }
    }

    /**
     * Obtient les métriques thermiques
     */
    async getThermalMetrics() {
        if (this.thermalMemory && this.thermalMemory.getDetailedStats) {
            const stats = this.thermalMemory.getDetailedStats();
            return {
                temperature: stats.averageTemperature || 0.5,
                totalMemories: stats.totalMemories || 0,
                efficiency: stats.memoryEfficiency || 0.7
            };
        }

        return { temperature: 0.5, totalMemories: 100, efficiency: 0.7 };
    }

    /**
     * Obtient les métriques neuronales
     */
    async getNeuralMetrics() {
        if (this.artificialBrain && this.artificialBrain.getBrainState) {
            const state = this.artificialBrain.getBrainState();
            return {
                qi: state.qi || 500,
                neuronCount: state.neuronCount || 100,
                activity: state.activity || 0.6,
                efficiency: state.efficiency || 0.7
            };
        }

        return { qi: 500, neuronCount: 100, activity: 0.6, efficiency: 0.7 };
    }

    /**
     * Obtient les métriques de temps de réponse
     */
    async getResponseMetrics() {
        // Simulation basée sur les métriques système
        const baseResponseTime = 2000; // 2 secondes de base
        const systemLoad = await this.getCPUMetrics();
        const adjustedTime = baseResponseTime * (1 + systemLoad.usage);

        return {
            averageTime: adjustedTime,
            lastResponseTime: adjustedTime * (0.8 + Math.random() * 0.4)
        };
    }

    /**
     * Analyse les besoins selon les métriques
     */
    analyzeNeeds(metrics) {
        const needs = [];

        // Vérifier charge CPU
        if (metrics.cpu.usage > this.thresholds.cpuLoad) {
            needs.push({
                type: 'high_cpu_load',
                severity: metrics.cpu.usage,
                acceleratorType: 'cpu_booster',
                description: `Charge CPU élevée: ${(metrics.cpu.usage * 100).toFixed(1)}%`
            });
        }

        // Vérifier usage mémoire
        if (metrics.memory.usage > this.thresholds.memoryUsage) {
            needs.push({
                type: 'high_memory_usage',
                severity: metrics.memory.usage,
                acceleratorType: 'memory_optimizer',
                description: `Usage mémoire élevé: ${(metrics.memory.usage * 100).toFixed(1)}%`
            });
        }

        // Vérifier temps de réponse
        if (metrics.response.averageTime > this.thresholds.responseTime) {
            needs.push({
                type: 'slow_response',
                severity: metrics.response.averageTime / this.thresholds.responseTime,
                acceleratorType: 'response_accelerator',
                description: `Temps de réponse lent: ${metrics.response.averageTime}ms`
            });
        }

        // Vérifier température thermique
        if (metrics.thermal.temperature > this.thresholds.thermalTemp) {
            needs.push({
                type: 'high_thermal_temp',
                severity: metrics.thermal.temperature,
                acceleratorType: 'thermal_cooler',
                description: `Température thermique élevée: ${(metrics.thermal.temperature * 100).toFixed(1)}%`
            });
        }

        // Vérifier activité neuronale
        if (metrics.neural.activity > this.thresholds.neuronActivity) {
            needs.push({
                type: 'high_neural_activity',
                severity: metrics.neural.activity,
                acceleratorType: 'neural_stimulator',
                description: `Activité neuronale élevée: ${(metrics.neural.activity * 100).toFixed(1)}%`
            });
        }

        // Vérifier niveau QI
        if (metrics.neural.qi < this.thresholds.qiLevel) {
            needs.push({
                type: 'low_qi',
                severity: this.thresholds.qiLevel / metrics.neural.qi,
                acceleratorType: 'qi_enhancer',
                description: `Niveau QI bas: ${metrics.neural.qi}`
            });
        }

        return needs;
    }

    /**
     * Ajoute un accélérateur pour un besoin spécifique
     */
    async addAcceleratorForNeed(need, metrics) {
        const acceleratorType = this.acceleratorTypes[need.acceleratorType];
        if (!acceleratorType) {
            console.error(`❌ Type d'accélérateur inconnu: ${need.acceleratorType}`);
            return;
        }

        // Vérifier si un accélérateur de ce type existe déjà (incluant les urgences)
        const existingKey = Array.from(this.activeAccelerators.keys())
            .find(key => key.includes(need.acceleratorType));

        if (existingKey) {
            console.log(`⚡ Accélérateur ${acceleratorType.name} déjà actif`);
            return;
        }

        // Créer un nouvel accélérateur
        const acceleratorId = `${need.acceleratorType}_auto_${++this.acceleratorCounter}`;
        const accelerator = {
            id: acceleratorId,
            type: need.acceleratorType,
            name: acceleratorType.name,
            description: acceleratorType.description,
            boostFactor: acceleratorType.boostFactor,
            energy: 1000, // Énergie maximale
            efficiency: 0.95,
            createdAt: Date.now(),
            expiresAt: Date.now() + (10 * 60 * 1000), // Expire dans 10 minutes
            reason: need.description,
            severity: need.severity
        };

        // Ajouter l'accélérateur au système Kyber
        if (this.kyberAccelerators && this.kyberAccelerators.addAccelerator) {
            try {
                await this.kyberAccelerators.addAccelerator(acceleratorId, accelerator);
                this.activeAccelerators.set(acceleratorId, accelerator);

                console.log(`🚀 ACCÉLÉRATEUR AJOUTÉ AUTOMATIQUEMENT:`);
                console.log(`   📛 Nom: ${accelerator.name}`);
                console.log(`   🎯 Raison: ${accelerator.reason}`);
                console.log(`   ⚡ Boost: ${accelerator.boostFactor}x`);
                console.log(`   ⏱️ Expire dans: 10 minutes`);

            } catch (error) {
                console.error(`❌ Erreur ajout accélérateur ${acceleratorId}:`, error);
            }
        } else {
            // Fallback: stocker localement
            this.activeAccelerators.set(acceleratorId, accelerator);
            console.log(`🚀 ACCÉLÉRATEUR SIMULÉ AJOUTÉ: ${accelerator.name}`);
        }
    }

    /**
     * Nettoie les accélérateurs expirés
     */
    cleanupExpiredAccelerators() {
        const now = Date.now();
        const expired = [];

        for (const [id, accelerator] of this.activeAccelerators.entries()) {
            if (accelerator.expiresAt < now) {
                expired.push(id);
            }
        }

        for (const id of expired) {
            this.removeAccelerator(id);
        }

        if (expired.length > 0) {
            console.log(`🧹 ${expired.length} accélérateurs expirés supprimés`);
        }
    }

    /**
     * Supprime un accélérateur
     */
    removeAccelerator(acceleratorId) {
        if (this.kyberAccelerators && this.kyberAccelerators.removeAccelerator) {
            try {
                this.kyberAccelerators.removeAccelerator(acceleratorId);
            } catch (error) {
                console.error(`❌ Erreur suppression accélérateur ${acceleratorId}:`, error);
            }
        }

        this.activeAccelerators.delete(acceleratorId);
        console.log(`🗑️ Accélérateur supprimé: ${acceleratorId}`);
    }

    /**
     * Obtient les statistiques du système
     */
    getStats() {
        return {
            isActive: this.isActive,
            activeAccelerators: Array.from(this.activeAccelerators.values()),
            acceleratorCount: this.activeAccelerators.size,
            totalCreated: this.acceleratorCounter,
            thresholds: this.thresholds,
            availableTypes: Object.keys(this.acceleratorTypes)
        };
    }

    /**
     * Force l'ajout d'un accélérateur spécifique
     */
    async forceAddAccelerator(type, duration = 600000) { // 10 minutes par défaut
        const acceleratorType = this.acceleratorTypes[type];
        if (!acceleratorType) {
            throw new Error(`Type d'accélérateur inconnu: ${type}`);
        }

        const acceleratorId = `${type}_forced_${++this.acceleratorCounter}`;
        const accelerator = {
            id: acceleratorId,
            type: type,
            name: acceleratorType.name,
            description: acceleratorType.description,
            boostFactor: acceleratorType.boostFactor,
            energy: 1000,
            efficiency: 0.98,
            createdAt: Date.now(),
            expiresAt: Date.now() + duration,
            reason: 'Ajouté manuellement',
            severity: 1.0
        };

        await this.addAcceleratorForNeed({ acceleratorType: type, description: 'Forcé manuellement' }, {});

        return accelerator;
    }

    /**
     * Gère les situations d'urgence détectées par le monitoring ultra-intelligent
     */
    async handleEmergency(metrics) {
        console.log('🚨 GESTION D\'URGENCE ACTIVÉE - Métriques critiques détectées');

        try {
            const emergencyNeeds = [];

            // Analyser les métriques critiques
            if (metrics.system.cpu.usage > 95) {
                emergencyNeeds.push({
                    type: 'critical_cpu_overload',
                    severity: 3.0,
                    acceleratorType: 'cpu_booster',
                    description: `CPU critique: ${metrics.system.cpu.usage.toFixed(1)}%`
                });
            }

            if (metrics.system.memory.usage > 99.5) {
                emergencyNeeds.push({
                    type: 'critical_memory_overload',
                    severity: 3.0,
                    acceleratorType: 'memory_optimizer',
                    description: `Mémoire critique: ${metrics.system.memory.usage.toFixed(1)}%`
                });
            }

            // Ajouter des accélérateurs d'urgence avec boost élevé
            for (const need of emergencyNeeds) {
                // Vérifier si un accélérateur d'urgence de ce type existe déjà
                const existingEmergency = Array.from(this.activeAccelerators.keys())
                    .find(key => key.includes(`emergency_${need.acceleratorType}`));

                if (existingEmergency) {
                    console.log(`⚡ Accélérateur d'urgence ${need.acceleratorType} déjà actif`);
                    continue;
                }

                const emergencyAcceleratorId = `emergency_${need.acceleratorType}_${++this.acceleratorCounter}`;
                const acceleratorType = this.acceleratorTypes[need.acceleratorType];

                if (acceleratorType) {
                    const emergencyAccelerator = {
                        id: emergencyAcceleratorId,
                        type: need.acceleratorType,
                        name: `URGENCE ${acceleratorType.name}`,
                        description: `${acceleratorType.description} - MODE URGENCE`,
                        boostFactor: acceleratorType.boostFactor * 2, // Double boost en urgence
                        energy: 1000,
                        efficiency: 0.99,
                        createdAt: Date.now(),
                        expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes en urgence
                        reason: need.description,
                        severity: need.severity,
                        emergency: true
                    };

                    if (this.kyberAccelerators && this.kyberAccelerators.addAccelerator) {
                        await this.kyberAccelerators.addAccelerator(emergencyAcceleratorId, emergencyAccelerator);
                        this.activeAccelerators.set(emergencyAcceleratorId, emergencyAccelerator);

                        console.log(`🚨 ACCÉLÉRATEUR D'URGENCE AJOUTÉ:`);
                        console.log(`   📛 Nom: ${emergencyAccelerator.name}`);
                        console.log(`   🎯 Raison: ${emergencyAccelerator.reason}`);
                        console.log(`   ⚡ Boost: ${emergencyAccelerator.boostFactor}x (URGENCE)`);
                        console.log(`   ⏱️ Expire dans: 5 minutes`);
                    }
                }
            }

            // Déclencher des optimisations d'urgence
            if (this.thermalMemory && this.thermalMemory.emergencyOptimization) {
                await this.thermalMemory.emergencyOptimization();
            }

            if (this.artificialBrain && this.artificialBrain.emergencyMode) {
                await this.artificialBrain.emergencyMode();
            }

            console.log(`🚨 Gestion d'urgence terminée - ${emergencyNeeds.length} accélérateurs d'urgence ajoutés`);

        } catch (error) {
            console.error('❌ Erreur lors de la gestion d\'urgence:', error);
        }
    }

    /**
     * Surveillance continue des métriques toutes les 100ms
     */
    async continuousMetricsMonitoring() {
        try {
            const currentMetrics = await this.gatherSystemMetrics();

            // Stocker les métriques dans l'historique
            this.metricsHistory.push({
                timestamp: Date.now(),
                metrics: currentMetrics
            });

            // Garder seulement les 100 dernières mesures (10 secondes d'historique)
            if (this.metricsHistory.length > 100) {
                this.metricsHistory.shift();
            }

            // Détecter les changements rapides
            if (this.lastMetrics) {
                this.detectRapidChanges(this.lastMetrics, currentMetrics);
            }

            this.lastMetrics = currentMetrics;

        } catch (error) {
            // Surveillance silencieuse pour éviter le spam de logs
        }
    }

    /**
     * Surveillance temps réel toutes les 500ms
     */
    async realTimeMonitoring() {
        try {
            const currentMetrics = await this.gatherSystemMetrics();
            const criticalNeeds = this.detectCriticalNeeds(currentMetrics);

            if (criticalNeeds.length > 0) {
                console.log(`🚨 BESOINS CRITIQUES DÉTECTÉS EN TEMPS RÉEL: ${criticalNeeds.length}`);

                for (const need of criticalNeeds) {
                    await this.addAcceleratorForNeed(need, currentMetrics);
                }
            }

        } catch (error) {
            // Surveillance silencieuse
        }
    }

    /**
     * Détecte les changements rapides dans les métriques
     */
    detectRapidChanges(previousMetrics, currentMetrics) {
        const changes = [];

        // Détecter les pics de CPU
        const cpuChange = Math.abs(currentMetrics.cpu.usage - previousMetrics.cpu.usage);
        if (cpuChange > 0.3) { // Changement de plus de 30%
            changes.push({
                type: 'cpu_spike',
                severity: cpuChange,
                description: `Pic CPU détecté: ${(cpuChange * 100).toFixed(1)}% de changement`
            });
        }

        // Détecter les pics de mémoire
        const memoryChange = Math.abs(currentMetrics.memory.usage - previousMetrics.memory.usage);
        if (memoryChange > 0.1) { // Changement de plus de 10%
            changes.push({
                type: 'memory_spike',
                severity: memoryChange,
                description: `Pic mémoire détecté: ${(memoryChange * 100).toFixed(1)}% de changement`
            });
        }

        // Détecter les changements de température thermique
        const thermalChange = Math.abs(currentMetrics.thermal.temperature - previousMetrics.thermal.temperature);
        if (thermalChange > 0.2) { // Changement de plus de 20%
            changes.push({
                type: 'thermal_spike',
                severity: thermalChange,
                description: `Pic thermique détecté: ${(thermalChange * 100).toFixed(1)}% de changement`
            });
        }

        if (changes.length > 0) {
            console.log(`⚡ CHANGEMENTS RAPIDES DÉTECTÉS:`, changes.map(c => c.description));

            // Déclencher une surveillance immédiate
            setTimeout(() => {
                this.monitorSystemAndAddAccelerators();
            }, 50);
        }
    }

    /**
     * Détecte les besoins critiques nécessitant une action immédiate
     */
    detectCriticalNeeds(metrics) {
        const criticalNeeds = [];

        // Seuils critiques (plus élevés que les seuils normaux)
        const criticalThresholds = {
            cpuLoad: 0.98,      // 98% charge CPU critique
            memoryUsage: 0.995, // 99.5% usage mémoire critique (plus restrictif)
            thermalTemp: 0.98,  // 98% température thermique critique
            responseTime: 15000 // 15 secondes de temps de réponse critique
        };

        // Vérifier charge CPU critique
        if (metrics.cpu.usage > criticalThresholds.cpuLoad) {
            criticalNeeds.push({
                type: 'critical_cpu_load',
                severity: metrics.cpu.usage,
                acceleratorType: 'cpu_booster',
                description: `CRITIQUE: Charge CPU: ${(metrics.cpu.usage * 100).toFixed(1)}%`,
                priority: 'URGENT'
            });
        }

        // Vérifier usage mémoire critique
        if (metrics.memory.usage > criticalThresholds.memoryUsage) {
            criticalNeeds.push({
                type: 'critical_memory_usage',
                severity: metrics.memory.usage,
                acceleratorType: 'memory_optimizer',
                description: `CRITIQUE: Usage mémoire: ${(metrics.memory.usage * 100).toFixed(1)}%`,
                priority: 'URGENT'
            });
        }

        // Vérifier température thermique critique
        if (metrics.thermal.temperature > criticalThresholds.thermalTemp) {
            criticalNeeds.push({
                type: 'critical_thermal_temp',
                severity: metrics.thermal.temperature,
                acceleratorType: 'thermal_cooler',
                description: `CRITIQUE: Température thermique: ${(metrics.thermal.temperature * 100).toFixed(1)}%`,
                priority: 'URGENT'
            });
        }

        // Vérifier temps de réponse critique
        if (metrics.response.averageTime > criticalThresholds.responseTime) {
            criticalNeeds.push({
                type: 'critical_slow_response',
                severity: metrics.response.averageTime / criticalThresholds.responseTime,
                acceleratorType: 'response_accelerator',
                description: `CRITIQUE: Temps de réponse: ${metrics.response.averageTime}ms`,
                priority: 'URGENT'
            });
        }

        return criticalNeeds;
    }

    /**
     * Obtient les statistiques de surveillance continue
     */
    getContinuousMonitoringStats() {
        const recentMetrics = this.metricsHistory.slice(-10); // 10 dernières mesures

        return {
            isActive: this.isActive,
            monitoringFrequency: {
                realTime: '500ms',
                continuous: '100ms',
                standard: '2000ms'
            },
            metricsHistory: {
                total: this.metricsHistory.length,
                recent: recentMetrics.length,
                timespan: recentMetrics.length > 0 ?
                    `${recentMetrics.length * 100}ms` : '0ms'
            },
            lastMetrics: this.lastMetrics,
            activeIntervals: {
                realTime: this.realTimeMonitoringInterval !== null,
                continuous: this.continuousMonitoringInterval !== null,
                standard: this.monitoringInterval !== null
            }
        };
    }

    /**
     * Force une surveillance immédiate
     */
    async forceContinuousCheck() {
        console.log('🔍 SURVEILLANCE FORCÉE IMMÉDIATE...');

        // Surveillance immédiate
        await this.continuousMetricsMonitoring();
        await this.realTimeMonitoring();
        await this.monitorSystemAndAddAccelerators();

        console.log('✅ Surveillance forcée terminée');

        return this.getContinuousMonitoringStats();
    }
}

module.exports = AutoAcceleratorSystem;
