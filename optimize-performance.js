#!/usr/bin/env node

/**
 * Script d'optimisation des performances de Louna
 * Désactive les processus lents pour améliorer la réactivité
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Optimisation des performances de Louna...\n');

// 1. Désactiver les optimisations automatiques dans enhanced-agent.js
const enhancedAgentPath = path.join(__dirname, 'enhanced-agent.js');
if (fs.existsSync(enhancedAgentPath)) {
    let content = fs.readFileSync(enhancedAgentPath, 'utf8');
    
    // Désactiver les actions MCP automatiques
    content = content.replace(
        /setInterval\(this\.performMCPAction\.bind\(this\), \d+\);/g,
        '// setInterval(this.performMCPAction.bind(this), 10000); // DÉSACTIVÉ POUR PERFORMANCE'
    );
    
    // Réduire la fréquence des optimisations
    content = content.replace(
        /setInterval\(.*optimize.*\d+\)/g,
        '// Optimisations automatiques désactivées pour la performance'
    );
    
    fs.writeFileSync(enhancedAgentPath, content);
    console.log('✅ Enhanced Agent optimisé');
}

// 2. Désactiver les cycles de mémoire fréquents
const thermalMemoryPath = path.join(__dirname, 'thermal-memory.js');
if (fs.existsSync(thermalMemoryPath)) {
    let content = fs.readFileSync(thermalMemoryPath, 'utf8');
    
    // Réduire la fréquence des cycles de mémoire
    content = content.replace(
        /setInterval\(.*runMemoryCycle.*\d+\)/g,
        'setInterval(() => this.runMemoryCycle(), 30000); // Réduit à 30s'
    );
    
    fs.writeFileSync(thermalMemoryPath, content);
    console.log('✅ Mémoire thermique optimisée');
}

// 3. Désactiver les sauvegardes fréquentes
const emergencyBackupPath = path.join(__dirname, 'emergency-backup.js');
if (fs.existsSync(emergencyBackupPath)) {
    let content = fs.readFileSync(emergencyBackupPath, 'utf8');
    
    // Réduire la fréquence des sauvegardes
    content = content.replace(
        /setInterval\(.*backup.*\d+\)/g,
        'setInterval(() => this.performBackup(), 60000); // Réduit à 1 minute'
    );
    
    fs.writeFileSync(emergencyBackupPath, content);
    console.log('✅ Système de sauvegarde optimisé');
}

// 4. Optimiser le monitoring des performances
const performanceMonitorPath = path.join(__dirname, 'performance-monitor.js');
if (fs.existsSync(performanceMonitorPath)) {
    let content = fs.readFileSync(performanceMonitorPath, 'utf8');
    
    // Réduire la fréquence du monitoring
    content = content.replace(
        /setInterval\(.*checkPerformance.*\d+\)/g,
        'setInterval(() => this.checkPerformance(), 30000); // Réduit à 30s'
    );
    
    fs.writeFileSync(performanceMonitorPath, content);
    console.log('✅ Monitoring des performances optimisé');
}

// 5. Créer un fichier de configuration rapide
const fastConfigPath = path.join(__dirname, 'fast-config.json');
const fastConfig = {
    "performance": {
        "mode": "fast",
        "disableAutomaticOptimizations": true,
        "reducedMemoryCycles": true,
        "reducedBackupFrequency": true,
        "reducedMonitoring": true,
        "cognitiveResponseDelay": 50,
        "memoryIntegration": false,
        "emotionalProcessing": false,
        "learningEnabled": false
    },
    "speech": {
        "voiceName": "Amelie",
        "gender": "female",
        "rate": 0.9,
        "pitch": 1.2,
        "volume": 0.8,
        "fastResponse": true
    },
    "chat": {
        "typingDelay": 100,
        "responseTimeout": 5000,
        "maxHistoryLength": 5
    }
};

fs.writeFileSync(fastConfigPath, JSON.stringify(fastConfig, null, 2));
console.log('✅ Configuration rapide créée');

// 6. Créer un script de test rapide
const testScriptPath = path.join(__dirname, 'test-fast-response.js');
const testScript = `#!/usr/bin/env node

/**
 * Test de réponse rapide de Louna
 */

const axios = require('axios');

async function testFastResponse() {
    console.log('🚀 Test de réponse rapide de Louna...');
    
    const startTime = Date.now();
    
    try {
        const response = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour Louna, peux-tu me répondre rapidement ?',
            history: []
        }, {
            timeout: 10000
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(\`✅ Réponse reçue en \${responseTime}ms\`);
        console.log(\`📝 Réponse: \${response.data.response}\`);
        
        if (responseTime < 3000) {
            console.log('🎉 Performance EXCELLENTE !');
        } else if (responseTime < 5000) {
            console.log('👍 Performance BONNE');
        } else {
            console.log('⚠️ Performance LENTE - Optimisations nécessaires');
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

testFastResponse();
`;

fs.writeFileSync(testScriptPath, testScript);
fs.chmodSync(testScriptPath, '755');
console.log('✅ Script de test créé');

console.log('\n🎉 Optimisation terminée !');
console.log('\n📋 Changements effectués :');
console.log('• Désactivation des optimisations automatiques');
console.log('• Réduction de la fréquence des cycles de mémoire');
console.log('• Réduction de la fréquence des sauvegardes');
console.log('• Optimisation du monitoring');
console.log('• Configuration rapide créée');
console.log('• Script de test de performance créé');

console.log('\n🚀 Pour tester les performances :');
console.log('node test-fast-response.js');

console.log('\n⚠️ Note: Certaines fonctionnalités avancées sont temporairement désactivées pour la vitesse.');
console.log('Vous pouvez les réactiver plus tard si nécessaire.');
