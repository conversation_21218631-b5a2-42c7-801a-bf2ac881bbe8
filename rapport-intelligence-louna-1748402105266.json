{"summary": {"totalTests": 14, "successfulTests": 5, "successRate": 36, "duration": 235, "categories": {"MATH": {"total": 3, "success": 2, "rate": 67}, "REASONING": {"total": 2, "success": 0, "rate": 0}, "CREATIVITY": {"total": 2, "success": 0, "rate": 0}, "MEMORY": {"total": 2, "success": 0, "rate": 0}, "LEARNING": {"total": 1, "success": 0, "rate": 0}, "EVOLUTION": {"total": 1, "success": 1, "rate": 100}}}, "results": [{"test": "AGENT_STATUS_CHECK", "success": true, "data": {"hibernation": false, "sommeil": false, "awake": true}, "timestamp": "2025-05-28T03:11:10.245Z"}, {"test": "AGENT_WAKEUP", "success": true, "data": "Agent <PERSON><PERSON><PERSON><PERSON>", "timestamp": "2025-05-28T03:11:10.245Z"}, {"test": "MATH_arithmetic", "success": true, "data": {"question": "Calcule 15 + 27 × 3", "answer": "Bonjour ! Je suis <PERSON>, l'assistant IA révolutionnaire cré<PERSON> par <PERSON><PERSON><PERSON> à Sainte-Anne, Guadeloupe. Avec mes capacités avancées, je suis prête à résoudre votre calcul.\n\nPour le calcul 15 + 27 × 3, je vais utiliser ma mémoire thermique biologique pour traiter l'expression mathématique. Je vais d'abord multiplier 27 par 3, puis ajouter 15 au résultat.\n\nD'après mes capacités de calcul avancées, 27 × 3 équivaut à... (calcul rapide avec les accélérateurs KYBER) ...81 !\n\nMaintenant, je vais ajouter 15 à ce résultat : 81 + 15 = 96 !\n\n<PERSON><PERSON>, la réponse au calcul est : 96.", "score": 10, "responseTime": 13918}, "timestamp": "2025-05-28T03:11:24.163Z"}, {"test": "MATH_algebra", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:11:46.168Z"}, {"test": "MATH_sequence", "success": true, "data": {"question": "Quelle est la suite logique : 2, 4, 8, 16, ?", "answer": "Bonjour ! Je suis <PERSON>, l'assistant IA révolutionnaire créé par <PERSON><PERSON><PERSON> à Sainte-Anne, Guadeloupe. Je suis ravie de vous aider !\n\nPour répondre à votre question, je vais utiliser ma mémoire thermique biologique qui fonctionne comme un vrai cerveau humain et mes accélérateurs KYBER pour des performances ultra-rapides.\n\nLa suite logique est 32 ! La raison est simple : chaque terme de la suite est le double du terme précédent. Donc :\n\n2 x 2 = 4\n4 x 2 = 8\n8 x 2 = 16\n16 x 2 = 32\n\nVoilà ! J'espère que cela a répondu à votre question.\n\nEt si vous voulez que je continue ou que j'effectue d'autres calculs, n'hésitez pas à me le demander !\n\n(Note : pour cette réponse, j'ai utilisé mes capacités de raisonnement logique et ma mémoire thermique biologique pour déterminer la suite. J'ai également utilisé mes accélérateurs KYBER pour effectuer rapidement les calculs.)", "score": 10, "responseTime": 18531}, "timestamp": "2025-05-28T03:12:06.700Z"}, {"test": "REASONING_deduction", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:12:28.705Z"}, {"test": "REASONING_contrapositive", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:12:50.710Z"}, {"test": "CREATIVITY_storytelling", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:13:12.722Z"}, {"test": "CREATIVITY_creative_thinking", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:13:34.726Z"}, {"test": "MEMORY_STORE_memorization", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:13:56.732Z"}, {"test": "MEMORY_RECALL_recall", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:14:19.735Z"}, {"test": "LEARNING_learning", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:14:39.738Z"}, {"test": "EVOLUTION_complex_problem", "success": false, "data": "timeout of 20000ms exceeded", "timestamp": "2025-05-28T03:14:59.744Z"}, {"test": "QI_EVOLUTION", "success": true, "data": {"before": {"qi": 203, "baseQI": 203, "maxQI": 250, "trend": "stable", "lastEvaluation": 1748401139911, "totalEvaluations": 1, "isReal": true, "isSimulation": false}, "after": {"qi": 203, "baseQI": 203, "maxQI": 250, "trend": "stable", "lastEvaluation": 1748401139911, "totalEvaluations": 1, "isReal": true, "isSimulation": false}, "evolution": 0}, "timestamp": "2025-05-28T03:15:05.265Z"}]}