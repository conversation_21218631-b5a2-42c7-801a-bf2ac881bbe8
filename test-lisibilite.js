#!/usr/bin/env node

/**
 * Script de test pour vérifier que toutes les corrections de lisibilité sont appliquées
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:3004';

// Pages à tester
const pagesToTest = [
    { url: '/', name: 'Page d\'accueil' },
    { url: '/chat', name: 'Interface de chat' },
    { url: '/presentation.html', name: 'Présentation' },
    { url: '/futuristic-interface.html', name: 'Mémoire thermique' },
    { url: '/brain-visualization.html', name: 'Visualisation 3D' },
    { url: '/kyber-dashboard.html', name: 'Accélérateurs Kyber' },
    { url: '/generation-studio.html', name: 'Studio de génération' },
    { url: '/agent-navigation.html', name: 'Navigation agent' },
    { url: '/security-dashboard.html', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { url: '/performance.html', name: 'Performances' },
    { url: '/qi-neuron-monitor.html', name: 'Monitoring Qi' },
    { url: '/agents.html', name: 'Gestion agents' },
    { url: '/training.html', name: 'Formation' },
    { url: '/memory-fusion.html', name: 'Fusion mémoire' },
    { url: '/settings.html', name: 'Paramètres' }
];

// Tests à effectuer
const tests = [
    {
        name: 'Fichier CSS de corrections chargé',
        test: (html) => html.includes('contrast-fixes.css'),
        description: 'Vérifie que le fichier CSS de corrections est inclus'
    },
    {
        name: 'Corrections globales présentes',
        test: (html) => html.includes('text-rendering: optimizeLegibility'),
        description: 'Vérifie que les corrections globales sont appliquées'
    },
    {
        name: 'Variables CSS corrigées',
        test: (html) => html.includes('--text-primary: #ffffff'),
        description: 'Vérifie que les variables CSS sont corrigées'
    },
    {
        name: 'Ombres de texte appliquées',
        test: (html) => html.includes('text-shadow'),
        description: 'Vérifie que les ombres de texte sont appliquées'
    },
    {
        name: 'Corrections de navigation',
        test: (html) => html.includes('nav-item') && html.includes('color: #000000'),
        description: 'Vérifie que la navigation est corrigée'
    }
];

/**
 * Teste une page spécifique
 */
async function testPage(page) {
    try {
        console.log(`\n🔍 Test de ${page.name} (${page.url})...`);
        
        const response = await axios.get(`${BASE_URL}${page.url}`, {
            timeout: 10000
        });
        
        if (response.status !== 200) {
            console.log(`❌ Erreur HTTP ${response.status}`);
            return { page: page.name, success: false, error: `HTTP ${response.status}` };
        }
        
        const html = response.data;
        const results = [];
        
        // Exécuter tous les tests
        for (const test of tests) {
            const passed = test.test(html);
            results.push({
                name: test.name,
                passed,
                description: test.description
            });
            
            console.log(`  ${passed ? '✅' : '❌'} ${test.name}`);
            if (!passed) {
                console.log(`     ${test.description}`);
            }
        }
        
        const passedTests = results.filter(r => r.passed).length;
        const totalTests = results.length;
        const successRate = (passedTests / totalTests) * 100;
        
        console.log(`📊 Résultat: ${passedTests}/${totalTests} tests réussis (${successRate.toFixed(1)}%)`);
        
        return {
            page: page.name,
            url: page.url,
            success: successRate >= 80, // 80% minimum requis
            successRate,
            passedTests,
            totalTests,
            results
        };
        
    } catch (error) {
        console.log(`❌ Erreur: ${error.message}`);
        return { 
            page: page.name, 
            url: page.url,
            success: false, 
            error: error.message 
        };
    }
}

/**
 * Teste le fichier CSS directement
 */
async function testCSSFile() {
    try {
        console.log('\n🎨 Test du fichier CSS de corrections...');
        
        const response = await axios.get(`${BASE_URL}/css/contrast-fixes.css`, {
            timeout: 5000
        });
        
        if (response.status !== 200) {
            console.log(`❌ Fichier CSS non accessible (HTTP ${response.status})`);
            return false;
        }
        
        const css = response.data;
        
        // Tests spécifiques au CSS
        const cssTests = [
            { name: 'Corrections globales', test: css.includes('text-rendering: optimizeLegibility') },
            { name: 'Variables CSS', test: css.includes('--text-primary: #ffffff') },
            { name: 'Ombres de texte', test: css.includes('text-shadow') },
            { name: 'Navigation', test: css.includes('.nav-item') },
            { name: 'Boutons', test: css.includes('.cta-button') },
            { name: 'Cartes', test: css.includes('.feature-card') },
            { name: 'Chat', test: css.includes('.message') },
            { name: 'Corrections d\'urgence', test: css.includes('.force-visible') }
        ];
        
        let passedCSSTests = 0;
        for (const test of cssTests) {
            console.log(`  ${test.test ? '✅' : '❌'} ${test.name}`);
            if (test.test) passedCSSTests++;
        }
        
        const cssSuccessRate = (passedCSSTests / cssTests.length) * 100;
        console.log(`📊 CSS: ${passedCSSTests}/${cssTests.length} tests réussis (${cssSuccessRate.toFixed(1)}%)`);
        
        return cssSuccessRate >= 90; // 90% minimum pour le CSS
        
    } catch (error) {
        console.log(`❌ Erreur CSS: ${error.message}`);
        return false;
    }
}

/**
 * Teste l'agent Claude 4GB
 */
async function testAgent() {
    try {
        console.log('\n🤖 Test de l\'agent Claude 4GB...');
        
        const response = await axios.post(`${BASE_URL}/api/chat/message`, {
            message: "Test de lisibilité - réponds brièvement",
            history: []
        }, {
            timeout: 30000,
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.status !== 200) {
            console.log(`❌ Agent non accessible (HTTP ${response.status})`);
            return false;
        }
        
        const data = response.data;
        
        console.log(`  ✅ Agent actif: ${data.agent?.name || 'Inconnu'}`);
        console.log(`  ✅ Mémoires utilisées: ${data.relevantMemories || 0}`);
        console.log(`  ✅ Temps de traitement: ${data.processingTime || 0}ms`);
        console.log(`  ✅ Boost factor: ${data.boostFactor || 0}`);
        
        return data.success && data.response;
        
    } catch (error) {
        console.log(`❌ Erreur agent: ${error.message}`);
        return false;
    }
}

/**
 * Fonction principale
 */
async function runTests() {
    console.log('🎨 TESTS DE LISIBILITÉ - APPLICATION LOUNA\n');
    console.log('='.repeat(50));
    
    const results = [];
    
    // Test du fichier CSS
    const cssOK = await testCSSFile();
    
    // Test de l'agent
    const agentOK = await testAgent();
    
    // Test des pages
    console.log('\n📄 Tests des pages HTML...');
    for (const page of pagesToTest) {
        const result = await testPage(page);
        results.push(result);
        
        // Petite pause entre les tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Résumé final
    console.log('\n' + '='.repeat(50));
    console.log('📊 RÉSUMÉ FINAL');
    console.log('='.repeat(50));
    
    const successfulPages = results.filter(r => r.success).length;
    const totalPages = results.length;
    const overallSuccessRate = (successfulPages / totalPages) * 100;
    
    console.log(`🎨 Fichier CSS: ${cssOK ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`🤖 Agent Claude 4GB: ${agentOK ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`📄 Pages testées: ${successfulPages}/${totalPages} (${overallSuccessRate.toFixed(1)}%)`);
    
    // Détail des échecs
    const failedPages = results.filter(r => !r.success);
    if (failedPages.length > 0) {
        console.log('\n❌ Pages avec problèmes:');
        failedPages.forEach(page => {
            console.log(`  - ${page.page}: ${page.error || `${page.successRate?.toFixed(1)}% réussite`}`);
        });
    }
    
    // Verdict final
    const allSystemsOK = cssOK && agentOK && overallSuccessRate >= 80;
    
    console.log('\n' + '='.repeat(50));
    if (allSystemsOK) {
        console.log('🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('✅ Les corrections de lisibilité sont parfaitement appliquées');
        console.log('✅ L\'agent Claude 4GB fonctionne correctement');
        console.log('✅ Toutes les pages sont accessibles et corrigées');
    } else {
        console.log('⚠️  CERTAINS TESTS ONT ÉCHOUÉ');
        console.log('🔧 Vérifiez les corrections et relancez les tests');
    }
    console.log('='.repeat(50));
    
    return allSystemsOK;
}

// Exécuter les tests
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ Erreur fatale:', error);
        process.exit(1);
    });
}

module.exports = { runTests };
