#!/usr/bin/env node

/**
 * Test visuel simple pour vérifier les corrections de lisibilité
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3004';

async function testVisuel() {
    console.log('🎨 TEST VISUEL SIMPLE - CORRECTIONS DE LISIBILITÉ\n');
    
    try {
        // Test 1: Fichier CSS accessible
        console.log('1️⃣ Test du fichier CSS de corrections...');
        const cssResponse = await axios.get(`${BASE_URL}/css/contrast-fixes.css`);
        const cssOK = cssResponse.status === 200 && cssResponse.data.includes('text-shadow');
        console.log(`   ${cssOK ? '✅' : '❌'} Fichier CSS accessible et contient les corrections`);
        
        // Test 2: Page d'accueil charge le CSS
        console.log('\n2️⃣ Test de la page d\'accueil...');
        const homeResponse = await axios.get(`${BASE_URL}/`);
        const homeOK = homeResponse.status === 200 && homeResponse.data.includes('contrast-fixes.css');
        console.log(`   ${homeOK ? '✅' : '❌'} Page d\'accueil charge le fichier de corrections`);
        
        // Test 3: Page de chat charge le CSS
        console.log('\n3️⃣ Test de la page de chat...');
        const chatResponse = await axios.get(`${BASE_URL}/chat`);
        const chatOK = chatResponse.status === 200 && chatResponse.data.includes('contrast-fixes.css');
        console.log(`   ${chatOK ? '✅' : '❌'} Page de chat charge le fichier de corrections`);
        
        // Test 4: Agent Claude 4GB fonctionne
        console.log('\n4️⃣ Test de l\'agent Claude 4GB...');
        const agentResponse = await axios.post(`${BASE_URL}/api/chat/message`, {
            message: "Test rapide",
            history: []
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 20000
        });
        const agentOK = agentResponse.status === 200 && agentResponse.data.success;
        console.log(`   ${agentOK ? '✅' : '❌'} Agent Claude 4GB répond correctement`);
        if (agentOK) {
            console.log(`   📊 Mémoires utilisées: ${agentResponse.data.relevantMemories || 0}`);
            console.log(`   ⚡ Boost factor: ${agentResponse.data.boostFactor?.toFixed(2) || 0}`);
        }
        
        // Test 5: Vérification des corrections CSS spécifiques
        console.log('\n5️⃣ Test des corrections CSS spécifiques...');
        const css = cssResponse.data;
        const corrections = [
            { name: 'Ombres de texte', test: css.includes('text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8)') },
            { name: 'Variables CSS', test: css.includes('--text-primary: #ffffff !important') },
            { name: 'Navigation', test: css.includes('.nav-item') && css.includes('color: #000000 !important') },
            { name: 'Boutons', test: css.includes('.cta-button') },
            { name: 'Cartes', test: css.includes('.feature-card') },
            { name: 'Corrections d\'urgence', test: css.includes('.force-visible') }
        ];
        
        let correctionsOK = 0;
        corrections.forEach(correction => {
            console.log(`   ${correction.test ? '✅' : '❌'} ${correction.name}`);
            if (correction.test) correctionsOK++;
        });
        
        // Résumé final
        console.log('\n' + '='.repeat(50));
        console.log('📊 RÉSUMÉ FINAL');
        console.log('='.repeat(50));
        
        const allTestsOK = cssOK && homeOK && chatOK && agentOK && (correctionsOK >= 5);
        
        console.log(`🎨 Fichier CSS: ${cssOK ? '✅ OK' : '❌ ÉCHEC'}`);
        console.log(`🏠 Page d'accueil: ${homeOK ? '✅ OK' : '❌ ÉCHEC'}`);
        console.log(`💬 Page de chat: ${chatOK ? '✅ OK' : '❌ ÉCHEC'}`);
        console.log(`🤖 Agent Claude 4GB: ${agentOK ? '✅ OK' : '❌ ÉCHEC'}`);
        console.log(`🔧 Corrections CSS: ${correctionsOK}/6 (${((correctionsOK/6)*100).toFixed(1)}%)`);
        
        console.log('\n' + '='.repeat(50));
        if (allTestsOK) {
            console.log('🎉 TOUS LES TESTS RÉUSSIS !');
            console.log('✅ Les corrections de lisibilité sont correctement appliquées');
            console.log('✅ L\'application fonctionne parfaitement');
            console.log('✅ Le problème de texte blanc sur blanc est résolu');
        } else {
            console.log('⚠️  CERTAINS TESTS ONT ÉCHOUÉ');
            console.log('🔧 Vérifiez la configuration et relancez');
        }
        console.log('='.repeat(50));
        
        return allTestsOK;
        
    } catch (error) {
        console.error('❌ Erreur lors des tests:', error.message);
        return false;
    }
}

// Exécuter le test
if (require.main === module) {
    testVisuel().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testVisuel };
