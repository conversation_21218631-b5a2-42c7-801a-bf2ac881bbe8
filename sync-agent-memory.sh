#!/bin/bash

# Script pour synchroniser la mémoire entre l'agent principal et l'agent de formation

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Fonction pour afficher un avertissement
print_warning() {
  echo -e "${YELLOW}[Attention]${NC} $1"
}

# Fonction pour afficher une information
print_info() {
  echo -e "${BLUE}[Info]${NC} $1"
}

# Vérifier si le serveur est en cours d'exécution
check_server() {
  if ! curl -s http://localhost:3004/ > /dev/null; then
    print_error "Le serveur n'est pas en cours d'exécution. Veuillez démarrer l'application avec './launch-louna-with-ollama.sh'"
    exit 1
  else
    print_success "Le serveur est en cours d'exécution."
  fi
}

# Vérifier si les agents sont configurés
check_agents() {
  # Vérifier si le fichier agents.json existe
  if [ ! -f "data/config/agents.json" ]; then
    print_error "Le fichier de configuration des agents n'existe pas. Veuillez démarrer l'application avec './launch-louna-with-ollama.sh'"
    exit 1
  fi

  # Vérifier si l'agent principal est configuré
  if ! grep -q "agent_claude" data/config/agents.json; then
    print_error "L'agent principal n'est pas configuré. Veuillez démarrer l'application avec './launch-louna-with-ollama.sh'"
    exit 1
  fi

  # Vérifier si l'agent de formation est configuré
  if ! grep -q "agent_training" data/config/agents.json; then
    print_warning "L'agent de formation n'est pas configuré. La synchronisation utilisera uniquement l'agent principal."
    TRAINING_AGENT_AVAILABLE=false
  else
    TRAINING_AGENT_AVAILABLE=true
    print_success "Les agents sont correctement configurés."
  fi
}

# Synchroniser la mémoire entre les agents
sync_memory() {
  print_message "Synchronisation de la mémoire entre les agents..."

  # Créer un script Node.js temporaire pour la synchronisation
  TMP_SCRIPT=$(mktemp)
  cat > "$TMP_SCRIPT" <<EOF
/**
 * Script temporaire pour synchroniser la mémoire entre les agents
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// Configuration
const MAIN_AGENT_ID = 'agent_claude';
const TRAINING_AGENT_ID = '${TRAINING_AGENT_ID}';
const SERVER_PORT = 3004; // Port utilisé par l'application

// Fonction pour effectuer une requête HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve(parsedData);
        } catch (error) {
          reject(new Error(\`Erreur lors de l'analyse de la réponse: \${error.message}\`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Fonction principale
async function syncMemory() {
  try {
    console.log('Récupération des entrées de mémoire de l\'agent principal...');
    
    // Récupérer les entrées de mémoire de l'agent principal
    const mainAgentMemoryOptions = {
      hostname: 'localhost',
      port: SERVER_PORT,
      path: '/api/thermal/memory/entries?agentId=' + MAIN_AGENT_ID,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const mainAgentMemoryResponse = await makeRequest(mainAgentMemoryOptions);
    
    if (!mainAgentMemoryResponse.success) {
      throw new Error(\`Erreur lors de la récupération des entrées de mémoire de l'agent principal: \${mainAgentMemoryResponse.error}\`);
    }
    
    const mainAgentEntries = mainAgentMemoryResponse.entries;
    console.log(\`\${mainAgentEntries.length} entrées de mémoire récupérées pour l'agent principal.\`);
    
    // Si l'agent de formation n'est pas disponible, terminer
    if (TRAINING_AGENT_ID === MAIN_AGENT_ID) {
      console.log('L\'agent de formation n\'est pas disponible. La synchronisation est terminée.');
      return;
    }
    
    // Récupérer les entrées de mémoire de l'agent de formation
    console.log('Récupération des entrées de mémoire de l\'agent de formation...');
    
    const trainingAgentMemoryOptions = {
      hostname: 'localhost',
      port: SERVER_PORT,
      path: '/api/thermal/memory/entries?agentId=' + TRAINING_AGENT_ID,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const trainingAgentMemoryResponse = await makeRequest(trainingAgentMemoryOptions);
    
    if (!trainingAgentMemoryResponse.success) {
      throw new Error(\`Erreur lors de la récupération des entrées de mémoire de l'agent de formation: \${trainingAgentMemoryResponse.error}\`);
    }
    
    const trainingAgentEntries = trainingAgentMemoryResponse.entries;
    console.log(\`\${trainingAgentEntries.length} entrées de mémoire récupérées pour l'agent de formation.\`);
    
    // Identifier les entrées de mémoire importantes à synchroniser
    console.log('Identification des entrées de mémoire importantes à synchroniser...');
    
    const entriesToSync = [];
    
    // Synchroniser de l'agent principal vers l'agent de formation
    for (const entry of mainAgentEntries) {
      // Vérifier si l'entrée existe déjà dans la mémoire de l'agent de formation
      const existsInTrainingAgent = trainingAgentEntries.some(e => e.key === entry.key);
      
      // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
      if (!existsInTrainingAgent && entry.importance > 0.5) {
        entriesToSync.push({
          source: MAIN_AGENT_ID,
          target: TRAINING_AGENT_ID,
          entry
        });
      }
    }
    
    // Synchroniser de l'agent de formation vers l'agent principal
    for (const entry of trainingAgentEntries) {
      // Vérifier si l'entrée existe déjà dans la mémoire de l'agent principal
      const existsInMainAgent = mainAgentEntries.some(e => e.key === entry.key);
      
      // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
      if (!existsInMainAgent && entry.importance > 0.7) {
        entriesToSync.push({
          source: TRAINING_AGENT_ID,
          target: MAIN_AGENT_ID,
          entry
        });
      }
    }
    
    console.log(\`\${entriesToSync.length} entrées de mémoire à synchroniser.\`);
    
    // Synchroniser les entrées de mémoire
    if (entriesToSync.length > 0) {
      console.log('Synchronisation des entrées de mémoire...');
      
      for (const syncItem of entriesToSync) {
        const { source, target, entry } = syncItem;
        
        // Créer une copie de l'entrée pour l'agent cible
        const entryCopy = { ...entry };
        delete entryCopy.id; // Supprimer l'ID pour en générer un nouveau
        
        // Ajouter des métadonnées de synchronisation
        entryCopy.metadata = entryCopy.metadata || {};
        entryCopy.metadata.syncedFrom = source;
        entryCopy.metadata.syncedAt = new Date().toISOString();
        
        // Ajouter l'entrée à la mémoire de l'agent cible
        const addEntryOptions = {
          hostname: 'localhost',
          port: SERVER_PORT,
          path: '/api/thermal/memory/add',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        };
        
        const addEntryData = {
          agentId: target,
          key: entryCopy.key,
          data: entryCopy.data,
          category: entryCopy.category,
          importance: entryCopy.importance,
          metadata: entryCopy.metadata
        };
        
        const addEntryResponse = await makeRequest(addEntryOptions, addEntryData);
        
        if (!addEntryResponse.success) {
          console.error(\`Erreur lors de l'ajout de l'entrée \${entryCopy.key} à la mémoire de l'agent \${target}: \${addEntryResponse.error}\`);
        } else {
          console.log(\`Entrée \${entryCopy.key} ajoutée à la mémoire de l'agent \${target}.\`);
        }
      }
      
      console.log('Synchronisation terminée.');
    } else {
      console.log('Aucune entrée de mémoire à synchroniser.');
    }
  } catch (error) {
    console.error('Erreur lors de la synchronisation de la mémoire:', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
syncMemory();
EOF

  # Exécuter le script Node.js
  node "$TMP_SCRIPT"
  
  # Supprimer le script temporaire
  rm "$TMP_SCRIPT"
}

# Fonction principale
main() {
  print_message "Synchronisation de la mémoire entre les agents..."
  
  # Vérifier si le serveur est en cours d'exécution
  check_server
  
  # Vérifier si les agents sont configurés
  check_agents
  
  # Définir l'agent de formation
  if [ "$TRAINING_AGENT_AVAILABLE" = true ]; then
    TRAINING_AGENT_ID="agent_training"
  else
    TRAINING_AGENT_ID="agent_claude"
  fi
  
  # Synchroniser la mémoire
  sync_memory
  
  print_success "Synchronisation terminée."
}

# Exécuter la fonction principale
main
