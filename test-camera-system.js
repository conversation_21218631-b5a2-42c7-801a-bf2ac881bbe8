/**
 * Script de test pour le système de caméra et analyse vidéo
 * C<PERSON><PERSON> pour <PERSON> - Louna Application
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3005';

async function testCameraSystem() {
    console.log('🎥 Test du système de caméra et analyse vidéo...\n');

    // Test 1: Vérifier le statut du serveur
    try {
        console.log('1️⃣ Test du statut du serveur...');
        const statusResponse = await axios.get(`${BASE_URL}/status`);
        console.log('✅ Serveur actif');
        console.log(`   Version: ${statusResponse.data.version}`);
        console.log(`   Uptime: ${Math.floor(statusResponse.data.uptime)}s`);
        console.log(`   Systèmes: ${Object.keys(statusResponse.data.systems || {}).length} actifs\n`);
    } catch (error) {
        console.log('❌ Erreur statut serveur:', error.message);
        return;
    }

    // Test 2: Vérifier les routes caméra
    try {
        console.log('2️⃣ Test des routes caméra...');
        const cameraResponse = await axios.get(`${BASE_URL}/api/camera/stats`);
        console.log('✅ Routes caméra actives');
        console.log('   Stats caméra:', cameraResponse.data);
    } catch (error) {
        console.log('⚠️ Routes caméra non disponibles:', error.response?.status || error.message);
    }

    // Test 3: Vérifier les routes YouTube
    try {
        console.log('\n3️⃣ Test des routes YouTube...');
        const youtubeResponse = await axios.get(`${BASE_URL}/api/youtube/stats`);
        console.log('✅ Routes YouTube actives');
        console.log('   Stats YouTube:', youtubeResponse.data);
    } catch (error) {
        console.log('⚠️ Routes YouTube non disponibles:', error.response?.status || error.message);
    }

    // Test 4: Vérifier l'interface caméra
    try {
        console.log('\n4️⃣ Test de l\'interface caméra...');
        const interfaceResponse = await axios.get(`${BASE_URL}/camera`);
        if (interfaceResponse.status === 200) {
            console.log('✅ Interface caméra accessible');
        }
    } catch (error) {
        console.log('❌ Interface caméra non accessible:', error.message);
    }

    // Test 5: Test de capture simulée
    try {
        console.log('\n5️⃣ Test de capture simulée...');
        const captureResponse = await axios.post(`${BASE_URL}/api/camera/capture`, {
            autoSave: false,
            facialRecognition: true
        });
        console.log('✅ Capture simulée réussie');
        console.log('   Résultat:', captureResponse.data);
    } catch (error) {
        console.log('⚠️ Capture non disponible:', error.response?.status || error.message);
    }

    // Test 6: Test d'analyse YouTube simulée
    try {
        console.log('\n6️⃣ Test d\'analyse YouTube simulée...');
        const analyzeResponse = await axios.post(`${BASE_URL}/api/youtube/analyze`, {
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            options: {
                frameExtraction: true,
                transcription: true
            }
        });
        console.log('✅ Analyse YouTube simulée réussie');
        console.log('   Résultat:', analyzeResponse.data);
    } catch (error) {
        console.log('⚠️ Analyse YouTube non disponible:', error.response?.status || error.message);
    }

    console.log('\n🎯 Test terminé!');
}

// Fonction pour tester les dépendances Python
async function testPythonDependencies() {
    console.log('\n🐍 Test des dépendances Python...');
    
    const dependencies = [
        'opencv-python',
        'face_recognition',
        'numpy',
        'pillow',
        'whisper',
        'yt-dlp'
    ];

    for (const dep of dependencies) {
        try {
            const { spawn } = require('child_process');
            const result = await new Promise((resolve, reject) => {
                const python = spawn('python3', ['-c', `import ${dep.replace('-', '_')}; print('OK')`]);
                let output = '';
                
                python.stdout.on('data', (data) => {
                    output += data.toString();
                });
                
                python.on('close', (code) => {
                    if (code === 0 && output.includes('OK')) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                });
                
                python.on('error', () => resolve(false));
            });
            
            if (result) {
                console.log(`✅ ${dep}`);
            } else {
                console.log(`❌ ${dep} - Non installé`);
            }
        } catch (error) {
            console.log(`❌ ${dep} - Erreur test`);
        }
    }
}

// Fonction pour afficher les informations système
function displaySystemInfo() {
    console.log('💻 Informations système:');
    console.log(`   OS: ${process.platform}`);
    console.log(`   Node.js: ${process.version}`);
    console.log(`   Architecture: ${process.arch}`);
    console.log(`   Répertoire: ${process.cwd()}`);
    console.log('');
}

// Exécution des tests
async function runAllTests() {
    displaySystemInfo();
    await testCameraSystem();
    await testPythonDependencies();
    
    console.log('\n📋 Résumé:');
    console.log('   - Vérifiez que toutes les dépendances sont installées');
    console.log('   - Ajoutez des photos de Jean-Luc dans data/face_data/jean-luc/');
    console.log('   - Redémarrez l\'application si nécessaire');
    console.log('   - Accédez à http://localhost:3005/camera pour tester');
}

// Lancer les tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testCameraSystem,
    testPythonDependencies,
    displaySystemInfo
};
