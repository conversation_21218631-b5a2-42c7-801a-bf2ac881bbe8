{"name": "louna-thermal-memory", "version": "1.0.0", "description": "Agent <PERSON><PERSON> avec mémoire thermique et accélérateurs Kyber", "main": "main.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "electron": "electron .", "electron-dev": "cross-env NODE_ENV=development electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "build-mac-custom": "node build-mac.js", "icons": "node convert-icons.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["louna", "thermal-memory", "kyber", "accelerators", "agent"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.3.0", "mime-types": "^3.0.1", "multer": "^2.0.0", "path-extra": "^4.3.0", "puppeteer": "^24.9.0", "socket.io": "^4.8.1", "uuid": "^11.1.0", "whisper-node": "^1.1.1"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^36.3.1", "electron-builder": "^26.0.12", "nodemon": "^2.0.22", "png2icons": "^2.0.1", "sharp": "^0.34.2"}, "build": {"appId": "com.louna.thermal-memory", "productName": "<PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "mac": {"category": "public.app-category.productivity", "target": ["dmg", "zip"], "icon": "public/img/louna-icon.icns"}, "win": {"target": ["nsis", "portable"], "icon": "public/img/louna-icon.ico"}, "linux": {"target": ["AppImage", "deb"], "category": "Utility", "icon": "public/img/louna-icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}