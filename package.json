{"name": "louna-ai", "version": "2.1.0", "description": "Intelligence Artificielle Évolutive avec Mémoire Thermique - Version Finale", "main": "main.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://louna-ai.com", "repository": {"type": "git", "url": "https://github.com/jean-luc-passave/louna-ai.git"}, "keywords": ["ai", "artificial-intelligence", "thermal-memory", "cognitive-system", "electron", "nodejs", "machine-learning", "neural-network"], "scripts": {"start": "node server.js", "dev": "nodemon server.js", "electron": "electron main.js", "electron-dev": "ELECTRON_IS_DEV=1 electron main.js", "build": "electron-builder", "build-mac": "electron-builder --mac", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "npm run build", "test": "jest", "test-watch": "jest --watch", "lint": "eslint .", "format": "prettier --write .", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"axios": "^1.4.0", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "ffmpeg-static": "^5.1.0", "fluent-ffmpeg": "^2.1.2", "helmet": "^6.1.5", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "node-fetch": "^3.3.1", "puppeteer": "^24.9.0", "serve-static": "^1.15.0", "sharp": "^0.32.1", "socket.io": "^4.6.2", "winston": "^3.9.0", "ws": "^8.13.0"}, "devDependencies": {"concurrently": "^8.2.0", "electron": "^25.2.0", "electron-builder": "^24.4.0", "eslint": "^8.42.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "prettier": "^2.8.8"}, "build": {"appId": "com.louna-ai.app", "productName": "Louna AI", "directories": {"output": "dist"}, "files": ["main.js", "server.js", "public/**/*", "data/**/*", "config/**/*", "modules/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.productivity", "target": ["dmg", "zip"], "icon": "public/img/louna-icon.icns"}, "win": {"target": "nsis", "icon": "public/img/louna-icon.ico"}, "linux": {"target": "AppImage", "icon": "public/img/louna-icon.png"}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}