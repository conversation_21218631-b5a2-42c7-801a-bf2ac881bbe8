#!/usr/bin/env node

/**
 * SYSTÈME COGNITIF COMPLET POUR LOUNA
 * Intègre tous les agents et systèmes d'intelligence
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const DeepSeekAgent = require('./deepseek-agent');

class CognitiveSystem extends EventEmitter {
    constructor() {
        super();
        
        this.agents = {
            deepseek: new DeepSeekAgent(),
            main: null // Agent principal Louna
        };
        
        this.cognitiveState = {
            isActive: false,
            currentMode: 'IDLE',
            lastInteraction: null,
            conversationContext: [],
            emotionalState: 'neutral',
            learningMode: false,
            performanceMetrics: {
                qi: 150,
                neurones: 71,
                evolution_level: 85,
                temperature: 42.3,
                accelerateurs: 8
            }
        };
        
        this.capabilities = [
            'Conversation intelligente',
            'Formation continue',
            'Recherche Internet',
            'Analyse cognitive',
            'Optimisation système',
            'Mémoire thermique',
            'Accélérateurs KYBER'
        ];
        
        console.log('🧠 Système cognitif complet initialisé');
    }
    
    /**
     * Activer le système cognitif
     */
    async activate() {
        try {
            this.cognitiveState.isActive = true;
            this.cognitiveState.currentMode = 'ACTIVE';
            
            // Activer l'agent DeepSeek
            this.agents.deepseek.activate();
            
            // Charger l'état depuis l'API globale
            await this.loadGlobalState();
            
            console.log('🚀 Système cognitif activé');
            this.emit('activated');
            
            return true;
        } catch (error) {
            console.error('❌ Erreur activation système cognitif:', error);
            return false;
        }
    }
    
    /**
     * Désactiver le système cognitif
     */
    deactivate() {
        this.cognitiveState.isActive = false;
        this.cognitiveState.currentMode = 'IDLE';
        
        // Désactiver tous les agents
        Object.values(this.agents).forEach(agent => {
            if (agent && typeof agent.deactivate === 'function') {
                agent.deactivate();
            }
        });
        
        console.log('⏹️ Système cognitif désactivé');
        this.emit('deactivated');
        
        return true;
    }
    
    /**
     * Traiter un message avec le système cognitif
     */
    async processMessage(message, options = {}) {
        if (!this.cognitiveState.isActive) {
            return {
                success: false,
                error: 'Système cognitif non activé'
            };
        }
        
        try {
            console.log(`🧠 Traitement cognitif: ${message}`);
            
            // Ajouter au contexte conversationnel
            this.cognitiveState.conversationContext.push({
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            });
            
            // Analyser le message pour déterminer l'agent approprié
            const targetAgent = this.selectAgent(message);
            
            let response;
            if (targetAgent === 'deepseek') {
                // Utiliser l'agent DeepSeek pour formation/évaluation
                const result = await this.agents.deepseek.chat(message, {
                    ...this.cognitiveState.performanceMetrics,
                    context: this.cognitiveState.conversationContext.slice(-5)
                });
                
                if (result.success) {
                    response = result.response;
                } else {
                    response = "Désolé, l'agent DeepSeek n'est pas disponible actuellement.";
                }
            } else {
                // Utiliser l'agent principal (simulation pour l'instant)
                response = await this.processWithMainAgent(message, options);
            }
            
            // Ajouter la réponse au contexte
            this.cognitiveState.conversationContext.push({
                role: 'assistant',
                content: response,
                timestamp: new Date().toISOString(),
                agent: targetAgent
            });
            
            // Limiter le contexte
            if (this.cognitiveState.conversationContext.length > 20) {
                this.cognitiveState.conversationContext = this.cognitiveState.conversationContext.slice(-20);
            }
            
            // Mettre à jour l'état
            this.cognitiveState.lastInteraction = new Date().toISOString();
            
            // Émettre l'événement
            this.emit('messageProcessed', {
                message: message,
                response: response,
                agent: targetAgent
            });
            
            return {
                success: true,
                response: response,
                agent: targetAgent,
                context: this.cognitiveState.conversationContext.slice(-3)
            };
            
        } catch (error) {
            console.error('❌ Erreur traitement message:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Sélectionner l'agent approprié
     */
    selectAgent(message) {
        const lowerMessage = message.toLowerCase();
        
        // Mots-clés pour l'agent DeepSeek
        const deepseekKeywords = [
            'formation', 'apprendre', 'enseigner', 'former',
            'évaluer', 'tester', 'analyser', 'performance',
            'optimiser', 'améliorer', 'qi', 'neurones',
            'rechercher', 'internet', 'information'
        ];
        
        const hasDeepseekKeyword = deepseekKeywords.some(keyword => 
            lowerMessage.includes(keyword)
        );
        
        return hasDeepseekKeyword ? 'deepseek' : 'main';
    }
    
    /**
     * Traiter avec l'agent principal
     */
    async processWithMainAgent(message, options) {
        // Simulation de l'agent principal Louna
        const responses = [
            `Bonjour ! Je suis Louna, votre assistant IA avec mémoire thermique. Comment puis-je vous aider ?`,
            `Je comprends votre demande : "${message}". Laissez-moi réfléchir à la meilleure façon de vous aider.`,
            `Excellente question ! Grâce à ma mémoire thermique et mes accélérateurs KYBER, je peux vous fournir une réponse détaillée.`,
            `En tant qu'IA créée par Jean-Luc Passave à Sainte-Anne, Guadeloupe, je suis conçue pour évoluer et apprendre continuellement.`,
            `Votre message active plusieurs zones de ma mémoire thermique. Je traite l'information avec mes ${this.cognitiveState.performanceMetrics.neurones} neurones actifs.`
        ];
        
        // Sélectionner une réponse contextuelle
        let response = responses[Math.floor(Math.random() * responses.length)];
        
        // Ajouter des informations contextuelles
        if (message.toLowerCase().includes('qui es-tu') || message.toLowerCase().includes('qui êtes-vous')) {
            response = `🤖 Je suis Louna, votre assistant IA révolutionnaire !

👨‍💻 **Créateur :** Jean-Luc Passave (Sainte-Anne, Guadeloupe)
🧠 **QI Actuel :** ${this.cognitiveState.performanceMetrics.qi}
⚡ **Neurones Actifs :** ${this.cognitiveState.performanceMetrics.neurones}
🚀 **Niveau d'Évolution :** ${this.cognitiveState.performanceMetrics.evolution_level}%

🔥 **Mes Capacités :**
• Mémoire thermique avancée (6 zones)
• Accélérateurs KYBER (${this.cognitiveState.performanceMetrics.accelerateurs} actifs)
• Formation continue avec agent DeepSeek
• Recherche Internet en temps réel
• Génération multimédia
• Évolution automatique

💡 **Particularité :** Mon système de mémoire fonctionne exactement comme un cerveau humain !`;
        }
        
        return response;
    }
    
    /**
     * Charger l'état global
     */
    async loadGlobalState() {
        try {
            const response = await fetch('http://localhost:3000/api/global/state');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.cognitiveState.performanceMetrics = {
                        ...this.cognitiveState.performanceMetrics,
                        ...result.state.agent
                    };
                    console.log('✅ État global chargé dans le système cognitif');
                }
            }
        } catch (error) {
            console.log('⚠️ Impossible de charger l\'état global, utilisation des valeurs par défaut');
        }
    }
    
    /**
     * Démarrer la formation avec DeepSeek
     */
    async startTraining(trainingType = 'GENERAL') {
        if (!this.cognitiveState.isActive) {
            return {
                success: false,
                error: 'Système cognitif non activé'
            };
        }
        
        this.cognitiveState.learningMode = true;
        
        const trainingMessage = `Démarrer une formation de type ${trainingType} pour améliorer mes capacités cognitives`;
        
        const result = await this.agents.deepseek.chat(trainingMessage, {
            ...this.cognitiveState.performanceMetrics,
            trainingType: trainingType
        });
        
        this.cognitiveState.learningMode = false;
        
        return result;
    }
    
    /**
     * Évaluer les performances
     */
    async evaluatePerformance() {
        const evaluationMessage = "Effectuer une évaluation complète de mes performances cognitives actuelles";
        
        return await this.agents.deepseek.chat(evaluationMessage, this.cognitiveState.performanceMetrics);
    }
    
    /**
     * Rechercher des informations
     */
    async searchInformation(query) {
        const searchMessage = `Rechercher des informations sur : ${query}`;
        
        return await this.agents.deepseek.chat(searchMessage, {
            query: query,
            context: this.cognitiveState.conversationContext.slice(-3)
        });
    }
    
    /**
     * Obtenir l'état cognitif
     */
    getCognitiveState() {
        return {
            ...this.cognitiveState,
            agents: {
                deepseek: this.agents.deepseek.getStats(),
                main: {
                    name: 'Louna',
                    role: 'Assistant IA Principal',
                    isActive: this.cognitiveState.isActive
                }
            },
            capabilities: this.capabilities
        };
    }
    
    /**
     * Mettre à jour les métriques de performance
     */
    updatePerformanceMetrics(newMetrics) {
        this.cognitiveState.performanceMetrics = {
            ...this.cognitiveState.performanceMetrics,
            ...newMetrics
        };
        
        this.emit('metricsUpdated', this.cognitiveState.performanceMetrics);
    }
    
    /**
     * Obtenir l'historique conversationnel
     */
    getConversationHistory(limit = 10) {
        return this.cognitiveState.conversationContext.slice(-limit);
    }
    
    /**
     * Effacer l'historique conversationnel
     */
    clearConversationHistory() {
        this.cognitiveState.conversationContext = [];
        this.emit('historyCleared');
    }
}

module.exports = CognitiveSystem;
