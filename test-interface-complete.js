/**
 * Script de test complet pour toutes les interfaces de Louna
 * Vérifie le fonctionnement des boutons, API, et fonctionnalités
 */

class LounaInterfaceTester {
    constructor() {
        this.baseUrl = 'http://localhost:3007';
        this.testResults = {
            interfaces: {},
            apis: {},
            buttons: {},
            media: {},
            overall: { passed: 0, failed: 0, total: 0 }
        };
        this.currentTest = '';
    }

    async runCompleteTest() {
        console.log('🧪 DÉMARRAGE DU TEST COMPLET DE L\'APPLICATION LOUNA');
        console.log('=' .repeat(60));

        try {
            // 1. Test des interfaces principales
            await this.testMainInterfaces();
            
            // 2. Test des APIs
            await this.testAPIs();
            
            // 3. Test des fonctionnalités multimédia
            await this.testMediaFeatures();
            
            // 4. Test des boutons et navigation
            await this.testButtonsAndNavigation();
            
            // 5. Test du cours ultra-avancé
            await this.testAdvancedCourse();
            
            // 6. Générer le rapport final
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Erreur lors du test complet:', error);
        }
    }

    async testMainInterfaces() {
        console.log('\n📱 TEST DES INTERFACES PRINCIPALES');
        console.log('-'.repeat(40));

        const interfaces = [
            { name: 'Accueil', url: '/' },
            { name: 'Chat', url: '/chat.html' },
            { name: 'Mémoire Thermique', url: '/futuristic-interface.html' },
            { name: 'Visualisation 3D', url: '/brain-visualization.html' },
            { name: 'Monitoring Qi', url: '/qi-neuron-monitor.html' },
            { name: 'Accélérateurs Kyber', url: '/kyber-dashboard.html' },
            { name: 'Cours Ultra-Avancé', url: '/advanced-course-monitor.html' },
            { name: 'Agents', url: '/agents.html' },
            { name: 'Formation', url: '/training.html' },
            { name: 'Paramètres', url: '/settings.html' },
            { name: 'Audio/Vidéo', url: '/audio-video-controls.html' }
        ];

        for (const interface of interfaces) {
            await this.testInterface(interface.name, interface.url);
        }
    }

    async testInterface(name, url) {
        this.currentTest = `Interface ${name}`;
        try {
            const response = await fetch(this.baseUrl + url);
            const content = await response.text();
            
            const tests = {
                'Chargement': response.ok,
                'Contenu HTML': content.includes('<html'),
                'CSS inclus': content.includes('.css') || content.includes('<style>'),
                'JavaScript inclus': content.includes('.js') || content.includes('<script>'),
                'Navigation': content.includes('nav') || content.includes('menu'),
                'Responsive': content.includes('viewport') || content.includes('responsive')
            };

            this.testResults.interfaces[name] = tests;
            
            const passed = Object.values(tests).filter(Boolean).length;
            const total = Object.keys(tests).length;
            
            console.log(`  ${name}: ${passed}/${total} tests réussis`);
            this.updateOverallStats(passed, total - passed, total);
            
        } catch (error) {
            console.log(`  ❌ ${name}: Erreur - ${error.message}`);
            this.testResults.interfaces[name] = { error: error.message };
            this.updateOverallStats(0, 1, 1);
        }
    }

    async testAPIs() {
        console.log('\n🔌 TEST DES APIs');
        console.log('-'.repeat(40));

        const apis = [
            { name: 'Mémoire Thermique Stats', url: '/api/thermal/memory/stats' },
            { name: 'Accélérateurs Stats', url: '/api/thermal/accelerators/stats' },
            { name: 'Chat', url: '/api/chat/message', method: 'POST' },
            { name: 'Agents Liste', url: '/api/agents/list' },
            { name: 'Formation État', url: '/api/training/state' },
            { name: 'Performance', url: '/api/performance/stats' },
            { name: 'Cerveau Activité', url: '/api/brain/activity' }
        ];

        for (const api of apis) {
            await this.testAPI(api.name, api.url, api.method || 'GET');
        }
    }

    async testAPI(name, url, method = 'GET') {
        this.currentTest = `API ${name}`;
        try {
            const options = { method };
            if (method === 'POST') {
                options.headers = { 'Content-Type': 'application/json' };
                options.body = JSON.stringify({ test: true });
            }

            const response = await fetch(this.baseUrl + url, options);
            const isJson = response.headers.get('content-type')?.includes('application/json');
            
            let data = null;
            if (isJson) {
                data = await response.json();
            }

            const tests = {
                'Réponse': response.ok || response.status < 500,
                'Format JSON': isJson,
                'Structure valide': data && typeof data === 'object',
                'Pas d\'erreur serveur': response.status < 500
            };

            this.testResults.apis[name] = { ...tests, status: response.status, data };
            
            const passed = Object.values(tests).filter(Boolean).length;
            const total = Object.keys(tests).length;
            
            console.log(`  ${name}: ${passed}/${total} tests réussis (Status: ${response.status})`);
            this.updateOverallStats(passed, total - passed, total);
            
        } catch (error) {
            console.log(`  ❌ ${name}: Erreur - ${error.message}`);
            this.testResults.apis[name] = { error: error.message };
            this.updateOverallStats(0, 1, 1);
        }
    }

    async testMediaFeatures() {
        console.log('\n🎵 TEST DES FONCTIONNALITÉS MULTIMÉDIA');
        console.log('-'.repeat(40));

        const mediaTests = {
            'getUserMedia Support': navigator.mediaDevices && navigator.mediaDevices.getUserMedia,
            'Speech Synthesis': 'speechSynthesis' in window,
            'Speech Recognition': 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
            'MediaRecorder Support': 'MediaRecorder' in window,
            'WebRTC Support': 'RTCPeerConnection' in window,
            'Audio Context': 'AudioContext' in window || 'webkitAudioContext' in window
        };

        this.testResults.media = mediaTests;
        
        const passed = Object.values(mediaTests).filter(Boolean).length;
        const total = Object.keys(mediaTests).length;
        
        Object.entries(mediaTests).forEach(([test, result]) => {
            console.log(`  ${test}: ${result ? '✅' : '❌'}`);
        });
        
        this.updateOverallStats(passed, total - passed, total);
    }

    async testButtonsAndNavigation() {
        console.log('\n🔘 TEST DES BOUTONS ET NAVIGATION');
        console.log('-'.repeat(40));

        // Test de la page de chat pour vérifier les boutons
        try {
            const response = await fetch(this.baseUrl + '/chat.html');
            const content = await response.text();
            
            const buttonTests = {
                'Bouton Envoi': content.includes('chat-send-btn') || content.includes('send'),
                'Bouton Micro': content.includes('microphone') || content.includes('mic'),
                'Bouton Caméra': content.includes('camera') || content.includes('video'),
                'Navigation Menu': content.includes('nav') || content.includes('menu'),
                'Boutons Options': content.includes('chat-option-btn') || content.includes('option'),
                'Contrôles Audio': content.includes('audio') || content.includes('volume')
            };

            this.testResults.buttons = buttonTests;
            
            const passed = Object.values(buttonTests).filter(Boolean).length;
            const total = Object.keys(buttonTests).length;
            
            Object.entries(buttonTests).forEach(([test, result]) => {
                console.log(`  ${test}: ${result ? '✅' : '❌'}`);
            });
            
            this.updateOverallStats(passed, total - passed, total);
            
        } catch (error) {
            console.log(`  ❌ Erreur lors du test des boutons: ${error.message}`);
            this.updateOverallStats(0, 1, 1);
        }
    }

    async testAdvancedCourse() {
        console.log('\n🎓 TEST DU COURS ULTRA-AVANCÉ');
        console.log('-'.repeat(40));

        try {
            const response = await fetch(this.baseUrl + '/advanced-course-monitor.html');
            const content = await response.text();
            
            const courseTests = {
                'Interface Chargée': response.ok,
                'Modules Présents': content.includes('module') && content.includes('course'),
                'Statistiques': content.includes('stats') || content.includes('progress'),
                'Animation': content.includes('animation') || content.includes('keyframes'),
                'Interactivité': content.includes('addEventListener') || content.includes('onclick'),
                'Données Temps Réel': content.includes('setInterval') || content.includes('update')
            };

            const passed = Object.values(courseTests).filter(Boolean).length;
            const total = Object.keys(courseTests).length;
            
            Object.entries(courseTests).forEach(([test, result]) => {
                console.log(`  ${test}: ${result ? '✅' : '❌'}`);
            });
            
            this.updateOverallStats(passed, total - passed, total);
            
        } catch (error) {
            console.log(`  ❌ Erreur lors du test du cours: ${error.message}`);
            this.updateOverallStats(0, 1, 1);
        }
    }

    updateOverallStats(passed, failed, total) {
        this.testResults.overall.passed += passed;
        this.testResults.overall.failed += failed;
        this.testResults.overall.total += total;
    }

    generateReport() {
        console.log('\n📊 RAPPORT FINAL DU TEST');
        console.log('='.repeat(60));
        
        const { passed, failed, total } = this.testResults.overall;
        const successRate = ((passed / total) * 100).toFixed(1);
        
        console.log(`\n🎯 RÉSULTATS GLOBAUX:`);
        console.log(`   Tests réussis: ${passed}/${total} (${successRate}%)`);
        console.log(`   Tests échoués: ${failed}`);
        
        if (successRate >= 80) {
            console.log(`\n✅ APPLICATION EN BON ÉTAT (${successRate}% de réussite)`);
        } else if (successRate >= 60) {
            console.log(`\n⚠️ APPLICATION NÉCESSITE DES AMÉLIORATIONS (${successRate}% de réussite)`);
        } else {
            console.log(`\n❌ APPLICATION NÉCESSITE DES CORRECTIONS IMPORTANTES (${successRate}% de réussite)`);
        }

        console.log('\n📋 DÉTAILS PAR CATÉGORIE:');
        
        // Interfaces
        const interfaceResults = Object.values(this.testResults.interfaces);
        const interfaceSuccess = interfaceResults.filter(r => !r.error).length;
        console.log(`   Interfaces: ${interfaceSuccess}/${interfaceResults.length} fonctionnelles`);
        
        // APIs
        const apiResults = Object.values(this.testResults.apis);
        const apiSuccess = apiResults.filter(r => !r.error).length;
        console.log(`   APIs: ${apiSuccess}/${apiResults.length} fonctionnelles`);
        
        // Média
        const mediaSuccess = Object.values(this.testResults.media).filter(Boolean).length;
        const mediaTotal = Object.keys(this.testResults.media).length;
        console.log(`   Fonctionnalités Média: ${mediaSuccess}/${mediaTotal} supportées`);
        
        // Boutons
        const buttonSuccess = Object.values(this.testResults.buttons).filter(Boolean).length;
        const buttonTotal = Object.keys(this.testResults.buttons).length;
        console.log(`   Boutons/Navigation: ${buttonSuccess}/${buttonTotal} détectés`);

        console.log('\n🔧 RECOMMANDATIONS:');
        this.generateRecommendations();
    }

    generateRecommendations() {
        const { interfaces, apis, media, buttons } = this.testResults;
        
        // Vérifier les interfaces problématiques
        Object.entries(interfaces).forEach(([name, result]) => {
            if (result.error) {
                console.log(`   ❌ Corriger l'interface ${name}: ${result.error}`);
            }
        });
        
        // Vérifier les APIs problématiques
        Object.entries(apis).forEach(([name, result]) => {
            if (result.error) {
                console.log(`   ❌ Corriger l'API ${name}: ${result.error}`);
            }
        });
        
        // Vérifier les fonctionnalités média manquantes
        Object.entries(media).forEach(([feature, supported]) => {
            if (!supported) {
                console.log(`   ⚠️ Fonctionnalité manquante: ${feature}`);
            }
        });
        
        console.log('\n✨ AMÉLIORATIONS SUGGÉRÉES:');
        console.log('   1. Vérifier les permissions micro/caméra');
        console.log('   2. Tester les boutons de navigation');
        console.log('   3. Valider les connexions API');
        console.log('   4. Optimiser les performances');
        console.log('   5. Améliorer l\'expérience utilisateur');
    }
}

// Lancer le test si exécuté directement
if (typeof window !== 'undefined') {
    // Exécution dans le navigateur
    window.LounaInterfaceTester = LounaInterfaceTester;
    console.log('🧪 Testeur d\'interface Louna chargé. Utilisez: new LounaInterfaceTester().runCompleteTest()');
} else {
    // Exécution en Node.js
    const tester = new LounaInterfaceTester();
    tester.runCompleteTest();
}
