<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Qi & Neurones - Louna</title>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Lou<PERSON></span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item active">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Monitoring Qi & Neurones</h1>
                <p class="interface-subtitle">Évolution en temps réel de l'énergie vitale et de l'activité neuronale</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator active" id="qi-status-indicator"></div>
                <span class="status-text" id="qi-status-text">Monitoring actif</span>

                <a href="/brain-visualization.html" class="action-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
                <a href="/futuristic-interface.html" class="action-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
            </div>
        </div>

        <!-- Grille de métriques -->
        <div class="metrics-grid">
            <!-- Carte QI -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-yin-yang"></i> Système QI</h3>
                    <div class="metric-status active" id="qi-system-status">Actif</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">QI Actuel</span>
                        <span class="metric-value" id="current-qi">1000</span>
                        <span class="metric-unit">points</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Niveau Cognitif</span>
                            <span class="value" id="cognitive-level">1</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Points XP</span>
                            <span class="value" id="experience-points">0</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Bonus Apprentissage</span>
                            <span class="value" id="learning-bonus">0%</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="qi-progress" style="width: 50%"></div>
                        </div>
                        <span class="progress-text">Progression vers niveau suivant</span>
                    </div>
                </div>
            </div>

            <!-- Carte Neurones -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-brain"></i> Réseau Neuronal</h3>
                    <div class="metric-status active" id="neuron-system-status">Actif</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">Neurones Totaux</span>
                        <span class="metric-value" id="total-neurons">71</span>
                        <span class="metric-unit">neurones</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Neurones Actifs</span>
                            <span class="value" id="active-neurons">45</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Efficacité</span>
                            <span class="value" id="neuron-efficiency">85%</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Santé</span>
                            <span class="value" id="neuron-health">92%</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="neuron-progress" style="width: 85%"></div>
                        </div>
                        <span class="progress-text">Activité neuronale globale</span>
                    </div>
                </div>
            </div>

            <!-- Carte Réseaux -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-network-wired"></i> Réseaux Spécialisés</h3>
                    <div class="metric-status active">6 Réseaux</div>
                </div>
                <div class="metric-content">
                    <div class="network-list">
                        <div class="network-item">
                            <span class="network-name">Sensoriel</span>
                            <span class="network-count" id="sensory-count">12</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Mémoire de Travail</span>
                            <span class="network-count" id="working-count">8</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Long Terme</span>
                            <span class="network-count" id="longterm-count">15</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Émotionnel</span>
                            <span class="network-count" id="emotional-count">10</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Exécutif</span>
                            <span class="network-count" id="executive-count">9</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Créatif</span>
                            <span class="network-count" id="creative-count">7</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte Émotions -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-heart"></i> État Émotionnel</h3>
                    <div class="metric-status active" id="emotional-status">Curieux</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">Humeur</span>
                        <span class="metric-value" id="current-mood">Curieux</span>
                        <span class="metric-unit" id="mood-intensity">100%</span>
                    </div>
                    <div class="emotion-grid">
                        <div class="emotion-item">
                            <span class="emotion-name">Bonheur</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="happiness-bar" style="width: 68%"></div>
                            </div>
                            <span class="emotion-value" id="happiness-value">68%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Curiosité</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="curiosity-bar" style="width: 100%"></div>
                            </div>
                            <span class="emotion-value" id="curiosity-value">100%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Confiance</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="confidence-bar" style="width: 60%"></div>
                            </div>
                            <span class="emotion-value" id="confidence-value">60%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Énergie</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="energy-bar" style="width: 75%"></div>
                            </div>
                            <span class="emotion-value" id="energy-value">75%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphique temps réel -->
        <div class="chart-section">
            <div class="chart-header">
                <h3><i class="fas fa-chart-line"></i> Évolution Temps Réel</h3>
                <div class="chart-controls">
                    <button class="control-btn" id="pause-chart">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="control-btn" id="reset-chart">
                        <i class="fas fa-redo"></i> Reset
                    </button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="monitoring-chart" width="800" height="300"></canvas>
            </div>
            <div class="chart-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>QI</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>Neurones Actifs</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>Efficacité</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96ceb4;"></div>
                    <span>Bonheur</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour le monitoring
        let isMonitoring = true;
        let updateInterval = null;
        let chartData = [];
        let maxDataPoints = 50;
        let chart = null;

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔮 Initialisation du monitoring Qi & Neurones...');

            // Initialiser le graphique
            initChart();

            // Démarrer le monitoring automatique
            startMonitoring();

            // Configurer les boutons
            setupEventListeners();

            console.log('✅ Monitoring Qi & Neurones initialisé');
        });

        // Configuration des événements
        function setupEventListeners() {
            // Bouton pause/reprendre
            const pauseBtn = document.getElementById('pause-chart');
            if (pauseBtn) {
                pauseBtn.addEventListener('click', toggleMonitoring);
            }

            // Bouton reset
            const resetBtn = document.getElementById('reset-chart');
            if (resetBtn) {
                resetBtn.addEventListener('click', resetChart);
            }

            // Actualisation automatique
            document.getElementById('refresh-btn')?.addEventListener('click', function() {
                loadBrainData();
            });
        }

        // Démarrer le monitoring
        function startMonitoring() {
            if (updateInterval) return;

            isMonitoring = true;
            updateInterval = setInterval(loadBrainData, 3000); // Mise à jour toutes les 3 secondes
            loadBrainData(); // Charger immédiatement

            // Mettre à jour le bouton
            const pauseBtn = document.getElementById('pause-chart');
            if (pauseBtn) {
                pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            }
        }

        // Arrêter/reprendre le monitoring
        function toggleMonitoring() {
            const pauseBtn = document.getElementById('pause-chart');

            if (isMonitoring) {
                // Pause
                clearInterval(updateInterval);
                updateInterval = null;
                isMonitoring = false;
                if (pauseBtn) {
                    pauseBtn.innerHTML = '<i class="fas fa-play"></i> Reprendre';
                }
            } else {
                // Reprendre
                startMonitoring();
            }
        }

        // Charger les données du cerveau artificiel
        async function loadBrainData() {
            try {
                const response = await fetch('/api/brain/qi-neuron-stats');
                const data = await response.json();

                if (data) {
                    updateUI(data);
                    addToChart(data);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
                // Utiliser des données simulées en cas d'erreur
                const simulatedData = generateSimulatedData();
                updateUI(simulatedData);
                addToChart(simulatedData);
            }
        }

        // Mettre à jour l'interface utilisateur
        function updateUI(data) {
            // Système QI
            if (data.qi) {
                document.getElementById('current-qi').textContent = data.qi.current || 1000;
                document.getElementById('cognitive-level').textContent = data.qi.level || 1;
                document.getElementById('experience-points').textContent = data.qi.experiencePoints || 0;
                document.getElementById('learning-bonus').textContent = (data.qi.learningBonus || 0) + '%';

                // Progression QI
                const qiProgress = ((data.qi.current || 1000) % 200) / 200 * 100;
                document.getElementById('qi-progress').style.width = qiProgress + '%';
            }

            // Système Neuronal
            if (data.neurons) {
                document.getElementById('total-neurons').textContent = data.neurons.total || 71;
                document.getElementById('active-neurons').textContent = data.neurons.active || 45;
                document.getElementById('neuron-efficiency').textContent = (data.neurons.efficiency || 85) + '%';
                document.getElementById('neuron-health').textContent = (data.neurons.health || 92) + '%';

                // Progression neurones
                document.getElementById('neuron-progress').style.width = (data.neurons.efficiency || 85) + '%';
            }

            // Réseaux spécialisés
            if (data.networks) {
                document.getElementById('sensory-count').textContent = data.networks.sensory || 12;
                document.getElementById('working-count').textContent = data.networks.working || 8;
                document.getElementById('longterm-count').textContent = data.networks.longTerm || 15;
                document.getElementById('emotional-count').textContent = data.networks.emotional || 10;
                document.getElementById('executive-count').textContent = data.networks.executive || 9;
                document.getElementById('creative-count').textContent = data.networks.creative || 7;
            }

            // État émotionnel
            if (data.emotional) {
                document.getElementById('current-mood').textContent = data.emotional.mood || 'Curieux';
                document.getElementById('mood-intensity').textContent = (data.emotional.moodIntensity || 100) + '%';
                document.getElementById('emotional-status').textContent = data.emotional.mood || 'Curieux';

                // Barres d'émotions
                updateEmotionBar('happiness', data.emotional.happiness || 68);
                updateEmotionBar('curiosity', data.emotional.curiosity || 100);
                updateEmotionBar('confidence', data.emotional.confidence || 60);
                updateEmotionBar('energy', data.emotional.energy || 75);
            }
        }

        // Mettre à jour une barre d'émotion
        function updateEmotionBar(emotion, value) {
            const bar = document.getElementById(emotion + '-bar');
            const valueSpan = document.getElementById(emotion + '-value');

            if (bar) bar.style.width = value + '%';
            if (valueSpan) valueSpan.textContent = value + '%';
        }

        // Initialiser le graphique
        function initChart() {
            const canvas = document.getElementById('monitoring-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Ajuster la taille du canvas
            canvas.width = canvas.offsetWidth;
            canvas.height = 300;

            chart = {
                canvas: canvas,
                ctx: ctx,
                width: canvas.width,
                height: canvas.height
            };

            drawChart();
        }

        // Ajouter des données au graphique
        function addToChart(data) {
            const timestamp = new Date();

            chartData.push({
                time: timestamp,
                qi: data.qi?.current || 1000,
                neurons: data.neurons?.active || 45,
                efficiency: data.neurons?.efficiency || 85,
                happiness: data.emotional?.happiness || 68
            });

            // Limiter le nombre de points
            if (chartData.length > maxDataPoints) {
                chartData.shift();
            }

            drawChart();
        }

        // Dessiner le graphique
        function drawChart() {
            if (!chart) return;

            const { ctx, width, height } = chart;

            // Effacer le canvas
            ctx.clearRect(0, 0, width, height);

            if (chartData.length === 0) return;

            // Dessiner la grille
            drawGrid(ctx, width, height);

            // Dessiner les courbes
            drawLine(ctx, chartData.map(d => d.qi), '#ff6b6b', width, height, 900, 1100);
            drawLine(ctx, chartData.map(d => d.neurons), '#4ecdc4', width, height, 0, 100);
            drawLine(ctx, chartData.map(d => d.efficiency), '#45b7d1', width, height, 0, 100);
            drawLine(ctx, chartData.map(d => d.happiness), '#96ceb4', width, height, 0, 100);
        }

        // Dessiner la grille
        function drawGrid(ctx, width, height) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Lignes horizontales
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Lignes verticales
            for (let i = 0; i <= 10; i++) {
                const x = (width / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }
        }

        // Dessiner une ligne
        function drawLine(ctx, data, color, width, height, minVal = 0, maxVal = 100) {
            if (data.length < 2) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < data.length; i++) {
                const x = (width / (maxDataPoints - 1)) * i;
                const normalizedValue = (data[i] - minVal) / (maxVal - minVal);
                const y = height - (normalizedValue * height);

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        // Réinitialiser le graphique
        function resetChart() {
            chartData = [];
            drawChart();
        }

        // Générer des données simulées
        function generateSimulatedData() {
            return {
                qi: {
                    current: 1000 + Math.random() * 100,
                    level: Math.floor((1000 + Math.random() * 100) / 200) + 1,
                    experiencePoints: Math.floor(Math.random() * 1000),
                    learningBonus: Math.floor(Math.random() * 20)
                },
                neurons: {
                    total: 71 + Math.floor(Math.random() * 5),
                    active: 45 + Math.floor(Math.random() * 10),
                    efficiency: 80 + Math.random() * 15,
                    health: 85 + Math.random() * 15
                },
                networks: {
                    sensory: 12,
                    working: 8,
                    longTerm: 15,
                    emotional: 10,
                    executive: 9,
                    creative: 7
                },
                emotional: {
                    mood: 'Curieux',
                    moodIntensity: 90 + Math.random() * 10,
                    happiness: 60 + Math.random() * 20,
                    curiosity: 90 + Math.random() * 10,
                    confidence: 50 + Math.random() * 30,
                    energy: 70 + Math.random() * 20
                }
            };
        }

        // Redimensionner le canvas
        window.addEventListener('resize', function() {
            setTimeout(initChart, 100);
        });
    </script>

    <script src="js/native-app.js"></script></script>
</body>
</html>
