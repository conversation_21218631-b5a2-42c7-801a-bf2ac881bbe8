<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Agent IA Révolutionnaire</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .presentation-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .presentation-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.2); }
            to { text-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 0 30px rgba(255,255,255,0.4); }
        }

        .hero-subtitle {
            font-size: 1.8rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 60px 0;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            padding: 0 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ff6b6b;
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .capabilities-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 60px;
            color: #333;
            font-weight: 700;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .capability-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .capability-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .capability-icon {
            font-size: 3.5rem;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .capability-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }

        .capability-description {
            color: #666;
            line-height: 1.7;
            margin-bottom: 25px;
        }

        .capability-features {
            list-style: none;
            padding: 0;
        }

        .capability-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .capability-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .demo-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .demo-buttons {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .demo-button {
            padding: 20px 40px;
            font-size: 1.2rem;
            border-radius: 50px;
            border: 2px solid white;
            background: transparent;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
        }

        .demo-button:hover {
            background: white;
            color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .tech-specs {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .spec-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .spec-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }

        .spec-list {
            list-style: none;
            padding: 0;
        }

        .spec-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }

        .spec-list li:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 500;
            color: #555;
        }

        .spec-value {
            color: #667eea;
            font-weight: 600;
        }
    </style>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/presentation.html" class="nav-item active">
                <i class="fas fa-presentation"></i>
                <span>Présentation</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
        </div>
    </nav>

    <!-- Section Hero -->
    <section class="presentation-hero">
        <div class="hero-content">
            <h1 class="hero-title">LOUNA</h1>
            <p class="hero-subtitle">Agent IA Révolutionnaire de Nouvelle Génération</p>
            <p class="hero-description">
                Découvrez l'agent intelligent le plus avancé au monde, capable de générer du contenu multimédia
                en quantité illimitée grâce à sa mémoire thermique révolutionnaire et ses accélérateurs Kyber.
            </p>

            <!-- Statistiques en temps réel -->
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number" id="total-generations">0</span>
                    <span class="stat-label">Générations Totales</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="video-count">0</span>
                    <span class="stat-label">Vidéos 4K</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="image-count">0</span>
                    <span class="stat-label">Images HD</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="music-count">0</span>
                    <span class="stat-label">Compositions Musicales</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="model3d-count">0</span>
                    <span class="stat-label">Modèles 3D</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Capacités -->
    <section class="capabilities-section">
        <div class="container">
            <h2 class="section-title">Capacités Révolutionnaires</h2>

            <div class="capabilities-grid">
                <!-- Génération Multimédia -->
                <div class="capability-card">
                    <i class="fas fa-magic capability-icon"></i>
                    <h3 class="capability-title">Studio de Génération Illimitée</h3>
                    <p class="capability-description">
                        Générez du contenu multimédia de qualité professionnelle en quantité illimitée.
                        Notre technologie révolutionnaire permet la création instantanée de contenus uniques.
                    </p>
                    <ul class="capability-features">
                        <li>Vidéos 4K jusqu'à 5 minutes</li>
                        <li>Images jusqu'à 4096x4096 pixels</li>
                        <li>Compositions musicales complètes</li>
                        <li>Modèles 3D ultra-détaillés</li>
                        <li>Génération simultanée multiple</li>
                    </ul>
                </div>

                <!-- Mémoire Thermique -->
                <div class="capability-card">
                    <i class="fas fa-fire capability-icon"></i>
                    <h3 class="capability-title">Mémoire Thermique Avancée</h3>
                    <p class="capability-description">
                        Système de mémoire révolutionnaire qui simule le fonctionnement du cerveau humain
                        avec des zones de mémoire dynamiques et une gestion thermique intelligente.
                    </p>
                    <ul class="capability-features">
                        <li>5 zones de mémoire distinctes</li>
                        <li>Gestion thermique adaptative</li>
                        <li>Consolidation automatique</li>
                        <li>Évolution en temps réel</li>
                        <li>Optimisation continue</li>
                    </ul>
                </div>

                <!-- Accélérateurs Kyber -->
                <div class="capability-card">
                    <i class="fas fa-bolt capability-icon"></i>
                    <h3 class="capability-title">Accélérateurs Kyber</h3>
                    <p class="capability-description">
                        Technologie d'accélération propriétaire qui optimise les performances de traitement
                        et améliore l'efficacité de la mémoire thermique.
                    </p>
                    <ul class="capability-features">
                        <li>Boost de performance jusqu'à 300%</li>
                        <li>Optimisation automatique</li>
                        <li>Stabilité garantie</li>
                        <li>Monitoring en temps réel</li>
                        <li>Configuration personnalisable</li>
                    </ul>
                </div>

                <!-- Monitoring Qi & Neurones -->
                <div class="capability-card">
                    <i class="fas fa-yin-yang capability-icon"></i>
                    <h3 class="capability-title">Monitoring Qi & Neurones</h3>
                    <p class="capability-description">
                        Surveillance avancée de l'énergie vitale du système et de l'activité neuronale
                        pour une performance optimale et une harmonie parfaite.
                    </p>
                    <ul class="capability-features">
                        <li>Mesure du Qi en temps réel</li>
                        <li>Analyse des connexions neuronales</li>
                        <li>Détection d'anomalies</li>
                        <li>Optimisation énergétique</li>
                        <li>Visualisation 3D interactive</li>
                    </ul>
                </div>

                <!-- LTX Video -->
                <div class="capability-card">
                    <i class="fas fa-video capability-icon"></i>
                    <h3 class="capability-title">LTX Video Engine</h3>
                    <p class="capability-description">
                        Moteur vidéo de nouvelle génération pour la création et l'édition de contenus
                        vidéo avec des effets avancés et une qualité cinématographique.
                    </p>
                    <ul class="capability-features">
                        <li>Rendu 4K en temps réel</li>
                        <li>Effets visuels avancés</li>
                        <li>Compression intelligente</li>
                        <li>Formats multiples</li>
                        <li>Streaming optimisé</li>
                    </ul>
                </div>

                <!-- Éditeur de Code -->
                <div class="capability-card">
                    <i class="fas fa-code capability-icon"></i>
                    <h3 class="capability-title">Éditeur de Code Intelligent</h3>
                    <p class="capability-description">
                        Environnement de développement intégré avec assistance IA pour la programmation
                        et l'automatisation des tâches complexes.
                    </p>
                    <ul class="capability-features">
                        <li>Autocomplétion intelligente</li>
                        <li>Détection d'erreurs en temps réel</li>
                        <li>Refactoring automatique</li>
                        <li>Support multi-langages</li>
                        <li>Intégration Git avancée</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Démonstration -->
    <section class="demo-section">
        <div class="container">
            <h2 class="section-title">Découvrez Louna en Action</h2>
            <p class="hero-description">
                Explorez toutes les interfaces et fonctionnalités de Louna pour découvrir
                la puissance de l'intelligence artificielle de nouvelle génération.
            </p>

            <div class="demo-buttons">
                <a href="/generation-studio.html" class="demo-button">
                    <i class="fas fa-magic"></i>
                    Studio de Génération
                </a>
                <a href="/futuristic-interface.html" class="demo-button">
                    <i class="fas fa-fire"></i>
                    Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="demo-button">
                    <i class="fas fa-brain"></i>
                    Visualisation 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="demo-button">
                    <i class="fas fa-yin-yang"></i>
                    Monitoring Qi
                </a>
                <a href="/kyber-dashboard.html" class="demo-button">
                    <i class="fas fa-bolt"></i>
                    Accélérateurs Kyber
                </a>
                <a href="/chat" class="demo-button">
                    <i class="fas fa-comments"></i>
                    Chat Intelligent
                </a>
            </div>
        </div>
    </section>

    <!-- Section Spécifications Techniques -->
    <section class="tech-specs">
        <div class="container">
            <h2 class="section-title">Spécifications Techniques</h2>

            <div class="specs-grid">
                <div class="spec-card">
                    <h3 class="spec-title">Génération Multimédia</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Vidéos</span><span class="spec-value">4K, H.264, 60 FPS</span></li>
                        <li><span class="spec-label">Images</span><span class="spec-value">4096x4096, PNG, 300 DPI</span></li>
                        <li><span class="spec-label">Audio</span><span class="spec-value">48kHz, 24-bit, Stéréo</span></li>
                        <li><span class="spec-label">3D</span><span class="spec-value">FBX, OBJ, GLTF</span></li>
                        <li><span class="spec-label">Génération</span><span class="spec-value">Illimitée</span></li>
                    </ul>
                </div>

                <div class="spec-card">
                    <h3 class="spec-title">Mémoire Thermique</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Zones</span><span class="spec-value">5 zones distinctes</span></li>
                        <li><span class="spec-label">Capacité</span><span class="spec-value">Illimitée</span></li>
                        <li><span class="spec-label">Température</span><span class="spec-value">0°C à 100°C</span></li>
                        <li><span class="spec-label">Consolidation</span><span class="spec-value">Automatique</span></li>
                        <li><span class="spec-label">Optimisation</span><span class="spec-value">Temps réel</span></li>
                    </ul>
                </div>

                <div class="spec-card">
                    <h3 class="spec-title">Performance</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Accélération</span><span class="spec-value">Jusqu'à 300%</span></li>
                        <li><span class="spec-label">Latence</span><span class="spec-value">&lt; 100ms</span></li>
                        <li><span class="spec-label">Throughput</span><span class="spec-value">1000+ req/s</span></li>
                        <li><span class="spec-label">Uptime</span><span class="spec-value">99.9%</span></li>
                        <li><span class="spec-label">Monitoring</span><span class="spec-value">24/7</span></li>
                    </ul>
                </div>

                <div class="spec-card">
                    <h3 class="spec-title">Compatibilité</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Plateforme</span><span class="spec-value">macOS M4</span></li>
                        <li><span class="spec-label">Architecture</span><span class="spec-value">ARM64</span></li>
                        <li><span class="spec-label">Mémoire</span><span class="spec-value">8GB+ RAM</span></li>
                        <li><span class="spec-label">Stockage</span><span class="spec-value">50GB+ SSD</span></li>
                        <li><span class="spec-label">Réseau</span><span class="spec-value">HTTP/2, WebSocket</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent IA révolutionnaire avec génération multimédia illimitée
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>

    <script>
        // Chargement des statistiques en temps réel
        async function loadStats() {
            try {
                const response = await fetch('/api/generation/stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.stats;

                    // Animation des compteurs
                    animateCounter('total-generations', stats.totalGenerations);
                    animateCounter('video-count', stats.videoGenerations);
                    animateCounter('image-count', stats.imageGenerations);
                    animateCounter('music-count', stats.musicGenerations);
                    animateCounter('model3d-count', stats.model3dGenerations);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des statistiques:', error);
            }
        }

        // Animation des compteurs
        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000; // 2 secondes
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Fonction d'easing pour une animation fluide
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

                element.textContent = currentValue;

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = targetValue;
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // Chargement initial et mise à jour périodique
        document.addEventListener('DOMContentLoaded', () => {
            loadStats();

            // Mise à jour toutes les 30 secondes
            setInterval(loadStats, 30000);

            // Notification de bienvenue
            setTimeout(() => {
                if (typeof notifications !== 'undefined') {
                    notifications.success(
                        "Découvrez toutes les capacités révolutionnaires de Louna !",
                        "Bienvenue dans la présentation",
                        { duration: 5000 }
                    );
                }
            }, 1000);
        });

        // Effet de parallaxe pour le hero
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.presentation-hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    </script>
    <script src="js/native-app.js"></script>
</body>
</html>