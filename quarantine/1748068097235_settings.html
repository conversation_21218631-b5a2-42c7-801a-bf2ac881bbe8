<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Paramètres</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .settings-section {
            margin-bottom: 30px;
        }

        .settings-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .settings-group {
            margin-bottom: 20px;
        }

        .settings-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
        }

        .settings-label {
            font-size: 14px;
        }

        .settings-description {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .settings-control {
            display: flex;
            align-items: center;
        }

        .settings-input {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px;
            color: white;
            width: 100px;
        }

        .settings-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px;
            color: white;
            width: 150px;
        }

        .settings-button {
            background-color: var(--accent);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .settings-button:hover {
            background-color: var(--primary-light);
            box-shadow: var(--glow-effect);
        }

        .settings-button.danger {
            background-color: var(--danger);
        }

        .settings-button.danger:hover {
            background-color: #ff4081;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--accent);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams.html" class="nav-item">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings.html" class="nav-item active">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Paramètres</h1>
                <p class="interface-subtitle">Configuration de l'agent Louna</p>
            </div>

            <div class="status-container">
                <button class="action-button" id="save-settings-btn">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
                <button class="action-button" id="reset-settings-btn">
                    <i class="fas fa-undo"></i> Réinitialiser
                </button>
            </div>
        </div>

        <!-- Grille principale -->
        <div class="grid-container">
            <!-- Paramètres de la mémoire thermique -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-fire card-icon"></i>
                        <h2 class="card-title">Mémoire Thermique</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Capacités des zones</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire Instantanée</div>
                                    <div class="settings-description">Capacité de la zone la plus chaude</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="instant-capacity" min="5" max="50" value="20">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire à Court Terme</div>
                                    <div class="settings-description">Capacité de la zone à court terme</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="short-term-capacity" min="10" max="100" value="50">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire à Long Terme</div>
                                    <div class="settings-description">Capacité de la zone à long terme</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="long-term-capacity" min="100" max="2000" value="1000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Cycle de mémoire</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle de cycle</div>
                                    <div class="settings-description">Intervalle entre les cycles de mémoire (secondes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="cycle-interval" min="60" max="3600" value="300">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Taux de décroissance</div>
                                    <div class="settings-description">Vitesse à laquelle la température des entrées diminue</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="decay-rate" min="0.8" max="0.99" step="0.01" value="0.95">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paramètres des accélérateurs Kyber -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bolt card-icon"></i>
                        <h2 class="card-title">Accélérateurs Kyber</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Configuration générale</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle de mise à jour</div>
                                    <div class="settings-description">Intervalle entre les mises à jour des accélérateurs (secondes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="update-interval" min="10" max="300" value="60">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Facteur de boost maximum</div>
                                    <div class="settings-description">Limite supérieure du facteur de boost</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="max-boost" min="2" max="10" step="0.1" value="5.0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Optimisation automatique</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Optimisation automatique</div>
                                    <div class="settings-description">Optimiser automatiquement les accélérateurs</div>
                                </div>
                                <div class="settings-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="auto-optimize">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle d'optimisation</div>
                                    <div class="settings-description">Intervalle entre les optimisations automatiques (minutes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="optimize-interval" min="5" max="120" value="30">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paramètres avancés et actions -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-tools card-icon"></i>
                        <h2 class="card-title">Paramètres avancés</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Sauvegarde et restauration</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Exporter les données</div>
                                    <div class="settings-description">Exporter toutes les données de la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button" id="export-btn">
                                        <i class="fas fa-download"></i> Exporter
                                    </button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Importer des données</div>
                                    <div class="settings-description">Importer des données dans la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button" id="import-btn">
                                        <i class="fas fa-upload"></i> Importer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Actions</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Réinitialiser la mémoire</div>
                                    <div class="settings-description">Effacer toutes les données de la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button danger" id="reset-memory-btn">
                                        <i class="fas fa-trash"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Réinitialiser les accélérateurs</div>
                                    <div class="settings-description">Réinitialiser tous les accélérateurs Kyber</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button danger" id="reset-accelerators-btn">
                                        <i class="fas fa-trash"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent intelligent avec mémoire thermique et accélérateurs Kyber
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Charger les paramètres actuels
            loadSettings();

            // Ajouter les écouteurs d'événements
            document.getElementById('save-settings-btn').addEventListener('click', saveSettings);
            document.getElementById('reset-settings-btn').addEventListener('click', resetSettings);
            document.getElementById('export-btn').addEventListener('click', exportData);
            document.getElementById('import-btn').addEventListener('click', importData);
            document.getElementById('reset-memory-btn').addEventListener('click', resetMemory);
            document.getElementById('reset-accelerators-btn').addEventListener('click', resetAccelerators);
        });

        // Fonctions à implémenter
        function loadSettings() {
            // À implémenter
        }

        function saveSettings() {
            // À implémenter
        }

        function resetSettings() {
            // À implémenter
        }

        function exportData() {
            // À implémenter
        }

        function importData() {
            // À implémenter
        }

        function resetMemory() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser la mémoire thermique ? Cette action est irréversible.')) {
                fetch('/api/thermal/memory/reset', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Mémoire thermique réinitialisée avec succès');
                    } else {
                        alert('Erreur lors de la réinitialisation de la mémoire: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la réinitialisation de la mémoire');
                });
            }
        }

        function resetAccelerators() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser les accélérateurs Kyber ? Cette action est irréversible.')) {
                fetch('/api/thermal/accelerators/reset', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Accélérateurs Kyber réinitialisés avec succès');
                    } else {
                        alert('Erreur lors de la réinitialisation des accélérateurs: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la réinitialisation des accélérateurs');
                });
            }
        }
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
