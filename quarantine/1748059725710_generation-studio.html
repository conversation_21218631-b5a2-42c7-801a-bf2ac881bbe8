<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Studio de Génération</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/native-app.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .generation-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .tab-button {
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 500;
        }

        .tab-button.active {
            color: var(--accent-color);
            border-bottom-color: var(--accent-color);
        }

        .tab-button:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .generation-form {
            background-color: var(--bg-card);
            padding: 25px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .generate-button {
            background: linear-gradient(135deg, var(--accent-color), var(--primary));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .generate-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
        }

        .generate-button:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .results-container {
            background-color: var(--bg-card);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
        }

        .result-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius);
            margin-bottom: 10px;
            border-left: 4px solid var(--accent-color);
        }

        .result-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--accent-color), var(--primary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .result-info {
            flex-grow: 1;
        }

        .result-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .result-details {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .result-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-generating {
            background-color: var(--warning);
            color: white;
        }

        .status-completed {
            background-color: var(--success);
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: var(--bg-tertiary);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color), var(--primary));
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: var(--bg-card);
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            border: 2px solid var(--border-color);
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/presentation.html" class="nav-item">
                <i class="fas fa-presentation"></i>
                <span>Présentation</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/generation-studio.html" class="nav-item active">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/agent-navigation.html" class="nav-item">
                <i class="fas fa-compass"></i>
                <span>Navigation Agent</span>
            </a>
            <a href="/security-dashboard.html" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>Sécurité</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/agents.html" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/settings.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Studio de Génération Louna</h1>
                <p class="interface-subtitle">Génération illimitée de vidéos, images, musique et modèles 3D de qualité professionnelle</p>
            </div>

            <div class="status-container">
                <span class="status-label">Agent:</span>
                <div class="status-indicator" style="background-color: var(--success);"></div>
                <span class="status-text">Louna Actif</span>
            </div>
        </div>

        <!-- Statistiques de génération -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-generations">0</div>
                <div class="stat-label">Générations totales</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="video-count">0</div>
                <div class="stat-label">Vidéos générées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="image-count">0</div>
                <div class="stat-label">Images générées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="music-count">0</div>
                <div class="stat-label">Musiques générées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="model3d-count">0</div>
                <div class="stat-label">Modèles 3D générés</div>
            </div>
        </div>

        <!-- Onglets de génération -->
        <div class="generation-tabs">
            <button class="tab-button active" data-tab="video">
                <i class="fas fa-video"></i> Vidéos LTX
            </button>
            <button class="tab-button" data-tab="image">
                <i class="fas fa-image"></i> Images HD
            </button>
            <button class="tab-button" data-tab="music">
                <i class="fas fa-music"></i> Musique
            </button>
            <button class="tab-button" data-tab="3d">
                <i class="fas fa-cube"></i> Modèles 3D
            </button>
        </div>

        <!-- Contenu des onglets -->

        <!-- Onglet Vidéo -->
        <div class="tab-content active" id="video-tab">
            <div class="generation-form">
                <h3><i class="fas fa-video"></i> Génération de Vidéos LTX</h3>
                <p>Créez des vidéos de qualité professionnelle en quantité illimitée</p>

                <div class="form-group">
                    <label class="form-label">Description de la vidéo</label>
                    <textarea class="form-textarea" id="video-prompt" placeholder="Décrivez la vidéo que vous souhaitez générer..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Durée (secondes)</label>
                        <input type="number" class="form-input" id="video-duration" value="10" min="1" max="300">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Qualité</label>
                        <select class="form-select" id="video-quality">
                            <option value="HD">HD (1280x720)</option>
                            <option value="FHD">Full HD (1920x1080)</option>
                            <option value="4K">4K (3840x2160)</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Style</label>
                        <select class="form-select" id="video-style">
                            <option value="realistic">Réaliste</option>
                            <option value="animated">Animé</option>
                            <option value="cinematic">Cinématique</option>
                            <option value="artistic">Artistique</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">FPS</label>
                        <select class="form-select" id="video-fps">
                            <option value="24">24 FPS</option>
                            <option value="30">30 FPS</option>
                            <option value="60">60 FPS</option>
                        </select>
                    </div>
                </div>

                <button class="generate-button" id="generate-video-btn">
                    <i class="fas fa-play"></i> Générer la Vidéo
                </button>
            </div>
        </div>

        <!-- Onglet Image -->
        <div class="tab-content" id="image-tab">
            <div class="generation-form">
                <h3><i class="fas fa-image"></i> Génération d'Images HD</h3>
                <p>Créez des images de qualité professionnelle avec une résolution jusqu'à 4K</p>

                <div class="form-group">
                    <label class="form-label">Description de l'image</label>
                    <textarea class="form-textarea" id="image-prompt" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Largeur</label>
                        <select class="form-select" id="image-width">
                            <option value="512">512px</option>
                            <option value="1024" selected>1024px</option>
                            <option value="2048">2048px</option>
                            <option value="4096">4096px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hauteur</label>
                        <select class="form-select" id="image-height">
                            <option value="512">512px</option>
                            <option value="1024" selected>1024px</option>
                            <option value="2048">2048px</option>
                            <option value="4096">4096px</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Style</label>
                        <select class="form-select" id="image-style">
                            <option value="photorealistic">Photoréaliste</option>
                            <option value="artistic">Artistique</option>
                            <option value="digital-art">Art numérique</option>
                            <option value="oil-painting">Peinture à l'huile</option>
                            <option value="watercolor">Aquarelle</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Nombre d'images</label>
                        <input type="number" class="form-input" id="image-count-input" value="1" min="1" max="10">
                    </div>
                </div>

                <button class="generate-button" id="generate-image-btn">
                    <i class="fas fa-camera"></i> Générer les Images
                </button>
            </div>
        </div>

        <!-- Onglet Musique -->
        <div class="tab-content" id="music-tab">
            <div class="generation-form">
                <h3><i class="fas fa-music"></i> Génération de Musique</h3>
                <p>Composez de la musique originale dans tous les genres</p>

                <div class="form-group">
                    <label class="form-label">Description de la musique</label>
                    <textarea class="form-textarea" id="music-prompt" placeholder="Décrivez la musique que vous souhaitez composer..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Durée (secondes)</label>
                        <input type="number" class="form-input" id="music-duration" value="120" min="10" max="600">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Genre</label>
                        <select class="form-select" id="music-genre">
                            <option value="ambient">Ambient</option>
                            <option value="classical">Classique</option>
                            <option value="electronic">Électronique</option>
                            <option value="jazz">Jazz</option>
                            <option value="rock">Rock</option>
                            <option value="pop">Pop</option>
                            <option value="cinematic">Cinématique</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Tempo (BPM)</label>
                        <input type="number" class="form-input" id="music-tempo" value="120" min="60" max="200">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tonalité</label>
                        <select class="form-select" id="music-key">
                            <option value="C major">Do majeur</option>
                            <option value="G major">Sol majeur</option>
                            <option value="D major">Ré majeur</option>
                            <option value="A major">La majeur</option>
                            <option value="E major">Mi majeur</option>
                            <option value="A minor">La mineur</option>
                            <option value="E minor">Mi mineur</option>
                        </select>
                    </div>
                </div>

                <button class="generate-button" id="generate-music-btn">
                    <i class="fas fa-music"></i> Composer la Musique
                </button>
            </div>
        </div>

        <!-- Onglet 3D -->
        <div class="tab-content" id="3d-tab">
            <div class="generation-form">
                <h3><i class="fas fa-cube"></i> Génération de Modèles 3D</h3>
                <p>Créez des modèles 3D détaillés pour vos projets</p>

                <div class="form-group">
                    <label class="form-label">Description du modèle 3D</label>
                    <textarea class="form-textarea" id="model3d-prompt" placeholder="Décrivez le modèle 3D que vous souhaitez créer..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Type</label>
                        <select class="form-select" id="model3d-type">
                            <option value="model">Modèle simple</option>
                            <option value="character">Personnage</option>
                            <option value="scene">Scène complète</option>
                            <option value="architecture">Architecture</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Format</label>
                        <select class="form-select" id="model3d-format">
                            <option value="OBJ">OBJ</option>
                            <option value="FBX">FBX</option>
                            <option value="GLTF">GLTF</option>
                            <option value="STL">STL</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Qualité</label>
                        <select class="form-select" id="model3d-quality">
                            <option value="low">Basse (rapide)</option>
                            <option value="medium">Moyenne</option>
                            <option value="high" selected>Haute (détaillée)</option>
                            <option value="ultra">Ultra (très détaillée)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Animation</label>
                        <select class="form-select" id="model3d-animated">
                            <option value="false">Statique</option>
                            <option value="true">Animé</option>
                        </select>
                    </div>
                </div>

                <button class="generate-button" id="generate-3d-btn">
                    <i class="fas fa-cube"></i> Générer le Modèle 3D
                </button>
            </div>
        </div>

        <!-- Résultats des générations -->
        <div class="results-container">
            <h3><i class="fas fa-list"></i> Générations en cours et terminées</h3>
            <div id="results-list">
                <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                    Aucune génération en cours. Utilisez les formulaires ci-dessus pour commencer.
                </p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Variables globales
        let currentGenerations = [];
        let stats = {
            totalGenerations: 0,
            videoGenerations: 0,
            imageGenerations: 0,
            musicGenerations: 0,
            model3dGenerations: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            initializeTabs();
            initializeGenerationButtons();
            loadStats();
            loadGenerationHistory();
        });

        // Gestion des onglets
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;

                    // Désactiver tous les onglets
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Activer l'onglet sélectionné
                    button.classList.add('active');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });
        }

        // Initialisation des boutons de génération
        function initializeGenerationButtons() {
            // Bouton génération vidéo
            document.getElementById('generate-video-btn').addEventListener('click', () => {
                generateVideo();
            });

            // Bouton génération image
            document.getElementById('generate-image-btn').addEventListener('click', () => {
                generateImage();
            });

            // Bouton génération musique
            document.getElementById('generate-music-btn').addEventListener('click', () => {
                generateMusic();
            });

            // Bouton génération 3D
            document.getElementById('generate-3d-btn').addEventListener('click', () => {
                generate3D();
            });
        }

        // Génération de vidéo
        async function generateVideo() {
            const prompt = document.getElementById('video-prompt').value;
            const duration = document.getElementById('video-duration').value;
            const quality = document.getElementById('video-quality').value;
            const style = document.getElementById('video-style').value;

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour la vidéo');
                return;
            }

            const button = document.getElementById('generate-video-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération en cours...';

            try {
                const response = await fetch('/api/generation/video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        duration: parseInt(duration),
                        quality: quality,
                        style: style
                    })
                });

                const result = await response.json();

                if (result.success) {
                    addGenerationToList(result.video, 'video');
                    updateStats();
                    document.getElementById('video-prompt').value = '';
                } else {
                    alert('Erreur lors de la génération: ' + result.error);
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur de connexion');
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-play"></i> Générer la Vidéo';
            }
        }

        // Génération d'image
        async function generateImage() {
            const prompt = document.getElementById('image-prompt').value;
            const width = document.getElementById('image-width').value;
            const height = document.getElementById('image-height').value;
            const style = document.getElementById('image-style').value;
            const count = document.getElementById('image-count-input').value;

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour l\'image');
                return;
            }

            const button = document.getElementById('generate-image-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération en cours...';

            try {
                const response = await fetch('/api/generation/image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        width: parseInt(width),
                        height: parseInt(height),
                        style: style,
                        count: parseInt(count)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    result.images.forEach(image => {
                        addGenerationToList(image, 'image');
                    });
                    updateStats();
                    document.getElementById('image-prompt').value = '';
                } else {
                    alert('Erreur lors de la génération: ' + result.error);
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur de connexion');
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-camera"></i> Générer les Images';
            }
        }

        // Génération de musique
        async function generateMusic() {
            const prompt = document.getElementById('music-prompt').value;
            const duration = document.getElementById('music-duration').value;
            const genre = document.getElementById('music-genre').value;
            const tempo = document.getElementById('music-tempo').value;
            const key = document.getElementById('music-key').value;

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour la musique');
                return;
            }

            const button = document.getElementById('generate-music-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Composition en cours...';

            try {
                const response = await fetch('/api/generation/music', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        duration: parseInt(duration),
                        genre: genre,
                        tempo: parseInt(tempo),
                        key: key
                    })
                });

                const result = await response.json();

                if (result.success) {
                    addGenerationToList(result.music, 'music');
                    updateStats();
                    document.getElementById('music-prompt').value = '';
                } else {
                    alert('Erreur lors de la génération: ' + result.error);
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur de connexion');
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-music"></i> Composer la Musique';
            }
        }

        // Génération 3D
        async function generate3D() {
            const prompt = document.getElementById('model3d-prompt').value;
            const type = document.getElementById('model3d-type').value;
            const format = document.getElementById('model3d-format').value;
            const quality = document.getElementById('model3d-quality').value;
            const animated = document.getElementById('model3d-animated').value === 'true';

            if (!prompt.trim()) {
                alert('Veuillez entrer une description pour le modèle 3D');
                return;
            }

            const button = document.getElementById('generate-3d-btn');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Modélisation en cours...';

            try {
                const response = await fetch('/api/generation/3d', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        type: type,
                        format: format,
                        quality: quality,
                        animated: animated
                    })
                });

                const result = await response.json();

                if (result.success) {
                    addGenerationToList(result.model, '3d');
                    updateStats();
                    document.getElementById('model3d-prompt').value = '';
                } else {
                    alert('Erreur lors de la génération: ' + result.error);
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur de connexion');
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-cube"></i> Générer le Modèle 3D';
            }
        }

        // Ajouter une génération à la liste
        function addGenerationToList(generation, type) {
            currentGenerations.unshift(generation);

            const resultsList = document.getElementById('results-list');

            // Supprimer le message "Aucune génération"
            if (resultsList.children.length === 1 && resultsList.children[0].tagName === 'P') {
                resultsList.innerHTML = '';
            }

            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.id = `result-${generation.id}`;

            const iconMap = {
                'video': 'fa-video',
                'image': 'fa-image',
                'music': 'fa-music',
                '3d': 'fa-cube'
            };

            resultItem.innerHTML = `
                <div class="result-icon">
                    <i class="fas ${iconMap[type]}"></i>
                </div>
                <div class="result-info">
                    <div class="result-title">${generation.prompt}</div>
                    <div class="result-details">
                        ${type === 'video' ? `${generation.duration}s, ${generation.quality}, ${generation.style}` : ''}
                        ${type === 'image' ? `${generation.width}x${generation.height}, ${generation.style}` : ''}
                        ${type === 'music' ? `${generation.duration}s, ${generation.genre}, ${generation.tempo} BPM` : ''}
                        ${type === '3d' ? `${generation.type}, ${generation.format}, ${generation.quality}` : ''}
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${generation.progress}%"></div>
                    </div>
                </div>
                <div class="result-status ${generation.status === 'completed' ? 'status-completed' : 'status-generating'}">
                    ${generation.status === 'completed' ? 'Terminé' : 'En cours'}
                </div>
            `;

            resultsList.insertBefore(resultItem, resultsList.firstChild);

            // Simuler la progression
            if (generation.status === 'generating') {
                simulateProgress(generation.id, type);
            }
        }

        // Simuler la progression
        function simulateProgress(generationId, type) {
            const progressInterval = setInterval(() => {
                const generation = currentGenerations.find(g => g.id === generationId);
                const resultItem = document.getElementById(`result-${generationId}`);

                if (!generation || !resultItem) {
                    clearInterval(progressInterval);
                    return;
                }

                if (generation.status === 'completed') {
                    const progressFill = resultItem.querySelector('.progress-fill');
                    const statusElement = resultItem.querySelector('.result-status');

                    progressFill.style.width = '100%';
                    statusElement.textContent = 'Terminé';
                    statusElement.className = 'result-status status-completed';

                    clearInterval(progressInterval);
                    updateStats();
                } else {
                    generation.progress = Math.min(generation.progress + Math.random() * 10, 95);
                    const progressFill = resultItem.querySelector('.progress-fill');
                    progressFill.style.width = `${generation.progress}%`;
                }
            }, 500);

            // Marquer comme terminé après le délai approprié
            const delays = { video: 3000, image: 2000, music: 4000, '3d': 5000 };
            setTimeout(() => {
                const generation = currentGenerations.find(g => g.id === generationId);
                if (generation) {
                    generation.status = 'completed';
                    generation.progress = 100;
                }
            }, delays[type] || 3000);
        }

        // Charger les statistiques
        async function loadStats() {
            try {
                const response = await fetch('/api/generation/stats');
                const result = await response.json();

                if (result.success) {
                    stats = result.stats;
                    updateStatsDisplay();
                }
            } catch (error) {
                console.error('Erreur lors du chargement des statistiques:', error);
            }
        }

        // Mettre à jour l'affichage des statistiques
        function updateStatsDisplay() {
            document.getElementById('total-generations').textContent = stats.totalGenerations;
            document.getElementById('video-count').textContent = stats.videoGenerations;
            document.getElementById('image-count').textContent = stats.imageGenerations;
            document.getElementById('music-count').textContent = stats.musicGenerations;
            document.getElementById('model3d-count').textContent = stats.model3dGenerations;
        }

        // Mettre à jour les statistiques
        function updateStats() {
            stats.totalGenerations = currentGenerations.length;
            stats.videoGenerations = currentGenerations.filter(g => g.id.startsWith('video_')).length;
            stats.imageGenerations = currentGenerations.filter(g => g.id.startsWith('img_')).length;
            stats.musicGenerations = currentGenerations.filter(g => g.id.startsWith('music_')).length;
            stats.model3dGenerations = currentGenerations.filter(g => g.id.startsWith('3d_')).length;

            updateStatsDisplay();
        }

        // Charger l'historique des générations
        async function loadGenerationHistory() {
            try {
                const response = await fetch('/api/generation/history?limit=10');
                const result = await response.json();

                if (result.success && result.history.length > 0) {
                    // Traiter l'historique si nécessaire
                    console.log('Historique chargé:', result.history.length, 'entrées');
                }
            } catch (error) {
                console.error('Erreur lors du chargement de l\'historique:', error);
            }
        }
    </script>

    <!-- Scripts supplémentaires -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="js/native-app.js"></script>

    <script>
        // Notification de bienvenue
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof notifications !== 'undefined') {
                    notifications.success(
                        "Studio de génération prêt ! Créez du contenu multimédia illimité avec Louna.",
                        "Studio Actif",
                        { duration: 5000 }
                    );
                }
            }, 1000);
        });
    </script>
</body>
</html>
