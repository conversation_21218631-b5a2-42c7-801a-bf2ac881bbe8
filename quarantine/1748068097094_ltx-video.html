<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - LTX Video</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/native-app.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .video-container {
            position: relative;
            width: 100%;
            height: 60vh;
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 20px;
        }

        #ltx-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #ltx-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .video-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 12px;
            z-index: 10;
        }

        .video-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            background-color: var(--accent-color);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-button:hover {
            background-color: var(--accent-hover);
            transform: translateY(-2px);
        }

        .control-button.active {
            background-color: var(--success);
        }

        .control-button:disabled {
            background-color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: var(--bg-card);
            padding: 15px;
            border-radius: var(--border-radius);
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
        }

        .detection-list {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .detection-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .detection-item:last-child {
            border-bottom: none;
        }

        .detection-object {
            font-weight: bold;
            color: var(--text-primary);
        }

        .detection-confidence {
            color: var(--success);
            font-size: 12px;
        }

        .detection-time {
            color: var(--text-secondary);
            font-size: 11px;
        }

        .settings-panel {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: var(--text-primary);
        }

        .setting-control {
            width: 100%;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .thermal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.6;
            mix-blend-mode: overlay;
        }

        .object-detection-box {
            position: absolute;
            border: 2px solid var(--accent-color);
            background-color: rgba(255, 105, 180, 0.1);
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .object-label {
            position: absolute;
            top: -25px;
            left: 0;
            background-color: var(--accent-color);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            white-space: nowrap;
        }

        .processing-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            z-index: 20;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background-color: var(--danger);
            color: white;
            padding: 10px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background-color: var(--success);
            color: white;
            padding: 10px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/thermal-memory.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item active">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">LTX Video</h1>
                <p class="interface-subtitle">Traitement vidéo en temps réel avec analyse thermique et détection d'objets</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="ltx-status-indicator"></div>
                <span class="status-text" id="ltx-status-text">Arrêté</span>

                <button class="action-button" id="start-camera-btn">
                    <i class="fas fa-play"></i> Démarrer la caméra
                </button>
                <button class="action-button" id="stop-camera-btn" disabled>
                    <i class="fas fa-stop"></i> Arrêter la caméra
                </button>
                <a href="/thermal-memory.html" class="action-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
            </div>
        </div>

        <!-- Messages d'erreur et de succès -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Conteneur vidéo principal -->
        <div class="video-container">
            <video id="ltx-video" autoplay muted playsinline></video>
            <canvas id="ltx-canvas"></canvas>
            <div class="thermal-overlay" id="thermal-overlay"></div>

            <div class="video-overlay">
                <div>FPS: <span id="fps-counter">0</span></div>
                <div>Résolution: <span id="resolution-display">640x480</span></div>
                <div>Objets détectés: <span id="objects-count">0</span></div>
            </div>

            <div id="processing-indicator" class="processing-indicator" style="display: none;">
                <div class="spinner"></div>
                <div>Initialisation de la caméra...</div>
            </div>
        </div>

        <!-- Contrôles vidéo -->
        <div class="video-controls">
            <button class="control-button" id="toggle-thermal-btn">
                <i class="fas fa-thermometer-half"></i> Analyse thermique
            </button>
            <button class="control-button" id="toggle-detection-btn">
                <i class="fas fa-search"></i> Détection d'objets
            </button>
            <button class="control-button" id="toggle-facial-btn">
                <i class="fas fa-user"></i> Reconnaissance faciale
            </button>
            <button class="control-button" id="capture-frame-btn">
                <i class="fas fa-camera"></i> Capturer l'image
            </button>
            <button class="control-button" id="record-btn">
                <i class="fas fa-record-vinyl"></i> Enregistrer
            </button>
            <button class="control-button" id="optimize-btn">
                <i class="fas fa-magic"></i> Optimiser
            </button>
        </div>

        <!-- Statistiques en temps réel -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="fps-stat">0</div>
                <div class="stat-label">FPS</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="objects-stat">0</div>
                <div class="stat-label">Objets détectés</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="faces-stat">0</div>
                <div class="stat-label">Visages détectés</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="processing-time-stat">0ms</div>
                <div class="stat-label">Temps de traitement</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="thermal-temp-stat">0°C</div>
                <div class="stat-label">Température moyenne</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="memory-usage-stat">0MB</div>
                <div class="stat-label">Mémoire utilisée</div>
            </div>
        </div>

        <!-- Panneau de configuration -->
        <div class="settings-panel">
            <h3>Configuration LTX Video</h3>

            <div class="setting-group">
                <label class="setting-label">Résolution vidéo</label>
                <select class="setting-control" id="resolution-select">
                    <option value="640x480">640x480 (VGA)</option>
                    <option value="1280x720">1280x720 (HD)</option>
                    <option value="1920x1080">1920x1080 (Full HD)</option>
                </select>
            </div>

            <div class="setting-group">
                <label class="setting-label">Fréquence d'images (FPS)</label>
                <select class="setting-control" id="framerate-select">
                    <option value="15">15 FPS</option>
                    <option value="30" selected>30 FPS</option>
                    <option value="60">60 FPS</option>
                </select>
            </div>

            <div class="setting-group">
                <label class="setting-label">Sensibilité de détection</label>
                <input type="range" class="setting-control" id="detection-sensitivity" min="0.1" max="1.0" step="0.1" value="0.5">
                <span id="sensitivity-value">0.5</span>
            </div>

            <div class="setting-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="auto-optimize" checked>
                    <label for="auto-optimize">Optimisation automatique</label>
                </div>
            </div>

            <div class="setting-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="save-to-memory" checked>
                    <label for="save-to-memory">Sauvegarder dans la mémoire thermique</label>
                </div>
            </div>

            <div class="setting-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="show-debug" checked>
                    <label for="show-debug">Afficher les informations de débogage</label>
                </div>
            </div>
        </div>

        <!-- Liste des détections récentes -->
        <div class="detection-list">
            <h3>Détections récentes</h3>
            <div id="detections-container">
                <div class="detection-item">
                    <div>
                        <div class="detection-object">Aucune détection</div>
                        <div class="detection-time">En attente...</div>
                    </div>
                    <div class="detection-confidence">--</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/ltx-video.js"></script>
    <script>
        let ltxVideo = null;
        let isRecording = false;
        let detections = [];

        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser LTX Video
            ltxVideo = new LTXVideo({
                captureWidth: 640,
                captureHeight: 480,
                frameRate: 30,
                processingEnabled: true,
                thermalAnalysisEnabled: true,
                objectDetectionEnabled: true,
                facialRecognitionEnabled: false
            });

            // Écouteurs d'événements
            setupEventListeners();

            // Mettre à jour l'interface
            updateInterface();

            // Démarrer la mise à jour des statistiques
            setInterval(updateStats, 1000);
        });

        function setupEventListeners() {
            // Boutons de contrôle de la caméra
            document.getElementById('start-camera-btn').addEventListener('click', startCamera);
            document.getElementById('stop-camera-btn').addEventListener('click', stopCamera);

            // Boutons de fonctionnalités
            document.getElementById('toggle-thermal-btn').addEventListener('click', toggleThermalAnalysis);
            document.getElementById('toggle-detection-btn').addEventListener('click', toggleObjectDetection);
            document.getElementById('toggle-facial-btn').addEventListener('click', toggleFacialRecognition);
            document.getElementById('capture-frame-btn').addEventListener('click', captureFrame);
            document.getElementById('record-btn').addEventListener('click', toggleRecording);
            document.getElementById('optimize-btn').addEventListener('click', optimizeLTX);

            // Contrôles de configuration
            document.getElementById('resolution-select').addEventListener('change', updateResolution);
            document.getElementById('framerate-select').addEventListener('change', updateFramerate);
            document.getElementById('detection-sensitivity').addEventListener('input', updateSensitivity);

            // Écouteurs LTX Video
            if (ltxVideo) {
                ltxVideo.on('objectDetected', handleObjectDetection);
                ltxVideo.on('faceDetected', handleFaceDetection);
                ltxVideo.on('thermalAnalysis', handleThermalAnalysis);
                ltxVideo.on('error', handleError);
            }
        }

        async function startCamera() {
            try {
                showProcessingIndicator('Démarrage de la caméra...');

                const videoElement = document.getElementById('ltx-video');
                const success = await ltxVideo.start(videoElement);

                if (success) {
                    updateInterface();
                    showSuccessMessage('Caméra démarrée avec succès');
                } else {
                    showErrorMessage('Impossible de démarrer la caméra');
                }
            } catch (error) {
                console.error('Erreur lors du démarrage de la caméra:', error);
                showErrorMessage('Erreur lors du démarrage de la caméra: ' + error.message);
            } finally {
                hideProcessingIndicator();
            }
        }

        async function stopCamera() {
            try {
                await ltxVideo.stop();
                updateInterface();
                showSuccessMessage('Caméra arrêtée');
            } catch (error) {
                console.error('Erreur lors de l\'arrêt de la caméra:', error);
                showErrorMessage('Erreur lors de l\'arrêt de la caméra: ' + error.message);
            }
        }

        function toggleThermalAnalysis() {
            const enabled = ltxVideo.toggleThermalAnalysis();
            const btn = document.getElementById('toggle-thermal-btn');
            btn.classList.toggle('active', enabled);
            showSuccessMessage(`Analyse thermique ${enabled ? 'activée' : 'désactivée'}`);
        }

        function toggleObjectDetection() {
            const enabled = ltxVideo.toggleObjectDetection();
            const btn = document.getElementById('toggle-detection-btn');
            btn.classList.toggle('active', enabled);
            showSuccessMessage(`Détection d'objets ${enabled ? 'activée' : 'désactivée'}`);
        }

        function toggleFacialRecognition() {
            const enabled = ltxVideo.toggleFacialRecognition();
            const btn = document.getElementById('toggle-facial-btn');
            btn.classList.toggle('active', enabled);
            showSuccessMessage(`Reconnaissance faciale ${enabled ? 'activée' : 'désactivée'}`);
        }

        function captureFrame() {
            if (ltxVideo && ltxVideo.state.active) {
                const imageData = ltxVideo.captureFrame();
                if (imageData) {
                    // Créer un lien de téléchargement
                    const link = document.createElement('a');
                    link.download = `ltx-capture-${Date.now()}.png`;
                    link.href = imageData;
                    link.click();
                    showSuccessMessage('Image capturée avec succès');
                }
            }
        }

        function toggleRecording() {
            if (!isRecording) {
                // Démarrer l'enregistrement
                isRecording = true;
                const btn = document.getElementById('record-btn');
                btn.innerHTML = '<i class="fas fa-stop"></i> Arrêter l\'enregistrement';
                btn.classList.add('active');
                showSuccessMessage('Enregistrement démarré');
            } else {
                // Arrêter l'enregistrement
                isRecording = false;
                const btn = document.getElementById('record-btn');
                btn.innerHTML = '<i class="fas fa-record-vinyl"></i> Enregistrer';
                btn.classList.remove('active');
                showSuccessMessage('Enregistrement arrêté');
            }
        }

        async function optimizeLTX() {
            try {
                const response = await fetch('/api/thermal/ltx/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        boostFactor: 1.2,
                        stability: 0.8
                    })
                });

                const result = await response.json();
                if (result.success) {
                    showSuccessMessage('LTX optimisé avec succès');
                } else {
                    showErrorMessage('Erreur lors de l\'optimisation: ' + result.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'optimisation:', error);
                showErrorMessage('Erreur lors de l\'optimisation');
            }
        }

        function updateInterface() {
            const isActive = ltxVideo && ltxVideo.state.active;

            // Mettre à jour les boutons
            document.getElementById('start-camera-btn').disabled = isActive;
            document.getElementById('stop-camera-btn').disabled = !isActive;
            document.getElementById('capture-frame-btn').disabled = !isActive;

            // Mettre à jour le statut
            const statusIndicator = document.getElementById('ltx-status-indicator');
            const statusText = document.getElementById('ltx-status-text');

            if (isActive) {
                statusIndicator.className = 'status-indicator active';
                statusText.textContent = 'Actif';
            } else {
                statusIndicator.className = 'status-indicator';
                statusText.textContent = 'Arrêté';
            }

            // Mettre à jour les boutons de fonctionnalités
            if (ltxVideo) {
                document.getElementById('toggle-thermal-btn').classList.toggle('active', ltxVideo.config.thermalAnalysisEnabled);
                document.getElementById('toggle-detection-btn').classList.toggle('active', ltxVideo.config.objectDetectionEnabled);
                document.getElementById('toggle-facial-btn').classList.toggle('active', ltxVideo.config.facialRecognitionEnabled);
            }
        }

        function updateStats() {
            if (ltxVideo && ltxVideo.state.active) {
                const stats = ltxVideo.getStats();

                document.getElementById('fps-stat').textContent = stats.fps;
                document.getElementById('fps-counter').textContent = stats.fps;
                document.getElementById('objects-stat').textContent = stats.objectsDetected;
                document.getElementById('objects-count').textContent = stats.objectsDetected;
                document.getElementById('faces-stat').textContent = stats.facesDetected;
                document.getElementById('processing-time-stat').textContent = stats.averageProcessingTime.toFixed(1) + 'ms';

                // Estimation de l'utilisation mémoire
                const memoryUsage = (stats.framesProcessed * 0.1).toFixed(1);
                document.getElementById('memory-usage-stat').textContent = memoryUsage + 'MB';
            }
        }

        function handleObjectDetection(event) {
            const detection = {
                object: event.object,
                confidence: event.confidence,
                time: new Date().toLocaleTimeString(),
                timestamp: Date.now()
            };

            detections.unshift(detection);
            if (detections.length > 10) {
                detections.pop();
            }

            updateDetectionsList();
        }

        function handleFaceDetection(event) {
            const detection = {
                object: 'Visage',
                confidence: event.confidence,
                time: new Date().toLocaleTimeString(),
                timestamp: Date.now()
            };

            detections.unshift(detection);
            if (detections.length > 10) {
                detections.pop();
            }

            updateDetectionsList();
        }

        function handleThermalAnalysis(event) {
            document.getElementById('thermal-temp-stat').textContent = event.averageTemperature.toFixed(1) + '°C';
        }

        function handleError(event) {
            console.error('Erreur LTX Video:', event.error);
            showErrorMessage('Erreur LTX Video: ' + event.error.message);
        }

        function updateDetectionsList() {
            const container = document.getElementById('detections-container');

            if (detections.length === 0) {
                container.innerHTML = `
                    <div class="detection-item">
                        <div>
                            <div class="detection-object">Aucune détection</div>
                            <div class="detection-time">En attente...</div>
                        </div>
                        <div class="detection-confidence">--</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = detections.map(detection => `
                <div class="detection-item">
                    <div>
                        <div class="detection-object">${detection.object}</div>
                        <div class="detection-time">${detection.time}</div>
                    </div>
                    <div class="detection-confidence">${(detection.confidence * 100).toFixed(1)}%</div>
                </div>
            `).join('');
        }

        function updateResolution() {
            const resolution = document.getElementById('resolution-select').value;
            const [width, height] = resolution.split('x').map(Number);

            if (ltxVideo) {
                ltxVideo.config.captureWidth = width;
                ltxVideo.config.captureHeight = height;
                document.getElementById('resolution-display').textContent = resolution;
                showSuccessMessage(`Résolution mise à jour: ${resolution}`);
            }
        }

        function updateFramerate() {
            const framerate = parseInt(document.getElementById('framerate-select').value);

            if (ltxVideo) {
                ltxVideo.config.frameRate = framerate;
                showSuccessMessage(`Fréquence d'images mise à jour: ${framerate} FPS`);
            }
        }

        function updateSensitivity() {
            const sensitivity = parseFloat(document.getElementById('detection-sensitivity').value);
            document.getElementById('sensitivity-value').textContent = sensitivity.toFixed(1);

            if (ltxVideo) {
                ltxVideo.config.detectionSensitivity = sensitivity;
                showSuccessMessage(`Sensibilité mise à jour: ${sensitivity.toFixed(1)}`);
            }
        }

        function showProcessingIndicator(message) {
            const indicator = document.getElementById('processing-indicator');
            indicator.querySelector('div:last-child').textContent = message;
            indicator.style.display = 'block';
        }

        function hideProcessingIndicator() {
            document.getElementById('processing-indicator').style.display = 'none';
        }

        function showErrorMessage(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccessMessage(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }
    </script>
    <script src="js/native-app.js"></script>
</body>
</html>
