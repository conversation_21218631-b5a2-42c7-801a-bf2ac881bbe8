<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Extensions de Code</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/native-app.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .extensions-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .extensions-sidebar {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            overflow-y: auto;
        }

        .extensions-main {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            overflow-y: auto;
        }

        .extension-category {
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 105, 180, 0.3);
        }

        .category-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .category-item:hover {
            background-color: rgba(255, 105, 180, 0.1);
        }

        .category-item.active {
            background-color: var(--accent-color);
            color: white;
        }

        .extensions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .extension-card {
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 20px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .extension-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border-color: var(--accent-color);
        }

        .extension-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .extension-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .extension-info {
            flex: 1;
        }

        .extension-name {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .extension-author {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .extension-description {
            color: var(--text-primary);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .extension-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .extension-stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .extension-actions {
            display: flex;
            gap: 10px;
        }

        .extension-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .extension-btn.install {
            background-color: var(--success);
            color: white;
        }

        .extension-btn.install:hover {
            background-color: #45a049;
        }

        .extension-btn.installed {
            background-color: var(--text-secondary);
            color: white;
            cursor: not-allowed;
        }

        .extension-btn.uninstall {
            background-color: var(--danger);
            color: white;
        }

        .extension-btn.uninstall:hover {
            background-color: #d32f2f;
        }

        .extension-btn.configure {
            background-color: var(--info);
            color: white;
        }

        .extension-btn.configure:hover {
            background-color: #1976d2;
        }

        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-tab {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .filter-tab.active {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        .filter-tab:hover {
            border-color: var(--accent-color);
        }

        .extension-rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stars {
            color: #ffc107;
        }

        .installed-badge {
            background-color: var(--success);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }

        .popular-badge {
            background-color: var(--accent-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }

        .new-badge {
            background-color: var(--info);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }

        .extension-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .extension-tag {
            background-color: rgba(255, 105, 180, 0.2);
            color: var(--accent-color);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-results {
            text-align: center;
            color: var(--text-secondary);
            padding: 40px;
        }

        .no-results i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item active">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Extensions de Code</h1>
                <p class="interface-subtitle">Étendez les fonctionnalités de votre éditeur avec des extensions puissantes</p>
            </div>

            <div class="status-container">
                <span class="status-label">Extensions installées:</span>
                <div class="status-indicator active"></div>
                <span class="status-text" id="installed-count">12</span>

                <button class="action-button" id="refresh-extensions-btn">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
                <a href="/code-editor.html" class="action-button">
                    <i class="fas fa-code"></i> Éditeur
                </a>
            </div>
        </div>

        <!-- Conteneur des extensions -->
        <div class="extensions-container">
            <!-- Barre latérale -->
            <div class="extensions-sidebar">
                <div class="search-container">
                    <input type="text" class="search-input" id="search-input" placeholder="Rechercher des extensions...">
                </div>

                <div class="filter-tabs">
                    <div class="filter-tab active" data-filter="all">Toutes</div>
                    <div class="filter-tab" data-filter="installed">Installées</div>
                    <div class="filter-tab" data-filter="popular">Populaires</div>
                </div>

                <div class="extension-category">
                    <div class="category-title">Catégories</div>
                    <div class="category-item active" data-category="all">
                        <i class="fas fa-th"></i>
                        <span>Toutes les catégories</span>
                    </div>
                    <div class="category-item" data-category="languages">
                        <i class="fas fa-code"></i>
                        <span>Langages</span>
                    </div>
                    <div class="category-item" data-category="themes">
                        <i class="fas fa-palette"></i>
                        <span>Thèmes</span>
                    </div>
                    <div class="category-item" data-category="snippets">
                        <i class="fas fa-cut"></i>
                        <span>Snippets</span>
                    </div>
                    <div class="category-item" data-category="formatters">
                        <i class="fas fa-align-left"></i>
                        <span>Formatage</span>
                    </div>
                    <div class="category-item" data-category="linters">
                        <i class="fas fa-bug"></i>
                        <span>Linting</span>
                    </div>
                    <div class="category-item" data-category="git">
                        <i class="fab fa-git-alt"></i>
                        <span>Git</span>
                    </div>
                    <div class="category-item" data-category="ai">
                        <i class="fas fa-brain"></i>
                        <span>IA & Louna</span>
                    </div>
                </div>
            </div>

            <!-- Zone principale -->
            <div class="extensions-main">
                <div class="extensions-grid" id="extensions-grid">
                    <!-- Les extensions seront chargées dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        let extensions = [];
        let filteredExtensions = [];
        let currentFilter = 'all';
        let currentCategory = 'all';
        let searchQuery = '';

        // Extensions simulées
        const mockExtensions = [
            {
                id: 'louna-ai-assistant',
                name: 'Louna AI Assistant',
                author: 'Louna Team',
                description: 'Assistant IA intégré pour la génération de code, l\'analyse et l\'optimisation avec accès à la mémoire thermique.',
                version: '2.1.0',
                downloads: 15420,
                rating: 4.9,
                category: 'ai',
                tags: ['ai', 'assistant', 'louna', 'thermal-memory'],
                installed: true,
                popular: true,
                icon: 'fas fa-brain'
            },
            {
                id: 'python-enhanced',
                name: 'Python Enhanced',
                author: 'Python Foundation',
                description: 'Support complet pour Python avec IntelliSense, débogage, linting et formatage automatique.',
                version: '1.8.2',
                downloads: 89234,
                rating: 4.7,
                category: 'languages',
                tags: ['python', 'intellisense', 'debugging'],
                installed: true,
                popular: true,
                icon: 'fab fa-python'
            },
            {
                id: 'louna-dark-theme',
                name: 'Louna Dark Theme',
                author: 'Louna Design',
                description: 'Thème sombre officiel de Louna avec des couleurs optimisées pour le confort visuel.',
                version: '1.5.0',
                downloads: 34567,
                rating: 4.8,
                category: 'themes',
                tags: ['theme', 'dark', 'louna'],
                installed: true,
                icon: 'fas fa-palette'
            },
            {
                id: 'javascript-snippets',
                name: 'JavaScript Snippets',
                author: 'JS Community',
                description: 'Collection de snippets JavaScript modernes avec support ES6+ et frameworks populaires.',
                version: '2.3.1',
                downloads: 67890,
                rating: 4.6,
                category: 'snippets',
                tags: ['javascript', 'snippets', 'es6'],
                installed: false,
                popular: true,
                icon: 'fab fa-js-square'
            },
            {
                id: 'prettier-formatter',
                name: 'Prettier Code Formatter',
                author: 'Prettier Team',
                description: 'Formatage automatique du code pour maintenir un style cohérent dans tous vos projets.',
                version: '3.0.0',
                downloads: 123456,
                rating: 4.9,
                category: 'formatters',
                tags: ['formatter', 'prettier', 'style'],
                installed: true,
                popular: true,
                icon: 'fas fa-align-left'
            },
            {
                id: 'eslint-linter',
                name: 'ESLint',
                author: 'ESLint Team',
                description: 'Détection et correction automatique des erreurs et problèmes de style dans votre code JavaScript.',
                version: '4.2.1',
                downloads: 98765,
                rating: 4.5,
                category: 'linters',
                tags: ['linter', 'eslint', 'javascript'],
                installed: false,
                icon: 'fas fa-bug'
            },
            {
                id: 'git-lens',
                name: 'GitLens',
                author: 'GitKraken',
                description: 'Visualisation avancée de l\'historique Git avec annotations de code et navigation dans les commits.',
                version: '12.1.0',
                downloads: 156789,
                rating: 4.8,
                category: 'git',
                tags: ['git', 'version-control', 'history'],
                installed: true,
                popular: true,
                icon: 'fab fa-git-alt'
            },
            {
                id: 'thermal-memory-sync',
                name: 'Thermal Memory Sync',
                author: 'Louna Team',
                description: 'Synchronisation automatique du code avec la mémoire thermique de Louna pour un apprentissage continu.',
                version: '1.2.0',
                downloads: 8934,
                rating: 4.7,
                category: 'ai',
                tags: ['louna', 'thermal-memory', 'sync'],
                installed: false,
                new: true,
                icon: 'fas fa-fire'
            }
        ];

        document.addEventListener('DOMContentLoaded', () => {
            extensions = [...mockExtensions];
            filteredExtensions = [...extensions];
            
            setupEventListeners();
            renderExtensions();
            updateInstalledCount();
        });

        function setupEventListeners() {
            // Recherche
            document.getElementById('search-input').addEventListener('input', (e) => {
                searchQuery = e.target.value.toLowerCase();
                filterExtensions();
            });

            // Filtres
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    currentFilter = tab.dataset.filter;
                    filterExtensions();
                });
            });

            // Catégories
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', () => {
                    document.querySelectorAll('.category-item').forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                    currentCategory = item.dataset.category;
                    filterExtensions();
                });
            });

            // Actualiser
            document.getElementById('refresh-extensions-btn').addEventListener('click', refreshExtensions);
        }

        function filterExtensions() {
            filteredExtensions = extensions.filter(ext => {
                // Filtre par recherche
                if (searchQuery && !ext.name.toLowerCase().includes(searchQuery) && 
                    !ext.description.toLowerCase().includes(searchQuery) &&
                    !ext.tags.some(tag => tag.toLowerCase().includes(searchQuery))) {
                    return false;
                }

                // Filtre par statut
                if (currentFilter === 'installed' && !ext.installed) return false;
                if (currentFilter === 'popular' && !ext.popular) return false;

                // Filtre par catégorie
                if (currentCategory !== 'all' && ext.category !== currentCategory) return false;

                return true;
            });

            renderExtensions();
        }

        function renderExtensions() {
            const grid = document.getElementById('extensions-grid');
            
            if (filteredExtensions.length === 0) {
                grid.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>Aucune extension trouvée</h3>
                        <p>Essayez de modifier vos critères de recherche</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredExtensions.map(ext => `
                <div class="extension-card" data-id="${ext.id}">
                    <div class="extension-header">
                        <div class="extension-icon">
                            <i class="${ext.icon}"></i>
                        </div>
                        <div class="extension-info">
                            <div class="extension-name">
                                ${ext.name}
                                ${ext.installed ? '<span class="installed-badge">Installé</span>' : ''}
                                ${ext.popular ? '<span class="popular-badge">Populaire</span>' : ''}
                                ${ext.new ? '<span class="new-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="extension-author">par ${ext.author}</div>
                        </div>
                    </div>
                    
                    <div class="extension-description">${ext.description}</div>
                    
                    <div class="extension-tags">
                        ${ext.tags.map(tag => `<span class="extension-tag">${tag}</span>`).join('')}
                    </div>
                    
                    <div class="extension-stats">
                        <div class="extension-stat">
                            <i class="fas fa-download"></i>
                            <span>${ext.downloads.toLocaleString()}</span>
                        </div>
                        <div class="extension-rating">
                            <div class="stars">
                                ${'★'.repeat(Math.floor(ext.rating))}${'☆'.repeat(5 - Math.floor(ext.rating))}
                            </div>
                            <span>${ext.rating}</span>
                        </div>
                        <div class="extension-stat">
                            <i class="fas fa-tag"></i>
                            <span>v${ext.version}</span>
                        </div>
                    </div>
                    
                    <div class="extension-actions">
                        ${ext.installed ? 
                            `<button class="extension-btn uninstall" onclick="uninstallExtension('${ext.id}')">
                                <i class="fas fa-trash"></i> Désinstaller
                            </button>
                            <button class="extension-btn configure" onclick="configureExtension('${ext.id}')">
                                <i class="fas fa-cog"></i> Configurer
                            </button>` :
                            `<button class="extension-btn install" onclick="installExtension('${ext.id}')">
                                <i class="fas fa-download"></i> Installer
                            </button>`
                        }
                    </div>
                </div>
            `).join('');
        }

        function installExtension(id) {
            const extension = extensions.find(ext => ext.id === id);
            if (extension) {
                extension.installed = true;
                showNotification(`Extension "${extension.name}" installée avec succès`, 'success');
                renderExtensions();
                updateInstalledCount();
                
                // Ajouter à la mémoire thermique
                addToThermalMemory(`extension_install_${id}`, `Installation de l'extension ${extension.name} - ${extension.description}`);
            }
        }

        function uninstallExtension(id) {
            const extension = extensions.find(ext => ext.id === id);
            if (extension && confirm(`Êtes-vous sûr de vouloir désinstaller "${extension.name}" ?`)) {
                extension.installed = false;
                showNotification(`Extension "${extension.name}" désinstallée`, 'info');
                renderExtensions();
                updateInstalledCount();
            }
        }

        function configureExtension(id) {
            const extension = extensions.find(ext => ext.id === id);
            if (extension) {
                showNotification(`Configuration de "${extension.name}" ouverte`, 'info');
                // Ici on pourrait ouvrir une modal de configuration
            }
        }

        function refreshExtensions() {
            showNotification('Actualisation des extensions...', 'info');
            
            // Simulation d'un chargement
            const grid = document.getElementById('extensions-grid');
            grid.innerHTML = '<div class="loading-spinner"><div class="spinner"></div></div>';
            
            setTimeout(() => {
                renderExtensions();
                showNotification('Extensions actualisées', 'success');
            }, 1500);
        }

        function updateInstalledCount() {
            const installedCount = extensions.filter(ext => ext.installed).length;
            document.getElementById('installed-count').textContent = installedCount;
        }

        async function addToThermalMemory(key, data) {
            try {
                const response = await fetch('/api/thermal/memory/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        key: key,
                        data: data,
                        importance: 0.6,
                        category: 'extensions'
                    })
                });
                
                const result = await response.json();
                console.log('Ajouté à la mémoire thermique:', result);
            } catch (error) {
                console.error('Erreur lors de l\'ajout à la mémoire thermique:', error);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: ${type === 'success' ? 'var(--success)' : type === 'info' ? 'var(--info)' : 'var(--warning)'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
    <script src="js/native-app.js"></script>
</body>
</html>
