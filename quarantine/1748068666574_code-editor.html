<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Code - Louna</title>
    <link rel="stylesheet" href="css/native-app.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js"></script>
    <style>
        .editor-layout {
            display: flex;
            height: calc(100vh - 60px);
            margin-top: 60px;
        }

        .editor-sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.1);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
        }

        .editor-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 40px;
            align-items: center;
        }

        .editor-tab {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .editor-tab.active {
            background: rgba(255, 255, 255, 0.2);
            border-bottom: 2px solid #ff69b4;
        }

        .editor-tab:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .editor-content {
            flex: 1;
            position: relative;
        }

        #monaco-editor {
            width: 100%;
            height: 100%;
        }

        .file-tree {
            padding: 20px;
        }

        .file-tree h3 {
            margin-bottom: 15px;
            color: #ff69b4;
            font-size: 16px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #ffffff;
            font-weight: 600;
            font-size: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .file-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .file-item.active {
            background: rgba(255, 105, 180, 0.3);
        }

        .file-icon {
            width: 20px;
            text-align: center;
        }

        .editor-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .toolbar-left, .toolbar-right {
            display: flex;
            gap: 10px;
        }

        .toolbar-btn {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .toolbar-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: rgba(255, 105, 180, 0.8);
            color: white;
            font-size: 12px;
        }

        .terminal-panel {
            height: 200px;
            background: rgba(0, 0, 0, 0.8);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #00ff00;
            overflow-y: auto;
            display: none;
        }

        .terminal-panel.visible {
            display: block;
        }

        .terminal-line {
            margin-bottom: 5px;
        }

        .terminal-prompt {
            color: #ff69b4;
        }

        .new-file-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 30px;
            width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-content h3 {
            margin-bottom: 20px;
            color: #ff69b4;
        }

        .modal-content input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            margin-bottom: 20px;
        }

        .modal-content input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background: #ff69b4;
            color: white;
        }

        .modal-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring QI</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item active">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
        </div>
    </nav>

    <!-- Layout principal de l'éditeur -->
    <div class="editor-layout">
        <!-- Sidebar avec explorateur de fichiers -->
        <div class="editor-sidebar">
            <div class="file-tree">
                <h3><i class="fas fa-folder-open"></i> Explorateur</h3>

                <div class="file-item active" data-file="main.js" data-language="javascript">
                    <i class="fab fa-js-square file-icon" style="color: #f7df1e;"></i>
                    <span>main.js</span>
                </div>

                <div class="file-item" data-file="style.css" data-language="css">
                    <i class="fab fa-css3-alt file-icon" style="color: #1572b6;"></i>
                    <span>style.css</span>
                </div>

                <div class="file-item" data-file="index.html" data-language="html">
                    <i class="fab fa-html5 file-icon" style="color: #e34f26;"></i>
                    <span>index.html</span>
                </div>

                <div class="file-item" data-file="README.md" data-language="markdown">
                    <i class="fab fa-markdown file-icon" style="color: #083fa1;"></i>
                    <span>README.md</span>
                </div>

                <div class="file-item" data-file="package.json" data-language="json">
                    <i class="fas fa-cog file-icon" style="color: #68217a;"></i>
                    <span>package.json</span>
                </div>

                <div class="file-item" data-file="server.js" data-language="javascript">
                    <i class="fab fa-node-js file-icon" style="color: #68a063;"></i>
                    <span>server.js</span>
                </div>
            </div>
        </div>

        <!-- Zone principale de l'éditeur -->
        <div class="editor-main">
            <!-- Barre d'outils -->
            <div class="editor-toolbar">
                <div class="toolbar-left">
                    <button class="toolbar-btn" id="new-file-btn">
                        <i class="fas fa-plus"></i> Nouveau
                    </button>
                    <button class="toolbar-btn" id="save-btn">
                        <i class="fas fa-save"></i> Sauvegarder
                    </button>
                    <button class="toolbar-btn" id="run-btn">
                        <i class="fas fa-play"></i> Exécuter
                    </button>
                </div>
                <div class="toolbar-right">
                    <button class="toolbar-btn" id="terminal-btn">
                        <i class="fas fa-terminal"></i> Terminal
                    </button>
                    <button class="toolbar-btn" id="format-btn">
                        <i class="fas fa-magic"></i> Formater
                    </button>
                </div>
            </div>

            <!-- Onglets des fichiers -->
            <div class="editor-tabs" id="editor-tabs">
                <div class="editor-tab active" data-file="main.js">
                    <i class="fab fa-js-square" style="color: #f7df1e;"></i>
                    <span>main.js</span>
                    <i class="fas fa-times" style="margin-left: 8px; cursor: pointer;" onclick="closeTab('main.js')"></i>
                </div>
            </div>

            <!-- Zone d'édition Monaco -->
            <div class="editor-content">
                <div id="monaco-editor"></div>
            </div>

            <!-- Barre de statut -->
            <div class="status-bar">
                <div class="status-left">
                    <span id="cursor-position">Ligne 1, Col 1</span>
                    <span id="file-language">JavaScript</span>
                    <span id="file-encoding">UTF-8</span>
                </div>
                <div class="status-right">
                    <span id="file-size">0 octets</span>
                    <span id="last-saved">Non sauvegardé</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Terminal panel -->
    <div class="terminal-panel" id="terminal-panel">
        <div class="terminal-line">
            <span class="terminal-prompt">louna@editor:~$</span> Bienvenue dans l'éditeur de code Louna
        </div>
        <div class="terminal-line">
            <span class="terminal-prompt">louna@editor:~$</span> Tapez 'help' pour voir les commandes disponibles
        </div>
        <div class="terminal-line" id="terminal-input-line">
            <span class="terminal-prompt">louna@editor:~$</span>
            <input type="text" id="terminal-input" style="background: transparent; border: none; color: #00ff00; outline: none; font-family: 'Courier New', monospace;">
        </div>
    </div>

    <!-- Modal pour nouveau fichier -->
    <div class="new-file-modal" id="new-file-modal">
        <div class="modal-content">
            <h3><i class="fas fa-file-plus"></i> Nouveau Fichier</h3>
            <input type="text" id="new-file-name" placeholder="Nom du fichier (ex: script.js)">
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="closeNewFileModal()">Annuler</button>
                <button class="modal-btn primary" onclick="createNewFile()">Créer</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let editor = null;
        let currentFile = 'main.js';
        let openFiles = new Map();
        let isTerminalVisible = false;

        // Contenu par défaut des fichiers
        const defaultFileContents = {
            'main.js': `// Fichier principal JavaScript
console.log('Bienvenue dans l\'éditeur de code Louna!');

// Fonction d'exemple
function helloWorld() {
    return 'Hello, World!';
}

// Appel de la fonction
console.log(helloWorld());`,
            'style.css': `/* Feuille de style CSS */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}`,
            'index.html': `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Projet</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Bienvenue dans mon projet</h1>
        <p>Ceci est un exemple de page HTML.</p>
    </div>
    <script src="main.js"></script>
</body>
</html>`,
            'README.md': `# Mon Projet

## Description
Ceci est un projet d'exemple créé avec l'éditeur de code Louna.

## Fonctionnalités
- Édition de code avec coloration syntaxique
- Support multi-langages
- Terminal intégré
- Sauvegarde automatique

## Installation
\`\`\`bash
npm install
\`\`\`

## Utilisation
\`\`\`bash
npm start
\`\`\``,
            'package.json': `{
  "name": "mon-projet",
  "version": "1.0.0",
  "description": "Projet créé avec l'éditeur Louna",
  "main": "main.js",
  "scripts": {
    "start": "node main.js",
    "test": "echo \\"Error: no test specified\\" && exit 1"
  },
  "keywords": ["louna", "editor", "javascript"],
  "author": "Utilisateur Louna",
  "license": "MIT"
}`,
            'server.js': `// Serveur Node.js simple
const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    console.log('Requête reçue:', req.url);

    if (req.url === '/') {
        fs.readFile('index.html', (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('Fichier non trouvé');
                return;
            }
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
        });
    } else {
        res.writeHead(404);
        res.end('Page non trouvée');
    }
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(\`Serveur démarré sur le port \${PORT}\`);
});`
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Initialisation de l\'éditeur de code...');

            // Initialiser Monaco Editor
            initMonacoEditor();

            // Configurer les événements
            setupEventListeners();

            console.log('✅ Éditeur de code initialisé');
        });

        // Initialiser Monaco Editor
        function initMonacoEditor() {
            require.config({ paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs' } });

            require(['vs/editor/editor.main'], function () {
                editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                    value: defaultFileContents[currentFile],
                    language: 'javascript',
                    theme: 'vs-dark',
                    automaticLayout: true,
                    fontSize: 14,
                    minimap: { enabled: true },
                    scrollBeyondLastLine: false,
                    wordWrap: 'on'
                });

                // Sauvegarder le contenu initial
                openFiles.set(currentFile, {
                    content: defaultFileContents[currentFile],
                    language: 'javascript',
                    modified: false
                });

                // Écouter les changements
                editor.onDidChangeModelContent(() => {
                    const content = editor.getValue();
                    const fileInfo = openFiles.get(currentFile);
                    if (fileInfo) {
                        fileInfo.content = content;
                        fileInfo.modified = true;
                        updateStatusBar();
                    }
                });

                // Écouter les changements de position du curseur
                editor.onDidChangeCursorPosition((e) => {
                    updateCursorPosition(e.position);
                });

                updateStatusBar();
            });
        }

        // Configurer les événements
        function setupEventListeners() {
            // Boutons de la barre d'outils
            document.getElementById('new-file-btn')?.addEventListener('click', showNewFileModal);
            document.getElementById('save-btn')?.addEventListener('click', saveCurrentFile);
            document.getElementById('run-btn')?.addEventListener('click', runCurrentFile);
            document.getElementById('terminal-btn')?.addEventListener('click', toggleTerminal);
            document.getElementById('format-btn')?.addEventListener('click', formatCode);

            // Fichiers dans l'explorateur
            document.querySelectorAll('.file-item').forEach(item => {
                item.addEventListener('click', () => {
                    const fileName = item.dataset.file;
                    const language = item.dataset.language;
                    openFile(fileName, language);
                });
            });

            // Terminal
            const terminalInput = document.getElementById('terminal-input');
            if (terminalInput) {
                terminalInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        executeTerminalCommand(terminalInput.value);
                        terminalInput.value = '';
                    }
                });
            }

            // Bouton actualiser
            document.getElementById('refresh-btn')?.addEventListener('click', (e) => {
                e.preventDefault();
                location.reload();
            });
        }

        // Ouvrir un fichier
        function openFile(fileName, language) {
            // Sauvegarder le fichier actuel
            if (editor && currentFile) {
                const content = editor.getValue();
                const fileInfo = openFiles.get(currentFile);
                if (fileInfo) {
                    fileInfo.content = content;
                }
            }

            // Charger le nouveau fichier
            currentFile = fileName;

            if (!openFiles.has(fileName)) {
                openFiles.set(fileName, {
                    content: defaultFileContents[fileName] || '// Nouveau fichier',
                    language: language,
                    modified: false
                });
            }

            const fileInfo = openFiles.get(fileName);

            if (editor) {
                editor.setValue(fileInfo.content);
                monaco.editor.setModelLanguage(editor.getModel(), language);
            }

            // Mettre à jour l'interface
            updateActiveFile(fileName);
            updateActiveTab(fileName);
            updateStatusBar();
        }

        // Mettre à jour le fichier actif dans l'explorateur
        function updateActiveFile(fileName) {
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.file === fileName) {
                    item.classList.add('active');
                }
            });
        }

        // Mettre à jour l'onglet actif
        function updateActiveTab(fileName) {
            // Créer un nouvel onglet s'il n'existe pas
            const existingTab = document.querySelector(`[data-file="${fileName}"]`);
            if (!existingTab) {
                createTab(fileName);
            }

            // Mettre à jour l'état actif
            document.querySelectorAll('.editor-tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.file === fileName) {
                    tab.classList.add('active');
                }
            });
        }

        // Créer un nouvel onglet
        function createTab(fileName) {
            const tabsContainer = document.getElementById('editor-tabs');
            const fileInfo = openFiles.get(fileName);

            const tab = document.createElement('div');
            tab.className = 'editor-tab';
            tab.dataset.file = fileName;

            const icon = getFileIcon(fileName);
            tab.innerHTML = `
                ${icon}
                <span>${fileName}</span>
                <i class="fas fa-times" style="margin-left: 8px; cursor: pointer;" onclick="closeTab('${fileName}')"></i>
            `;

            tab.addEventListener('click', (e) => {
                if (!e.target.classList.contains('fa-times')) {
                    openFile(fileName, fileInfo.language);
                }
            });

            tabsContainer.appendChild(tab);
        }

        // Obtenir l'icône d'un fichier
        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop();
            switch (ext) {
                case 'js': return '<i class="fab fa-js-square" style="color: #f7df1e;"></i>';
                case 'css': return '<i class="fab fa-css3-alt" style="color: #1572b6;"></i>';
                case 'html': return '<i class="fab fa-html5" style="color: #e34f26;"></i>';
                case 'md': return '<i class="fab fa-markdown" style="color: #083fa1;"></i>';
                case 'json': return '<i class="fas fa-cog" style="color: #68217a;"></i>';
                default: return '<i class="fas fa-file-code"></i>';
            }
        }

        // Fermer un onglet
        function closeTab(fileName) {
            const tab = document.querySelector(`[data-file="${fileName}"]`);
            if (tab) {
                tab.remove();
            }

            openFiles.delete(fileName);

            // Si c'était le fichier actuel, ouvrir un autre fichier
            if (currentFile === fileName) {
                const remainingTabs = document.querySelectorAll('.editor-tab');
                if (remainingTabs.length > 0) {
                    const nextFile = remainingTabs[0].dataset.file;
                    const fileInfo = openFiles.get(nextFile);
                    openFile(nextFile, fileInfo.language);
                } else {
                    // Ouvrir main.js par défaut
                    openFile('main.js', 'javascript');
                }
            }
        }

        // Sauvegarder le fichier actuel
        function saveCurrentFile() {
            if (editor && currentFile) {
                const content = editor.getValue();
                const fileInfo = openFiles.get(currentFile);
                if (fileInfo) {
                    fileInfo.content = content;
                    fileInfo.modified = false;
                    updateStatusBar();

                    // Simuler la sauvegarde
                    addTerminalLine(`Fichier ${currentFile} sauvegardé avec succès`);
                }
            }
        }

        // Exécuter le fichier actuel
        function runCurrentFile() {
            if (currentFile.endsWith('.js')) {
                addTerminalLine(`Exécution de ${currentFile}...`);
                addTerminalLine('> node ' + currentFile);

                // Simuler l'exécution
                setTimeout(() => {
                    addTerminalLine('Fichier exécuté avec succès');
                }, 1000);
            } else {
                addTerminalLine(`Impossible d'exécuter ${currentFile} - Type de fichier non supporté`);
            }
        }

        // Formater le code
        function formatCode() {
            if (editor) {
                editor.getAction('editor.action.formatDocument').run();
                addTerminalLine('Code formaté');
            }
        }

        // Basculer le terminal
        function toggleTerminal() {
            const terminal = document.getElementById('terminal-panel');
            isTerminalVisible = !isTerminalVisible;

            if (isTerminalVisible) {
                terminal.classList.add('visible');
                document.querySelector('.editor-layout').style.height = 'calc(100vh - 260px)';
            } else {
                terminal.classList.remove('visible');
                document.querySelector('.editor-layout').style.height = 'calc(100vh - 60px)';
            }

            // Redimensionner l'éditeur
            if (editor) {
                editor.layout();
            }
        }

        // Exécuter une commande terminal
        function executeTerminalCommand(command) {
            addTerminalLine(`louna@editor:~$ ${command}`);

            switch (command.toLowerCase()) {
                case 'help':
                    addTerminalLine('Commandes disponibles:');
                    addTerminalLine('  help - Afficher cette aide');
                    addTerminalLine('  clear - Effacer le terminal');
                    addTerminalLine('  ls - Lister les fichiers');
                    addTerminalLine('  save - Sauvegarder le fichier actuel');
                    break;
                case 'clear':
                    clearTerminal();
                    break;
                case 'ls':
                    addTerminalLine('Fichiers disponibles:');
                    openFiles.forEach((info, fileName) => {
                        addTerminalLine(`  ${fileName}`);
                    });
                    break;
                case 'save':
                    saveCurrentFile();
                    break;
                default:
                    addTerminalLine(`Commande non reconnue: ${command}`);
                    addTerminalLine('Tapez "help" pour voir les commandes disponibles');
            }
        }

        // Ajouter une ligne au terminal
        function addTerminalLine(text) {
            const terminal = document.getElementById('terminal-panel');
            const inputLine = document.getElementById('terminal-input-line');

            const newLine = document.createElement('div');
            newLine.className = 'terminal-line';
            newLine.textContent = text;

            terminal.insertBefore(newLine, inputLine);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // Effacer le terminal
        function clearTerminal() {
            const terminal = document.getElementById('terminal-panel');
            const lines = terminal.querySelectorAll('.terminal-line:not(#terminal-input-line)');
            lines.forEach(line => line.remove());
        }

        // Mettre à jour la barre de statut
        function updateStatusBar() {
            if (editor) {
                const position = editor.getPosition();
                document.getElementById('cursor-position').textContent = `Ligne ${position.lineNumber}, Col ${position.column}`;

                const content = editor.getValue();
                document.getElementById('file-size').textContent = `${content.length} octets`;

                const fileInfo = openFiles.get(currentFile);
                if (fileInfo) {
                    document.getElementById('file-language').textContent = fileInfo.language.charAt(0).toUpperCase() + fileInfo.language.slice(1);
                    document.getElementById('last-saved').textContent = fileInfo.modified ? 'Non sauvegardé' : 'Sauvegardé';
                }
            }
        }

        // Mettre à jour la position du curseur
        function updateCursorPosition(position) {
            document.getElementById('cursor-position').textContent = `Ligne ${position.lineNumber}, Col ${position.column}`;
        }

        // Afficher le modal nouveau fichier
        function showNewFileModal() {
            document.getElementById('new-file-modal').style.display = 'flex';
            document.getElementById('new-file-name').focus();
        }

        // Fermer le modal nouveau fichier
        function closeNewFileModal() {
            document.getElementById('new-file-modal').style.display = 'none';
            document.getElementById('new-file-name').value = '';
        }

        // Créer un nouveau fichier
        function createNewFile() {
            const fileName = document.getElementById('new-file-name').value.trim();

            if (!fileName) {
                alert('Veuillez entrer un nom de fichier');
                return;
            }

            if (openFiles.has(fileName)) {
                alert('Ce fichier existe déjà');
                return;
            }

            // Déterminer le langage basé sur l'extension
            const ext = fileName.split('.').pop();
            let language = 'plaintext';
            switch (ext) {
                case 'js': language = 'javascript'; break;
                case 'css': language = 'css'; break;
                case 'html': language = 'html'; break;
                case 'md': language = 'markdown'; break;
                case 'json': language = 'json'; break;
                case 'py': language = 'python'; break;
                case 'java': language = 'java'; break;
                case 'cpp': case 'c': language = 'cpp'; break;
            }

            // Créer le fichier
            openFiles.set(fileName, {
                content: `// Nouveau fichier: ${fileName}\n`,
                language: language,
                modified: true
            });

            // Ouvrir le fichier
            openFile(fileName, language);

            // Fermer le modal
            closeNewFileModal();

            addTerminalLine(`Nouveau fichier créé: ${fileName}`);
        }

        // Gestion des raccourcis clavier
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        saveCurrentFile();
                        break;
                    case 'n':
                        e.preventDefault();
                        showNewFileModal();
                        break;
                    case '`':
                        e.preventDefault();
                        toggleTerminal();
                        break;
                }
            }
        });
    </script>

    <script src="js/native-app.js"></script>
