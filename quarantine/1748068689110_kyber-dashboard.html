<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Accélérateurs Kyber</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        /* Styles spécifiques au tableau de bord Kyber */
        .accelerator-card {
            background: linear-gradient(135deg, var(--bg-card), rgba(13, 71, 161, 0.85)); /* Plus opaque pour un meilleur contraste */
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1); /* Ajouter une bordure pour améliorer la visibilité */
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2); /* Bordure plus visible au survol */
        }

        .accelerator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .accelerator-title {
            font-size: 18px;
            font-weight: bold;
        }

        .accelerator-controls {
            display: flex;
            align-items: center;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            margin-left: 10px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--accent);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .boost-slider {
            width: 100%;
            margin: 15px 0;
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            outline: none;
        }

        .boost-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
            box-shadow: 0 0 5px var(--accent-glow);
        }

        .boost-value {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }

        .boost-label {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .metrics-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 15px;
        }

        .metric-item {
            background-color: rgba(0, 0, 0, 0.3); /* Plus foncé pour un meilleur contraste */
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1); /* Ajouter une bordure pour améliorer la visibilité */
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: white; /* Blanc pour un meilleur contraste */
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Ajouter une ombre pour améliorer la lisibilité */
        }

        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9); /* Plus opaque pour un meilleur contraste */
            font-weight: 500; /* Légèrement plus gras pour une meilleure lisibilité */
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item active">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Accélérateurs Kyber</h1>
                <p class="interface-subtitle">Contrôle et optimisation des performances</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="kyber-status-indicator"></div>
                <span class="status-text" id="kyber-status-text">Chargement...</span>

                <button class="action-button" id="reset-accelerators-btn">
                    <i class="fas fa-redo"></i> Réinitialiser
                </button>
                <button class="action-button" id="optimize-btn">
                    <i class="fas fa-magic"></i> Optimiser
                </button>
            </div>
        </div>

        <!-- Grille principale -->
        <div class="grid-container">
            <!-- Carte des statistiques -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line card-icon"></i>
                        <h2 class="card-title">Performances globales</h2>
                    </div>

                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-label">Boost moyen</div>
                            <div class="stat-value" id="avg-boost">x2.6</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Efficacité</div>
                            <div class="stat-value" id="efficiency">92%</div>
                            <div class="stat-badge" id="efficiency-status">Optimale</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Événements de stabilité</div>
                            <div class="stat-value" id="stability-events">0</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Dernière mise à jour</div>
                            <div class="stat-value" id="last-update-time">Jamais</div>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <canvas id="performance-chart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Carte des accélérateurs -->
            <div class="grid-item">
                <!-- Accélérateur Réflexif -->
                <div class="card accelerator-card">
                    <div class="accelerator-header">
                        <div class="accelerator-title">
                            <i class="fas fa-microchip card-icon"></i>
                            Accélérateur Réflexif
                        </div>
                        <div class="accelerator-controls">
                            <span id="reflexive-status">Actif</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="reflexive-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <p>Améliore la vitesse de traitement des informations</p>

                    <div class="boost-value" id="reflexive-boost-value">x3.1</div>

                    <input type="range" min="1" max="5" step="0.1" value="3.1" class="boost-slider" id="reflexive-boost-slider">

                    <div class="boost-label">
                        <span>Min</span>
                        <span>Max</span>
                    </div>

                    <div class="metrics-container">
                        <div class="metric-item">
                            <div class="metric-value" id="reflexive-stability">92%</div>
                            <div class="metric-label">Stabilité</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="reflexive-energy">100%</div>
                            <div class="metric-label">Énergie</div>
                        </div>
                    </div>
                </div>

                <!-- Accélérateur Thermique -->
                <div class="card accelerator-card">
                    <div class="accelerator-header">
                        <div class="accelerator-title">
                            <i class="fas fa-fire card-icon"></i>
                            Accélérateur Thermique
                        </div>
                        <div class="accelerator-controls">
                            <span id="thermal-status">Actif</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="thermal-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <p>Optimise les transferts entre zones de mémoire thermique</p>

                    <div class="boost-value" id="thermal-boost-value">x2.7</div>

                    <input type="range" min="1" max="5" step="0.1" value="2.7" class="boost-slider" id="thermal-boost-slider">

                    <div class="boost-label">
                        <span>Min</span>
                        <span>Max</span>
                    </div>

                    <div class="metrics-container">
                        <div class="metric-item">
                            <div class="metric-value" id="thermal-stability">88%</div>
                            <div class="metric-label">Stabilité</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="thermal-energy">100%</div>
                            <div class="metric-label">Énergie</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte du connecteur et des graphiques -->
            <div class="grid-item">
                <!-- Connecteur Thermique -->
                <div class="card accelerator-card">
                    <div class="accelerator-header">
                        <div class="accelerator-title">
                            <i class="fas fa-project-diagram card-icon"></i>
                            Connecteur Thermique
                        </div>
                        <div class="accelerator-controls">
                            <span id="connector-status">Actif</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="connector-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <p>Facilite les connexions entre informations dans différentes zones</p>

                    <div class="boost-value" id="connector-boost-value">x2.1</div>

                    <input type="range" min="1" max="5" step="0.1" value="2.1" class="boost-slider" id="connector-boost-slider">

                    <div class="boost-label">
                        <span>Min</span>
                        <span>Max</span>
                    </div>

                    <div class="metrics-container">
                        <div class="metric-item">
                            <div class="metric-value" id="connector-stability">95%</div>
                            <div class="metric-label">Stabilité</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="connector-energy">100%</div>
                            <div class="metric-label">Énergie</div>
                        </div>
                    </div>
                </div>

                <!-- Graphique de température -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-temperature-high card-icon"></i>
                        <h2 class="card-title">Température des accélérateurs</h2>
                    </div>

                    <canvas id="temperature-chart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        // Initialiser les accélérateurs Kyber
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser les accélérateurs Kyber
            await window.kyberAccelerators.initialize();

            // Mettre à jour l'interface
            updateInterface();

            // Ajouter des écouteurs pour les mises à jour
            window.kyberAccelerators.addListener(updateAcceleratorsUI);

            // Initialiser les graphiques
            initPerformanceChart();
            initTemperatureChart();

            // Ajouter des écouteurs d'événements pour les contrôles
            setupEventListeners();
        });

        // Mise à jour de l'interface
        function updateInterface() {
            // Mettre à jour les statuts des accélérateurs
            updateAcceleratorStatus('reflexive', window.kyberAccelerators?.getAccelerator('reflexive'));
            updateAcceleratorStatus('thermal', window.kyberAccelerators?.getAccelerator('thermal'));
            updateAcceleratorStatus('connector', window.kyberAccelerators?.getAccelerator('connector'));

            // Mettre à jour les statistiques globales
            updateGlobalStats();
        }

        function updateAcceleratorsUI(type, data) {
            console.log(`🔄 Mise à jour de l'accélérateur ${type}:`, data);

            if (data) {
                // Mettre à jour les valeurs affichées
                const boostElement = document.getElementById(`${type}-boost-value`);
                const stabilityElement = document.getElementById(`${type}-stability`);
                const energyElement = document.getElementById(`${type}-energy`);

                if (boostElement) boostElement.textContent = `x${data.boost?.toFixed(1) || '2.0'}`;
                if (stabilityElement) stabilityElement.textContent = `${Math.round(data.stability * 100 || 90)}%`;
                if (energyElement) energyElement.textContent = `${Math.round(data.energy * 100 || 100)}%`;
            }
        }

        function initPerformanceChart() {
            const ctx = document.getElementById('performance-chart');
            if (!ctx) return;

            // Données simulées pour le graphique de performance
            const performanceData = {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                datasets: [{
                    label: 'Boost Moyen',
                    data: [2.1, 2.3, 2.8, 2.6, 2.9, 2.7],
                    borderColor: '#ff69b4',
                    backgroundColor: 'rgba(255, 105, 180, 0.1)',
                    tension: 0.4
                }]
            };

            new Chart(ctx, {
                type: 'line',
                data: performanceData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initTemperatureChart() {
            const ctx = document.getElementById('temperature-chart');
            if (!ctx) return;

            // Données simulées pour le graphique de température
            const tempData = {
                labels: ['Réflexif', 'Thermique', 'Connecteur'],
                datasets: [{
                    label: 'Température (°C)',
                    data: [45, 52, 38],
                    backgroundColor: [
                        'rgba(255, 107, 107, 0.8)',
                        'rgba(255, 167, 38, 0.8)',
                        'rgba(66, 165, 245, 0.8)'
                    ],
                    borderColor: [
                        '#ff6b6b',
                        '#ffa726',
                        '#42a5f5'
                    ],
                    borderWidth: 2
                }]
            };

            new Chart(ctx, {
                type: 'doughnut',
                data: tempData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    }
                }
            });
        }

        function setupEventListeners() {
            // Écouteurs pour les toggles d'accélérateurs
            document.getElementById('reflexive-toggle')?.addEventListener('change', function(e) {
                toggleAccelerator('reflexive', e.target.checked);
            });

            document.getElementById('thermal-toggle')?.addEventListener('change', function(e) {
                toggleAccelerator('thermal', e.target.checked);
            });

            document.getElementById('connector-toggle')?.addEventListener('change', function(e) {
                toggleAccelerator('connector', e.target.checked);
            });

            // Écouteurs pour les sliders de boost
            document.getElementById('reflexive-boost-slider')?.addEventListener('input', function(e) {
                updateBoostValue('reflexive', e.target.value);
            });

            document.getElementById('thermal-boost-slider')?.addEventListener('input', function(e) {
                updateBoostValue('thermal', e.target.value);
            });

            document.getElementById('connector-boost-slider')?.addEventListener('input', function(e) {
                updateBoostValue('connector', e.target.value);
            });

            // Boutons d'action
            document.getElementById('reset-accelerators-btn')?.addEventListener('click', resetAccelerators);
            document.getElementById('optimize-btn')?.addEventListener('click', optimizeAccelerators);
        }

        // Fonctions utilitaires
        function updateAcceleratorStatus(type, accelerator) {
            const statusElement = document.getElementById(`${type}-status`);
            if (statusElement) {
                statusElement.textContent = accelerator?.active ? 'Actif' : 'Inactif';
                statusElement.style.color = accelerator?.active ? '#4caf50' : '#f44336';
            }
        }

        function updateGlobalStats() {
            // Calculer les statistiques moyennes
            const avgBoost = 2.6;
            const efficiency = 92;
            const stabilityEvents = 0;

            document.getElementById('avg-boost').textContent = `x${avgBoost}`;
            document.getElementById('efficiency').textContent = `${efficiency}%`;
            document.getElementById('stability-events').textContent = stabilityEvents;
            document.getElementById('last-update-time').textContent = new Date().toLocaleTimeString();
        }

        function toggleAccelerator(type, enabled) {
            console.log(`🔄 ${enabled ? 'Activation' : 'Désactivation'} de l'accélérateur ${type}`);

            if (window.kyberAccelerators) {
                if (enabled) {
                    window.kyberAccelerators.enableAccelerator(type);
                } else {
                    window.kyberAccelerators.disableAccelerator(type);
                }
            }

            updateInterface();
        }

        function updateBoostValue(type, value) {
            const boostValueElement = document.getElementById(`${type}-boost-value`);
            if (boostValueElement) {
                boostValueElement.textContent = `x${parseFloat(value).toFixed(1)}`;
            }

            if (window.kyberAccelerators) {
                window.kyberAccelerators.setBoost(type, parseFloat(value));
            }
        }

        function resetAccelerators() {
            console.log('🔄 Réinitialisation des accélérateurs...');

            // Réinitialiser les valeurs par défaut
            document.getElementById('reflexive-boost-slider').value = 3.1;
            document.getElementById('thermal-boost-slider').value = 2.7;
            document.getElementById('connector-boost-slider').value = 2.1;

            updateBoostValue('reflexive', 3.1);
            updateBoostValue('thermal', 2.7);
            updateBoostValue('connector', 2.1);

            updateInterface();
        }

        function optimizeAccelerators() {
            console.log('⚡ Optimisation automatique des accélérateurs...');

            // Simulation d'optimisation
            const optimizedValues = {
                reflexive: 3.2 + Math.random() * 0.3,
                thermal: 2.8 + Math.random() * 0.3,
                connector: 2.2 + Math.random() * 0.3
            };

            Object.entries(optimizedValues).forEach(([type, value]) => {
                const slider = document.getElementById(`${type}-boost-slider`);
                if (slider) {
                    slider.value = value;
                    updateBoostValue(type, value);
                }
            });

            updateInterface();
        }
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
