<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Performances</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .performance-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            max-width: 100%;
        }

        .chart-container {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            position: relative;
            min-height: 300px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        .chart-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .metric-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .metric-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--accent);
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--text-primary);
        }

        .metric-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .performance-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .time-range-selector {
            display: flex;
            gap: 10px;
        }

        .time-range-button {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 8px 15px;
            color: #ffffff;
            cursor: pointer;
            transition: var(--transition-fast);
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .time-range-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .time-range-button.active {
            background-color: #ff6b6b;
            color: #ffffff;
            border-color: #ff6b6b;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .refresh-button {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 8px 15px;
            color: #ffffff;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition-fast);
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .refresh-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .refresh-button i {
            font-size: 14px;
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: var(--border-radius);
            z-index: 10;
        }

        .chart-loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--accent);
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Styles pour les modales */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            width: 500px;
            max-width: 90%;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .modal-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: var(--border-light);
        }

        .modal-header h2 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            border-top: var(--border-light);
        }

        /* Styles pour les options d'exportation */
        .export-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .export-option {
            display: flex;
            align-items: center;
        }

        .export-option input[type="checkbox"] {
            margin-right: 10px;
        }

        .export-format {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .export-format select {
            background-color: var(--bg-tertiary);
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            color: var(--text-primary);
        }

        /* Styles pour les options de comparaison */
        .compare-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .compare-option {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .compare-option label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .compare-option select {
            background-color: var(--bg-tertiary);
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            color: var(--text-primary);
        }

        /* Styles pour les boutons d'action */
        .action-buttons {
            display: flex;
            gap: 10px;
        }

        /* Styles pour la comparaison */
        .comparison-container {
            display: none;
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .comparison-container.show {
            display: block;
        }

        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .comparison-title {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .comparison-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            transition: var(--transition-fast);
        }

        .comparison-close:hover {
            color: var(--text-primary);
        }

        .comparison-chart-container {
            height: 300px;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/performance.html" class="nav-item active">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/agent-navigation.html" class="nav-item">
                <i class="fas fa-compass"></i>
                <span>Navigation Agent</span>
            </a>
            <a href="/security-dashboard.html" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>Sécurité</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Performances</h1>
                <p class="interface-subtitle">Analyse des performances de la mémoire thermique et des accélérateurs Kyber</p>
            </div>

            <div class="status-container">
                <div class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="last-update">Dernière mise à jour: --:--:--</span>
                </div>
            </div>
        </div>

        <!-- Contrôles des performances -->
        <div class="performance-controls">
            <div class="time-range-selector">
                <button class="time-range-button active" data-range="hour">Dernière heure</button>
                <button class="time-range-button" data-range="day">Dernier jour</button>
                <button class="time-range-button" data-range="week">Dernière semaine</button>
                <button class="time-range-button" data-range="month">Dernier mois</button>
            </div>

            <div class="action-buttons">
                <button class="action-button" id="export-btn">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
                <button class="action-button" id="compare-btn">
                    <i class="fas fa-chart-bar"></i> Comparer
                </button>
                <a href="/performance-comparison.html" class="action-button">
                    <i class="fas fa-exchange-alt"></i> Comparer les Agents
                </a>
                <button class="refresh-button" id="refresh-btn">
                    <i class="fas fa-sync-alt"></i> Rafraîchir
                </button>
            </div>
        </div>

        <!-- Modal d'exportation -->
        <div class="modal" id="export-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Exporter les données de performance</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="export-options">
                        <div class="export-option">
                            <input type="checkbox" id="export-temperature" checked>
                            <label for="export-temperature">Température</label>
                        </div>
                        <div class="export-option">
                            <input type="checkbox" id="export-entries" checked>
                            <label for="export-entries">Entrées</label>
                        </div>
                        <div class="export-option">
                            <input type="checkbox" id="export-accelerators" checked>
                            <label for="export-accelerators">Accélérateurs</label>
                        </div>
                        <div class="export-option">
                            <input type="checkbox" id="export-cycles" checked>
                            <label for="export-cycles">Cycles</label>
                        </div>
                    </div>
                    <div class="export-format">
                        <label for="export-format-select">Format:</label>
                        <select id="export-format-select">
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="action-button" id="export-download-btn">
                        <i class="fas fa-download"></i> Télécharger
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal de comparaison -->
        <div class="modal" id="compare-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Comparer les performances</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="compare-options">
                        <div class="compare-option">
                            <label for="compare-baseline">Période de référence:</label>
                            <select id="compare-baseline">
                                <option value="hour">Dernière heure</option>
                                <option value="day">Dernier jour</option>
                                <option value="week">Dernière semaine</option>
                                <option value="month">Dernier mois</option>
                            </select>
                        </div>
                        <div class="compare-option">
                            <label for="compare-target">Période à comparer:</label>
                            <select id="compare-target">
                                <option value="hour">Dernière heure</option>
                                <option value="day" selected>Dernier jour</option>
                                <option value="week">Dernière semaine</option>
                                <option value="month">Dernier mois</option>
                            </select>
                        </div>
                        <div class="compare-option">
                            <label for="compare-metric">Métrique:</label>
                            <select id="compare-metric">
                                <option value="temperature">Température</option>
                                <option value="efficiency">Efficacité</option>
                                <option value="stability">Stabilité</option>
                                <option value="cycles">Cycles</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="action-button" id="compare-generate-btn">
                        <i class="fas fa-chart-line"></i> Générer la comparaison
                    </button>
                </div>
            </div>
        </div>

        <!-- Conteneur de comparaison -->
        <div class="comparison-container" id="comparison-container">
            <div class="comparison-header">
                <div class="comparison-title">Comparaison des performances</div>
                <button class="comparison-close" id="comparison-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="comparison-chart-container">
                <canvas id="comparison-chart"></canvas>
            </div>
        </div>

        <!-- Métriques de performance -->
        <div class="performance-metrics">
            <div class="metric-card">
                <div class="metric-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="metric-value" id="avg-temperature">0.00</div>
                <div class="metric-label">Température moyenne</div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">
                    <i class="fas fa-memory"></i>
                </div>
                <div class="metric-value" id="total-entries">0</div>
                <div class="metric-label">Entrées totales</div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">
                    <i class="fas fa-recycle"></i>
                </div>
                <div class="metric-value" id="cycles-performed">0</div>
                <div class="metric-label">Cycles effectués</div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="metric-value" id="kyber-efficiency">0%</div>
                <div class="metric-label">Efficacité Kyber</div>
            </div>
        </div>

        <!-- Graphiques de performance -->
        <div class="performance-container">
            <!-- Graphique de température -->
            <div class="chart-container">
                <div class="chart-title">Évolution de la température</div>
                <div class="chart-description">Température moyenne de la mémoire thermique au fil du temps</div>
                <canvas id="temperature-chart"></canvas>
                <div class="chart-loading" id="temperature-loading">
                    <div class="chart-loading-spinner"></div>
                </div>
            </div>

            <!-- Graphique des entrées -->
            <div class="chart-container">
                <div class="chart-title">Distribution des entrées</div>
                <div class="chart-description">Répartition des entrées dans les différentes zones de mémoire</div>
                <canvas id="entries-chart"></canvas>
                <div class="chart-loading" id="entries-loading">
                    <div class="chart-loading-spinner"></div>
                </div>
            </div>

            <!-- Graphique des accélérateurs -->
            <div class="chart-container">
                <div class="chart-title">Performance des accélérateurs Kyber</div>
                <div class="chart-description">Efficacité et stabilité des accélérateurs au fil du temps</div>
                <canvas id="accelerators-chart"></canvas>
                <div class="chart-loading" id="accelerators-loading">
                    <div class="chart-loading-spinner"></div>
                </div>
            </div>

            <!-- Graphique des cycles -->
            <div class="chart-container">
                <div class="chart-title">Cycles de mémoire</div>
                <div class="chart-description">Nombre de cycles de mémoire effectués et leur impact</div>
                <canvas id="cycles-chart"></canvas>
                <div class="chart-loading" id="cycles-loading">
                    <div class="chart-loading-spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent intelligent avec mémoire thermique et accélérateurs Kyber
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script src="/js/performance-monitor.js"></script>
  <script src="js/native-app.js"></script>
</body>
</html>
