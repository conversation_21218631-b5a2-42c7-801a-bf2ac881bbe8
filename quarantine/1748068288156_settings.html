<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Paramètres</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .settings-section {
            margin-bottom: 30px;
        }

        .settings-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .settings-group {
            margin-bottom: 20px;
        }

        .settings-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
        }

        .settings-label {
            font-size: 14px;
        }

        .settings-description {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .settings-control {
            display: flex;
            align-items: center;
        }

        .settings-input {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px;
            color: white;
            width: 100px;
        }

        .settings-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px;
            color: white;
            width: 150px;
        }

        .settings-button {
            background-color: var(--accent);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .settings-button:hover {
            background-color: var(--primary-light);
            box-shadow: var(--glow-effect);
        }

        .settings-button.danger {
            background-color: var(--danger);
        }

        .settings-button.danger:hover {
            background-color: #ff4081;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--accent);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams.html" class="nav-item">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings.html" class="nav-item active">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Paramètres</h1>
                <p class="interface-subtitle">Configuration de l'agent Louna</p>
            </div>

            <div class="status-container">
                <button class="action-button" id="save-settings-btn">
                    <i class="fas fa-save"></i> Enregistrer
                </button>
                <button class="action-button" id="reset-settings-btn">
                    <i class="fas fa-undo"></i> Réinitialiser
                </button>
            </div>
        </div>

        <!-- Grille principale -->
        <div class="grid-container">
            <!-- Paramètres de la mémoire thermique -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-fire card-icon"></i>
                        <h2 class="card-title">Mémoire Thermique</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Capacités des zones</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire Instantanée</div>
                                    <div class="settings-description">Capacité de la zone la plus chaude</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="instant-capacity" min="5" max="50" value="20">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire à Court Terme</div>
                                    <div class="settings-description">Capacité de la zone à court terme</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="short-term-capacity" min="10" max="100" value="50">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Mémoire à Long Terme</div>
                                    <div class="settings-description">Capacité de la zone à long terme</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="long-term-capacity" min="100" max="2000" value="1000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Cycle de mémoire</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle de cycle</div>
                                    <div class="settings-description">Intervalle entre les cycles de mémoire (secondes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="cycle-interval" min="60" max="3600" value="300">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Taux de décroissance</div>
                                    <div class="settings-description">Vitesse à laquelle la température des entrées diminue</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="decay-rate" min="0.8" max="0.99" step="0.01" value="0.95">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paramètres des accélérateurs Kyber -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bolt card-icon"></i>
                        <h2 class="card-title">Accélérateurs Kyber</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Configuration générale</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle de mise à jour</div>
                                    <div class="settings-description">Intervalle entre les mises à jour des accélérateurs (secondes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="update-interval" min="10" max="300" value="60">
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Facteur de boost maximum</div>
                                    <div class="settings-description">Limite supérieure du facteur de boost</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="max-boost" min="2" max="10" step="0.1" value="5.0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Optimisation automatique</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Optimisation automatique</div>
                                    <div class="settings-description">Optimiser automatiquement les accélérateurs</div>
                                </div>
                                <div class="settings-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="auto-optimize">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Intervalle d'optimisation</div>
                                    <div class="settings-description">Intervalle entre les optimisations automatiques (minutes)</div>
                                </div>
                                <div class="settings-control">
                                    <input type="number" class="settings-input" id="optimize-interval" min="5" max="120" value="30">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paramètres avancés et actions -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-tools card-icon"></i>
                        <h2 class="card-title">Paramètres avancés</h2>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Sauvegarde et restauration</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Exporter les données</div>
                                    <div class="settings-description">Exporter toutes les données de la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button" id="export-btn">
                                        <i class="fas fa-download"></i> Exporter
                                    </button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Importer des données</div>
                                    <div class="settings-description">Importer des données dans la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button" id="import-btn">
                                        <i class="fas fa-upload"></i> Importer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">Actions</h3>

                        <div class="settings-group">
                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Réinitialiser la mémoire</div>
                                    <div class="settings-description">Effacer toutes les données de la mémoire thermique</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button danger" id="reset-memory-btn">
                                        <i class="fas fa-trash"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div>
                                    <div class="settings-label">Réinitialiser les accélérateurs</div>
                                    <div class="settings-description">Réinitialiser tous les accélérateurs Kyber</div>
                                </div>
                                <div class="settings-control">
                                    <button class="settings-button danger" id="reset-accelerators-btn">
                                        <i class="fas fa-trash"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent intelligent avec mémoire thermique et accélérateurs Kyber
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Charger les paramètres actuels
            loadSettings();

            // Ajouter les écouteurs d'événements
            document.getElementById('save-settings-btn').addEventListener('click', saveSettings);
            document.getElementById('reset-settings-btn').addEventListener('click', resetSettings);
            document.getElementById('export-btn').addEventListener('click', exportData);
            document.getElementById('import-btn').addEventListener('click', importData);
            document.getElementById('reset-memory-btn').addEventListener('click', resetMemory);
            document.getElementById('reset-accelerators-btn').addEventListener('click', resetAccelerators);
        });

        // Charger les paramètres depuis le localStorage
        function loadSettings() {
            console.log('🔄 Chargement des paramètres...');

            // Paramètres de la mémoire thermique
            const instantCapacity = localStorage.getItem('thermal.instantCapacity') || '20';
            const shortTermCapacity = localStorage.getItem('thermal.shortTermCapacity') || '50';
            const longTermCapacity = localStorage.getItem('thermal.longTermCapacity') || '1000';
            const cycleInterval = localStorage.getItem('thermal.cycleInterval') || '300';
            const decayRate = localStorage.getItem('thermal.decayRate') || '0.95';

            // Paramètres des accélérateurs Kyber
            const updateInterval = localStorage.getItem('kyber.updateInterval') || '60';
            const maxBoost = localStorage.getItem('kyber.maxBoost') || '5.0';
            const autoOptimize = localStorage.getItem('kyber.autoOptimize') === 'true';
            const optimizeInterval = localStorage.getItem('kyber.optimizeInterval') || '30';

            // Appliquer les valeurs aux éléments
            document.getElementById('instant-capacity').value = instantCapacity;
            document.getElementById('short-term-capacity').value = shortTermCapacity;
            document.getElementById('long-term-capacity').value = longTermCapacity;
            document.getElementById('cycle-interval').value = cycleInterval;
            document.getElementById('decay-rate').value = decayRate;
            document.getElementById('update-interval').value = updateInterval;
            document.getElementById('max-boost').value = maxBoost;
            document.getElementById('auto-optimize').checked = autoOptimize;
            document.getElementById('optimize-interval').value = optimizeInterval;

            console.log('✅ Paramètres chargés');
        }

        function saveSettings() {
            console.log('💾 Sauvegarde des paramètres...');

            try {
                // Récupérer les valeurs des éléments
                const instantCapacity = document.getElementById('instant-capacity').value;
                const shortTermCapacity = document.getElementById('short-term-capacity').value;
                const longTermCapacity = document.getElementById('long-term-capacity').value;
                const cycleInterval = document.getElementById('cycle-interval').value;
                const decayRate = document.getElementById('decay-rate').value;
                const updateInterval = document.getElementById('update-interval').value;
                const maxBoost = document.getElementById('max-boost').value;
                const autoOptimize = document.getElementById('auto-optimize').checked;
                const optimizeInterval = document.getElementById('optimize-interval').value;

                // Sauvegarder dans le localStorage
                localStorage.setItem('thermal.instantCapacity', instantCapacity);
                localStorage.setItem('thermal.shortTermCapacity', shortTermCapacity);
                localStorage.setItem('thermal.longTermCapacity', longTermCapacity);
                localStorage.setItem('thermal.cycleInterval', cycleInterval);
                localStorage.setItem('thermal.decayRate', decayRate);
                localStorage.setItem('kyber.updateInterval', updateInterval);
                localStorage.setItem('kyber.maxBoost', maxBoost);
                localStorage.setItem('kyber.autoOptimize', autoOptimize.toString());
                localStorage.setItem('kyber.optimizeInterval', optimizeInterval);

                // Appliquer les nouveaux paramètres aux systèmes
                if (window.thermalMemory) {
                    window.thermalMemory.updateSettings({
                        instantCapacity: parseInt(instantCapacity),
                        shortTermCapacity: parseInt(shortTermCapacity),
                        longTermCapacity: parseInt(longTermCapacity),
                        cycleInterval: parseInt(cycleInterval) * 1000,
                        decayRate: parseFloat(decayRate)
                    });
                }

                if (window.kyberAccelerators) {
                    window.kyberAccelerators.updateSettings({
                        updateInterval: parseInt(updateInterval) * 1000,
                        maxBoost: parseFloat(maxBoost),
                        autoOptimize: autoOptimize,
                        optimizeInterval: parseInt(optimizeInterval) * 60000
                    });
                }

                showNotification('Paramètres sauvegardés avec succès', 'success');
                console.log('✅ Paramètres sauvegardés');

            } catch (error) {
                console.error('❌ Erreur lors de la sauvegarde:', error);
                showNotification('Erreur lors de la sauvegarde des paramètres', 'error');
            }
        }

        function resetSettings() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?')) {
                console.log('🔄 Réinitialisation des paramètres...');

                // Supprimer tous les paramètres du localStorage
                const keys = Object.keys(localStorage).filter(key =>
                    key.startsWith('thermal.') || key.startsWith('kyber.')
                );

                keys.forEach(key => localStorage.removeItem(key));

                // Recharger les paramètres par défaut
                loadSettings();

                showNotification('Paramètres réinitialisés aux valeurs par défaut', 'info');
                console.log('✅ Paramètres réinitialisés');
            }
        }

        function exportData() {
            console.log('📤 Export des données...');

            try {
                const exportData = {
                    timestamp: new Date().toISOString(),
                    version: '1.0.0',
                    settings: {},
                    thermalMemory: null,
                    kyberAccelerators: null
                };

                // Exporter les paramètres
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.startsWith('thermal.') || key.startsWith('kyber.')) {
                        exportData.settings[key] = localStorage.getItem(key);
                    }
                });

                // Exporter les données de la mémoire thermique
                if (window.thermalMemory && window.thermalMemory.exportData) {
                    exportData.thermalMemory = window.thermalMemory.exportData();
                }

                // Exporter les données des accélérateurs
                if (window.kyberAccelerators && window.kyberAccelerators.exportData) {
                    exportData.kyberAccelerators = window.kyberAccelerators.exportData();
                }

                // Créer et télécharger le fichier
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `louna-export-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                showNotification('Données exportées avec succès', 'success');
                console.log('✅ Export terminé');

            } catch (error) {
                console.error('❌ Erreur lors de l\'export:', error);
                showNotification('Erreur lors de l\'export des données', 'error');
            }
        }

        function importData() {
            console.log('📥 Import des données...');

            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importData = JSON.parse(e.target.result);

                        // Vérifier la structure des données
                        if (!importData.version || !importData.settings) {
                            throw new Error('Format de fichier invalide');
                        }

                        // Importer les paramètres
                        Object.entries(importData.settings).forEach(([key, value]) => {
                            localStorage.setItem(key, value);
                        });

                        // Importer les données de la mémoire thermique
                        if (importData.thermalMemory && window.thermalMemory && window.thermalMemory.importData) {
                            window.thermalMemory.importData(importData.thermalMemory);
                        }

                        // Importer les données des accélérateurs
                        if (importData.kyberAccelerators && window.kyberAccelerators && window.kyberAccelerators.importData) {
                            window.kyberAccelerators.importData(importData.kyberAccelerators);
                        }

                        // Recharger les paramètres
                        loadSettings();

                        showNotification('Données importées avec succès', 'success');
                        console.log('✅ Import terminé');

                    } catch (error) {
                        console.error('❌ Erreur lors de l\'import:', error);
                        showNotification('Erreur lors de l\'import: ' + error.message, 'error');
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // Fonction utilitaire pour afficher les notifications
        function showNotification(message, type = 'info') {
            // Créer l'élément de notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
                <span>${message}</span>
            `;

            // Ajouter au DOM
            document.body.appendChild(notification);

            // Afficher avec animation
            setTimeout(() => notification.classList.add('visible'), 100);

            // Masquer après 3 secondes
            setTimeout(() => {
                notification.classList.remove('visible');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        function resetMemory() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser la mémoire thermique ? Cette action est irréversible.')) {
                fetch('/api/thermal/memory/reset', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Mémoire thermique réinitialisée avec succès');
                    } else {
                        alert('Erreur lors de la réinitialisation de la mémoire: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la réinitialisation de la mémoire');
                });
            }
        }

        function resetAccelerators() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser les accélérateurs Kyber ? Cette action est irréversible.')) {
                fetch('/api/thermal/accelerators/reset', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Accélérateurs Kyber réinitialisés avec succès');
                    } else {
                        alert('Erreur lors de la réinitialisation des accélérateurs: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la réinitialisation des accélérateurs');
                });
            }
        }
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
