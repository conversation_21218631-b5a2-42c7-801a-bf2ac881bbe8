<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation de l'Agent - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .navigation-container {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            padding: 20px;
            height: calc(100vh - 120px);
        }

        .navigation-panel {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .panel-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .search-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid var(--accent);
            transition: all 0.3s ease;
        }

        .search-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .search-query {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .search-meta {
            font-size: 0.9rem;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-results-count {
            background: var(--accent);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .current-activity {
            text-align: center;
        }

        .activity-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            margin-bottom: 20px;
        }

        .activity-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .activity-status {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .activity-details {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .navigation-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .browsing-history {
            max-height: 500px;
            overflow-y: auto;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .history-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .history-url {
            font-weight: 600;
            color: var(--accent);
            margin-bottom: 5px;
            word-break: break-all;
        }

        .history-title {
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .history-meta {
            font-size: 0.9rem;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-time {
            font-size: 0.8rem;
        }

        .search-controls {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 15px;
            border: 1px solid var(--accent);
            border-radius: 20px;
            background: transparent;
            color: var(--accent);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--accent);
            color: white;
        }

        .real-time-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4CAF50;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .clear-history-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            width: 100%;
        }

        .clear-history-btn:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }
    </style>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/presentation.html" class="nav-item">
                <i class="fas fa-presentation"></i>
                <span>Présentation</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/agent-navigation.html" class="nav-item active">
                <i class="fas fa-compass"></i>
                <span>Navigation Agent</span>
            </a>
            <a href="/security-dashboard.html" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>Sécurité</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <div class="navigation-container">
            <!-- Panneau de recherche -->
            <div class="navigation-panel">
                <h2 class="panel-title">
                    <i class="fas fa-search"></i>
                    Historique de Recherche
                </h2>
                
                <div class="real-time-indicator">
                    <div class="status-dot"></div>
                    <span>Surveillance en temps réel active</span>
                </div>

                <div class="search-controls">
                    <input type="text" class="search-input" placeholder="Filtrer les recherches..." id="search-filter">
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">Toutes</button>
                        <button class="filter-btn" data-filter="web">Web</button>
                        <button class="filter-btn" data-filter="memory">Mémoire</button>
                        <button class="filter-btn" data-filter="files">Fichiers</button>
                    </div>
                </div>

                <div class="search-history" id="search-history">
                    <!-- Les recherches seront ajoutées dynamiquement -->
                </div>

                <button class="clear-history-btn" onclick="clearSearchHistory()">
                    <i class="fas fa-trash"></i> Effacer l'historique
                </button>
            </div>

            <!-- Panneau d'activité actuelle -->
            <div class="navigation-panel">
                <h2 class="panel-title">
                    <i class="fas fa-brain"></i>
                    Activité Cérébrale
                </h2>

                <div class="activity-display">
                    <i class="fas fa-cog activity-icon" id="activity-icon"></i>
                    <div class="activity-status" id="activity-status">En veille</div>
                    <div class="activity-details" id="activity-details">
                        L'agent surveille l'environnement et traite les informations en arrière-plan
                    </div>
                </div>

                <div class="navigation-stats">
                    <div class="stat-card">
                        <span class="stat-number" id="searches-count">0</span>
                        <span class="stat-label">Recherches</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="pages-visited">0</span>
                        <span class="stat-label">Pages visitées</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="memory-accesses">0</span>
                        <span class="stat-label">Accès mémoire</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="brain-cycles">0</span>
                        <span class="stat-label">Cycles cerveau</span>
                    </div>
                </div>
            </div>

            <!-- Panneau d'historique de navigation -->
            <div class="navigation-panel">
                <h2 class="panel-title">
                    <i class="fas fa-history"></i>
                    Historique de Navigation
                </h2>

                <div class="search-controls">
                    <input type="text" class="search-input" placeholder="Filtrer l'historique..." id="history-filter">
                </div>

                <div class="browsing-history" id="browsing-history">
                    <!-- L'historique sera ajouté dynamiquement -->
                </div>

                <button class="clear-history-btn" onclick="clearBrowsingHistory()">
                    <i class="fas fa-trash"></i> Effacer l'historique
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>

    <script>
        // Données simulées pour l'historique
        let searchHistory = [
            {
                query: "intelligence artificielle avancée",
                type: "web",
                results: 1247,
                timestamp: new Date(Date.now() - 300000).toISOString(),
                source: "Google"
            },
            {
                query: "mémoire thermique fonctionnement",
                type: "memory",
                results: 89,
                timestamp: new Date(Date.now() - 600000).toISOString(),
                source: "Mémoire interne"
            },
            {
                query: "accélérateurs Kyber optimisation",
                type: "files",
                results: 23,
                timestamp: new Date(Date.now() - 900000).toISOString(),
                source: "Système de fichiers"
            }
        ];

        let browsingHistory = [
            {
                url: "https://www.nature.com/articles/ai-research-2024",
                title: "Advances in AI Research 2024",
                timestamp: new Date(Date.now() - 180000).toISOString(),
                visits: 3
            },
            {
                url: "https://arxiv.org/abs/2024.thermal-memory",
                title: "Thermal Memory Systems in Cognitive Computing",
                timestamp: new Date(Date.now() - 420000).toISOString(),
                visits: 1
            },
            {
                url: "https://github.com/kyber-accelerators/optimization",
                title: "Kyber Accelerators - Optimization Framework",
                timestamp: new Date(Date.now() - 720000).toISOString(),
                visits: 5
            }
        ];

        let stats = {
            searches: 0,
            pagesVisited: 0,
            memoryAccesses: 0,
            brainCycles: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            loadSearchHistory();
            loadBrowsingHistory();
            updateStats();
            startRealTimeUpdates();
            setupFilters();
            
            // Notification de bienvenue
            setTimeout(() => {
                if (typeof notifications !== 'undefined') {
                    notifications.info(
                        "Interface de navigation de l'agent initialisée. Surveillance en temps réel active.",
                        "Navigation Agent",
                        { duration: 5000 }
                    );
                }
            }, 1000);
        });

        function loadSearchHistory() {
            const container = document.getElementById('search-history');
            container.innerHTML = '';
            
            searchHistory.forEach(search => {
                const item = createSearchItem(search);
                container.appendChild(item);
            });
        }

        function createSearchItem(search) {
            const item = document.createElement('div');
            item.className = 'search-item';
            item.dataset.type = search.type;
            
            const timeAgo = getTimeAgo(search.timestamp);
            
            item.innerHTML = `
                <div class="search-query">${search.query}</div>
                <div class="search-meta">
                    <span>${search.source}</span>
                    <span class="search-results-count">${search.results} résultats</span>
                </div>
                <div class="search-meta">
                    <span class="history-time">${timeAgo}</span>
                    <span style="color: var(--accent);">${search.type.toUpperCase()}</span>
                </div>
            `;
            
            return item;
        }

        function loadBrowsingHistory() {
            const container = document.getElementById('browsing-history');
            container.innerHTML = '';
            
            browsingHistory.forEach(page => {
                const item = createHistoryItem(page);
                container.appendChild(item);
            });
        }

        function createHistoryItem(page) {
            const item = document.createElement('div');
            item.className = 'history-item';
            
            const timeAgo = getTimeAgo(page.timestamp);
            
            item.innerHTML = `
                <div class="history-url">${page.url}</div>
                <div class="history-title">${page.title}</div>
                <div class="history-meta">
                    <span>${page.visits} visite(s)</span>
                    <span class="history-time">${timeAgo}</span>
                </div>
            `;
            
            return item;
        }

        function getTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffMs = now - time;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            
            if (diffMins < 1) return 'À l\'instant';
            if (diffMins < 60) return `Il y a ${diffMins} min`;
            if (diffHours < 24) return `Il y a ${diffHours}h`;
            return time.toLocaleDateString();
        }

        function updateStats() {
            stats.searches = searchHistory.length;
            stats.pagesVisited = browsingHistory.length;
            stats.memoryAccesses = Math.floor(Math.random() * 1000) + 500;
            stats.brainCycles = Math.floor(Math.random() * 10000) + 5000;
            
            document.getElementById('searches-count').textContent = stats.searches;
            document.getElementById('pages-visited').textContent = stats.pagesVisited;
            document.getElementById('memory-accesses').textContent = stats.memoryAccesses;
            document.getElementById('brain-cycles').textContent = stats.brainCycles;
        }

        function startRealTimeUpdates() {
            // Simuler l'activité en temps réel
            setInterval(() => {
                updateBrainActivity();
                
                // Ajouter occasionnellement de nouvelles recherches
                if (Math.random() < 0.1) { // 10% de chance
                    addNewSearch();
                }
                
                // Ajouter occasionnellement de nouvelles pages
                if (Math.random() < 0.05) { // 5% de chance
                    addNewPage();
                }
            }, 5000);
            
            // Mettre à jour les stats plus fréquemment
            setInterval(() => {
                stats.memoryAccesses += Math.floor(Math.random() * 10);
                stats.brainCycles += Math.floor(Math.random() * 50);
                updateStats();
            }, 2000);
        }

        function updateBrainActivity() {
            const activities = [
                { icon: 'fa-search', status: 'Recherche en cours', details: 'Analyse des données web et mémoire thermique' },
                { icon: 'fa-brain', status: 'Traitement cognitif', details: 'Consolidation des informations en mémoire' },
                { icon: 'fa-cog', status: 'Optimisation', details: 'Ajustement des accélérateurs Kyber' },
                { icon: 'fa-eye', status: 'Surveillance', details: 'Monitoring de l\'environnement système' },
                { icon: 'fa-moon', status: 'En veille', details: 'Traitement en arrière-plan actif' }
            ];
            
            const activity = activities[Math.floor(Math.random() * activities.length)];
            
            document.getElementById('activity-icon').className = `fas ${activity.icon} activity-icon`;
            document.getElementById('activity-status').textContent = activity.status;
            document.getElementById('activity-details').textContent = activity.details;
        }

        function addNewSearch() {
            const queries = [
                "optimisation performance IA",
                "algorithmes apprentissage profond",
                "réseaux neuronaux convolutionnels",
                "traitement langage naturel",
                "vision par ordinateur avancée"
            ];
            
            const types = ["web", "memory", "files"];
            const sources = ["Google", "Mémoire interne", "Système de fichiers", "ArXiv", "GitHub"];
            
            const newSearch = {
                query: queries[Math.floor(Math.random() * queries.length)],
                type: types[Math.floor(Math.random() * types.length)],
                results: Math.floor(Math.random() * 2000) + 100,
                timestamp: new Date().toISOString(),
                source: sources[Math.floor(Math.random() * sources.length)]
            };
            
            searchHistory.unshift(newSearch);
            if (searchHistory.length > 20) searchHistory.pop();
            
            loadSearchHistory();
            updateStats();
        }

        function addNewPage() {
            const pages = [
                {
                    url: "https://openai.com/research/latest",
                    title: "Latest AI Research - OpenAI"
                },
                {
                    url: "https://deepmind.com/publications",
                    title: "DeepMind Publications"
                },
                {
                    url: "https://ai.google/research/",
                    title: "Google AI Research"
                }
            ];
            
            const page = pages[Math.floor(Math.random() * pages.length)];
            const newPage = {
                ...page,
                timestamp: new Date().toISOString(),
                visits: 1
            };
            
            browsingHistory.unshift(newPage);
            if (browsingHistory.length > 15) browsingHistory.pop();
            
            loadBrowsingHistory();
            updateStats();
        }

        function setupFilters() {
            // Filtres pour l'historique de recherche
            document.getElementById('search-filter').addEventListener('input', (e) => {
                filterSearchHistory(e.target.value);
            });
            
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    filterSearchByType(e.target.dataset.filter);
                });
            });
            
            // Filtre pour l'historique de navigation
            document.getElementById('history-filter').addEventListener('input', (e) => {
                filterBrowsingHistory(e.target.value);
            });
        }

        function filterSearchHistory(query) {
            const items = document.querySelectorAll('#search-history .search-item');
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(query.toLowerCase()) ? 'block' : 'none';
            });
        }

        function filterSearchByType(type) {
            const items = document.querySelectorAll('#search-history .search-item');
            items.forEach(item => {
                if (type === 'all' || item.dataset.type === type) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function filterBrowsingHistory(query) {
            const items = document.querySelectorAll('#browsing-history .history-item');
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(query.toLowerCase()) ? 'block' : 'none';
            });
        }

        function clearSearchHistory() {
            if (confirm('Êtes-vous sûr de vouloir effacer l\'historique de recherche ?')) {
                searchHistory = [];
                loadSearchHistory();
                updateStats();
                
                if (typeof notifications !== 'undefined') {
                    notifications.success('Historique de recherche effacé', 'Navigation');
                }
            }
        }

        function clearBrowsingHistory() {
            if (confirm('Êtes-vous sûr de vouloir effacer l\'historique de navigation ?')) {
                browsingHistory = [];
                loadBrowsingHistory();
                updateStats();
                
                if (typeof notifications !== 'undefined') {
                    notifications.success('Historique de navigation effacé', 'Navigation');
                }
            }
        }
    </script>
    <script src="js/native-app.js"></script>
</body>
</html>
