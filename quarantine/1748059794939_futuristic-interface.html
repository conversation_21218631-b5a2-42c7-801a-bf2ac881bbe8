<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Mémoire Thermique</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        /* Styles pour les entrées de mémoire */
        .memory-entry {
            background-color: rgba(255, 255, 255, 0.08); /* Plus opaque pour un meilleur contraste */
            border-radius: var(--border-radius);
            padding: 12px;
            margin-bottom: 10px;
            border-left: 3px solid var(--temp-medium);
            transition: var(--transition-fast);
            border: 1px solid rgba(255, 255, 255, 0.1); /* Ajouter une bordure pour améliorer la visibilité */
        }

        .memory-entry:hover {
            background-color: rgba(255, 255, 255, 0.15); /* Plus opaque pour un meilleur contraste */
            transform: translateX(3px);
            border-color: rgba(255, 255, 255, 0.2); /* Bordure plus visible au survol */
        }

        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .entry-type {
            font-weight: 500;
            font-size: 14px;
            color: var(--text-primary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 10px;
        }

        .entry-temp {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }

        .entry-temp.hot {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--temp-hot);
        }

        .entry-temp.warm {
            background-color: rgba(255, 159, 67, 0.2);
            color: var(--temp-warm);
        }

        .entry-temp.medium {
            background-color: rgba(29, 209, 161, 0.2);
            color: var(--temp-medium);
        }

        .entry-temp.cool {
            background-color: rgba(84, 160, 255, 0.2);
            color: var(--temp-cool);
        }

        .entry-content {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            line-height: 1.5; /* Légèrement augmenté pour une meilleure lisibilité */
            font-weight: 400; /* Légèrement plus gras pour une meilleure lisibilité */
        }

        .entry-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .entry-tags {
            color: var(--text-secondary); /* Plus opaque pour un meilleur contraste */
            font-style: italic;
            font-weight: 400; /* Légèrement plus gras pour une meilleure lisibilité */
        }

        .entry-actions {
            display: flex;
            gap: 5px;
        }

        .entry-action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 3px;
            border-radius: 3px;
            transition: var(--transition-fast);
        }

        .entry-action-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .entry-action-btn.view-btn:hover {
            color: var(--info);
        }

        .entry-action-btn.boost-btn:hover {
            color: var(--temp-hot);
        }

        .entry-action-btn.delete-btn:hover {
            color: var(--danger);
        }

        /* Animation pour les mises à jour */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .pulse-animation {
            animation: pulse 0.5s ease;
        }

        /* Styles pour les boutons désactivés */
        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Styles pour le conteneur des entrées récentes */
        #recent-entries-container {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 5px;
        }

        #recent-entries-container::-webkit-scrollbar {
            width: 6px;
        }

        #recent-entries-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        #recent-entries-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        #recent-entries-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Styles pour le texte muté */
        .text-muted {
            color: var(--text-tertiary);
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        /* Styles pour le toggle switch */
        .toggle-container {
            display: flex;
            align-items: center;
        }

        .toggle {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-tertiary);
            transition: .4s;
            border-radius: 20px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--accent-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* Styles pour les métriques d'apprentissage */
        #learning-metrics-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .metric-name {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .metric-value {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            text-align: right;
            margin-top: 2px;
        }

        /* Styles pour les informations du mode sommeil */
        #sleep-info-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Styles pour le monitoring Qi/Neurones */
        .metric-group {
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.02);
        }

        .flow-indicator {
            display: inline-block;
            margin-left: 10px;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }

        .flow-up {
            color: #4caf50;
            animation: pulse-up 1s infinite;
        }

        .flow-down {
            color: #f44336;
            animation: pulse-down 1s infinite;
        }

        .flow-stable {
            color: #ffc107;
        }

        @keyframes pulse-up {
            0%, 100% { transform: translateY(0); opacity: 1; }
            50% { transform: translateY(-2px); opacity: 0.8; }
        }

        @keyframes pulse-down {
            0%, 100% { transform: translateY(0); opacity: 1; }
            50% { transform: translateY(2px); opacity: 0.8; }
        }

        .qi-neuron-active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4caf50;
        }

        .qi-neuron-inactive {
            background: rgba(158, 158, 158, 0.3);
            border-color: #9e9e9e;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/presentation.html" class="nav-item">
                <i class="fas fa-presentation"></i>
                <span>Présentation</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item active">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/agent-navigation.html" class="nav-item">
                <i class="fas fa-compass"></i>
                <span>Navigation Agent</span>
            </a>
            <a href="/security-dashboard.html" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>Sécurité</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/agents.html" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/settings.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Mémoire Thermique</h1>
                <p class="interface-subtitle">Visualisation et gestion des zones de mémoire</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="memory-status-indicator"></div>
                <span class="status-text" id="memory-status-text">Chargement...</span>

                <button class="action-button" id="cycle-memory-btn">
                    <i class="fas fa-sync"></i> Cycle de mémoire
                </button>
                <a href="/memory-sync.html" class="action-button">
                    <i class="fas fa-exchange-alt"></i> Synchroniser
                </a>
                <button class="action-button" id="dream-btn">
                    <i class="fas fa-cloud-moon"></i> Générer un rêve
                </button>
            </div>
        </div>

        <!-- Grille principale -->
        <div class="grid-container">
            <!-- Carte des statistiques -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line card-icon"></i>
                        <h2 class="card-title">Statistiques de la mémoire</h2>
                    </div>

                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-label">Entrées totales</div>
                            <div class="stat-value" id="total-entries">0</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Température moyenne</div>
                            <div class="stat-value" id="avg-temperature">0.5</div>
                            <div class="stat-badge" id="temp-status">Normale</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Cycles effectués</div>
                            <div class="stat-value" id="cycles-performed">0</div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Dernier cycle</div>
                            <div class="stat-value" id="last-cycle-time">Jamais</div>
                        </div>
                    </div>
                </div>

                <!-- Carte des accélérateurs -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-bolt card-icon"></i>
                        <h2 class="card-title">Accélérateurs Kyber</h2>
                        <span class="card-badge" id="kyber-status">Actifs</span>
                    </div>

                    <div id="accelerators-container">
                        <div class="accelerator-item">
                            <div class="accelerator-name">Accélérateur Réflexif</div>
                            <div class="accelerator-value" id="reflexive-boost">x3.1</div>
                        </div>

                        <div class="accelerator-item">
                            <div class="accelerator-name">Accélérateur Thermique</div>
                            <div class="accelerator-value" id="thermal-boost">x2.7</div>
                        </div>

                        <div class="accelerator-item">
                            <div class="accelerator-name">Connecteur Thermique</div>
                            <div class="accelerator-value" id="connector-boost">x2.1</div>
                        </div>

                        <div class="accelerator-item">
                            <div class="accelerator-name">Efficacité globale</div>
                            <div class="accelerator-value" id="efficiency">92%</div>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <button class="action-button" id="view-kyber-btn">
                            <i class="fas fa-external-link-alt"></i> Voir le tableau de bord
                        </button>
                    </div>
                </div>

                <!-- Carte du système d'auto-apprentissage -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-brain card-icon"></i>
                        <h2 class="card-title">Auto-Apprentissage</h2>
                        <span class="card-badge" id="evolution-status">Actif</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div class="toggle-container">
                                <label class="toggle">
                                    <input type="checkbox" id="auto-evolution-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span style="font-size: 14px; margin-left: 5px;">Auto-Évolution</span>
                            </div>
                            <div class="toggle-container">
                                <label class="toggle">
                                    <input type="checkbox" id="adaptation-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span style="font-size: 14px; margin-left: 5px;">Adaptation</span>
                            </div>
                        </div>
                    </div>

                    <div id="learning-metrics-container">
                        <div class="metric-item">
                            <div class="metric-name">Efficacité de la mémoire</div>
                            <div class="progress-bar">
                                <div class="progress-fill medium" id="memory-efficiency-fill" style="width: 75%;"></div>
                            </div>
                            <div class="metric-value" id="memory-efficiency">75%</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-name">Stabilité des températures</div>
                            <div class="progress-bar">
                                <div class="progress-fill warm" id="temperature-stability-fill" style="width: 82%;"></div>
                            </div>
                            <div class="metric-value" id="temperature-stability">82%</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-name">Optimisation des accès</div>
                            <div class="progress-bar">
                                <div class="progress-fill hot" id="access-optimization-fill" style="width: 90%;"></div>
                            </div>
                            <div class="metric-value" id="access-optimization">90%</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-name">Performance globale</div>
                            <div class="progress-bar">
                                <div class="progress-fill hot" id="overall-performance-fill" style="width: 85%;"></div>
                            </div>
                            <div class="metric-value" id="overall-performance">85%</div>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <button class="action-button" id="force-optimization-btn">
                            <i class="fas fa-sync"></i> Forcer l'optimisation
                        </button>
                        <button class="action-button" id="view-learning-btn">
                            <i class="fas fa-chart-line"></i> Voir les insights
                        </button>
                    </div>
                </div>

                <!-- Carte du mode sommeil -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-moon card-icon"></i>
                        <h2 class="card-title">Mode Sommeil</h2>
                        <span class="card-badge" id="sleep-status">Inactif</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div class="toggle-container">
                                <label class="toggle">
                                    <input type="checkbox" id="sleep-mode-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span style="font-size: 14px; margin-left: 5px;">Mode Sommeil Automatique</span>
                            </div>
                        </div>
                    </div>

                    <div id="sleep-info-container">
                        <div class="info-item">
                            <div class="info-label">État actuel:</div>
                            <div class="info-value" id="sleep-current-status">Inactif</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Dernière activité:</div>
                            <div class="info-value" id="sleep-last-activity">Jamais</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Consolidations effectuées:</div>
                            <div class="info-value" id="sleep-consolidation-count">0</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Dernière consolidation:</div>
                            <div class="info-value" id="sleep-last-consolidation">Jamais</div>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <button class="action-button" id="force-sleep-btn">
                            <i class="fas fa-bed"></i> Forcer le mode sommeil
                        </button>
                        <button class="action-button" id="force-consolidation-btn">
                            <i class="fas fa-compress-arrows-alt"></i> Forcer la consolidation
                        </button>
                    </div>

                    <div style="margin-top: 15px; font-size: 12px; color: var(--text-tertiary);">
                        <p>Le mode sommeil s'active automatiquement après 5 minutes d'inactivité. Pendant le sommeil, le système consolide la mémoire en renforçant les connexions entre les entrées similaires et en nettoyant les informations redondantes.</p>
                    </div>
                </div>

                <!-- Carte du monitoring Qi & Neurones -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-yin-yang card-icon"></i>
                        <h2 class="card-title">Monitoring Qi & Neurones</h2>
                        <span class="card-badge" id="qi-neuron-status">Inactif</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div class="toggle-container">
                                <label class="toggle">
                                    <input type="checkbox" id="qi-neuron-monitoring-toggle">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span style="font-size: 14px; margin-left: 5px;">Monitoring en temps réel</span>
                            </div>
                        </div>
                    </div>

                    <div id="qi-neuron-metrics-container">
                        <div class="metric-group" style="margin-bottom: 15px;">
                            <h4 style="color: #ff6b6b; margin-bottom: 10px; font-size: 14px;">🔥 Énergie Qi</h4>

                            <div class="metric-item">
                                <div class="metric-name">Niveau</div>
                                <div class="progress-bar">
                                    <div class="progress-fill hot" id="qi-level-fill" style="width: 0%;"></div>
                                </div>
                                <div class="metric-value" id="qi-level">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Flux</div>
                                <div class="metric-value" id="qi-flow">0.000</div>
                                <span class="flow-indicator" id="qi-flow-indicator">●</span>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Stabilité</div>
                                <div class="metric-value" id="qi-stability">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Harmonie</div>
                                <div class="metric-value" id="qi-harmony">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Vitalité</div>
                                <div class="metric-value" id="qi-vitality">0.000</div>
                            </div>
                        </div>

                        <div class="metric-group">
                            <h4 style="color: #4ecdc4; margin-bottom: 10px; font-size: 14px;">🧬 Réseau Neuronal</h4>

                            <div class="metric-item">
                                <div class="metric-name">Connexions actives</div>
                                <div class="metric-value" id="neuron-active-connections">0</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Densité du réseau</div>
                                <div class="progress-bar">
                                    <div class="progress-fill medium" id="neuron-density-fill" style="width: 0%;"></div>
                                </div>
                                <div class="metric-value" id="neuron-density">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Force synaptique</div>
                                <div class="metric-value" id="neuron-strength">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Activité neuronale</div>
                                <div class="metric-value" id="neuron-activity">0.000</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-name">Plasticité</div>
                                <div class="metric-value" id="neuron-plasticity">0.000</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <button class="action-button" id="qi-neuron-start-btn">
                            <i class="fas fa-play"></i> Démarrer le monitoring
                        </button>
                        <button class="action-button" id="qi-neuron-stop-btn" disabled>
                            <i class="fas fa-stop"></i> Arrêter le monitoring
                        </button>
                        <button class="action-button" id="qi-neuron-view-btn" onclick="window.open('/qi-neuron-monitor.html', '_blank')">
                            <i class="fas fa-chart-line"></i> Voir l'évolution
                        </button>
                        <button class="action-button" onclick="window.open('/brain-visualization.html', '_blank')">
                            <i class="fas fa-brain"></i> Visualisation 3D
                        </button>
                        <button class="action-button" onclick="window.open('/memory-fusion.html', '_blank')">
                            <i class="fas fa-code-branch"></i> Fusion Mémoire
                        </button>
                    </div>

                    <div style="margin-top: 15px; font-size: 12px; color: var(--text-tertiary);">
                        <p>Le monitoring Qi/Neurones surveille en temps réel l'énergie vitale du système et l'activité des connexions neuronales entre les entrées de mémoire. Le Qi reflète l'harmonie globale du système, tandis que les métriques neuronales montrent la complexité et la richesse des interconnexions.</p>
                    </div>
                </div>
            </div>

            <!-- Carte des zones de mémoire -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-layer-group card-icon"></i>
                        <h2 class="card-title">Zones de mémoire</h2>
                    </div>

                    <!-- Températures du système -->
                    <div style="margin-bottom: 20px; border: 1px solid var(--border-color); border-radius: 8px; padding: 10px; background-color: var(--bg-secondary);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div class="zone-name" style="font-size: 16px; font-weight: bold;">Températures du système</div>
                            <div class="toggle-container">
                                <label class="toggle">
                                    <input type="checkbox" id="use-real-temps-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span style="font-size: 12px; margin-left: 5px;">Utiliser les températures réelles</span>
                            </div>
                        </div>

                        <!-- Températures CPU -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <div class="zone-name">CPU</div>
                            <div class="zone-temp hot" id="cpu-temp">0°C</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill hot" id="cpu-temp-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Températures GPU -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; margin-top: 10px;">
                            <div class="zone-name">GPU</div>
                            <div class="zone-temp warm" id="gpu-temp">0°C</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warm" id="gpu-temp-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Températures Mémoire -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; margin-top: 10px;">
                            <div class="zone-name">Mémoire</div>
                            <div class="zone-temp medium" id="memory-temp">0°C</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill medium" id="memory-temp-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Température normalisée -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px; font-size: 12px; color: var(--text-tertiary);">
                            <span>Température normalisée:</span>
                            <span id="normalized-temp">0.5</span>
                        </div>
                    </div>

                    <!-- Curseur de température -->
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <div class="zone-name">Curseur de température</div>
                            <div class="zone-temp medium" id="temperature-cursor-value">0.5</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="color: var(--temp-cool);">Froid</span>
                            <input type="range" id="temperature-cursor" min="0" max="1" step="0.01" value="0.5" style="flex-grow: 1; height: 10px; -webkit-appearance: none; background: linear-gradient(to right, var(--temp-cool), var(--temp-medium), var(--temp-warm), var(--temp-hot)); border-radius: 5px;">
                            <span style="color: var(--temp-hot);">Chaud</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 12px; color: var(--text-tertiary);">
                            <span>Déplace les seuils entre les zones</span>
                            <button id="reset-cursor-btn" class="action-button" style="padding: 2px 8px; font-size: 12px;">Réinitialiser</button>
                        </div>
                    </div>

                    <div id="memory-zones-container">
                        <!-- Zone instantanée -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire Instantanée</div>
                            <div class="zone-temp hot" id="instant-temp">0.9</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill hot" id="instant-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Zone à court terme -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire à Court Terme</div>
                            <div class="zone-temp warm" id="short-term-temp">0.7</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warm" id="short-term-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Zone de travail -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire de Travail</div>
                            <div class="zone-temp medium" id="working-memory-temp">0.5</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill medium" id="working-memory-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Zone à moyen terme -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire à Moyen Terme</div>
                            <div class="zone-temp medium" id="medium-term-temp">0.3</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill medium" id="medium-term-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Zone à long terme -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire à Long Terme</div>
                            <div class="zone-temp cool" id="long-term-temp">0.1</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill cool" id="long-term-fill" style="width: 0%;"></div>
                        </div>

                        <!-- Zone des rêves -->
                        <div class="thermal-zone">
                            <div class="zone-name">Mémoire des Rêves</div>
                            <div class="zone-temp cool" id="dream-memory-temp">0.05</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill cool" id="dream-memory-fill" style="width: 0%;"></div>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <canvas id="memory-chart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Carte des entrées récentes -->
            <div class="grid-item">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history card-icon"></i>
                        <h2 class="card-title">Entrées récentes</h2>
                    </div>

                    <div id="recent-entries-container">
                        <p class="text-muted">Chargement des entrées récentes...</p>
                    </div>

                    <div style="margin-top: 15px;">
                        <input type="text" id="search-input" placeholder="Rechercher dans la mémoire..." style="width: 100%; padding: 10px; border-radius: 5px; border: 1px solid rgba(255, 255, 255, 0.3); background: rgba(255, 255, 255, 0.15); color: white; margin-bottom: 10px; font-size: 14px;">

                        <button class="action-button" id="search-btn" style="background-color: var(--accent); color: black; font-weight: bold;">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                        <button class="action-button" id="add-entry-btn" style="background-color: var(--primary); color: white; font-weight: bold;">
                            <i class="fas fa-plus"></i> Ajouter une entrée
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        // Initialiser la mémoire thermique et les accélérateurs
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser la mémoire thermique
            await window.thermalMemory.initialize();

            // Initialiser les accélérateurs Kyber
            await window.kyberAccelerators.initialize();

            // Mettre à jour l'interface
            updateInterface();

            // Ajouter des écouteurs pour les mises à jour
            window.thermalMemory.addListener(updateMemoryUI);
            window.kyberAccelerators.addListener(updateAcceleratorsUI);

            // Initialiser le graphique
            initMemoryChart();

            // Ajouter des écouteurs d'événements pour les boutons
            document.getElementById('cycle-memory-btn').addEventListener('click', cycleMem);
            document.getElementById('dream-btn').addEventListener('click', generateDream);
            document.getElementById('view-kyber-btn').addEventListener('click', () => {
                window.location.href = '/kyber-dashboard.html';
            });
            document.getElementById('search-btn').addEventListener('click', searchMemory);
            document.getElementById('add-entry-btn').addEventListener('click', showAddEntryForm);
            document.getElementById('refresh-btn').addEventListener('click', refreshAll);

            // Ajouter des écouteurs d'événements pour le curseur de température
            const temperatureCursor = document.getElementById('temperature-cursor');
            temperatureCursor.addEventListener('input', updateTemperatureCursorValue);
            temperatureCursor.addEventListener('change', setTemperatureCursorPosition);
            document.getElementById('reset-cursor-btn').addEventListener('click', resetTemperatureCursor);

            // Ajouter un écouteur d'événements pour le toggle des températures réelles
            document.getElementById('use-real-temps-toggle').addEventListener('change', toggleRealTemperatures);

            // Ajouter des écouteurs d'événements pour le système d'auto-apprentissage
            document.getElementById('auto-evolution-toggle').addEventListener('change', toggleAutoEvolution);
            document.getElementById('adaptation-toggle').addEventListener('change', toggleAdaptation);
            document.getElementById('force-optimization-btn').addEventListener('click', forceOptimization);
            document.getElementById('view-learning-btn').addEventListener('click', viewLearningInsights);

            // Ajouter des écouteurs d'événements pour le mode sommeil
            document.getElementById('sleep-mode-toggle').addEventListener('change', toggleSleepMode);
            document.getElementById('force-sleep-btn').addEventListener('click', forceSleepMode);
            document.getElementById('force-consolidation-btn').addEventListener('click', forceConsolidation);

            // Ajouter des écouteurs d'événements pour le monitoring Qi/Neurones
            document.getElementById('qi-neuron-monitoring-toggle').addEventListener('change', toggleQiNeuronMonitoring);
            document.getElementById('qi-neuron-start-btn').addEventListener('click', startQiNeuronMonitoring);
            document.getElementById('qi-neuron-stop-btn').addEventListener('click', stopQiNeuronMonitoring);
            document.getElementById('qi-neuron-view-btn').addEventListener('click', viewQiNeuronEvolution);

            // Initialiser le curseur de température
            initTemperatureCursor();

            // Initialiser les températures du système
            initSystemTemperatures();

            // Initialiser le système d'auto-apprentissage
            initLearningSystem();

            // Initialiser le mode sommeil
            initSleepMode();

            // Initialiser le monitoring Qi/Neurones
            initQiNeuronMonitoring();

            // Signaler une activité utilisateur toutes les 30 secondes si l'utilisateur est actif
            setInterval(checkUserActivity, 30000);
        });

        // Fonctions d'interface
        function updateInterface() {
            // Mettre à jour les statistiques de la mémoire
            updateMemoryStats();

            // Mettre à jour les accélérateurs
            updateAccelerators();

            // Mettre à jour les entrées récentes
            updateRecentEntries();

            // Mettre à jour les zones de mémoire
            updateMemoryZones();

            // Mettre à jour le statut
            updateStatus();
        }

        function updateMemoryStats() {
            const stats = window.thermalMemory.getStats();

            // Mettre à jour les statistiques
            document.getElementById('total-entries').textContent = stats.totalEntries;
            document.getElementById('avg-temperature').textContent = stats.averageTemperature.toFixed(2);
            document.getElementById('cycles-performed').textContent = stats.cyclesPerformed;

            // Mettre à jour le dernier cycle
            if (stats.lastCycleTime) {
                const date = new Date(stats.lastCycleTime);
                document.getElementById('last-cycle-time').textContent =
                    `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
            }

            // Mettre à jour le statut de température
            const tempStatus = document.getElementById('temp-status');
            if (stats.averageTemperature > 0.8) {
                tempStatus.textContent = 'Élevée';
                tempStatus.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
                tempStatus.style.color = 'var(--temp-hot)';
            } else if (stats.averageTemperature > 0.5) {
                tempStatus.textContent = 'Modérée';
                tempStatus.style.backgroundColor = 'rgba(255, 159, 67, 0.2)';
                tempStatus.style.color = 'var(--temp-warm)';
            } else {
                tempStatus.textContent = 'Normale';
                tempStatus.style.backgroundColor = 'rgba(29, 209, 161, 0.2)';
                tempStatus.style.color = 'var(--temp-medium)';
            }
        }

        function updateAccelerators() {
            const accelerators = window.kyberAccelerators.getAccelerators();

            // Mettre à jour les valeurs des accélérateurs
            document.getElementById('reflexive-boost').textContent = `x${accelerators.reflexiveBoost.toFixed(1)}`;
            document.getElementById('thermal-boost').textContent = `x${accelerators.thermalBoost.toFixed(1)}`;
            document.getElementById('connector-boost').textContent = `x${accelerators.connectorBoost.toFixed(1)}`;
            document.getElementById('efficiency').textContent = `${Math.round(accelerators.efficiency * 100)}%`;

            // Mettre à jour le statut des accélérateurs
            const kyberStatus = document.getElementById('kyber-status');
            if (accelerators.efficiency > 0.9) {
                kyberStatus.textContent = 'Optimaux';
                kyberStatus.style.backgroundColor = 'var(--success)';
            } else if (accelerators.efficiency > 0.7) {
                kyberStatus.textContent = 'Actifs';
                kyberStatus.style.backgroundColor = 'var(--temp-warm)';
            } else {
                kyberStatus.textContent = 'Dégradés';
                kyberStatus.style.backgroundColor = 'var(--danger)';
            }
        }

        function updateRecentEntries() {
            const entries = window.thermalMemory.getRecentEntries(10);
            const container = document.getElementById('recent-entries-container');

            // Vider le conteneur
            container.innerHTML = '';

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-muted">Aucune entrée récente</p>';
                return;
            }

            // Créer un élément pour chaque entrée
            entries.forEach(entry => {
                const entryElement = document.createElement('div');
                entryElement.className = 'memory-entry';

                // Déterminer la classe de température
                let tempClass = 'cool';
                if (entry.temperature > 0.8) {
                    tempClass = 'hot';
                } else if (entry.temperature > 0.5) {
                    tempClass = 'warm';
                } else if (entry.temperature > 0.3) {
                    tempClass = 'medium';
                }

                // Créer le contenu de l'entrée
                entryElement.innerHTML = `
                    <div class="entry-header">
                        <div class="entry-type">${entry.type}</div>
                        <div class="entry-temp ${tempClass}">${entry.temperature.toFixed(2)}</div>
                    </div>
                    <div class="entry-content">${entry.content.substring(0, 100)}${entry.content.length > 100 ? '...' : ''}</div>
                    <div class="entry-footer">
                        <div class="entry-tags">${entry.tags ? entry.tags.join(', ') : ''}</div>
                        <div class="entry-actions">
                            <button class="entry-action-btn view-btn" data-id="${entry.id}" title="Voir">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="entry-action-btn boost-btn" data-id="${entry.id}" title="Booster">
                                <i class="fas fa-fire"></i>
                            </button>
                            <button class="entry-action-btn delete-btn" data-id="${entry.id}" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                // Ajouter l'entrée au conteneur
                container.appendChild(entryElement);

                // Ajouter des écouteurs d'événements pour les boutons
                entryElement.querySelector('.view-btn').addEventListener('click', () => viewEntry(entry.id));
                entryElement.querySelector('.boost-btn').addEventListener('click', () => boostEntry(entry.id));
                entryElement.querySelector('.delete-btn').addEventListener('click', () => deleteEntry(entry.id));
            });
        }

        function updateMemoryZones() {
            const zones = window.thermalMemory.getZones();

            // Mettre à jour les zones de mémoire
            Object.keys(zones).forEach(zone => {
                const entries = zones[zone].entries;
                const capacity = zones[zone].capacity;
                const temperature = zones[zone].temperature;

                // Calculer le pourcentage de remplissage
                const fillPercentage = (entries.length / capacity) * 100;

                // Mettre à jour la température et le remplissage
                let zoneId = '';
                switch (zone) {
                    case 'instant':
                        zoneId = 'instant';
                        break;
                    case 'shortTerm':
                        zoneId = 'short-term';
                        break;
                    case 'workingMemory':
                        zoneId = 'working-memory';
                        break;
                    case 'mediumTerm':
                        zoneId = 'medium-term';
                        break;
                    case 'longTerm':
                        zoneId = 'long-term';
                        break;
                    case 'dreamMemory':
                        zoneId = 'dream-memory';
                        break;
                }

                if (zoneId) {
                    document.getElementById(`${zoneId}-temp`).textContent = temperature.toFixed(2);
                    document.getElementById(`${zoneId}-fill`).style.width = `${fillPercentage}%`;
                }
            });
        }

        function updateStatus() {
            const status = window.thermalMemory.getStatus();
            const statusIndicator = document.getElementById('memory-status-indicator');
            const statusText = document.getElementById('memory-status-text');

            // Mettre à jour l'indicateur de statut
            if (status === 'ready') {
                statusIndicator.style.backgroundColor = 'var(--success)';
                statusText.textContent = 'Prête';
            } else if (status === 'cycling') {
                statusIndicator.style.backgroundColor = 'var(--warning)';
                statusText.textContent = 'Cycle en cours...';
            } else if (status === 'dreaming') {
                statusIndicator.style.backgroundColor = 'var(--temp-warm)';
                statusText.textContent = 'Génération de rêve...';
            } else {
                statusIndicator.style.backgroundColor = 'var(--danger)';
                statusText.textContent = 'Erreur';
            }
        }

        function updateMemoryUI(type, data) {
            // Mettre à jour l'interface en fonction du type d'événement
            switch (type) {
                case 'cycle':
                    updateMemoryStats();
                    updateMemoryZones();
                    updateStatus();
                    break;
                case 'entry':
                    updateRecentEntries();
                    updateMemoryStats();
                    updateMemoryZones();
                    break;
                case 'dream':
                    updateMemoryStats();
                    updateStatus();
                    break;
                default:
                    updateInterface();
                    break;
            }
        }

        function updateAcceleratorsUI(type, data) {
            // Mettre à jour l'interface en fonction du type d'événement
            switch (type) {
                case 'update':
                    updateAccelerators();
                    break;
                case 'optimize':
                    updateAccelerators();
                    break;
                default:
                    updateAccelerators();
                    break;
            }
        }

        function initMemoryChart() {
            const ctx = document.getElementById('memory-chart').getContext('2d');

            // Créer le graphique
            window.memoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Instantanée', 'Court Terme', 'Travail', 'Moyen Terme', 'Long Terme', 'Rêves'],
                    datasets: [{
                        data: [0, 0, 0, 0, 0, 0],
                        backgroundColor: [
                            'var(--temp-hot)',
                            'var(--temp-warm)',
                            'var(--temp-medium)',
                            'var(--temp-medium)',
                            'var(--temp-cool)',
                            'var(--temp-cool)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: 'var(--text-primary)'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value} entrées`;
                                }
                            }
                        }
                    }
                }
            });

            // Mettre à jour le graphique avec les données actuelles
            updateMemoryChart();
        }

        function updateMemoryChart() {
            const zones = window.thermalMemory.getZones();

            // Mettre à jour les données du graphique
            window.memoryChart.data.datasets[0].data = [
                zones.instant.entries.length,
                zones.shortTerm.entries.length,
                zones.workingMemory.entries.length,
                zones.mediumTerm.entries.length,
                zones.longTerm.entries.length,
                zones.dreamMemory.entries.length
            ];

            // Mettre à jour le graphique
            window.memoryChart.update();
        }

        async function cycleMem() {
            // Désactiver le bouton pendant le cycle
            const cycleBtn = document.getElementById('cycle-memory-btn');
            cycleBtn.disabled = true;

            // Mettre à jour le statut
            document.getElementById('memory-status-indicator').style.backgroundColor = 'var(--warning)';
            document.getElementById('memory-status-text').textContent = 'Cycle en cours...';

            try {
                // Effectuer un cycle de mémoire
                await window.thermalMemory.cycle();

                // Mettre à jour l'interface
                updateInterface();
                updateMemoryChart();
            } catch (error) {
                console.error('Erreur lors du cycle de mémoire:', error);
                alert('Une erreur est survenue lors du cycle de mémoire.');
            } finally {
                // Réactiver le bouton
                cycleBtn.disabled = false;
            }
        }

        async function generateDream() {
            // Désactiver le bouton pendant la génération
            const dreamBtn = document.getElementById('dream-btn');
            dreamBtn.disabled = true;

            // Mettre à jour le statut
            document.getElementById('memory-status-indicator').style.backgroundColor = 'var(--temp-warm)';
            document.getElementById('memory-status-text').textContent = 'Génération de rêve...';

            try {
                // Générer un rêve
                const dream = await window.thermalMemory.generateDream();

                // Afficher le rêve
                if (dream) {
                    alert(`Rêve généré: ${dream.content}`);
                } else {
                    alert('Aucun rêve n\'a pu être généré. Essayez d\'ajouter plus d\'entrées dans la mémoire.');
                }

                // Mettre à jour l'interface
                updateInterface();
                updateMemoryChart();
            } catch (error) {
                console.error('Erreur lors de la génération du rêve:', error);
                alert('Une erreur est survenue lors de la génération du rêve.');
            } finally {
                // Réactiver le bouton
                dreamBtn.disabled = false;
            }
        }

        async function searchMemory() {
            const searchInput = document.getElementById('search-input');
            const query = searchInput.value.trim();

            if (!query) {
                alert('Veuillez entrer un terme de recherche.');
                return;
            }

            try {
                // Rechercher dans la mémoire
                const results = await window.thermalMemory.search(query);

                // Afficher les résultats
                if (results.length === 0) {
                    alert('Aucun résultat trouvé pour cette recherche.');
                    return;
                }

                // Créer une liste de résultats
                let resultsList = 'Résultats de recherche:\n\n';
                results.forEach((result, index) => {
                    resultsList += `${index + 1}. ${result.type} (${result.temperature.toFixed(2)}): ${result.content.substring(0, 100)}${result.content.length > 100 ? '...' : ''}\n\n`;
                });

                alert(resultsList);
            } catch (error) {
                console.error('Erreur lors de la recherche:', error);
                alert('Une erreur est survenue lors de la recherche.');
            }
        }

        function showAddEntryForm() {
            // Créer un formulaire pour ajouter une entrée
            const type = prompt('Type d\'entrée:');
            if (!type) return;

            const content = prompt('Contenu:');
            if (!content) return;

            const temperatureStr = prompt('Température (0.0 - 1.0):', '0.7');
            if (!temperatureStr) return;

            const temperature = parseFloat(temperatureStr);
            if (isNaN(temperature) || temperature < 0 || temperature > 1) {
                alert('La température doit être un nombre entre 0 et 1.');
                return;
            }

            const tagsStr = prompt('Tags (séparés par des virgules):', 'tag1, tag2');
            const tags = tagsStr ? tagsStr.split(',').map(tag => tag.trim()) : [];

            // Ajouter l'entrée
            try {
                window.thermalMemory.addEntry(type, content, temperature, tags);
                alert('Entrée ajoutée avec succès.');

                // Mettre à jour l'interface
                updateInterface();
                updateMemoryChart();
            } catch (error) {
                console.error('Erreur lors de l\'ajout de l\'entrée:', error);
                alert('Une erreur est survenue lors de l\'ajout de l\'entrée.');
            }
        }

        function refreshAll() {
            // Mettre à jour l'interface
            updateInterface();
            updateMemoryChart();
        }

        // Fonctions supplémentaires pour les actions sur les entrées
        function viewEntry(id) {
            const entry = window.thermalMemory.getEntryById(id);
            if (!entry) {
                alert('Entrée non trouvée.');
                return;
            }

            // Afficher les détails de l'entrée
            let details = `Type: ${entry.type}\n`;
            details += `Température: ${entry.temperature.toFixed(2)}\n`;
            details += `Tags: ${entry.tags ? entry.tags.join(', ') : 'Aucun'}\n`;
            details += `Date: ${new Date(entry.timestamp).toLocaleString()}\n\n`;
            details += `Contenu:\n${entry.content}`;

            alert(details);
        }

        function boostEntry(id) {
            try {
                window.thermalMemory.boostEntry(id);
                alert('Entrée boostée avec succès.');

                // Mettre à jour l'interface
                updateInterface();
                updateMemoryChart();
            } catch (error) {
                console.error('Erreur lors du boost de l\'entrée:', error);
                alert('Une erreur est survenue lors du boost de l\'entrée.');
            }
        }

        function deleteEntry(id) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette entrée ?')) {
                return;
            }

            try {
                window.thermalMemory.removeEntry(id);
                alert('Entrée supprimée avec succès.');

                // Mettre à jour l'interface
                updateInterface();
                updateMemoryChart();
            } catch (error) {
                console.error('Erreur lors de la suppression de l\'entrée:', error);
                alert('Une erreur est survenue lors de la suppression de l\'entrée.');
            }
        }

        // Fonctions pour le curseur de température
        async function initTemperatureCursor() {
            try {
                // Récupérer la position actuelle du curseur
                const response = await fetch('/api/thermal/memory/temperature-cursor');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour le curseur
                    const cursor = document.getElementById('temperature-cursor');
                    cursor.value = data.position;

                    // Mettre à jour la valeur affichée
                    document.getElementById('temperature-cursor-value').textContent = data.position.toFixed(2);

                    // Mettre à jour les seuils de température
                    updateTemperatureThresholds(data.thresholds);
                }
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du curseur de température:', error);
            }
        }

        function updateTemperatureCursorValue() {
            const cursor = document.getElementById('temperature-cursor');
            const value = parseFloat(cursor.value);

            // Mettre à jour la valeur affichée
            document.getElementById('temperature-cursor-value').textContent = value.toFixed(2);

            // Mettre à jour la couleur en fonction de la valeur
            const valueElement = document.getElementById('temperature-cursor-value');
            if (value >= 0.8) {
                valueElement.className = 'zone-temp hot';
            } else if (value >= 0.6) {
                valueElement.className = 'zone-temp warm';
            } else if (value >= 0.4) {
                valueElement.className = 'zone-temp medium';
            } else {
                valueElement.className = 'zone-temp cool';
            }
        }

        async function setTemperatureCursorPosition() {
            const cursor = document.getElementById('temperature-cursor');
            const position = parseFloat(cursor.value);

            try {
                // Envoyer la nouvelle position au serveur
                const response = await fetch('/api/thermal/memory/temperature-cursor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ position })
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour les seuils de température
                    updateTemperatureThresholds(data.thresholds);

                    // Mettre à jour l'interface
                    updateInterface();
                    updateMemoryChart();
                } else {
                    console.error('Erreur lors de la définition de la position du curseur:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de la définition de la position du curseur:', error);
            }
        }

        function updateTemperatureThresholds(thresholds) {
            // Mettre à jour les seuils affichés
            document.getElementById('instant-temp').textContent = thresholds.instant.toFixed(2);
            document.getElementById('short-term-temp').textContent = thresholds.shortTerm.toFixed(2);
            document.getElementById('working-memory-temp').textContent = thresholds.working.toFixed(2);
            document.getElementById('medium-term-temp').textContent = thresholds.mediumTerm.toFixed(2);
            document.getElementById('long-term-temp').textContent = thresholds.longTerm.toFixed(2);
            document.getElementById('dream-memory-temp').textContent = (thresholds.longTerm * 0.5).toFixed(2);
        }

        async function resetTemperatureCursor() {
            // Réinitialiser le curseur à 0.5
            const cursor = document.getElementById('temperature-cursor');
            cursor.value = 0.5;

            // Mettre à jour la valeur affichée
            document.getElementById('temperature-cursor-value').textContent = '0.50';
            document.getElementById('temperature-cursor-value').className = 'zone-temp medium';

            // Envoyer la nouvelle position au serveur
            await setTemperatureCursorPosition();
        }

        // Fonctions pour les températures du système
        async function initSystemTemperatures() {
            try {
                // Récupérer les températures du système
                const response = await fetch('/api/thermal/system-temperatures');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec les températures du système
                    updateSystemTemperatures(data.temperatures);

                    // Mettre à jour le toggle des températures réelles
                    document.getElementById('use-real-temps-toggle').checked = data.useRealTemperatures;
                }

                // Mettre à jour les températures toutes les 5 secondes
                setInterval(fetchSystemTemperatures, 5000);
            } catch (error) {
                console.error('Erreur lors de l\'initialisation des températures du système:', error);
            }
        }

        async function fetchSystemTemperatures() {
            try {
                // Récupérer les températures du système
                const response = await fetch('/api/thermal/system-temperatures');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec les températures du système
                    updateSystemTemperatures(data.temperatures);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des températures du système:', error);
            }
        }

        function updateSystemTemperatures(temperatures) {
            // Mettre à jour les températures affichées
            document.getElementById('cpu-temp').textContent = `${temperatures.cpu.toFixed(1)}°C`;
            document.getElementById('gpu-temp').textContent = `${temperatures.gpu.toFixed(1)}°C`;
            document.getElementById('memory-temp').textContent = `${temperatures.memory.toFixed(1)}°C`;
            document.getElementById('normalized-temp').textContent = temperatures.normalized.toFixed(2);

            // Mettre à jour les barres de progression
            // Considérer 30°C comme minimum et 90°C comme maximum
            const minTemp = 30;
            const maxTemp = 90;

            const cpuPercent = Math.min(100, Math.max(0, ((temperatures.cpu - minTemp) / (maxTemp - minTemp)) * 100));
            const gpuPercent = Math.min(100, Math.max(0, ((temperatures.gpu - minTemp) / (maxTemp - minTemp)) * 100));
            const memoryPercent = Math.min(100, Math.max(0, ((temperatures.memory - minTemp) / (maxTemp - minTemp)) * 100));

            document.getElementById('cpu-temp-fill').style.width = `${cpuPercent}%`;
            document.getElementById('gpu-temp-fill').style.width = `${gpuPercent}%`;
            document.getElementById('memory-temp-fill').style.width = `${memoryPercent}%`;

            // Mettre à jour les classes de température
            updateTemperatureClass('cpu-temp', temperatures.cpu);
            updateTemperatureClass('gpu-temp', temperatures.gpu);
            updateTemperatureClass('memory-temp', temperatures.memory);
        }

        function updateTemperatureClass(elementId, temperature) {
            const element = document.getElementById(elementId);

            if (temperature >= 70) {
                element.className = 'zone-temp hot';
            } else if (temperature >= 50) {
                element.className = 'zone-temp warm';
            } else if (temperature >= 40) {
                element.className = 'zone-temp medium';
            } else {
                element.className = 'zone-temp cool';
            }
        }

        async function toggleRealTemperatures(event) {
            try {
                const useRealTemps = event.target.checked;

                // Envoyer la nouvelle valeur au serveur
                const response = await fetch('/api/thermal/use-real-temperatures', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ useRealTemps })
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateSystemTemperatures(data.temperatures);

                    // Mettre à jour le curseur de température
                    document.getElementById('temperature-cursor').value = data.temperatureCursor;
                    document.getElementById('temperature-cursor-value').textContent = data.temperatureCursor.toFixed(2);

                    // Mettre à jour les seuils de température
                    updateTemperatureThresholds(data.thresholds);

                    // Afficher un message
                    const message = useRealTemps ?
                        'Utilisation des températures réelles activée. Le curseur suivra maintenant les températures du système.' :
                        'Utilisation des températures réelles désactivée. Le curseur peut être ajusté manuellement.';

                    alert(message);
                } else {
                    console.error('Erreur lors de la définition de l\'utilisation des températures réelles:', data.error);
                    // Remettre le toggle dans son état précédent
                    event.target.checked = !useRealTemps;
                }
            } catch (error) {
                console.error('Erreur lors de la définition de l\'utilisation des températures réelles:', error);
                // Remettre le toggle dans son état précédent
                event.target.checked = !event.target.checked;
            }
        }

        // Fonctions pour le système d'auto-apprentissage
        async function initLearningSystem() {
            try {
                // Récupérer les statistiques de la mémoire
                const response = await fetch('/api/thermal/memory/stats');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec les données d'auto-apprentissage
                    updateLearningUI(data.stats.autoEvolution);

                    // Mettre à jour les toggles
                    document.getElementById('auto-evolution-toggle').checked = data.stats.autoEvolution.enabled;
                    document.getElementById('adaptation-toggle').checked = data.stats.autoEvolution.adaptationEnabled;
                }

                // Mettre à jour les métriques toutes les 10 secondes
                setInterval(fetchLearningMetrics, 10000);
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du système d\'auto-apprentissage:', error);
            }
        }

        async function fetchLearningMetrics() {
            try {
                // Récupérer les métriques de performance
                const response = await fetch('/api/thermal/learning-metrics');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec les métriques
                    updateLearningMetrics(data.metrics);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des métriques d\'apprentissage:', error);
            }
        }

        function updateLearningUI(autoEvolution) {
            // Mettre à jour le statut
            const evolutionStatus = document.getElementById('evolution-status');
            if (autoEvolution.enabled) {
                evolutionStatus.textContent = 'Actif';
                evolutionStatus.style.backgroundColor = 'var(--success)';
            } else {
                evolutionStatus.textContent = 'Inactif';
                evolutionStatus.style.backgroundColor = 'var(--danger)';
            }

            // Mettre à jour les métriques
            updateLearningMetrics(autoEvolution.performanceMetrics);

            // Mettre à jour les paramètres
            console.log('Paramètres optimisés:', autoEvolution.parameters);
        }

        function updateLearningMetrics(metrics) {
            // Mettre à jour les métriques de performance
            const memoryEfficiency = Math.round(metrics.memoryEfficiency * 100);
            const temperatureStability = Math.round(metrics.temperatureStability * 100);
            const accessOptimization = Math.round(metrics.accessOptimization * 100);
            const overallPerformance = Math.round(metrics.overallPerformance * 100);

            // Mettre à jour les valeurs
            document.getElementById('memory-efficiency').textContent = `${memoryEfficiency}%`;
            document.getElementById('temperature-stability').textContent = `${temperatureStability}%`;
            document.getElementById('access-optimization').textContent = `${accessOptimization}%`;
            document.getElementById('overall-performance').textContent = `${overallPerformance}%`;

            // Mettre à jour les barres de progression
            document.getElementById('memory-efficiency-fill').style.width = `${memoryEfficiency}%`;
            document.getElementById('temperature-stability-fill').style.width = `${temperatureStability}%`;
            document.getElementById('access-optimization-fill').style.width = `${accessOptimization}%`;
            document.getElementById('overall-performance-fill').style.width = `${overallPerformance}%`;

            // Mettre à jour les classes de couleur
            updateMetricClass('memory-efficiency-fill', memoryEfficiency);
            updateMetricClass('temperature-stability-fill', temperatureStability);
            updateMetricClass('access-optimization-fill', accessOptimization);
            updateMetricClass('overall-performance-fill', overallPerformance);
        }

        function updateMetricClass(elementId, value) {
            const element = document.getElementById(elementId);

            if (value >= 80) {
                element.className = 'progress-fill hot';
            } else if (value >= 60) {
                element.className = 'progress-fill warm';
            } else if (value >= 40) {
                element.className = 'progress-fill medium';
            } else {
                element.className = 'progress-fill cool';
            }
        }

        async function toggleAutoEvolution(event) {
            try {
                const enabled = event.target.checked;

                // Envoyer la nouvelle valeur au serveur
                const response = await fetch('/api/thermal/auto-evolution', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateLearningUI(data.stats);

                    // Afficher un message
                    const message = enabled ?
                        'Auto-évolution activée. Le système optimisera automatiquement ses paramètres.' :
                        'Auto-évolution désactivée. Les paramètres resteront fixes.';

                    alert(message);
                } else {
                    console.error('Erreur lors de la définition de l\'auto-évolution:', data.error);
                    // Remettre le toggle dans son état précédent
                    event.target.checked = !enabled;
                }
            } catch (error) {
                console.error('Erreur lors de la définition de l\'auto-évolution:', error);
                // Remettre le toggle dans son état précédent
                event.target.checked = !event.target.checked;
            }
        }

        async function toggleAdaptation(event) {
            try {
                const enabled = event.target.checked;

                // Envoyer la nouvelle valeur au serveur
                const response = await fetch('/api/thermal/adaptation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateLearningUI(data.stats);

                    // Afficher un message
                    const message = enabled ?
                        'Adaptation automatique activée. Le système ajustera ses paramètres en fonction des performances.' :
                        'Adaptation automatique désactivée. Les optimisations seront plus conservatrices.';

                    alert(message);
                } else {
                    console.error('Erreur lors de la définition de l\'adaptation:', data.error);
                    // Remettre le toggle dans son état précédent
                    event.target.checked = !enabled;
                }
            } catch (error) {
                console.error('Erreur lors de la définition de l\'adaptation:', error);
                // Remettre le toggle dans son état précédent
                event.target.checked = !event.target.checked;
            }
        }

        async function forceOptimization() {
            try {
                // Désactiver le bouton pendant l'optimisation
                const button = document.getElementById('force-optimization-btn');
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimisation en cours...';

                // Envoyer la requête au serveur
                const response = await fetch('/api/thermal/force-optimization', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateLearningMetrics(data.performanceMetrics);

                    // Afficher un message
                    alert('Optimisation forcée terminée avec succès. Les paramètres ont été ajustés pour améliorer les performances.');

                    // Mettre à jour les statistiques
                    updateMemoryStats();
                } else {
                    console.error('Erreur lors de l\'optimisation forcée:', data.error);
                    alert('Erreur lors de l\'optimisation forcée: ' + data.error);
                }

                // Réactiver le bouton
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync"></i> Forcer l\'optimisation';
            } catch (error) {
                console.error('Erreur lors de l\'optimisation forcée:', error);
                alert('Erreur lors de l\'optimisation forcée: ' + error.message);

                // Réactiver le bouton
                const button = document.getElementById('force-optimization-btn');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync"></i> Forcer l\'optimisation';
            }
        }

        async function viewLearningInsights() {
            try {
                // Récupérer les insights
                const response = await fetch('/api/thermal/learning-insights');
                const data = await response.json();

                if (data.success) {
                    // Afficher les insights
                    let message = 'Insights du système d\'apprentissage:\n\n';

                    if (Object.keys(data.insights).length === 0) {
                        message += 'Aucun insight disponible pour le moment. Le système continue d\'apprendre...';
                    } else {
                        for (const [key, value] of Object.entries(data.insights)) {
                            message += `${key}: ${value}\n`;
                        }
                    }

                    alert(message);
                } else {
                    console.error('Erreur lors de la récupération des insights:', data.error);
                    alert('Erreur lors de la récupération des insights: ' + data.error);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des insights:', error);
                alert('Erreur lors de la récupération des insights: ' + error.message);
            }
        }

        // Fonctions pour le mode sommeil
        let lastUserActivity = Date.now();
        let userIsActive = true;

        async function initSleepMode() {
            try {
                // Récupérer l'état du mode sommeil
                const response = await fetch('/api/thermal/sleep-mode');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec l'état du mode sommeil
                    updateSleepModeUI(data.sleepMode);

                    // Mettre à jour le toggle
                    document.getElementById('sleep-mode-toggle').checked = data.sleepMode.enabled;
                }

                // Mettre à jour l'état du mode sommeil toutes les 10 secondes
                setInterval(fetchSleepModeStatus, 10000);

                // Ajouter des écouteurs d'événements pour détecter l'activité utilisateur
                document.addEventListener('mousemove', userActivityDetected);
                document.addEventListener('keydown', userActivityDetected);
                document.addEventListener('click', userActivityDetected);
                document.addEventListener('scroll', userActivityDetected);
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du mode sommeil:', error);
            }
        }

        async function fetchSleepModeStatus() {
            try {
                // Récupérer l'état du mode sommeil
                const response = await fetch('/api/thermal/sleep-mode');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface avec l'état du mode sommeil
                    updateSleepModeUI(data.sleepMode);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération de l\'état du mode sommeil:', error);
            }
        }

        function updateSleepModeUI(sleepMode) {
            // Mettre à jour le statut
            const sleepStatus = document.getElementById('sleep-status');
            if (sleepMode.active) {
                sleepStatus.textContent = 'Actif';
                sleepStatus.style.backgroundColor = 'var(--success)';
            } else {
                sleepStatus.textContent = 'Inactif';
                sleepStatus.style.backgroundColor = 'var(--text-tertiary)';
            }

            // Mettre à jour l'état actuel
            document.getElementById('sleep-current-status').textContent = sleepMode.active ? 'En sommeil' : 'Éveillé';

            // Mettre à jour la dernière activité
            const lastActivity = new Date(sleepMode.lastActivity).toLocaleString();
            document.getElementById('sleep-last-activity').textContent = lastActivity;

            // Mettre à jour le nombre de consolidations
            document.getElementById('sleep-consolidation-count').textContent = sleepMode.consolidationCount || 0;

            // Mettre à jour la dernière consolidation
            const lastConsolidation = sleepMode.lastConsolidation ? new Date(sleepMode.lastConsolidation).toLocaleString() : 'Jamais';
            document.getElementById('sleep-last-consolidation').textContent = lastConsolidation;

            // Mettre à jour le bouton de forçage du mode sommeil
            const forceSleepBtn = document.getElementById('force-sleep-btn');
            if (sleepMode.active) {
                forceSleepBtn.innerHTML = '<i class="fas fa-coffee"></i> Sortir du mode sommeil';
            } else {
                forceSleepBtn.innerHTML = '<i class="fas fa-bed"></i> Forcer le mode sommeil';
            }
        }

        function userActivityDetected() {
            lastUserActivity = Date.now();
            userIsActive = true;
        }

        async function checkUserActivity() {
            if (userIsActive) {
                // Signaler l'activité au serveur
                try {
                    await fetch('/api/thermal/signal-activity', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    // Réinitialiser le flag d'activité
                    userIsActive = false;
                } catch (error) {
                    console.error('Erreur lors de la signalisation de l\'activité:', error);
                }
            }
        }

        async function toggleSleepMode(event) {
            try {
                const enabled = event.target.checked;

                // Envoyer la nouvelle valeur au serveur
                const response = await fetch('/api/thermal/sleep-mode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateSleepModeUI(data.sleepMode);

                    // Afficher un message
                    const message = enabled ?
                        'Mode sommeil automatique activé. Le système entrera en mode sommeil après 5 minutes d\'inactivité.' :
                        'Mode sommeil automatique désactivé. Le système ne consolidera pas automatiquement la mémoire.';

                    alert(message);
                } else {
                    console.error('Erreur lors de la définition du mode sommeil:', data.error);
                    // Remettre le toggle dans son état précédent
                    event.target.checked = !enabled;
                }
            } catch (error) {
                console.error('Erreur lors de la définition du mode sommeil:', error);
                // Remettre le toggle dans son état précédent
                event.target.checked = !event.target.checked;
            }
        }

        async function forceSleepMode() {
            try {
                // Récupérer l'état actuel du mode sommeil
                const response = await fetch('/api/thermal/sleep-mode');
                const data = await response.json();

                if (data.success) {
                    const isActive = data.sleepMode.active;

                    if (isActive) {
                        // Si déjà en mode sommeil, signaler une activité pour en sortir
                        await fetch('/api/thermal/signal-activity', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        alert('Sortie du mode sommeil forcée.');
                    } else {
                        // Sinon, forcer l'entrée en mode sommeil en désactivant temporairement la détection d'activité
                        userIsActive = false;

                        // Attendre 2 secondes pour simuler l'inactivité
                        setTimeout(async () => {
                            // Vérifier que le mode sommeil est activé
                            const sleepResponse = await fetch('/api/thermal/sleep-mode');
                            const sleepData = await sleepResponse.json();

                            if (sleepData.success && sleepData.sleepMode.enabled) {
                                // Forcer l'entrée en mode sommeil
                                const inactivityThreshold = sleepData.sleepMode.inactivityThreshold;
                                lastUserActivity = Date.now() - inactivityThreshold - 1000;

                                alert('Entrée en mode sommeil forcée. La consolidation de mémoire va commencer.');
                            } else {
                                alert('Impossible de forcer le mode sommeil car il est désactivé. Veuillez l\'activer d\'abord.');
                            }
                        }, 2000);
                    }
                }
            } catch (error) {
                console.error('Erreur lors du forçage du mode sommeil:', error);
                alert('Erreur lors du forçage du mode sommeil: ' + error.message);
            }
        }

        async function forceConsolidation() {
            try {
                // Désactiver le bouton pendant la consolidation
                const button = document.getElementById('force-consolidation-btn');
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Consolidation en cours...';

                // Envoyer la requête au serveur
                const response = await fetch('/api/thermal/force-consolidation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Mettre à jour l'interface
                    updateSleepModeUI(data.sleepMode);

                    // Afficher un message
                    alert('Consolidation de mémoire forcée terminée avec succès. Les connexions entre les entrées similaires ont été renforcées et les informations redondantes ont été nettoyées.');

                    // Mettre à jour les statistiques
                    updateMemoryStats();
                } else {
                    console.error('Erreur lors de la consolidation forcée:', data.error);
                    alert('Erreur lors de la consolidation forcée: ' + data.error);
                }

                // Réactiver le bouton
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-compress-arrows-alt"></i> Forcer la consolidation';
            } catch (error) {
                console.error('Erreur lors de la consolidation forcée:', error);
                alert('Erreur lors de la consolidation forcée: ' + error.message);

                // Réactiver le bouton
                const button = document.getElementById('force-consolidation-btn');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-compress-arrows-alt"></i> Forcer la consolidation';
            }
        }

        // Variables globales pour le monitoring Qi/Neurones
        let qiNeuronMonitoringActive = false;
        let qiNeuronUpdateInterval = null;

        // Fonctions pour le monitoring Qi/Neurones
        async function initQiNeuronMonitoring() {
            try {
                // Récupérer l'état initial
                const response = await fetch('/api/qi-neuron/current');
                const data = await response.json();

                if (data.success) {
                    updateQiNeuronUI(data.state);
                }

                // Vérifier le statut du monitoring
                const statsResponse = await fetch('/api/qi-neuron/stats');
                const statsData = await statsResponse.json();

                if (statsData.success && statsData.stats.isRunning) {
                    qiNeuronMonitoringActive = true;
                    updateQiNeuronStatus('Actif', true);
                    document.getElementById('qi-neuron-monitoring-toggle').checked = true;
                    document.getElementById('qi-neuron-start-btn').disabled = true;
                    document.getElementById('qi-neuron-stop-btn').disabled = false;

                    // Démarrer les mises à jour périodiques
                    startQiNeuronUpdates();
                } else {
                    updateQiNeuronStatus('Inactif', false);
                }
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du monitoring Qi/Neurones:', error);
                updateQiNeuronStatus('Erreur', false);
            }
        }

        async function toggleQiNeuronMonitoring(event) {
            if (event.target.checked) {
                await startQiNeuronMonitoring();
            } else {
                await stopQiNeuronMonitoring();
            }
        }

        async function startQiNeuronMonitoring() {
            try {
                // Démarrer le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/start', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    qiNeuronMonitoringActive = true;
                    updateQiNeuronStatus('Actif', true);

                    // Mettre à jour les contrôles
                    document.getElementById('qi-neuron-monitoring-toggle').checked = true;
                    document.getElementById('qi-neuron-start-btn').disabled = true;
                    document.getElementById('qi-neuron-stop-btn').disabled = false;

                    // Démarrer les mises à jour périodiques
                    startQiNeuronUpdates();

                    console.log('Monitoring Qi/Neurones démarré');
                } else {
                    console.error('Erreur lors du démarrage du monitoring:', data.error);
                    alert('Erreur lors du démarrage du monitoring: ' + data.error);
                }
            } catch (error) {
                console.error('Erreur lors du démarrage du monitoring:', error);
                alert('Erreur lors du démarrage du monitoring: ' + error.message);
            }
        }

        async function stopQiNeuronMonitoring() {
            try {
                // Arrêter le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/stop', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    qiNeuronMonitoringActive = false;
                    updateQiNeuronStatus('Inactif', false);

                    // Mettre à jour les contrôles
                    document.getElementById('qi-neuron-monitoring-toggle').checked = false;
                    document.getElementById('qi-neuron-start-btn').disabled = false;
                    document.getElementById('qi-neuron-stop-btn').disabled = true;

                    // Arrêter les mises à jour périodiques
                    stopQiNeuronUpdates();

                    console.log('Monitoring Qi/Neurones arrêté');
                } else {
                    console.error('Erreur lors de l\'arrêt du monitoring:', data.error);
                    alert('Erreur lors de l\'arrêt du monitoring: ' + data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'arrêt du monitoring:', error);
                alert('Erreur lors de l\'arrêt du monitoring: ' + error.message);
            }
        }

        function startQiNeuronUpdates() {
            if (qiNeuronUpdateInterval) {
                clearInterval(qiNeuronUpdateInterval);
            }

            // Mettre à jour toutes les 3 secondes
            qiNeuronUpdateInterval = setInterval(updateQiNeuronData, 3000);

            // Première mise à jour immédiate
            updateQiNeuronData();
        }

        function stopQiNeuronUpdates() {
            if (qiNeuronUpdateInterval) {
                clearInterval(qiNeuronUpdateInterval);
                qiNeuronUpdateInterval = null;
            }
        }

        async function updateQiNeuronData() {
            try {
                const response = await fetch('/api/qi-neuron/current');
                const data = await response.json();

                if (data.success) {
                    updateQiNeuronUI(data.state);
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour des données Qi/Neurones:', error);
            }
        }

        function updateQiNeuronUI(state) {
            // Mettre à jour les métriques Qi
            if (state.qi) {
                const qi = state.qi;

                document.getElementById('qi-level').textContent = qi.level.toFixed(3);
                document.getElementById('qi-level-fill').style.width = (qi.level * 100) + '%';
                document.getElementById('qi-flow').textContent = qi.flow.toFixed(3);
                document.getElementById('qi-stability').textContent = qi.stability.toFixed(3);
                document.getElementById('qi-harmony').textContent = qi.harmony.toFixed(3);
                document.getElementById('qi-vitality').textContent = qi.vitality.toFixed(3);

                // Mettre à jour l'indicateur de flux
                const flowIndicator = document.getElementById('qi-flow-indicator');
                if (qi.flow > 0.001) {
                    flowIndicator.textContent = '↗️';
                    flowIndicator.className = 'flow-indicator flow-up';
                } else if (qi.flow < -0.001) {
                    flowIndicator.textContent = '↘️';
                    flowIndicator.className = 'flow-indicator flow-down';
                } else {
                    flowIndicator.textContent = '➡️';
                    flowIndicator.className = 'flow-indicator flow-stable';
                }
            }

            // Mettre à jour les métriques neuronales
            if (state.neurons) {
                const neurons = state.neurons;

                document.getElementById('neuron-active-connections').textContent = neurons.activeConnections;
                document.getElementById('neuron-density').textContent = neurons.networkDensity.toFixed(3);
                document.getElementById('neuron-density-fill').style.width = (neurons.networkDensity * 100) + '%';
                document.getElementById('neuron-strength').textContent = neurons.synapticStrength.toFixed(3);
                document.getElementById('neuron-activity').textContent = neurons.neuralActivity.toFixed(3);
                document.getElementById('neuron-plasticity').textContent = neurons.plasticity.toFixed(3);
            }
        }

        function updateQiNeuronStatus(text, isActive) {
            const statusElement = document.getElementById('qi-neuron-status');
            statusElement.textContent = text;

            if (isActive) {
                statusElement.className = 'card-badge qi-neuron-active';
            } else {
                statusElement.className = 'card-badge qi-neuron-inactive';
            }
        }

        async function viewQiNeuronEvolution() {
            try {
                // Récupérer l'historique
                const response = await fetch('/api/qi-neuron/history?limit=50');
                const data = await response.json();

                if (data.success) {
                    // Ouvrir la page de monitoring dédiée dans un nouvel onglet
                    window.open('/qi-neuron-monitor.html', '_blank');
                } else {
                    console.error('Erreur lors de la récupération de l\'historique:', data.error);
                    alert('Erreur lors de la récupération de l\'historique: ' + data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'ouverture de la vue évolution:', error);
                alert('Erreur lors de l\'ouverture de la vue évolution: ' + error.message);
            }
        }
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
