<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synchronisation de Mémoire - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .sync-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sync-section {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: var(--border-light);
        }

        .sync-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .sync-header i {
            font-size: 24px;
            margin-right: 10px;
            color: var(--accent);
        }

        .sync-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agents-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .agent-card {
            flex: 1;
            background: linear-gradient(135deg, var(--bg-card), rgba(13, 71, 161, 0.85));
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .agent-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .agent-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }

        .agent-details {
            flex-grow: 1;
        }

        .agent-name {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agent-model {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .agent-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .agent-stat {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 8px 12px;
            font-size: 14px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .agent-stat i {
            margin-right: 8px;
            color: var(--accent);
        }

        .sync-options {
            margin-bottom: 20px;
        }

        .option-group {
            margin-bottom: 15px;
        }

        .option-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .option-input {
            width: 100%;
            padding: 10px;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .option-checkbox {
            margin-right: 8px;
        }

        .sync-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn {
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--accent);
            color: #000;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .sync-progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar-container {
            width: 100%;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--accent);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-status {
            font-size: 14px;
            color: var(--text-secondary);
            text-align: center;
        }

        .sync-results {
            margin-top: 20px;
            display: none;
        }

        .result-item {
            background-color: rgba(255, 255, 255, 0.08);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 10px;
            border-left: 3px solid var(--accent);
            transition: var(--transition-fast);
        }

        .result-item:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(3px);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .result-title {
            font-weight: bold;
            color: var(--text-primary);
        }

        .result-direction {
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
        }

        .result-content {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .result-footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }
    </style>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item active">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams-new.html" class="nav-item">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/agents.html" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/training.html" class="nav-item">
                <i class="fas fa-graduation-cap"></i>
                <span>Formation</span>
            </a>
            <a href="/settings-new.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Synchronisation de Mémoire</h1>
                <p class="interface-subtitle">Synchronisez la mémoire entre l'agent principal et l'agent de formation</p>
            </div>

            <div class="status-container">
                <a href="/futuristic-interface.html" class="action-button">
                    <i class="fas fa-arrow-left"></i> Retour à la Mémoire Thermique
                </a>
                <button class="action-button" id="refresh-btn">
                    <i class="fas fa-sync"></i> Actualiser
                </button>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="sync-container">
            <!-- Section des agents -->
            <div class="sync-section">
                <div class="sync-header">
                    <i class="fas fa-robot"></i>
                    <h2 class="sync-title">Agents</h2>
                </div>
                <div class="agents-container">
                    <!-- Agent principal -->
                    <div class="agent-card" id="main-agent-card">
                        <div class="agent-info">
                            <div class="agent-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="agent-details">
                                <div class="agent-name" id="main-agent-name">Claude (4GB)</div>
                                <div class="agent-model" id="main-agent-model">incept5/llama3.1-claude:latest</div>
                            </div>
                        </div>
                        <div class="agent-stats" id="main-agent-stats">
                            <div class="agent-stat">
                                <i class="fas fa-fire"></i>
                                <span id="main-agent-entries">0 entrées</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-thermometer-half"></i>
                                <span id="main-agent-temperature">Température: 0.0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Agent de formation -->
                    <div class="agent-card" id="training-agent-card">
                        <div class="agent-info">
                            <div class="agent-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="agent-details">
                                <div class="agent-name" id="training-agent-name">Agent de Formation</div>
                                <div class="agent-model" id="training-agent-model">llama3:8b</div>
                            </div>
                        </div>
                        <div class="agent-stats" id="training-agent-stats">
                            <div class="agent-stat">
                                <i class="fas fa-fire"></i>
                                <span id="training-agent-entries">0 entrées</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-thermometer-half"></i>
                                <span id="training-agent-temperature">Température: 0.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section des options de synchronisation -->
            <div class="sync-section">
                <div class="sync-header">
                    <i class="fas fa-cog"></i>
                    <h2 class="sync-title">Options de Synchronisation</h2>
                </div>
                <div class="sync-options">
                    <div class="option-group">
                        <label class="option-label">Seuil d'importance pour la synchronisation</label>
                        <input type="range" id="importance-threshold" class="option-input" min="0" max="1" step="0.1" value="0.5">
                        <div id="importance-threshold-value" style="text-align: center; margin-top: 5px;">0.5</div>
                    </div>
                    <div class="option-group">
                        <label class="option-checkbox-label">
                            <input type="checkbox" id="bidirectional-sync" class="option-checkbox" checked>
                            Synchronisation bidirectionnelle
                        </label>
                    </div>
                    <div class="option-group">
                        <label class="option-checkbox-label">
                            <input type="checkbox" id="auto-sync" class="option-checkbox">
                            Synchronisation automatique périodique
                        </label>
                    </div>
                </div>
                <div class="sync-actions">
                    <button class="btn btn-secondary" id="reset-btn">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button class="btn btn-primary" id="sync-btn">
                        <i class="fas fa-sync"></i> Synchroniser
                    </button>
                </div>
            </div>

            <!-- Section de progression de la synchronisation -->
            <div class="sync-section sync-progress" id="sync-progress-section">
                <div class="sync-header">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h2 class="sync-title">Progression de la Synchronisation</h2>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="sync-progress-bar"></div>
                </div>
                <div class="progress-status" id="sync-progress-status">Initialisation de la synchronisation...</div>
            </div>

            <!-- Section des résultats de la synchronisation -->
            <div class="sync-section sync-results" id="sync-results-section">
                <div class="sync-header">
                    <i class="fas fa-check-circle"></i>
                    <h2 class="sync-title">Résultats de la Synchronisation</h2>
                </div>
                <div id="sync-results-container">
                    <!-- Les résultats seront ajoutés ici dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="/js/memory-sync.js"></script>
    <script src="js/native-app.js"></script>
</body>
</html>
