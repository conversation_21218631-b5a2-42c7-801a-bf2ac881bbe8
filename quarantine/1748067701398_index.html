<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>na - Agent à Mémoire Thermique</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .hero-section {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .hero-title {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(to right, #ff6b6b, #54a0ff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            color: var(--text-secondary);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: var(--bg-card);
            border-radius: 10px;
            padding: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1); /* Ajouter une bordure subtile */
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2); /* Bordure plus visible au survol */
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--accent);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.3); /* Ajouter un effet de lueur */
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--text-primary);
            font-weight: bold; /* Rendre le titre plus visible */
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            font-size: 1.05rem; /* Légèrement plus grand pour une meilleure lisibilité */
            font-weight: 400; /* Un peu plus gras pour améliorer la lisibilité */
        }

        .cta-section {
            text-align: center;
            margin-top: 40px;
            padding: 40px;
            background: linear-gradient(135deg, rgba(13, 71, 161, 0.85), rgba(33, 150, 243, 0.85));
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Ajouter une ombre pour améliorer la lisibilité */
            font-weight: bold;
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .cta-button {
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 30px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .cta-button.primary {
            background: var(--accent);
            color: #000; /* Noir pour un meilleur contraste sur fond coloré */
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .cta-button.secondary {
            background: rgba(255, 255, 255, 0.9); /* Plus opaque pour un meilleur contraste */
            color: #0d47a1; /* Couleur foncée pour un meilleur contraste */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .cta-button i {
            margin-right: 10px;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/presentation.html" class="nav-item">
                <i class="fas fa-presentation"></i>
                <span>Présentation</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/agents" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/training" class="nav-item">
                <i class="fas fa-graduation-cap"></i>
                <span>Formation</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- Section héro avec animation -->
        <div class="hero-section">
            <div id="animation-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 0;"></div>
            <div style="position: relative; z-index: 1;">
                <h1 class="hero-title">Louna</h1>
                <p class="hero-subtitle">Agent intelligent avec mémoire thermique et accélérateurs Kyber</p>

                <div class="cta-buttons">
                    <a href="/presentation.html" class="cta-button primary">
                        <i class="fas fa-presentation"></i> Présentation Complète
                    </a>
                    <a href="/generation-studio.html" class="cta-button secondary">
                        <i class="fas fa-magic"></i> Studio de Génération
                    </a>
                    <a href="/futuristic-interface.html" class="cta-button secondary">
                        <i class="fas fa-fire"></i> Mémoire Thermique
                    </a>
                    <a href="/chat" class="cta-button secondary">
                        <i class="fas fa-comments"></i> Chat Intelligent
                    </a>
                </div>
            </div>
        </div>

        <!-- Grille de fonctionnalités -->
        <div class="feature-grid">
            <!-- Mémoire thermique -->
            <div class="feature-card">
                <i class="fas fa-fire feature-icon"></i>
                <h2 class="feature-title">Mémoire Thermique</h2>
                <p class="feature-description">
                    Système de mémoire avancé qui simule les différentes zones de mémoire du cerveau humain.
                    Les informations se déplacent entre les zones en fonction de leur importance et de leur fraîcheur.
                </p>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="feature-card">
                <i class="fas fa-bolt feature-icon"></i>
                <h2 class="feature-title">Accélérateurs Kyber</h2>
                <p class="feature-description">
                    Optimisez les performances de la mémoire thermique grâce aux accélérateurs Kyber.
                    Ajustez les facteurs de boost pour améliorer le traitement des informations.
                </p>
            </div>

            <!-- Studio de Génération -->
            <div class="feature-card">
                <i class="fas fa-magic feature-icon"></i>
                <h2 class="feature-title">Studio de Génération Illimitée</h2>
                <p class="feature-description">
                    Générez du contenu multimédia en quantité illimitée : vidéos 4K, images HD,
                    musiques originales et modèles 3D ultra-détaillés. Qualité professionnelle garantie.
                </p>
            </div>

            <!-- Monitoring Qi & Neurones -->
            <div class="feature-card">
                <i class="fas fa-yin-yang feature-icon"></i>
                <h2 class="feature-title">Monitoring Qi & Neurones</h2>
                <p class="feature-description">
                    Surveillez en temps réel l'énergie vitale du système et l'activité des connexions neuronales.
                    Visualisez l'évolution du Qi et la complexité du réseau neuronal.
                </p>
            </div>
        </div>

        <!-- Section CTA -->
        <div class="cta-section">
            <h2 class="cta-title">Prêt à explorer les capacités de Louna ?</h2>
            <div class="cta-buttons">
                <a href="/thermal-memory.html" class="cta-button primary">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
                <a href="/kyber-dashboard.html" class="cta-button secondary">
                    <i class="fas fa-bolt"></i> Accélérateurs Kyber
                </a>
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent intelligent avec mémoire thermique et accélérateurs Kyber
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/home-animation.js"></script>
    <script src="/js/notifications.js"></script>

    <script>
        // Afficher une notification de bienvenue
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                notifications.info(
                    "Bienvenue dans l'interface de l'agent Louna avec mémoire thermique et accélérateurs Kyber.",
                    "Bienvenue",
                    { duration: 8000 }
                );
            }, 1000);
        });
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
