<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Gestion des Agents</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        :root {
            --primary: #ff69b4;
            --primary-dark: #d44a91;
            --secondary: #9932cc;
            --secondary-dark: #7b28a4;
            --bg-dark: #1a1a1a;
            --bg-card: #2a2a2a;
            --text-primary: #f0f0f0;
            --text-secondary: #b0b0b0;
            --success: #4caf50;
            --warning: #ff9800;
            --error: #f44336;
            --info: #2196f3;
            --border-radius: 12px;
            --border-radius-sm: 6px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-dark);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 28px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header h1 i {
            color: var(--primary);
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 16px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        .nav-link.active {
            color: var(--primary);
            font-weight: 600;
        }

        .card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-title {
            font-size: 20px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title i {
            color: var(--primary);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-secondary {
            background-color: var(--secondary);
            color: white;
        }

        .btn-secondary:hover {
            background-color: var(--secondary-dark);
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background-color: var(--primary);
            color: white;
        }

        .btn-danger {
            background-color: var(--error);
            color: white;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .agent-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .agent-card.active {
            border: 1px solid var(--primary);
        }

        .agent-card.active::before {
            content: "Actif";
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: var(--primary);
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
        }

        .agent-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .agent-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .agent-info h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
        }

        .agent-info p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .agent-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .stat-item {
            background-color: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: var(--border-radius-sm);
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--primary);
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .agent-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.visible {
            display: flex;
        }

        .modal-content {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 25px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 20px;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--primary);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(0, 0, 0, 0.2);
            color: var(--text-primary);
            font-size: 16px;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            background-color: var(--bg-card);
            color: var(--text-primary);
            box-shadow: var(--shadow);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .notification.success {
            border-left: 4px solid var(--success);
        }

        .notification.error {
            border-left: 4px solid var(--error);
        }

        .notification.info {
            border-left: 4px solid var(--info);
        }

        .notification.warning {
            border-left: 4px solid var(--warning);
        }

        .loader {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.online {
            background-color: var(--success);
        }

        .status-indicator.offline {
            background-color: var(--error);
        }

        .status-indicator.loading {
            background-color: var(--warning);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        .empty-state i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-robot"></i> Gestion des Agents</h1>
            <div class="nav-links">
                <a href="/" class="nav-link"><i class="fas fa-home"></i> Accueil</a>
                <a href="/chat" class="nav-link"><i class="fas fa-comments"></i> Chat</a>
                <a href="/futuristic-interface.html" class="nav-link"><i class="fas fa-fire"></i> Mémoire Thermique</a>
                <a href="/brain-visualization.html" class="nav-link"><i class="fas fa-brain"></i> Visualisation 3D</a>
                <a href="/qi-neuron-monitor.html" class="nav-link"><i class="fas fa-yin-yang"></i> Monitoring Qi</a>
                <a href="/kyber-dashboard.html" class="nav-link"><i class="fas fa-bolt"></i> Accélérateurs Kyber</a>
                <a href="/memory-fusion.html" class="nav-link"><i class="fas fa-code-branch"></i> Fusion Mémoire</a>
                <a href="/performance.html" class="nav-link"><i class="fas fa-chart-line"></i> Performances</a>
                <a href="/agents.html" class="nav-link active"><i class="fas fa-robot"></i> Agents</a>
                <a href="/settings" class="nav-link"><i class="fas fa-cog"></i> Paramètres</a>
            </div>
        </header>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-server"></i> État du Serveur Ollama</h2>
                <button id="refresh-status" class="btn btn-outline btn-sm">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
            </div>
            <div id="ollama-status">
                <div class="loader"></div> Vérification de l'état d'Ollama...
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title"><i class="fas fa-robot"></i> Agents Disponibles</h2>
                <button id="add-agent-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajouter un Agent
                </button>
            </div>
            <div id="agents-container" class="grid">
                <!-- Les agents seront ajoutés ici dynamiquement -->
                <div class="empty-state">
                    <i class="fas fa-robot"></i>
                    <h3>Aucun agent disponible</h3>
                    <p>Ajoutez un nouvel agent pour commencer à interagir avec l'application.</p>
                    <button id="add-first-agent-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ajouter un Agent
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier un agent -->
    <div id="agent-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Ajouter un Agent</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <form id="agent-form">
                <input type="hidden" id="agent-id">
                <div class="form-group">
                    <label for="agent-name">Nom de l'Agent</label>
                    <input type="text" id="agent-name" class="form-control" placeholder="Ex: Assistant Principal" required>
                </div>
                <div class="form-group">
                    <label for="agent-type">Type d'Agent</label>
                    <select id="agent-type" class="form-control" required>
                        <option value="ollama">Ollama (Local)</option>
                        <option value="simulation" disabled>Simulation (Bientôt disponible)</option>
                    </select>
                </div>
                <div id="ollama-settings">
                    <div class="form-group">
                        <label for="agent-model">Modèle</label>
                        <select id="agent-model" class="form-control" required>
                            <option value="">Chargement des modèles...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="agent-temperature">Température (Créativité)</label>
                        <input type="range" id="agent-temperature" class="form-control" min="0" max="1" step="0.1" value="0.7">
                        <span id="temperature-value">0.7</span>
                    </div>
                    <div class="form-group">
                        <label for="agent-max-tokens">Nombre maximum de tokens</label>
                        <input type="number" id="agent-max-tokens" class="form-control" min="100" max="4096" value="1000">
                    </div>
                </div>
                <div class="form-group">
                    <label for="agent-description">Description</label>
                    <textarea id="agent-description" class="form-control" rows="3" placeholder="Description de l'agent et de ses capacités"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" id="cancel-btn">Annuler</button>
                    <button type="submit" class="btn btn-primary" id="save-agent-btn">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notification-message"></span>
    </div>

    <script src="js/agents.js"></script>
  <script src="js/native-app.js"></script>
</body>
</html>
