<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chargement - Louna</title>
    <style>
        :root {
            --primary: #ff69b4;
            --primary-light: #ffb6c1;
            --primary-dark: #c71585;
            --bg-primary: #1a1a2e;
            --text-primary: #ffffff;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            overflow: hidden;
            -webkit-user-select: none;
            user-select: none;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .logo {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            position: relative;
        }

        .logo-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 30px rgba(255, 105, 180, 0.5);
            animation: pulse 2s infinite;
        }

        .logo-icon {
            font-size: 60px;
            color: white;
        }

        .loading-text {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 30px;
            color: var(--text-primary);
        }

        .loading-bar {
            width: 300px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .loading-progress {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
            animation: loading 3s ease-in-out infinite;
        }

        .loading-status {
            margin-top: 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            height: 20px;
        }

        .version {
            position: absolute;
            bottom: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(255, 105, 180, 0.7);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
                left: 0;
            }
            50% {
                width: 100%;
                left: 0;
            }
            100% {
                width: 0%;
                left: 100%;
            }
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 105, 180, 0.3);
            pointer-events: none;
            animation: float linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <div class="particles" id="particles"></div>

    <div class="loading-container">
        <div class="logo">
            <div class="logo-circle">
                <div class="logo-icon">L</div>
            </div>
        </div>
        <div class="loading-text">Louna</div>
        <div class="loading-bar">
            <div class="loading-progress"></div>
        </div>
        <div class="loading-status" id="loading-status">Démarrage du serveur...</div>
    </div>

    <div class="version">Version 1.0.0</div>

    <script>
        // Créer des particules
        const particlesContainer = document.getElementById('particles');
        const particleCount = 30;

        for (let i = 0; i < particleCount; i++) {
            createParticle();
        }

        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Taille aléatoire
            const size = Math.random() * 10 + 5;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            
            // Position aléatoire
            const x = Math.random() * 100;
            const y = Math.random() * 100 + 100;
            particle.style.left = `${x}%`;
            particle.style.top = `${y}%`;
            
            // Opacité aléatoire
            particle.style.opacity = Math.random() * 0.5 + 0.1;
            
            // Durée d'animation aléatoire
            const duration = Math.random() * 20 + 10;
            particle.style.animationDuration = `${duration}s`;
            
            particlesContainer.appendChild(particle);
            
            // Supprimer la particule après l'animation
            setTimeout(() => {
                particle.remove();
                createParticle();
            }, duration * 1000);
        }

        // Mettre à jour le statut de chargement
        const loadingStatus = document.getElementById('loading-status');
        const loadingSteps = [
            'Démarrage du serveur...',
            'Initialisation de la mémoire thermique...',
            'Activation des accélérateurs Kyber...',
            'Chargement des agents...',
            'Préparation de l\'interface...'
        ];

        let currentStep = 0;
        const statusInterval = setInterval(() => {
            loadingStatus.textContent = loadingSteps[currentStep];
            currentStep = (currentStep + 1) % loadingSteps.length;
        }, 2000);

        // Nettoyer l'intervalle lorsque la page est déchargée
        window.addEventListener('beforeunload', () => {
            clearInterval(statusInterval);
        });
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
