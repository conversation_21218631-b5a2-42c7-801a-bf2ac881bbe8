<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring QI & Neurones - Louna</title>
    <link rel="stylesheet" href="css/native-app.css">
    <style>
        .monitoring-container {
            padding: 20px;
            margin-top: 60px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .metric-header h3 {
            color: #ff69b4;
            margin: 0;
            font-size: 18px;
        }

        .metric-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .metric-status.active {
            background: rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .primary-metric {
            text-align: center;
            margin-bottom: 20px;
        }

        .metric-value {
            font-size: 36px;
            font-weight: bold;
            color: #ff69b4;
            display: block;
        }

        .metric-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
        }

        .metric-unit {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.5);
        }

        .secondary-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .secondary-metric {
            text-align: center;
        }

        .secondary-metric .label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            display: block;
            margin-bottom: 5px;
        }

        .secondary-metric .value {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .progress-container {
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 5px;
            text-align: center;
        }

        .chart-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-header h3 {
            color: #ff69b4;
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .chart-container {
            width: 100%;
            height: 300px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin-bottom: 15px;
            position: relative;
        }

        #monitoring-chart {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .loading-indicator {
            display: block;
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.6);
        }

        .loading-indicator.hidden {
            display: none;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .interface-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .interface-title {
            font-size: 32px;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .interface-subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
        }

        .status-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .status-label {
            color: rgba(255, 255, 255, 0.7);
        }

        .status-text {
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item active">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring QI</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="monitoring-container">
        <div class="interface-header">
            <h1 class="interface-title">Monitoring QI & Neurones</h1>
            <p class="interface-subtitle">Évolution en temps réel de l'énergie vitale et de l'activité neuronale</p>
            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="qi-status-indicator"></div>
                <span class="status-text" id="qi-status-text">Monitoring actif</span>
            </div>
        </div>

        <!-- Indicateur de chargement -->
        <div class="loading-indicator" id="loading-indicator">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Chargement des données du cerveau artificiel...</p>
        </div>

        <!-- Grille de métriques -->
        <div class="metrics-grid" id="metrics-grid" style="display: none;">
            <!-- Les cartes seront générées dynamiquement -->
        </div>

        <!-- Graphique temps réel -->
        <div class="chart-section" id="chart-section" style="display: none;">
            <div class="chart-header">
                <h3><i class="fas fa-chart-line"></i> Évolution Temps Réel</h3>
                <div class="chart-controls">
                    <button class="control-btn" id="pause-chart">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="control-btn" id="reset-chart">
                        <i class="fas fa-redo"></i> Reset
                    </button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="monitoring-chart" width="800" height="300"></canvas>
            </div>
            <div class="chart-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>QI</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>Neurones Actifs</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>Efficacité</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96ceb4;"></div>
                    <span>Bonheur</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour le monitoring
        let isMonitoring = true;
        let updateInterval = null;
        let chartData = [];
        let maxDataPoints = 50;
        let chart = null;

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔮 Initialisation du monitoring Qi & Neurones...');

            // Démarrer le chargement des données
            setTimeout(() => {
                loadBrainData();
            }, 1000);

            // Configurer les boutons
            setupEventListeners();

            console.log('✅ Monitoring Qi & Neurones initialisé');
        });

        // Configuration des événements
        function setupEventListeners() {
            // Bouton actualiser
            document.getElementById('refresh-btn')?.addEventListener('click', function(e) {
                e.preventDefault();
                loadBrainData();
            });

            // Bouton pause/reprendre
            const pauseBtn = document.getElementById('pause-chart');
            if (pauseBtn) {
                pauseBtn.addEventListener('click', toggleMonitoring);
            }

            // Bouton reset
            const resetBtn = document.getElementById('reset-chart');
            if (resetBtn) {
                resetBtn.addEventListener('click', resetChart);
            }
        }

        // Charger les données du cerveau artificiel
        async function loadBrainData() {
            const loadingIndicator = document.getElementById('loading-indicator');
            const metricsGrid = document.getElementById('metrics-grid');
            const chartSection = document.getElementById('chart-section');

            try {
                // Afficher l'indicateur de chargement
                loadingIndicator.style.display = 'block';
                metricsGrid.style.display = 'none';
                chartSection.style.display = 'none';

                // Essayer de charger les vraies données
                const response = await fetch('/api/brain/qi-neuron-stats');
                let data;

                if (response.ok) {
                    data = await response.json();
                } else {
                    throw new Error('API non disponible');
                }

                // Afficher les données
                displayData(data);

            } catch (error) {
                console.log('API non disponible, utilisation de données simulées');
                // Utiliser des données simulées
                const simulatedData = generateSimulatedData();
                displayData(simulatedData);
            }
        }

        // Afficher les données
        function displayData(data) {
            const loadingIndicator = document.getElementById('loading-indicator');
            const metricsGrid = document.getElementById('metrics-grid');
            const chartSection = document.getElementById('chart-section');

            // Masquer l'indicateur de chargement
            loadingIndicator.style.display = 'none';

            // Créer les cartes de métriques
            createMetricCards(data);

            // Afficher les sections
            metricsGrid.style.display = 'grid';
            chartSection.style.display = 'block';

            // Initialiser le graphique
            initChart();
            addToChart(data);

            // Démarrer le monitoring automatique
            if (!updateInterval) {
                updateInterval = setInterval(() => {
                    if (isMonitoring) {
                        loadBrainData();
                    }
                }, 5000); // Mise à jour toutes les 5 secondes
            }
        }

        // Créer les cartes de métriques
        function createMetricCards(data) {
            const metricsGrid = document.getElementById('metrics-grid');

            metricsGrid.innerHTML = `
                <!-- Carte QI -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3><i class="fas fa-yin-yang"></i> Système QI</h3>
                        <div class="metric-status active">Actif</div>
                    </div>
                    <div class="metric-content">
                        <div class="primary-metric">
                            <span class="metric-label">QI Actuel</span>
                            <span class="metric-value">${data.qi?.current || 1001}</span>
                            <span class="metric-unit">points</span>
                        </div>
                        <div class="secondary-metrics">
                            <div class="secondary-metric">
                                <span class="label">Niveau Cognitif</span>
                                <span class="value">${data.qi?.level || 6}</span>
                            </div>
                            <div class="secondary-metric">
                                <span class="label">Points XP</span>
                                <span class="value">${data.qi?.experiencePoints || 150}</span>
                            </div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${((data.qi?.current || 1001) % 200) / 200 * 100}%"></div>
                            </div>
                            <span class="progress-text">Progression vers niveau suivant</span>
                        </div>
                    </div>
                </div>

                <!-- Carte Neurones -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3><i class="fas fa-brain"></i> Réseau Neuronal</h3>
                        <div class="metric-status active">Actif</div>
                    </div>
                    <div class="metric-content">
                        <div class="primary-metric">
                            <span class="metric-label">Neurones Totaux</span>
                            <span class="metric-value">${data.neurons?.total || 72}</span>
                            <span class="metric-unit">neurones</span>
                        </div>
                        <div class="secondary-metrics">
                            <div class="secondary-metric">
                                <span class="label">Neurones Actifs</span>
                                <span class="value">${data.neurons?.active || 49}</span>
                            </div>
                            <div class="secondary-metric">
                                <span class="label">Efficacité</span>
                                <span class="value">${data.neurons?.efficiency || 86}%</span>
                            </div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${data.neurons?.efficiency || 86}%"></div>
                            </div>
                            <span class="progress-text">Activité neuronale globale</span>
                        </div>
                    </div>
                </div>

                <!-- Carte État Émotionnel -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3><i class="fas fa-heart"></i> État Émotionnel</h3>
                        <div class="metric-status active">${data.emotional?.mood || 'Curieux'}</div>
                    </div>
                    <div class="metric-content">
                        <div class="primary-metric">
                            <span class="metric-label">Humeur</span>
                            <span class="metric-value">${data.emotional?.mood || 'Curieux'}</span>
                            <span class="metric-unit">${data.emotional?.moodIntensity || 100}%</span>
                        </div>
                        <div class="emotion-grid">
                            <div class="emotion-item">
                                <span class="emotion-name">Bonheur</span>
                                <div class="emotion-bar">
                                    <div class="emotion-fill" style="width: ${data.emotional?.happiness || 45}%"></div>
                                </div>
                                <span class="emotion-value">${data.emotional?.happiness || 45}%</span>
                            </div>
                            <div class="emotion-item">
                                <span class="emotion-name">Curiosité</span>
                                <div class="emotion-bar">
                                    <div class="emotion-fill" style="width: ${data.emotional?.curiosity || 100}%"></div>
                                </div>
                                <span class="emotion-value">${data.emotional?.curiosity || 100}%</span>
                            </div>
                            <div class="emotion-item">
                                <span class="emotion-name">Énergie</span>
                                <div class="emotion-bar">
                                    <div class="emotion-fill" style="width: ${data.emotional?.energy || 75}%"></div>
                                </div>
                                <span class="emotion-value">${data.emotional?.energy || 75}%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Carte Réseaux Spécialisés -->
                <div class="metric-card">
                    <div class="metric-header">
                        <h3><i class="fas fa-network-wired"></i> Réseaux Spécialisés</h3>
                        <div class="metric-status active">6 Réseaux</div>
                    </div>
                    <div class="metric-content">
                        <div class="network-list">
                            <div class="network-item">
                                <span class="network-name">Sensoriel</span>
                                <span class="network-count">${data.networks?.sensory || 12}</span>
                            </div>
                            <div class="network-item">
                                <span class="network-name">Mémoire de Travail</span>
                                <span class="network-count">${data.networks?.working || 8}</span>
                            </div>
                            <div class="network-item">
                                <span class="network-name">Long Terme</span>
                                <span class="network-count">${data.networks?.longTerm || 15}</span>
                            </div>
                            <div class="network-item">
                                <span class="network-name">Émotionnel</span>
                                <span class="network-count">${data.networks?.emotional || 10}</span>
                            </div>
                            <div class="network-item">
                                <span class="network-name">Exécutif</span>
                                <span class="network-count">${data.networks?.executive || 9}</span>
                            </div>
                            <div class="network-item">
                                <span class="network-name">Créatif</span>
                                <span class="network-count">${data.networks?.creative || 7}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Initialiser le graphique
        function initChart() {
            const canvas = document.getElementById('monitoring-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Ajuster la taille du canvas
            canvas.width = canvas.offsetWidth;
            canvas.height = 300;

            chart = {
                canvas: canvas,
                ctx: ctx,
                width: canvas.width,
                height: canvas.height
            };

            drawChart();
        }

        // Ajouter des données au graphique
        function addToChart(data) {
            const timestamp = new Date();

            chartData.push({
                time: timestamp,
                qi: data.qi?.current || 1001,
                neurons: data.neurons?.active || 49,
                efficiency: data.neurons?.efficiency || 86,
                happiness: data.emotional?.happiness || 45
            });

            // Limiter le nombre de points
            if (chartData.length > maxDataPoints) {
                chartData.shift();
            }

            drawChart();
        }

        // Dessiner le graphique
        function drawChart() {
            if (!chart) return;

            const { ctx, width, height } = chart;

            // Effacer le canvas
            ctx.clearRect(0, 0, width, height);

            if (chartData.length === 0) return;

            // Dessiner la grille
            drawGrid(ctx, width, height);

            // Dessiner les courbes
            drawLine(ctx, chartData.map(d => d.qi), '#ff6b6b', width, height, 900, 1100);
            drawLine(ctx, chartData.map(d => d.neurons), '#4ecdc4', width, height, 0, 100);
            drawLine(ctx, chartData.map(d => d.efficiency), '#45b7d1', width, height, 0, 100);
            drawLine(ctx, chartData.map(d => d.happiness), '#96ceb4', width, height, 0, 100);
        }

        // Dessiner la grille
        function drawGrid(ctx, width, height) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Lignes horizontales
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Lignes verticales
            for (let i = 0; i <= 10; i++) {
                const x = (width / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }
        }

        // Dessiner une ligne
        function drawLine(ctx, data, color, width, height, minVal = 0, maxVal = 100) {
            if (data.length < 2) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < data.length; i++) {
                const x = (width / (maxDataPoints - 1)) * i;
                const normalizedValue = (data[i] - minVal) / (maxVal - minVal);
                const y = height - (normalizedValue * height);

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        // Arrêter/reprendre le monitoring
        function toggleMonitoring() {
            const pauseBtn = document.getElementById('pause-chart');

            if (isMonitoring) {
                isMonitoring = false;
                if (pauseBtn) {
                    pauseBtn.innerHTML = '<i class="fas fa-play"></i> Reprendre';
                }
            } else {
                isMonitoring = true;
                if (pauseBtn) {
                    pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
                }
            }
        }

        // Réinitialiser le graphique
        function resetChart() {
            chartData = [];
            drawChart();
        }

        // Générer des données simulées
        function generateSimulatedData() {
            return {
                qi: {
                    current: 1001 + Math.random() * 10,
                    level: 6,
                    experiencePoints: 150 + Math.floor(Math.random() * 50)
                },
                neurons: {
                    total: 72,
                    active: 49 + Math.floor(Math.random() * 5),
                    efficiency: 80 + Math.random() * 15
                },
                networks: {
                    sensory: 12,
                    working: 8,
                    longTerm: 15,
                    emotional: 10,
                    executive: 9,
                    creative: 7
                },
                emotional: {
                    mood: Math.random() > 0.5 ? 'Curieux' : 'Créatif',
                    moodIntensity: 90 + Math.random() * 10,
                    happiness: 40 + Math.random() * 20,
                    curiosity: 90 + Math.random() * 10,
                    energy: 70 + Math.random() * 20
                }
            };
        }

        // Redimensionner le canvas
        window.addEventListener('resize', function() {
            setTimeout(initChart, 100);
        });
    </script>

    <script src="js/native-app.js"></script>
