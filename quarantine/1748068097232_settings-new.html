<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Paramètres</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .settings-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
        }

        .settings-section {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: var(--border-light);
        }

        .settings-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .settings-header i {
            font-size: 24px;
            margin-right: 10px;
            color: var(--accent);
        }

        .settings-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .settings-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .form-control {
            padding: 10px;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.3); /* Plus visible pour un meilleur contraste */
            background-color: rgba(255, 255, 255, 0.15); /* Plus opaque pour un meilleur contraste */
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 400; /* Légèrement plus gras pour une meilleure lisibilité */
        }

        .form-control:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 0 2px var(--accent-glow);
            background-color: rgba(255, 255, 255, 0.2); /* Plus opaque au focus pour un meilleur contraste */
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--accent);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .toggle-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toggle-label {
            font-size: 14px;
            color: var(--text-primary);
        }

        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--accent);
            color: #000; /* Noir pour un meilleur contraste sur fond coloré */
            font-weight: bold; /* Plus gras pour une meilleure lisibilité */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Ajouter une ombre pour améliorer la visibilité */
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Ombre plus prononcée au survol */
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2); /* Plus opaque pour un meilleur contraste */
            color: var(--text-primary);
            font-weight: 500; /* Légèrement plus gras pour une meilleure lisibilité */
            border: 1px solid rgba(255, 255, 255, 0.3); /* Ajouter une bordure pour améliorer la visibilité */
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3); /* Plus opaque au survol pour un meilleur contraste */
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Ajouter une ombre au survol */
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff4757;
            transform: translateY(-2px);
        }

        .range-slider {
            width: 100%;
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
        }

        .range-value {
            margin-left: 10px;
            font-size: 14px;
            color: var(--text-secondary);
            width: 40px;
            text-align: right;
        }

        .range-container {
            display: flex;
            align-items: center;
        }

        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            background-color: var(--bg-card);
            color: var(--text-primary);
            box-shadow: var(--shadow);
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .notification.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .notification.success {
            border-left: 4px solid var(--success);
        }

        .notification.error {
            border-left: 4px solid var(--danger);
        }

        .notification.info {
            border-left: 4px solid var(--info);
        }

        .notification.warning {
            border-left: 4px solid var(--warning);
        }

        /* Styles pour les éléments spécifiques à l'application desktop */
        .desktop-only {
            display: none;
        }

        body.electron-app .desktop-only {
            display: block;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/thermal-memory.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item active">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <div class="settings-container">
            <!-- Section Mémoire Thermique -->
            <div class="settings-section">
                <div class="settings-header">
                    <i class="fas fa-fire"></i>
                    <h2 class="settings-title">Paramètres de la Mémoire Thermique</h2>
                </div>
                <div class="settings-form">
                    <div class="toggle-group">
                        <span class="toggle-label">Cycles de mémoire automatiques</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-memory-cycles">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="memory-cycle-interval">Intervalle des cycles de mémoire (minutes)</label>
                        <div class="range-container">
                            <input type="range" id="memory-cycle-interval" class="range-slider" min="5" max="60" step="5" value="30">
                            <span class="range-value" id="memory-cycle-interval-value">30</span>
                        </div>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Génération automatique de rêves</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-dreams">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="dream-generation-interval">Intervalle de génération de rêves (heures)</label>
                        <div class="range-container">
                            <input type="range" id="dream-generation-interval" class="range-slider" min="1" max="24" step="1" value="12">
                            <span class="range-value" id="dream-generation-interval-value">12</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Accélérateurs Kyber -->
            <div class="settings-section">
                <div class="settings-header">
                    <i class="fas fa-bolt"></i>
                    <h2 class="settings-title">Paramètres des Accélérateurs Kyber</h2>
                </div>
                <div class="settings-form">
                    <div class="toggle-group">
                        <span class="toggle-label">Optimisation automatique des accélérateurs</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-accelerator-optimization">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="accelerator-optimization-interval">Intervalle d'optimisation (minutes)</label>
                        <div class="range-container">
                            <input type="range" id="accelerator-optimization-interval" class="range-slider" min="5" max="60" step="5" value="15">
                            <span class="range-value" id="accelerator-optimization-interval-value">15</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="max-boost-factor">Facteur de boost maximum</label>
                        <div class="range-container">
                            <input type="range" id="max-boost-factor" class="range-slider" min="1" max="5" step="0.1" value="3.5">
                            <span class="range-value" id="max-boost-factor-value">3.5</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section LTX Vidéo -->
            <div class="settings-section">
                <div class="settings-header">
                    <i class="fas fa-video"></i>
                    <h2 class="settings-title">Paramètres LTX Vidéo</h2>
                </div>
                <div class="settings-form">
                    <div class="toggle-group">
                        <span class="toggle-label">Démarrage automatique de la caméra</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-camera">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="video-width">Largeur de la vidéo</label>
                            <input type="number" id="video-width" class="form-control" min="320" max="1920" step="80" value="640">
                        </div>
                        <div class="form-group">
                            <label for="video-height">Hauteur de la vidéo</label>
                            <input type="number" id="video-height" class="form-control" min="240" max="1080" step="60" value="480">
                        </div>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Détection d'objets</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="object-detection" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Analyse thermique</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="thermal-analysis" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Reconnaissance faciale</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="facial-recognition">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Section Interface Utilisateur -->
            <div class="settings-section">
                <div class="settings-header">
                    <i class="fas fa-palette"></i>
                    <h2 class="settings-title">Paramètres de l'Interface</h2>
                </div>
                <div class="settings-form">
                    <div class="toggle-group">
                        <span class="toggle-label">Thème sombre</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="dark-theme" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Animations</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="animations" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="font-size">Taille de police</label>
                        <div class="range-container">
                            <input type="range" id="font-size" class="range-slider" min="0.8" max="1.5" step="0.1" value="1">
                            <span class="range-value" id="font-size-value">1.0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Application Desktop -->
            <div class="settings-section desktop-only">
                <div class="settings-header">
                    <i class="fas fa-desktop"></i>
                    <h2 class="settings-title">Paramètres de l'Application</h2>
                </div>
                <div class="settings-form">
                    <div class="toggle-group">
                        <span class="toggle-label">Démarrer automatiquement avec le système</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-launch">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Démarrer minimisé dans la barre des tâches</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="start-minimized">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="toggle-group">
                        <span class="toggle-label">Réduire dans la barre des tâches à la fermeture</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="minimize-to-tray" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <button id="backup-btn" class="btn btn-secondary">
                            <i class="fas fa-save"></i> Sauvegarder la mémoire
                        </button>
                    </div>
                    <div class="form-group">
                        <button id="restore-btn" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Restaurer une sauvegarde
                        </button>
                    </div>
                </div>
            </div>

            <!-- Boutons d'action -->
            <div class="action-buttons">
                <button id="reset-btn" class="btn btn-danger">Réinitialiser tous les paramètres</button>
                <button id="save-btn" class="btn btn-primary">Enregistrer les paramètres</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script src="/js/ltx-video.js"></script>
    <script src="/js/settings.js"></script>
  <script src="js/native-app.js"></script>
</body>
</html>
