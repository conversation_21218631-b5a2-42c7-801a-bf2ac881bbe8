<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparaison des Performances - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/performance-alerts.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .comparison-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .comparison-section {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: var(--border-light);
        }

        .comparison-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .comparison-header i {
            font-size: 24px;
            margin-right: 10px;
            color: var(--accent);
        }

        .comparison-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agents-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .agent-card {
            flex: 1;
            background: linear-gradient(135deg, var(--bg-card), rgba(13, 71, 161, 0.85));
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .agent-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .agent-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }

        .agent-details {
            flex-grow: 1;
        }

        .agent-name {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agent-model {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .agent-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .agent-stat {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 8px 12px;
            font-size: 14px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .agent-stat i {
            margin-right: 8px;
            color: var(--accent);
        }

        .chart-container {
            width: 100%;
            height: 400px;
            margin-top: 20px;
        }

        .chart-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .chart-column {
            flex: 1;
            min-width: 300px;
        }

        .chart-wrapper {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius);
            padding: 20px;
            height: 100%;
        }

        .sync-history {
            margin-top: 20px;
        }

        .sync-item {
            background-color: rgba(255, 255, 255, 0.08);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 10px;
            border-left: 3px solid var(--accent);
            transition: var(--transition-fast);
        }

        .sync-item:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(3px);
        }

        .sync-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .sync-title {
            font-weight: bold;
            color: var(--text-primary);
        }

        .sync-date {
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
        }

        .sync-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .sync-stat {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .sync-stat i {
            margin-right: 5px;
            color: var(--accent);
        }

        .sync-footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .performance-change {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .performance-change.positive {
            color: var(--success);
        }

        .performance-change.negative {
            color: var(--error);
        }

        .performance-change.neutral {
            color: var(--text-secondary);
        }

        .btn {
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--accent);
            color: #000;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams-new.html" class="nav-item">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item active">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/agents.html" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/training.html" class="nav-item">
                <i class="fas fa-graduation-cap"></i>
                <span>Formation</span>
            </a>
            <a href="/settings-new.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <div class="theme-switcher">
                <input type="checkbox" class="theme-toggle">
                <span class="theme-slider">
                    <i class="fas fa-sun theme-icon sun"></i>
                    <i class="fas fa-moon theme-icon moon"></i>
                </span>
            </div>
            <a href="#" class="nav-item" id="alerts-btn">
                <div class="alerts-counter">
                    <i class="fas fa-bell"></i>
                    <span class="alerts-badge" id="alerts-count">0</span>
                </div>
                <span>Alertes</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Comparaison des Performances</h1>
                <p class="interface-subtitle">Analysez l'impact de la synchronisation sur les performances des agents</p>
            </div>

            <div class="status-container">
                <a href="/performance.html" class="action-button">
                    <i class="fas fa-arrow-left"></i> Retour aux Performances
                </a>
                <a href="/memory-sync.html" class="action-button">
                    <i class="fas fa-exchange-alt"></i> Synchroniser
                </a>
                <button class="action-button" id="refresh-btn">
                    <i class="fas fa-sync"></i> Actualiser
                </button>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="comparison-container">
            <!-- Section des alertes -->
            <div class="comparison-section" id="alerts-section">
                <div class="comparison-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2 class="comparison-title">Alertes de Performance</h2>
                </div>
                <div class="alerts-dashboard">
                    <div class="alerts-dashboard-tabs">
                        <div class="alerts-dashboard-tab active" data-tab="active">Alertes actives</div>
                        <div class="alerts-dashboard-tab" data-tab="history">Historique</div>
                        <div class="alerts-dashboard-tab" data-tab="dismissed">Alertes ignorées</div>
                    </div>
                    <div class="alerts-list" id="alerts-list">
                        <!-- Les alertes seront ajoutées ici dynamiquement -->
                    </div>
                </div>
            </div>

            <!-- Section des agents -->
            <div class="comparison-section">
                <div class="comparison-header">
                    <i class="fas fa-robot"></i>
                    <h2 class="comparison-title">Agents</h2>
                </div>
                <div class="agents-container">
                    <!-- Agent principal -->
                    <div class="agent-card" id="main-agent-card">
                        <div class="agent-info">
                            <div class="agent-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="agent-details">
                                <div class="agent-name" id="main-agent-name">Claude (4GB)</div>
                                <div class="agent-model" id="main-agent-model">incept5/llama3.1-claude:latest</div>
                            </div>
                        </div>
                        <div class="agent-stats" id="main-agent-stats">
                            <div class="agent-stat">
                                <i class="fas fa-fire"></i>
                                <span id="main-agent-entries">0 entrées</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-thermometer-half"></i>
                                <span id="main-agent-temperature">Température: 0.0</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-chart-line"></i>
                                <span id="main-agent-performance">Performance: 0.0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Agent de formation -->
                    <div class="agent-card" id="training-agent-card">
                        <div class="agent-info">
                            <div class="agent-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="agent-details">
                                <div class="agent-name" id="training-agent-name">Agent de Formation</div>
                                <div class="agent-model" id="training-agent-model">llama3:8b</div>
                            </div>
                        </div>
                        <div class="agent-stats" id="training-agent-stats">
                            <div class="agent-stat">
                                <i class="fas fa-fire"></i>
                                <span id="training-agent-entries">0 entrées</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-thermometer-half"></i>
                                <span id="training-agent-temperature">Température: 0.0</span>
                            </div>
                            <div class="agent-stat">
                                <i class="fas fa-chart-line"></i>
                                <span id="training-agent-performance">Performance: 0.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section des graphiques -->
            <div class="comparison-section">
                <div class="comparison-header">
                    <i class="fas fa-chart-line"></i>
                    <h2 class="comparison-title">Évolution des Performances</h2>
                </div>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
                <div class="chart-row">
                    <div class="chart-column">
                        <div class="chart-wrapper">
                            <canvas id="main-agent-radar"></canvas>
                        </div>
                    </div>
                    <div class="chart-column">
                        <div class="chart-wrapper">
                            <canvas id="training-agent-radar"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section de l'historique des synchronisations -->
            <div class="comparison-section">
                <div class="comparison-header">
                    <i class="fas fa-history"></i>
                    <h2 class="comparison-title">Historique des Synchronisations</h2>
                </div>
                <div class="sync-history" id="sync-history">
                    <!-- Les éléments de l'historique seront ajoutés ici dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="/js/performance-alerts.js"></script>
    <script src="/js/performance-comparison.js"></script>
    <script src="js/native-app.js"></script>

    <script>
        // Initialiser le système d'alertes
        document.addEventListener('DOMContentLoaded', () => {
            if (window.performanceAlerts) {
                window.performanceAlerts.initialize();

                // Afficher les alertes dans le tableau de bord
                displayAlertsDashboard();

                // Mettre à jour le compteur d'alertes
                updateAlertsCounter();

                // Ajouter les écouteurs d'événements pour les onglets
                const tabs = document.querySelectorAll('.alerts-dashboard-tab');
                tabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        // Désactiver tous les onglets
                        tabs.forEach(t => t.classList.remove('active'));

                        // Activer l'onglet cliqué
                        tab.classList.add('active');

                        // Afficher les alertes correspondantes
                        displayAlertsDashboard(tab.dataset.tab);
                    });
                });
            }
        });

        /**
         * Affiche les alertes dans le tableau de bord
         * @param {string} tab - Onglet actif (active, history, dismissed)
         */
        function displayAlertsDashboard(tab = 'active') {
            if (!window.performanceAlerts) {
                return;
            }

            const alertsList = document.getElementById('alerts-list');
            if (!alertsList) {
                return;
            }

            // Vider la liste
            alertsList.innerHTML = '';

            // Récupérer les alertes
            let alerts = [];

            switch (tab) {
                case 'active':
                    alerts = window.performanceAlerts.getActiveAlerts();
                    break;
                case 'history':
                    alerts = window.performanceAlerts.getAlertHistory();
                    break;
                case 'dismissed':
                    alerts = window.performanceAlerts.getDismissedAlerts();
                    break;
            }

            // Si aucune alerte, afficher un message
            if (alerts.length === 0) {
                alertsList.innerHTML = `
                    <div class="no-alerts">
                        <i class="fas fa-check-circle"></i>
                        <p>Aucune alerte ${tab === 'active' ? 'active' : tab === 'history' ? 'dans l\'historique' : 'ignorée'}</p>
                    </div>
                `;
                return;
            }

            // Afficher les alertes
            alerts.forEach(alert => {
                const alertItem = document.createElement('div');
                alertItem.className = `alert-item ${alert.severity}`;
                alertItem.dataset.alertId = alert.id;

                // Formater la date
                const date = new Date(alert.createdAt || alert.timestamp || alert.dismissedAt);
                const formattedDate = date.toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                alertItem.innerHTML = `
                    <div class="alert-item-header">
                        <div class="alert-item-title">${alert.title || 'Alerte sans titre'}</div>
                        <div class="alert-item-badge ${alert.severity}">${alert.severity === 'critical' ? 'Critique' : alert.severity === 'warning' ? 'Avertissement' : 'Information'}</div>
                    </div>
                    <div class="alert-item-content">${alert.message || 'Aucun détail disponible'}</div>
                    <div class="alert-item-footer">
                        <div class="alert-item-category">
                            <i class="fas ${getCategoryIcon(alert.category)}"></i>
                            <span>${getCategoryLabel(alert.category)}</span>
                        </div>
                        <div class="alert-item-date">${formattedDate}</div>
                    </div>
                `;

                // Ajouter des actions si c'est une alerte active
                if (tab === 'active') {
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'alert-item-actions';

                    actionsDiv.innerHTML = `
                        <button class="alert-item-action" data-action="view" data-alert-id="${alert.id}">
                            <i class="fas fa-eye"></i> Détails
                        </button>
                        <button class="alert-item-action danger" data-action="dismiss" data-alert-id="${alert.id}">
                            <i class="fas fa-times"></i> Ignorer
                        </button>
                    `;

                    alertItem.appendChild(actionsDiv);
                }

                alertsList.appendChild(alertItem);
            });

            // Ajouter les écouteurs d'événements pour les actions
            const actionButtons = alertsList.querySelectorAll('.alert-item-action');
            actionButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const action = button.dataset.action;
                    const alertId = button.dataset.alertId;

                    if (action === 'view') {
                        // Trouver l'alerte
                        const alert = window.performanceAlerts.getActiveAlerts().find(a => a.id === alertId);
                        if (alert) {
                            window.performanceAlerts.viewAlertDetails(alert);
                        }
                    } else if (action === 'dismiss') {
                        window.performanceAlerts.dismissAlert(alertId);
                        displayAlertsDashboard(tab);
                    }
                });
            });
        }

        /**
         * Retourne l'icône d'une catégorie d'alerte
         * @param {string} category - Catégorie de l'alerte
         * @returns {string} - Classe d'icône Font Awesome
         */
        function getCategoryIcon(category) {
            switch (category) {
                case 'memory':
                    return 'fa-fire';
                case 'accelerators':
                    return 'fa-bolt';
                case 'agents':
                    return 'fa-robot';
                case 'sync':
                    return 'fa-exchange-alt';
                default:
                    return 'fa-exclamation-circle';
            }
        }

        /**
         * Retourne le libellé d'une catégorie d'alerte
         * @param {string} category - Catégorie de l'alerte
         * @returns {string} - Libellé de la catégorie
         */
        function getCategoryLabel(category) {
            switch (category) {
                case 'memory':
                    return 'Mémoire Thermique';
                case 'accelerators':
                    return 'Accélérateurs Kyber';
                case 'agents':
                    return 'Agents';
                case 'sync':
                    return 'Synchronisation';
                default:
                    return category;
            }
        }

        /**
         * Met à jour le compteur d'alertes
         */
        function updateAlertsCounter() {
            if (!window.performanceAlerts) {
                return;
            }

            const alertsCount = document.getElementById('alerts-count');
            if (!alertsCount) {
                return;
            }

            // Récupérer le nombre d'alertes actives
            const activeAlerts = window.performanceAlerts.getActiveAlerts();
            const count = activeAlerts.length;

            // Mettre à jour le compteur
            alertsCount.textContent = count;

            // Masquer le badge si aucune alerte
            if (count === 0) {
                alertsCount.style.display = 'none';
            } else {
                alertsCount.style.display = 'flex';
            }

            // Ajouter un écouteur d'événement pour le bouton d'alertes
            const alertsBtn = document.getElementById('alerts-btn');
            if (alertsBtn) {
                alertsBtn.addEventListener('click', (event) => {
                    event.preventDefault();

                    // Faire défiler jusqu'à la section des alertes
                    const alertsSection = document.getElementById('alerts-section');
                    if (alertsSection) {
                        alertsSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            }
        }
    </script>
</body>
</html>
