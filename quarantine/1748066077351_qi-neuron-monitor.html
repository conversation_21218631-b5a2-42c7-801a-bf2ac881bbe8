<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Qi & Neurones - Louna</title>
    <style>
        /* Styles spécifiques pour le monitoring QI et neurones */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 20px;
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .status-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .status-active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4caf50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
        }

        .card h2 {
            font-size: 1.8em;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 600;
            text-shadow: 0 0 10px currentColor;
        }

        .qi-card h2 {
            color: #ff6b6b;
        }

        .neuron-card h2 {
            color: #4ecdc4;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 18px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
        }

        .metric-name {
            font-weight: 500;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .metric-value {
            font-size: 1.3em;
            font-weight: bold;
            text-shadow: 0 0 8px currentColor;
        }

        .qi-value {
            color: #ff6b6b;
        }

        .neuron-value {
            color: #4ecdc4;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 8px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 5px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .qi-progress {
            background: linear-gradient(90deg, #ff6b6b, #ff8e8e, #ffb3b3);
        }

        .neuron-progress {
            background: linear-gradient(90deg, #4ecdc4, #6ee7db, #8ef0e8);
        }

        .chart-container {
            grid-column: 1 / -1;
            height: 350px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 25px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .chart {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #764ba2, #667eea);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
            100% { opacity: 1; transform: scale(1); }
        }

        .flow-indicator {
            display: inline-block;
            margin-left: 15px;
            font-size: 1.5em;
            animation: pulse 1.5s infinite;
        }

        .flow-up {
            color: #4caf50;
            text-shadow: 0 0 10px #4caf50;
        }

        .flow-down {
            color: #f44336;
            text-shadow: 0 0 10px #f44336;
        }

        .flow-stable {
            color: #ffc107;
            text-shadow: 0 0 10px #ffc107;
        }

        /* Responsive design amélioré */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .status-bar {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .card {
                padding: 20px;
            }

            .card h2 {
                font-size: 1.5em;
            }

            .controls {
                gap: 15px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 13px;
            }
        }

        /* Animations d'entrée */
        .card {
            animation: slideInUp 0.6s ease-out;
        }

        .card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .chart-container {
            animation: slideInUp 0.6s ease-out 0.2s both;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item active">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Monitoring Qi & Neurones</h1>
                <p class="interface-subtitle">Évolution en temps réel de l'énergie vitale et de l'activité neuronale</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator" id="memory-status-indicator"></div>
                <span class="status-text" id="memory-status-text">Chargement...</span>

                <a href="/thermal-memory.html" class="action-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="action-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
            </div>
        </div>

    <div class="container">

        <div class="status-bar">
            <div class="status-item" id="monitoring-status">
                <span>⚡ Monitoring: </span>
                <span id="status-text">Chargement...</span>
            </div>
            <div class="status-item">
                <span>🕒 Dernière mise à jour: </span>
                <span id="last-update">--:--:--</span>
            </div>
            <div class="status-item">
                <span>📊 Points d'historique: </span>
                <span id="history-count">0</span>
            </div>
        </div>

        <div class="main-grid">
            <!-- Carte Qi -->
            <div class="card qi-card">
                <h2>🔥 Énergie Qi</h2>

                <div class="metric">
                    <span class="metric-name">Niveau actuel</span>
                    <span class="metric-value qi-value" id="qi-level">0.00</span>
                    <span class="flow-indicator" id="qi-flow">●</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill qi-progress" id="qi-progress" style="width: 0%"></div>
                </div>

                <div class="metric">
                    <span class="metric-name">Flux</span>
                    <span class="metric-value qi-value" id="qi-flow-value">0.000</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Stabilité</span>
                    <span class="metric-value qi-value" id="qi-stability">0.00</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Harmonie</span>
                    <span class="metric-value qi-value" id="qi-harmony">0.00</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Vitalité</span>
                    <span class="metric-value qi-value" id="qi-vitality">0.00</span>
                </div>
            </div>

            <!-- Carte Neurones -->
            <div class="card neuron-card">
                <h2>🧬 Réseau Neuronal</h2>

                <div class="metric">
                    <span class="metric-name">Connexions actives</span>
                    <span class="metric-value neuron-value" id="neuron-active">0</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Densité du réseau</span>
                    <span class="metric-value neuron-value" id="neuron-density">0.00</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill neuron-progress" id="neuron-density-progress" style="width: 0%"></div>
                </div>

                <div class="metric">
                    <span class="metric-name">Force synaptique</span>
                    <span class="metric-value neuron-value" id="neuron-strength">0.00</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Activité neuronale</span>
                    <span class="metric-value neuron-value" id="neuron-activity">0.00</span>
                </div>

                <div class="metric">
                    <span class="metric-name">Plasticité</span>
                    <span class="metric-value neuron-value" id="neuron-plasticity">0.00</span>
                </div>
            </div>

            <!-- Graphique en temps réel -->
            <div class="chart-container">
                <h2 style="text-align: center; margin-bottom: 20px;">📈 Évolution Temporelle</h2>
                <div class="chart" id="realtime-chart">
                    <canvas id="chart-canvas" width="800" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="start-btn">▶️ Démarrer</button>
            <button class="btn" id="stop-btn">⏹️ Arrêter</button>
            <button class="btn" id="reset-btn">🔄 Réinitialiser</button>
        </div>
    </div>

    <script>
        // Variables globales
        let isMonitoring = false;
        let updateInterval = null;
        let chartData = {
            qi: [],
            neurons: [],
            timestamps: []
        };
        let maxDataPoints = 50;

        // Éléments DOM
        const elements = {
            status: document.getElementById('monitoring-status'),
            statusText: document.getElementById('status-text'),
            lastUpdate: document.getElementById('last-update'),
            historyCount: document.getElementById('history-count'),

            // Qi
            qiLevel: document.getElementById('qi-level'),
            qiProgress: document.getElementById('qi-progress'),
            qiFlow: document.getElementById('qi-flow'),
            qiFlowValue: document.getElementById('qi-flow-value'),
            qiStability: document.getElementById('qi-stability'),
            qiHarmony: document.getElementById('qi-harmony'),
            qiVitality: document.getElementById('qi-vitality'),

            // Neurones
            neuronActive: document.getElementById('neuron-active'),
            neuronDensity: document.getElementById('neuron-density'),
            neuronDensityProgress: document.getElementById('neuron-density-progress'),
            neuronStrength: document.getElementById('neuron-strength'),
            neuronActivity: document.getElementById('neuron-activity'),
            neuronPlasticity: document.getElementById('neuron-plasticity'),

            // Contrôles
            startBtn: document.getElementById('start-btn'),
            stopBtn: document.getElementById('stop-btn'),
            resetBtn: document.getElementById('reset-btn'),

            // Graphique
            chartCanvas: document.getElementById('chart-canvas')
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initialisation du monitoring Qi/Neurones...');

            // Configurer les boutons
            elements.startBtn.addEventListener('click', startMonitoring);
            elements.stopBtn.addEventListener('click', stopMonitoring);
            elements.resetBtn.addEventListener('click', resetData);

            // Initialiser le canvas
            initChart();

            // Charger l'état initial
            loadCurrentState();

            console.log('Monitoring Qi/Neurones initialisé');
        });

        // Fonctions principales
        async function loadCurrentState() {
            try {
                const response = await fetch('/api/qi-neuron/current');
                const data = await response.json();

                if (data.success) {
                    updateUI(data.state);
                    updateStatus('Connecté', true);
                } else {
                    console.error('Erreur lors du chargement de l\'état:', data.error);
                    updateStatus('Erreur', false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateStatus('Déconnecté', false);
            }
        }

        async function startMonitoring() {
            try {
                // Démarrer le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/start', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    isMonitoring = true;
                    updateStatus('Actif', true);

                    // Démarrer les mises à jour périodiques
                    updateInterval = setInterval(loadCurrentState, 2000);

                    // Mettre à jour les boutons
                    elements.startBtn.disabled = true;
                    elements.stopBtn.disabled = false;

                    console.log('Monitoring démarré');
                } else {
                    console.error('Erreur lors du démarrage:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors du démarrage:', error);
            }
        }

        async function stopMonitoring() {
            try {
                // Arrêter le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/stop', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    isMonitoring = false;
                    updateStatus('Arrêté', false);

                    // Arrêter les mises à jour
                    if (updateInterval) {
                        clearInterval(updateInterval);
                        updateInterval = null;
                    }

                    // Mettre à jour les boutons
                    elements.startBtn.disabled = false;
                    elements.stopBtn.disabled = true;

                    console.log('Monitoring arrêté');
                } else {
                    console.error('Erreur lors de l\'arrêt:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'arrêt:', error);
            }
        }

        function resetData() {
            chartData = {
                qi: [],
                neurons: [],
                timestamps: []
            };

            elements.historyCount.textContent = '0';
            drawChart();

            console.log('Données réinitialisées');
        }

        function updateUI(state) {
            // Mettre à jour les métriques Qi
            if (state.qi) {
                const qi = state.qi;
                elements.qiLevel.textContent = qi.level.toFixed(3);
                elements.qiProgress.style.width = (qi.level * 100) + '%';
                elements.qiFlowValue.textContent = qi.flow.toFixed(3);
                elements.qiStability.textContent = qi.stability.toFixed(3);
                elements.qiHarmony.textContent = qi.harmony.toFixed(3);
                elements.qiVitality.textContent = qi.vitality.toFixed(3);

                // Indicateur de flux
                if (qi.flow > 0.001) {
                    elements.qiFlow.textContent = '↗️';
                    elements.qiFlow.className = 'flow-indicator flow-up';
                } else if (qi.flow < -0.001) {
                    elements.qiFlow.textContent = '↘️';
                    elements.qiFlow.className = 'flow-indicator flow-down';
                } else {
                    elements.qiFlow.textContent = '➡️';
                    elements.qiFlow.className = 'flow-indicator flow-stable';
                }
            }

            // Mettre à jour les métriques neuronales
            if (state.neurons) {
                const neurons = state.neurons;
                elements.neuronActive.textContent = neurons.activeConnections;
                elements.neuronDensity.textContent = neurons.networkDensity.toFixed(3);
                elements.neuronDensityProgress.style.width = (neurons.networkDensity * 100) + '%';
                elements.neuronStrength.textContent = neurons.synapticStrength.toFixed(3);
                elements.neuronActivity.textContent = neurons.neuralActivity.toFixed(3);
                elements.neuronPlasticity.textContent = neurons.plasticity.toFixed(3);
            }

            // Ajouter aux données du graphique
            if (state.qi && state.neurons) {
                addToChart(state.qi.level, state.neurons.networkDensity);
            }

            // Mettre à jour l'heure
            elements.lastUpdate.textContent = new Date().toLocaleTimeString();
        }

        function updateStatus(text, isActive) {
            elements.statusText.textContent = text;

            if (isActive) {
                elements.status.classList.add('status-active');
                elements.status.classList.add('pulse');
            } else {
                elements.status.classList.remove('status-active');
                elements.status.classList.remove('pulse');
            }
        }

        function initChart() {
            const canvas = elements.chartCanvas;
            const ctx = canvas.getContext('2d');

            // Configurer le canvas
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawChart();
        }

        function addToChart(qiValue, neuronValue) {
            const now = new Date();

            chartData.qi.push(qiValue);
            chartData.neurons.push(neuronValue);
            chartData.timestamps.push(now);

            // Limiter le nombre de points
            if (chartData.qi.length > maxDataPoints) {
                chartData.qi.shift();
                chartData.neurons.shift();
                chartData.timestamps.shift();
            }

            elements.historyCount.textContent = chartData.qi.length;
            drawChart();
        }

        function drawChart() {
            const canvas = elements.chartCanvas;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Effacer le canvas
            ctx.clearRect(0, 0, width, height);

            if (chartData.qi.length === 0) return;

            // Dessiner la grille
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Lignes horizontales
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Lignes verticales
            for (let i = 0; i <= 10; i++) {
                const x = (width / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Dessiner les courbes
            drawLine(ctx, chartData.qi, '#ff6b6b', width, height);
            drawLine(ctx, chartData.neurons, '#4ecdc4', width, height);

            // Légende
            ctx.font = '12px Arial';
            ctx.fillStyle = '#ff6b6b';
            ctx.fillText('Qi', 10, 20);
            ctx.fillStyle = '#4ecdc4';
            ctx.fillText('Neurones', 10, 35);
        }

        function drawLine(ctx, data, color, width, height) {
            if (data.length < 2) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < data.length; i++) {
                const x = (width / (maxDataPoints - 1)) * i;
                const y = height - (data[i] * height);

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        // Redimensionner le canvas quand la fenêtre change de taille
        window.addEventListener('resize', function() {
            setTimeout(initChart, 100);
        });
    </script>
    </div>
    </div>
  <script src="js/native-app.js"></script>
</body>
</html>
