<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Code - Louna</title>
    <link rel="stylesheet" href="css/native-app.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js"></script>

    <style>
        :root {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --bg-card: #3c3c3c;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --text-tertiary: #6a6a6a;
            --accent-color: #ff69b4;
            --accent-hover: #ff1493;
            --border-color: #464647;
            --success: #4caf50;
            --warning: #ff9800;
            --danger: #f44336;
            --info: #2196f3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }

        .editor-container {
            display: flex;
            height: 100vh;
            flex-direction: column;
        }

        /* Barre de titre */
        .title-bar {
            background-color: var(--bg-secondary);
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            border-bottom: 1px solid var(--border-color);
            -webkit-app-region: drag;
        }

        .title-bar-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .title-bar-title {
            font-size: 13px;
            color: var(--text-primary);
            font-weight: 400;
        }

        .title-bar-controls {
            display: flex;
            gap: 5px;
            -webkit-app-region: no-drag;
        }

        .title-bar-btn {
            width: 25px;
            height: 25px;
            border: none;
            background: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .title-bar-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        /* Barre de menu */
        .menu-bar {
            background-color: var(--bg-secondary);
            height: 30px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .menu-item {
            padding: 5px 10px;
            font-size: 13px;
            color: var(--text-primary);
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Zone principale */
        .main-area {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* Barre latérale */
        .sidebar {
            width: 250px;
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
            font-size: 11px;
            font-weight: bold;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
        }

        /* Explorateur de fichiers */
        .file-explorer {
            padding: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            font-size: 13px;
            color: var(--text-primary);
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .file-item.active {
            background-color: var(--accent-color);
            color: white;
        }

        .file-icon {
            width: 16px;
            text-align: center;
            font-size: 12px;
        }

        /* Zone d'édition */
        .editor-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .tabs-container {
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            min-height: 35px;
            overflow-x: auto;
        }

        .tab {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            font-size: 13px;
            color: var(--text-secondary);
            background-color: var(--bg-tertiary);
            border-right: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .tab.active {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border-bottom: 2px solid var(--accent-color);
        }

        .tab:hover {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .tab-close:hover {
            background-color: rgba(255, 255, 255, 0.2);
            opacity: 1;
        }

        .editor-content {
            flex: 1;
            position: relative;
        }

        #monaco-editor {
            width: 100%;
            height: 100%;
        }

        /* Barre de statut */
        .status-bar {
            background-color: var(--accent-color);
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            font-size: 12px;
            color: white;
        }

        .status-left, .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-item {
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .status-item:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Panneau inférieur */
        .bottom-panel {
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            height: 200px;
            display: none;
            flex-direction: column;
        }

        .bottom-panel.visible {
            display: flex;
        }

        .panel-tabs {
            display: flex;
            background-color: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
        }

        .panel-tab {
            padding: 8px 15px;
            font-size: 13px;
            color: var(--text-secondary);
            cursor: pointer;
            border-right: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .panel-tab.active {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .panel-content {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        /* Extensions */
        .extensions-list {
            padding: 10px;
        }

        .extension-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 5px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .extension-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .extension-icon {
            width: 32px;
            height: 32px;
            background-color: var(--accent-color);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .extension-info {
            flex: 1;
        }

        .extension-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .extension-description {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .extension-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            background-color: var(--success);
            color: white;
        }

        /* Scrollbars personnalisées */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- Barre de titre -->
        <div class="title-bar">
            <div class="title-bar-left">
                <i class="fas fa-brain" style="color: var(--accent-color);"></i>
                <span class="title-bar-title">Louna Code Editor</span>
            </div>
            <div class="title-bar-controls">
                <button class="title-bar-btn" id="minimize-btn" title="Réduire">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="title-bar-btn" id="maximize-btn" title="Agrandir">
                    <i class="fas fa-square"></i>
                </button>
                <button class="title-bar-btn" id="close-btn" title="Fermer">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Barre de menu -->
        <div class="menu-bar">
            <div class="menu-item">Fichier</div>
            <div class="menu-item">Édition</div>
            <div class="menu-item">Affichage</div>
            <div class="menu-item">Aller</div>
            <div class="menu-item">Exécuter</div>
            <div class="menu-item">Terminal</div>
            <div class="menu-item">Aide</div>
        </div>

        <!-- Zone principale -->
        <div class="main-area">
            <!-- Barre latérale -->
            <div class="sidebar">
                <div class="sidebar-header">Explorateur</div>
                <div class="sidebar-content">
                    <div class="file-explorer" id="file-explorer">
                        <div class="file-item active" data-file="main.js">
                            <i class="fab fa-js-square file-icon" style="color: #f7df1e;"></i>
                            <span>main.js</span>
                        </div>
                        <div class="file-item" data-file="style.css">
                            <i class="fab fa-css3-alt file-icon" style="color: #1572b6;"></i>
                            <span>style.css</span>
                        </div>
                        <div class="file-item" data-file="index.html">
                            <i class="fab fa-html5 file-icon" style="color: #e34f26;"></i>
                            <span>index.html</span>
                        </div>
                        <div class="file-item" data-file="README.md">
                            <i class="fab fa-markdown file-icon" style="color: #083fa1;"></i>
                            <span>README.md</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Zone d'édition -->
            <div class="editor-area">
                <div class="tabs-container" id="tabs-container">
                    <div class="tab active" data-file="main.js">
                        <i class="fab fa-js-square" style="color: #f7df1e;"></i>
                        <span>main.js</span>
                        <div class="tab-close">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                </div>
                <div class="editor-content">
                    <div id="monaco-editor"></div>
                </div>
            </div>
        </div>

        <!-- Panneau inférieur -->
        <div class="bottom-panel" id="bottom-panel">
            <div class="panel-tabs">
                <div class="panel-tab active" data-panel="terminal">Terminal</div>
                <div class="panel-tab" data-panel="output">Sortie</div>
                <div class="panel-tab" data-panel="problems">Problèmes</div>
                <div class="panel-tab" data-panel="debug">Débogage</div>
            </div>
            <div class="panel-content" id="panel-content">
                <div>$ Louna Code Editor Terminal</div>
                <div>Prêt pour l'exécution de commandes...</div>
            </div>
        </div>

        <!-- Barre de statut -->
        <div class="status-bar">
            <div class="status-left">
                <div class="status-item" id="git-branch">
                    <i class="fas fa-code-branch"></i> main
                </div>
                <div class="status-item" id="sync-status">
                    <i class="fas fa-sync-alt"></i> Synchronisé
                </div>
                <div class="status-item" id="errors-warnings">
                    <i class="fas fa-exclamation-triangle"></i> 0 ⚠ 0
                </div>
            </div>
            <div class="status-right">
                <div class="status-item" id="cursor-position">Ln 1, Col 1</div>
                <div class="status-item" id="file-encoding">UTF-8</div>
                <div class="status-item" id="file-type">JavaScript</div>
                <div class="status-item" id="live-share">
                    <i class="fas fa-share-alt"></i> Partager
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        let editor;
        let currentFile = 'main.js';
        let files = {
            'main.js': {
                content: `// Bienvenue dans l'éditeur de code Louna !
// Cet éditeur utilise Monaco Editor (le même que VS Code)

function helloLouna() {
    console.log("Bonjour depuis Louna !");

    // Votre code ici...
    const message = "Louna est prête à coder !";
    return message;
}

// Appeler la fonction
helloLouna();

// Exemple d'utilisation de la mémoire thermique
async function addToThermalMemory(key, data) {
    try {
        const response = await fetch('/api/thermal/memory/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                key: key,
                data: data,
                importance: 0.7,
                category: 'code'
            })
        });

        const result = await response.json();
        console.log('Ajouté à la mémoire thermique:', result);
        return result;
    } catch (error) {
        console.error('Erreur:', error);
    }
}`,
                language: 'javascript'
            },
            'style.css': {
                content: `/* Styles pour votre application Louna */

:root {
    --primary-color: #ff69b4;
    --secondary-color: #1e1e1e;
    --text-color: #cccccc;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--secondary-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.button:hover {
    background-color: #ff1493;
    transform: translateY(-2px);
}`,
                language: 'css'
            },
            'index.html': {
                content: `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Application Louna</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Bienvenue dans mon application Louna</h1>
        <p>Cette application a été créée avec l'aide de Louna !</p>

        <button class="button" onclick="helloLouna()">
            Dire bonjour à Louna
        </button>

        <div id="output"></div>
    </div>

    <script src="main.js"></script>
</body>
</html>`,
                language: 'html'
            },
            'README.md': {
                content: `# Mon Projet Louna

Ce projet a été créé avec l'aide de **Louna**, l'agent à mémoire thermique.

## Fonctionnalités

- ✨ Interface moderne et responsive
- 🧠 Intégration avec la mémoire thermique de Louna
- 🚀 Code optimisé et documenté
- 🎨 Design élégant avec thème sombre

## Installation

\`\`\`bash
# Cloner le projet
git clone https://github.com/votre-nom/mon-projet-louna.git

# Installer les dépendances
npm install

# Lancer le projet
npm start
\`\`\`

## Utilisation

1. Ouvrez \`index.html\` dans votre navigateur
2. Cliquez sur le bouton pour interagir avec Louna
3. Profitez de votre application !

## Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou une pull request.

## Licence

MIT License - voir le fichier LICENSE pour plus de détails.`,
                language: 'markdown'
            }
        };

        // Initialiser Monaco Editor
        require.config({ paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs' } });
        require(['vs/editor/editor.main'], function () {
            // Configuration du thème sombre
            monaco.editor.defineTheme('louna-dark', {
                base: 'vs-dark',
                inherit: true,
                rules: [
                    { token: 'comment', foreground: '6A9955' },
                    { token: 'keyword', foreground: 'C586C0' },
                    { token: 'string', foreground: 'CE9178' },
                    { token: 'number', foreground: 'B5CEA8' },
                    { token: 'function', foreground: 'DCDCAA' },
                    { token: 'variable', foreground: '9CDCFE' }
                ],
                colors: {
                    'editor.background': '#1e1e1e',
                    'editor.foreground': '#cccccc',
                    'editorCursor.foreground': '#ff69b4',
                    'editor.lineHighlightBackground': '#2d2d30',
                    'editorLineNumber.foreground': '#6a6a6a',
                    'editor.selectionBackground': '#ff69b440',
                    'editor.inactiveSelectionBackground': '#ff69b420'
                }
            });

            // Créer l'éditeur
            editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                value: files[currentFile].content,
                language: files[currentFile].language,
                theme: 'louna-dark',
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                readOnly: false,
                automaticLayout: true,
                minimap: {
                    enabled: true
                },
                suggestOnTriggerCharacters: true,
                quickSuggestions: true,
                wordBasedSuggestions: true,
                folding: true,
                foldingStrategy: 'indentation',
                showFoldingControls: 'always',
                bracketPairColorization: {
                    enabled: true
                }
            });

            // Écouter les changements de position du curseur
            editor.onDidChangeCursorPosition((e) => {
                document.getElementById('cursor-position').textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
            });

            // Écouter les changements de contenu
            editor.onDidChangeModelContent(() => {
                files[currentFile].content = editor.getValue();
                updateTabTitle(currentFile);
            });

            // Récupérer le code depuis l'URL si présent
            const urlParams = new URLSearchParams(window.location.search);
            const codeFromUrl = urlParams.get('code');
            if (codeFromUrl) {
                try {
                    const decodedCode = decodeURIComponent(codeFromUrl);
                    files[currentFile].content = decodedCode;
                    editor.setValue(decodedCode);
                } catch (error) {
                    console.error('Erreur lors du décodage du code:', error);
                }
            }
        });

        // Gestion des fichiers
        function switchFile(fileName) {
            if (files[fileName]) {
                // Sauvegarder le contenu actuel
                if (editor) {
                    files[currentFile].content = editor.getValue();
                }

                // Changer de fichier
                currentFile = fileName;

                // Mettre à jour l'éditeur
                if (editor) {
                    editor.setValue(files[fileName].content);
                    monaco.editor.setModelLanguage(editor.getModel(), files[fileName].language);
                }

                // Mettre à jour l'interface
                updateFileExplorer();
                updateTabs();
                updateStatusBar();
            }
        }

        function updateFileExplorer() {
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.file === currentFile) {
                    item.classList.add('active');
                }
            });
        }

        function updateTabs() {
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.file === currentFile) {
                    tab.classList.add('active');
                }
            });
        }

        function updateTabTitle(fileName) {
            const tab = document.querySelector(`[data-file="${fileName}"]`);
            if (tab && !tab.textContent.includes('●')) {
                const span = tab.querySelector('span');
                if (span) {
                    span.textContent = span.textContent + ' ●';
                }
            }
        }

        function updateStatusBar() {
            const fileType = files[currentFile].language;
            document.getElementById('file-type').textContent = fileType.charAt(0).toUpperCase() + fileType.slice(1);
        }

        // Événements
        document.addEventListener('DOMContentLoaded', () => {
            // Gestion des clics sur les fichiers
            document.querySelectorAll('.file-item').forEach(item => {
                item.addEventListener('click', () => {
                    switchFile(item.dataset.file);
                });
            });

            // Gestion des onglets
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('tab-close')) {
                        switchFile(tab.dataset.file);
                    }
                });

                // Fermeture d'onglet
                const closeBtn = tab.querySelector('.tab-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // Pour l'instant, on ne ferme pas les onglets
                        console.log('Fermeture d\'onglet:', tab.dataset.file);
                    });
                }
            });

            // Gestion du panneau inférieur
            document.querySelectorAll('.panel-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.panel-tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');

                    const panel = tab.dataset.panel;
                    updatePanelContent(panel);
                });
            });

            // Gestion des boutons de la barre de titre
            document.getElementById('close-btn').addEventListener('click', () => {
                window.close();
            });

            document.getElementById('minimize-btn').addEventListener('click', () => {
                // Minimiser la fenêtre (fonctionne dans Electron)
                if (window.electronAPI) {
                    window.electronAPI.minimize();
                }
            });

            document.getElementById('maximize-btn').addEventListener('click', () => {
                // Maximiser/restaurer la fenêtre (fonctionne dans Electron)
                if (window.electronAPI) {
                    window.electronAPI.toggleMaximize();
                }
            });

            // Raccourcis clavier
            document.addEventListener('keydown', (e) => {
                // Ctrl+S pour sauvegarder
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    saveCurrentFile();
                }

                // Ctrl+` pour ouvrir/fermer le terminal
                if (e.ctrlKey && e.key === '`') {
                    e.preventDefault();
                    toggleBottomPanel();
                }

                // Ctrl+Shift+P pour la palette de commandes
                if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                    e.preventDefault();
                    openCommandPalette();
                }
            });
        });

        function updatePanelContent(panel) {
            const content = document.getElementById('panel-content');

            switch (panel) {
                case 'terminal':
                    content.innerHTML = \`
                        <div>$ Louna Code Editor Terminal</div>
                        <div>Prêt pour l'exécution de commandes...</div>
                        <div style="margin-top: 10px;">
                            <span style="color: var(--accent-color);">louna@editor:~$</span>
                            <input type="text" style="background: none; border: none; color: var(--text-primary); outline: none; flex: 1;" placeholder="Tapez votre commande...">
                        </div>
                    \`;
                    break;
                case 'output':
                    content.innerHTML = \`
                        <div>Sortie de l'application:</div>
                        <div style="margin-top: 10px; color: var(--success);">✓ Application démarrée avec succès</div>
                        <div style="color: var(--info);">ℹ Connecté à la mémoire thermique de Louna</div>
                    \`;
                    break;
                case 'problems':
                    content.innerHTML = \`
                        <div>Aucun problème détecté dans le code actuel.</div>
                        <div style="margin-top: 10px; color: var(--success);">✓ Code validé par Louna</div>
                    \`;
                    break;
                case 'debug':
                    content.innerHTML = \`
                        <div>Console de débogage:</div>
                        <div style="margin-top: 10px;">
                            <div>🔍 Débogueur prêt</div>
                            <div>📍 Points d'arrêt: 0</div>
                            <div>🔄 Variables surveillées: 0</div>
                        </div>
                    \`;
                    break;
            }
        }

        function toggleBottomPanel() {
            const panel = document.getElementById('bottom-panel');
            panel.classList.toggle('visible');
        }

        function saveCurrentFile() {
            if (editor) {
                files[currentFile].content = editor.getValue();

                // Simulation de sauvegarde
                console.log(\`Fichier \${currentFile} sauvegardé\`);

                // Retirer l'indicateur de modification
                const tab = document.querySelector(\`[data-file="\${currentFile}"] span\`);
                if (tab && tab.textContent.includes('●')) {
                    tab.textContent = tab.textContent.replace(' ●', '');
                }

                // Afficher une notification
                showNotification('Fichier sauvegardé avec succès', 'success');
            }
        }

        function openCommandPalette() {
            // Simulation de la palette de commandes
            const command = prompt('Palette de commandes Louna:\\n\\n1. Formater le document\\n2. Organiser les imports\\n3. Générer la documentation\\n4. Optimiser le code\\n5. Envoyer à Louna pour analyse\\n\\nTapez le numéro de la commande:');

            if (command) {
                switch (command) {
                    case '1':
                        formatDocument();
                        break;
                    case '2':
                        organizeImports();
                        break;
                    case '3':
                        generateDocumentation();
                        break;
                    case '4':
                        optimizeCode();
                        break;
                    case '5':
                        sendToLouna();
                        break;
                    default:
                        showNotification('Commande non reconnue', 'warning');
                }
            }
        }

        function formatDocument() {
            if (editor) {
                editor.getAction('editor.action.formatDocument').run();
                showNotification('Document formaté', 'success');
            }
        }

        function organizeImports() {
            showNotification('Imports organisés', 'success');
        }

        function generateDocumentation() {
            showNotification('Documentation générée', 'success');
        }

        function optimizeCode() {
            showNotification('Code optimisé par Louna', 'success');
        }

        function sendToLouna() {
            if (editor) {
                const code = editor.getValue();
                // Envoyer le code à Louna pour analyse
                fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: \`Analyse ce code et donne-moi tes suggestions d'amélioration:\\n\\\`\\\`\\\`\${files[currentFile].language}\\n\${code}\\n\\\`\\\`\\\`\`
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Code envoyé à Louna pour analyse', 'success');
                        // Ouvrir le chat dans un nouvel onglet
                        window.open('/chat', '_blank');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur lors de l\\'envoi à Louna', 'error');
                });
            }
        }

        function showNotification(message, type = 'info') {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: \${type === 'success' ? 'var(--success)' : type === 'warning' ? 'var(--warning)' : type === 'error' ? 'var(--danger)' : 'var(--info)'};
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease;
            \`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = \`
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        \`;
        document.head.appendChild(style);
    </script>
</body>
</html>