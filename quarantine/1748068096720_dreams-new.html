<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON><PERSON></title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .dreams-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
        }

        .dreams-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .dreams-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dreams-title i {
            color: var(--accent);
        }

        .dreams-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 15px;
            border-radius: var(--border-radius-sm);
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background-color: var(--accent);
            color: var(--text-primary);
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .dreams-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .dream-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            border: var(--border-light);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dream-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .dream-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--accent), var(--primary-light));
        }

        .dream-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .dream-title {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .dream-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .dream-content {
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }

        .dream-content.expanded {
            max-height: none;
        }

        .dream-content::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(transparent, var(--bg-card));
            pointer-events: none;
        }

        .dream-content.expanded::after {
            display: none;
        }

        .dream-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dream-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .dream-tag {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
        }

        .dream-actions {
            display: flex;
            gap: 10px;
        }

        .dream-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .dream-action:hover {
            color: var(--accent);
        }

        .dream-expand {
            color: var(--accent);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 12px;
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .dream-expand:hover {
            text-decoration: underline;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px;
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            text-align: center;
        }

        .empty-state i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .empty-state-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .empty-state-text {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .dream-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .dream-modal.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .dream-modal-content {
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-lg);
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }

        .dream-modal.visible .dream-modal-content {
            transform: translateY(0);
        }

        .dream-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .dream-modal-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .dream-modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .dream-modal-close:hover {
            color: var(--accent);
        }

        .dream-modal-body {
            color: var(--text-primary);
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .dream-modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
        }

        .dream-modal-info {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .dream-modal-actions {
            display: flex;
            gap: 10px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--accent);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams-new.html" class="nav-item active">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings-new.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <div class="dreams-container">
            <div class="dreams-header">
                <h1 class="dreams-title">
                    <i class="fas fa-cloud-moon"></i>
                    Rêves de Louna
                </h1>
                <div class="dreams-actions">
                    <button id="generate-dream-btn" class="btn btn-primary">
                        <i class="fas fa-magic"></i>
                        Générer un rêve
                    </button>
                    <button id="refresh-dreams-btn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        Actualiser
                    </button>
                </div>
            </div>

            <div id="dreams-grid" class="dreams-grid">
                <!-- Les cartes de rêves seront ajoutées ici dynamiquement -->
            </div>

            <!-- État vide (affiché lorsqu'il n'y a pas de rêves) -->
            <div id="empty-state" class="empty-state" style="display: none;">
                <i class="fas fa-moon"></i>
                <h2 class="empty-state-title">Aucun rêve pour le moment</h2>
                <p class="empty-state-text">Louna n'a pas encore généré de rêves. Générez un rêve pour commencer.</p>
                <button id="empty-generate-btn" class="btn btn-primary">
                    <i class="fas fa-magic"></i>
                    Générer un rêve
                </button>
            </div>
        </div>
    </div>

    <!-- Modal pour afficher un rêve en détail -->
    <div id="dream-modal" class="dream-modal">
        <div class="dream-modal-content">
            <div class="dream-modal-header">
                <h2 id="modal-title" class="dream-modal-title">Titre du rêve</h2>
                <button id="modal-close" class="dream-modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-body" class="dream-modal-body">
                <!-- Contenu du rêve -->
            </div>
            <div class="dream-modal-footer">
                <div id="modal-info" class="dream-modal-info">
                    <!-- Informations sur le rêve -->
                </div>
                <div class="dream-modal-actions">
                    <button id="modal-delete" class="btn btn-secondary">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script src="/js/dreams.js"></script>
  <script src="js/native-app.js"></script>
</body>
</html>
