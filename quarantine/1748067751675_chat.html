<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - <PERSON><PERSON></title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/native-app.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .chat-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 140px);
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            border: var(--border-light);
        }

        .chat-header {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: var(--header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-title {
            font-size: 18px;
            font-weight: bold;
            color: #000; /* Noir pour un meilleur contraste sur le fond rose/violet */
            margin-left: 10px;
        }

        .chat-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--success);
            margin-left: auto;
        }

        .agent-selector {
            margin-left: auto;
            position: relative;
        }

        .agent-selector-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: var(--text-primary);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .agent-selector-btn:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        .agent-selector-btn i {
            font-size: 12px;
            opacity: 0.7;
        }

        .agent-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 250px;
            background-color: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            padding: 10px;
            z-index: 100;
            display: none;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 5px;
        }

        .agent-dropdown.visible {
            display: block;
        }

        .agent-dropdown-header {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-list {
            display: flex;
            flex-direction: column;
            gap: 5px;
            max-height: 200px;
            overflow-y: auto;
        }

        .agent-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .agent-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .agent-item.active {
            background-color: rgba(255, 105, 180, 0.2);
            border-left: 3px solid var(--primary);
        }

        .agent-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .agent-info {
            flex-grow: 1;
        }

        .agent-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .agent-model {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .agent-dropdown-footer {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
        }

        .agent-dropdown-btn {
            font-size: 12px;
            padding: 5px 10px;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .agent-dropdown-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .agent-dropdown-btn.primary {
            background-color: var(--primary);
            color: white;
        }

        .chat-messages {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 15px;
            border-radius: 18px;
            position: relative;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            align-self: flex-end;
            background-color: var(--primary);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.agent {
            align-self: flex-start;
            background-color: rgba(255, 255, 255, 0.15); /* Plus opaque pour un meilleur contraste */
            color: var(--text-primary);
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2); /* Ajouter une bordure pour améliorer la visibilité */
        }

        .message-content {
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400; /* Légèrement plus gras pour une meilleure lisibilité */
        }

        .message-time {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8); /* Plus opaque pour un meilleur contraste */
            margin-top: 5px;
            text-align: right;
        }

        .message.agent .message-time {
            color: rgba(255, 255, 255, 0.8); /* Plus opaque pour un meilleur contraste */
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.9); /* Plus opaque pour un meilleur contraste */
        }

        .chat-input-container {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-input {
            flex-grow: 1;
            padding: 12px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 14px;
            outline: none;
            transition: var(--transition-fast);
        }

        .chat-input:focus {
            border-color: var(--accent);
            box-shadow: 0 0 0 2px var(--accent-glow);
        }

        .chat-send-btn {
            background-color: var(--accent);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-left: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
        }

        .chat-send-btn:hover {
            background-color: var(--primary-light);
            transform: scale(1.05);
        }

        .chat-send-btn i {
            font-size: 16px;
        }

        .chat-options {
            display: flex;
            gap: 10px;
            margin-right: 10px;
        }

        .chat-option-btn {
            background-color: transparent;
            color: var(--text-secondary);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
        }

        .chat-option-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .chat-option-btn.active {
            color: var(--accent);
            background-color: rgba(255, 255, 255, 0.05);
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 18px;
            align-self: flex-start;
            margin-top: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .typing-indicator.visible {
            opacity: 1;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: var(--text-secondary);
            border-radius: 50%;
            margin: 0 2px;
            animation: typingAnimation 1.5s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-5px); }
        }

        .memory-indicator {
            display: flex;
            align-items: center;
            margin-left: 15px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .memory-indicator i {
            margin-right: 5px;
            color: var(--temp-warm);
        }

        .memory-temp {
            width: 30px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin-left: 5px;
            overflow: hidden;
        }

        .memory-temp-fill {
            height: 100%;
            background-color: var(--temp-warm);
            width: 70%;
        }

        /* Styles pour les suggestions */
        .suggestions-container {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background-color: var(--bg-secondary);
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 10px;
            display: none;
            flex-wrap: wrap;
            gap: 10px;
            box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            max-height: 100px;
            overflow-y: auto;
        }

        .suggestions-container.visible {
            display: flex;
        }

        .suggestion-item {
            background-color: var(--bg-tertiary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 5px 12px;
            font-size: 13px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .suggestion-item:hover {
            background-color: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        /* Styles pour l'indicateur d'enregistrement */
        .recording-indicator {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--danger);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .recording-indicator.visible {
            opacity: 1;
        }

        .recording-indicator i {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.3;
            }
            100% {
                opacity: 1;
            }
        }

        /* Styles pour le conteneur vidéo */
        .video-container {
            display: none;
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 320px;
            height: 240px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            z-index: 100;
            opacity: 0;
            transition: all 0.3s ease;
            border: 2px solid var(--accent);
        }

        .video-container.active {
            display: block;
            opacity: 1;
            transform: scale(1);
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }

        .video-control-btn {
            transition: all 0.2s ease;
        }

        .video-control-btn:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: scale(1.1);
        }

        .video-controls {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }

        .video-control-btn {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .video-control-btn:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .video-stats {
            position: absolute;
            bottom: 5px;
            left: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 3px;
        }

        /* Styles pour les actions de message */
        .message-actions {
            display: flex;
            gap: 5px;
            margin-top: 5px;
            align-self: flex-end;
        }

        .message-action-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .message-action-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .message.agent .message-action-btn {
            color: var(--text-secondary);
        }

        .message.agent .message-action-btn:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
        }

        /* Styles pour les suggestions contextuelles */
        .contextual-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
            padding: 0 10px;
        }

        .contextual-suggestion {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .contextual-suggestion:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .contextual-suggestion i {
            font-size: 10px;
            opacity: 0.7;
        }

        /* Styles pour les raccourcis de code */
        .code-shortcuts {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background-color: var(--bg-secondary);
            border-radius: 10px 10px 0 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.3);
            z-index: 100;
        }

        .code-shortcuts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: bold;
            color: var(--accent-color);
        }

        .close-shortcuts-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .close-shortcuts-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .code-shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            padding: 20px;
        }

        .code-shortcut-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 15px 10px;
            background-color: var(--bg-tertiary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .code-shortcut-btn:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
        }

        .code-shortcut-btn i {
            font-size: 20px;
            opacity: 0.8;
        }

        .code-shortcut-btn:hover i {
            opacity: 1;
        }

        .code-shortcut-btn span {
            font-weight: 500;
        }

        /* Styles pour les messages avec code */
        .message-content pre {
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid var(--accent-color);
        }

        .message-content code {
            background-color: rgba(0, 0, 0, 0.2);
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item active">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/agents" class="nav-item">
                <i class="fas fa-robot"></i>
                <span>Agents</span>
            </a>
            <a href="/training" class="nav-item">
                <i class="fas fa-graduation-cap"></i>
                <span>Formation</span>
            </a>
            <a href="/memory-fusion.html" class="nav-item">
                <i class="fas fa-code-branch"></i>
                <span>Fusion Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- Chat -->
        <div class="chat-container">
            <div class="chat-header">
                <i class="fas fa-robot"></i>
                <div class="chat-title">Louna</div>
                <div class="memory-indicator">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                    <div class="memory-temp">
                        <div class="memory-temp-fill"></div>
                    </div>
                </div>

                <!-- Sélecteur d'agent -->
                <div class="agent-selector">
                    <button class="agent-selector-btn" id="agent-selector-btn">
                        <span id="current-agent-name">Agent par défaut</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>

                    <div class="agent-dropdown" id="agent-dropdown">
                        <div class="agent-dropdown-header">Choisir un agent</div>
                        <div class="agent-list" id="agent-list">
                            <!-- Les agents seront ajoutés ici dynamiquement -->
                        </div>
                        <div class="agent-dropdown-footer">
                            <button class="agent-dropdown-btn" id="refresh-agents-btn">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <button class="agent-dropdown-btn primary" id="manage-agents-btn">
                                <i class="fas fa-cog"></i> Gérer les agents
                            </button>
                        </div>
                    </div>
                </div>

                <div class="chat-status"></div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <!-- Les messages seront ajoutés ici dynamiquement -->
                <div class="message agent">
                    <div class="message-content">Bonjour ! Je suis Louna, votre agent à mémoire thermique. Comment puis-je vous aider aujourd'hui ?</div>
                    <div class="message-time">10:00</div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-options">
                    <button class="chat-option-btn" id="clear-chat-btn" title="Effacer la conversation">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="chat-option-btn" id="memory-btn" title="Voir la mémoire associée">
                        <i class="fas fa-brain"></i>
                    </button>
                    <button class="chat-option-btn" id="voice-btn" title="Activer/désactiver la synthèse vocale">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <button class="chat-option-btn" id="mic-btn" title="Utiliser la reconnaissance vocale">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="chat-option-btn" id="camera-btn" title="Activer/désactiver la caméra">
                        <i class="fas fa-video"></i>
                    </button>
                    <button class="chat-option-btn" id="code-editor-btn" title="Ouvrir l'éditeur de code">
                        <i class="fas fa-code"></i>
                    </button>
                </div>

                <!-- Raccourcis de génération de code -->
                <div class="code-shortcuts" id="code-shortcuts" style="display: none;">
                    <div class="code-shortcuts-header">
                        <span>Génération de code</span>
                        <button class="close-shortcuts-btn" id="close-shortcuts-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="code-shortcuts-grid">
                        <button class="code-shortcut-btn" data-prompt="Écris-moi une fonction JavaScript pour">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi une classe Python pour">
                            <i class="fab fa-python"></i>
                            <span>Python</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi du code HTML/CSS pour">
                            <i class="fab fa-html5"></i>
                            <span>HTML/CSS</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi un composant React pour">
                            <i class="fab fa-react"></i>
                            <span>React</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi une API Node.js pour">
                            <i class="fab fa-node-js"></i>
                            <span>Node.js</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi du code SQL pour">
                            <i class="fas fa-database"></i>
                            <span>SQL</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi un script Bash pour">
                            <i class="fas fa-terminal"></i>
                            <span>Bash</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Écris-moi du code C++ pour">
                            <i class="fas fa-code"></i>
                            <span>C++</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Explique-moi ce code et améliore-le :">
                            <i class="fas fa-search-plus"></i>
                            <span>Analyser</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Débugge ce code :">
                            <i class="fas fa-bug"></i>
                            <span>Debug</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Optimise ce code :">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Optimiser</span>
                        </button>
                        <button class="code-shortcut-btn" data-prompt="Documente ce code :">
                            <i class="fas fa-file-alt"></i>
                            <span>Documenter</span>
                        </button>
                    </div>
                </div>

                <!-- Conteneur vidéo pour LTX -->
                <div id="video-container" class="video-container">
                    <!-- La vidéo sera ajoutée ici dynamiquement -->
                </div>

                <input type="text" class="chat-input" id="chat-input" placeholder="Écrivez votre message..." autocomplete="off">

                <button class="chat-send-btn" id="chat-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>

                <div class="suggestions-container" id="suggestions-container">
                    <!-- Les suggestions seront ajoutées ici dynamiquement -->
                </div>

                <div class="recording-indicator" id="recording-indicator">
                    <i class="fas fa-microphone"></i>
                    <span>Enregistrement en cours...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script src="/js/ltx-video.js"></script>
    <script src="/js/agents.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser la mémoire thermique et les accélérateurs
            await window.thermalMemory.initialize();
            await window.kyberAccelerators.initialize();

            // Initialiser le gestionnaire d'agents
            await window.agentManager.initialize();

            // Éléments du DOM
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const clearChatBtn = document.getElementById('clear-chat-btn');
            const memoryBtn = document.getElementById('memory-btn');
            const voiceBtn = document.getElementById('voice-btn');
            const micBtn = document.getElementById('mic-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const suggestionsContainer = document.getElementById('suggestions-container');
            const recordingIndicator = document.getElementById('recording-indicator');

            // Éléments du sélecteur d'agent
            const agentSelectorBtn = document.getElementById('agent-selector-btn');
            const agentDropdown = document.getElementById('agent-dropdown');
            const agentList = document.getElementById('agent-list');
            const currentAgentName = document.getElementById('current-agent-name');
            const refreshAgentsBtn = document.getElementById('refresh-agents-btn');
            const manageAgentsBtn = document.getElementById('manage-agents-btn');

            // Variables pour les fonctionnalités
            let voiceEnabled = false;
            let recognition = null;
            let isRecording = false;
            let currentAgent = null;
            let suggestions = [
                "Qu'est-ce que la mémoire thermique ?",
                "Comment fonctionnent les accélérateurs Kyber ?",
                "Peux-tu me parler des rêves ?",
                "Montre-moi le graphe de mémoire",
                "Quelles sont les performances actuelles ?"
            ];

            // Fonction pour charger les agents
            async function loadAgents() {
                try {
                    const agents = await window.agentManager.getAgents();

                    // Vider la liste des agents
                    agentList.innerHTML = '';

                    // Ajouter les agents à la liste
                    Object.values(agents).forEach(agent => {
                        const agentItem = document.createElement('div');
                        agentItem.className = `agent-item ${agent.id === window.agentManager.activeAgentId ? 'active' : ''}`;
                        agentItem.dataset.agentId = agent.id;

                        agentItem.innerHTML = `
                            <div class="agent-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="agent-info">
                                <div class="agent-name">${agent.name}</div>
                                <div class="agent-model">${agent.type === 'ollama' ? agent.model : agent.type}</div>
                            </div>
                        `;

                        agentItem.addEventListener('click', () => {
                            activateAgent(agent.id);
                        });

                        agentList.appendChild(agentItem);

                        // Si c'est l'agent actif, mettre à jour le nom affiché
                        if (agent.id === window.agentManager.activeAgentId) {
                            currentAgentName.textContent = agent.name;
                            currentAgent = agent;
                        }
                    });

                    // Si aucun agent n'est actif, utiliser le premier agent
                    if (!currentAgent && Object.values(agents).length > 0) {
                        currentAgent = Object.values(agents)[0];
                        currentAgentName.textContent = currentAgent.name;
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des agents:', error);
                }
            }

            // Fonction pour activer un agent
            async function activateAgent(agentId) {
                try {
                    const result = await window.agentManager.activateAgent(agentId);

                    if (result.success) {
                        // Mettre à jour l'interface
                        const agents = await window.agentManager.getAgents();
                        const agent = agents[agentId];

                        if (agent) {
                            currentAgentName.textContent = agent.name;
                            currentAgent = agent;

                            // Mettre à jour la classe active
                            const agentItems = agentList.querySelectorAll('.agent-item');
                            agentItems.forEach(item => {
                                item.classList.remove('active');
                                if (item.dataset.agentId === agentId) {
                                    item.classList.add('active');
                                }
                            });

                            // Fermer le dropdown
                            agentDropdown.classList.remove('visible');

                            // Ajouter un message système
                            addSystemMessage(`Agent ${agent.name} activé.`);
                        }
                    } else {
                        console.error('Erreur lors de l\'activation de l\'agent:', result.error);
                    }
                } catch (error) {
                    console.error('Erreur lors de l\'activation de l\'agent:', error);
                }
            }

            // Fonction pour ajouter un message système
            function addSystemMessage(content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message system';
                messageDiv.style.alignSelf = 'center';
                messageDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                messageDiv.style.color = 'var(--text-secondary)';
                messageDiv.style.fontSize = '12px';
                messageDiv.style.padding = '5px 10px';
                messageDiv.style.borderRadius = '10px';
                messageDiv.style.margin = '10px 0';

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                messageDiv.appendChild(contentDiv);

                // Insérer avant l'indicateur de frappe
                chatMessages.insertBefore(messageDiv, typingIndicator);

                // Faire défiler vers le bas
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Fonction pour ajouter un message
            function addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                const now = new Date();
                timeDiv.textContent = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);

                // Ajouter des actions pour les messages de l'agent
                if (!isUser) {
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'message-actions';

                    const speakBtn = document.createElement('button');
                    speakBtn.className = 'message-action-btn';
                    speakBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                    speakBtn.title = 'Lire à haute voix';
                    speakBtn.addEventListener('click', () => {
                        speakText(content);
                    });

                    const copyBtn = document.createElement('button');
                    copyBtn.className = 'message-action-btn';
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                    copyBtn.title = 'Copier';
                    copyBtn.addEventListener('click', () => {
                        navigator.clipboard.writeText(content)
                            .then(() => {
                                // Afficher une notification de succès
                                const originalText = copyBtn.innerHTML;
                                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                                setTimeout(() => {
                                    copyBtn.innerHTML = originalText;
                                }, 1500);
                            });
                    });

                    // Bouton pour ouvrir dans l'éditeur de code si le message contient du code
                    const codeBtn = document.createElement('button');
                    codeBtn.className = 'message-action-btn';
                    codeBtn.innerHTML = '<i class="fas fa-code"></i>';
                    codeBtn.title = 'Ouvrir dans l\'éditeur';
                    codeBtn.style.display = 'none';

                    // Vérifier si le message contient du code
                    if (content.includes('```') || content.includes('function') || content.includes('class') || content.includes('import') || content.includes('const ') || content.includes('let ') || content.includes('var ')) {
                        codeBtn.style.display = 'inline-block';
                        codeBtn.addEventListener('click', () => {
                            openInCodeEditor(content);
                        });
                    }

                    actionsDiv.appendChild(speakBtn);
                    actionsDiv.appendChild(copyBtn);
                    actionsDiv.appendChild(codeBtn);
                    messageDiv.appendChild(actionsDiv);
                }

                // Insérer avant l'indicateur de frappe
                chatMessages.insertBefore(messageDiv, typingIndicator);

                // Faire défiler vers le bas
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Si c'est un message utilisateur, l'ajouter à la mémoire thermique
                if (isUser) {
                    window.thermalMemory.addEntry(
                        'user_message',
                        content,
                        0.7,
                        'conversation'
                    );
                } else {
                    window.thermalMemory.addEntry(
                        'agent_response',
                        content,
                        0.6,
                        'conversation'
                    );

                    // Lire le message à haute voix si la synthèse vocale est activée
                    if (voiceEnabled) {
                        speakText(content);
                    }

                    // Ajouter des suggestions contextuelles après un court délai
                    setTimeout(() => {
                        showContextualSuggestions(content);
                    }, 500);
                }

                return messageDiv;
            }

            // Fonction pour la synthèse vocale
            function speakText(text) {
                if ('speechSynthesis' in window) {
                    // Arrêter toute synthèse vocale en cours
                    window.speechSynthesis.cancel();

                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = 'fr-FR';
                    utterance.rate = 1.0;
                    utterance.pitch = 1.0;

                    // Obtenir les voix disponibles
                    let voices = window.speechSynthesis.getVoices();

                    // Si les voix ne sont pas encore chargées, attendre qu'elles le soient
                    if (voices.length === 0) {
                        window.speechSynthesis.addEventListener('voiceschanged', () => {
                            voices = window.speechSynthesis.getVoices();
                            setVoice();
                        });
                    } else {
                        setVoice();
                    }

                    function setVoice() {
                        // Chercher une voix française féminine
                        const frenchVoice = voices.find(voice =>
                            voice.lang.includes('fr') && voice.name.includes('female'));

                        if (frenchVoice) {
                            utterance.voice = frenchVoice;
                        }

                        window.speechSynthesis.speak(utterance);
                    }
                }
            }

            // Fonction pour initialiser la reconnaissance vocale
            function initSpeechRecognition() {
                if ('webkitSpeechRecognition' in window) {
                    recognition = new webkitSpeechRecognition();
                    recognition.continuous = false;
                    recognition.interimResults = false;
                    recognition.lang = 'fr-FR';

                    recognition.onstart = function() {
                        isRecording = true;
                        recordingIndicator.classList.add('visible');
                        micBtn.classList.add('active');
                    };

                    recognition.onend = function() {
                        isRecording = false;
                        recordingIndicator.classList.remove('visible');
                        micBtn.classList.remove('active');
                    };

                    recognition.onresult = function(event) {
                        const transcript = event.results[0][0].transcript;
                        chatInput.value = transcript;

                        // Envoyer automatiquement le message après un court délai
                        setTimeout(() => {
                            sendMessage();
                        }, 500);
                    };

                    recognition.onerror = function(event) {
                        console.error('Erreur de reconnaissance vocale:', event.error);
                        isRecording = false;
                        recordingIndicator.classList.remove('visible');
                        micBtn.classList.remove('active');
                    };

                    return true;
                }

                return false;
            }

            // Fonction pour afficher les suggestions
            function showSuggestions() {
                suggestionsContainer.innerHTML = '';

                suggestions.forEach(suggestion => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'suggestion-item';
                    suggestionItem.textContent = suggestion;
                    suggestionItem.addEventListener('click', () => {
                        chatInput.value = suggestion;
                        suggestionsContainer.classList.remove('visible');
                        sendMessage();
                    });

                    suggestionsContainer.appendChild(suggestionItem);
                });

                suggestionsContainer.classList.add('visible');
            }

            // Fonction pour générer des suggestions contextuelles basées sur le dernier message
            function generateContextualSuggestions(lastMessage) {
                // Suggestions par défaut si aucun contexte n'est trouvé
                const defaultSuggestions = [
                    { text: "Dis-m'en plus", icon: "fas fa-info-circle" },
                    { text: "Explique-moi ça", icon: "fas fa-question-circle" },
                    { text: "Merci !", icon: "fas fa-heart" }
                ];

                // Suggestions basées sur le contexte
                let contextualSuggestions = [];

                // Analyser le dernier message pour générer des suggestions contextuelles
                if (lastMessage.includes("mémoire thermique") || lastMessage.includes("mémoire")) {
                    contextualSuggestions = [
                        { text: "Comment fonctionne la mémoire thermique ?", icon: "fas fa-brain" },
                        { text: "Quelles sont les zones de mémoire ?", icon: "fas fa-layer-group" },
                        { text: "Optimise ta mémoire", icon: "fas fa-bolt" }
                    ];
                } else if (lastMessage.includes("accélérateur") || lastMessage.includes("Kyber")) {
                    contextualSuggestions = [
                        { text: "Quels sont les types d'accélérateurs ?", icon: "fas fa-microchip" },
                        { text: "Comment optimiser les accélérateurs ?", icon: "fas fa-tachometer-alt" },
                        { text: "Montre-moi les performances", icon: "fas fa-chart-line" }
                    ];
                } else if (lastMessage.includes("rêve") || lastMessage.includes("dream")) {
                    contextualSuggestions = [
                        { text: "Génère un rêve", icon: "fas fa-cloud-moon" },
                        { text: "Comment fonctionnent les rêves ?", icon: "fas fa-moon" },
                        { text: "Montre-moi tes rêves récents", icon: "fas fa-book-open" }
                    ];
                } else if (lastMessage.includes("vidéo") || lastMessage.includes("caméra") || lastMessage.includes("LTX")) {
                    contextualSuggestions = [
                        { text: "Active la caméra", icon: "fas fa-video" },
                        { text: "Comment fonctionne LTX vidéo ?", icon: "fas fa-camera" },
                        { text: "Désactive la détection d'objets", icon: "fas fa-eye-slash" }
                    ];
                }

                // Si aucune suggestion contextuelle n'a été générée, utiliser les suggestions par défaut
                return contextualSuggestions.length > 0 ? contextualSuggestions : defaultSuggestions;
            }

            // Fonction pour afficher les suggestions contextuelles
            function showContextualSuggestions(lastMessage) {
                const suggestions = generateContextualSuggestions(lastMessage);

                // Créer le conteneur pour les suggestions contextuelles
                const contextualContainer = document.createElement('div');
                contextualContainer.className = 'contextual-suggestions';

                // Ajouter chaque suggestion au conteneur
                suggestions.forEach(suggestion => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'contextual-suggestion';
                    suggestionItem.innerHTML = `<i class="${suggestion.icon}"></i> ${suggestion.text}`;
                    suggestionItem.addEventListener('click', () => {
                        chatInput.value = suggestion.text;
                        sendMessage();
                    });

                    contextualContainer.appendChild(suggestionItem);
                });

                // Ajouter le conteneur après le dernier message de l'agent
                const messages = document.querySelectorAll('.message.agent');
                if (messages.length > 0) {
                    const lastAgentMessage = messages[messages.length - 1];
                    lastAgentMessage.appendChild(contextualContainer);
                }
            }

            // Fonction pour montrer l'indicateur de frappe
            function showTypingIndicator() {
                typingIndicator.classList.add('visible');
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Fonction pour cacher l'indicateur de frappe
            function hideTypingIndicator() {
                typingIndicator.classList.remove('visible');
            }

            // Fonction pour envoyer un message
            async function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Ajouter le message de l'utilisateur
                addMessage(message, true);

                // Effacer l'input
                chatInput.value = '';

                // Montrer l'indicateur de frappe
                showTypingIndicator();

                try {
                    // Récupérer l'historique de conversation pour le contexte
                    const history = [];
                    const messageElements = document.querySelectorAll('.message');

                    messageElements.forEach(element => {
                        const content = element.querySelector('.message-content').textContent;
                        if (element.classList.contains('user')) {
                            history.push({ role: 'user', content });
                        } else if (element.classList.contains('agent')) {
                            history.push({ role: 'assistant', content });
                        }
                    });

                    // Limiter l'historique aux 10 derniers messages
                    const limitedHistory = history.slice(-10);

                    // Envoyer le message à l'API
                    const response = await fetch('/api/chat/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message,
                            history: limitedHistory
                        })
                    });

                    const data = await response.json();

                    // Simuler un délai de réponse basé sur le temps de traitement
                    const responseDelay = Math.max(500, Math.min(2000, data.processingTime || 1000));

                    setTimeout(() => {
                        // Cacher l'indicateur de frappe
                        hideTypingIndicator();

                        if (data.success) {
                            // Ajouter la réponse de l'agent
                            addMessage(data.response);

                            // Mettre à jour l'indicateur de mémoire
                            updateMemoryIndicator(data.boostFactor || 1.0);

                            // Si la réponse contient des commandes spéciales, les traiter
                            if (data.commands) {
                                processCommands(data.commands);
                            }

                            // Si les informations sur l'agent sont disponibles, mettre à jour l'interface
                            if (data.agent) {
                                // Mettre à jour le nom de l'agent actif si nécessaire
                                if (currentAgentName.textContent !== data.agent.name) {
                                    currentAgentName.textContent = data.agent.name;

                                    // Ajouter un message système
                                    addSystemMessage(`Réponse générée par l'agent ${data.agent.name}`);
                                }
                            }
                        } else {
                            // Afficher un message d'erreur
                            addMessage("Désolé, je n'ai pas pu traiter votre message. Veuillez réessayer.");
                        }
                    }, responseDelay);
                } catch (error) {
                    console.error('Erreur lors de l\'envoi du message:', error);

                    // Cacher l'indicateur de frappe après un délai
                    setTimeout(() => {
                        hideTypingIndicator();

                        // Afficher un message d'erreur
                        addMessage("Désolé, une erreur s'est produite lors de la communication avec le serveur. Veuillez réessayer.");
                    }, 1000);
                }
            }

            /**
             * Traite les commandes spéciales envoyées par l'agent
             * @param {Array} commands - Liste des commandes à exécuter
             */
            function processCommands(commands) {
                if (!commands || !Array.isArray(commands)) return;

                commands.forEach(cmd => {
                    switch (cmd.type) {
                        case 'optimize_memory':
                            // Optimiser la mémoire
                            window.kyberAccelerators.optimizeMemoryCirculation()
                                .then(success => {
                                    if (success) {
                                        addMessage("J'ai optimisé ma mémoire thermique pour une meilleure circulation des informations.", false);
                                    }
                                });
                            break;

                        case 'optimize_ltx':
                            // Optimiser le traitement LTX
                            window.kyberAccelerators.optimizeLTXProcessing()
                                .then(success => {
                                    if (success) {
                                        addMessage("J'ai optimisé mon traitement vidéo LTX pour une meilleure reconnaissance d'objets.", false);
                                    }
                                });
                            break;

                        case 'cycle_memory':
                            // Effectuer un cycle de mémoire
                            window.thermalMemory.cycle()
                                .then(() => {
                                    addMessage("J'ai effectué un cycle de mémoire pour consolider les informations importantes.", false);
                                });
                            break;

                        case 'generate_dream':
                            // Générer un rêve
                            window.thermalMemory.generateDream()
                                .then(dream => {
                                    if (dream) {
                                        addMessage(`J'ai généré un rêve basé sur nos interactions: "${dream.content}"`, false);
                                    }
                                });
                            break;

                        case 'toggle_camera':
                            // Activer/désactiver la caméra
                            toggleCamera();
                            break;

                        default:
                            console.log('Commande inconnue:', cmd.type);
                    }
                });
            }

            // Fonction pour mettre à jour l'indicateur de mémoire
            function updateMemoryIndicator(boostFactor) {
                const memoryTempFill = document.querySelector('.memory-temp-fill');
                const percentage = Math.min(100, Math.max(10, boostFactor * 20));
                memoryTempFill.style.width = `${percentage}%`;

                // Changer la couleur en fonction du facteur de boost
                if (boostFactor >= 3.0) {
                    memoryTempFill.style.backgroundColor = 'var(--temp-hot)';
                } else if (boostFactor >= 2.0) {
                    memoryTempFill.style.backgroundColor = 'var(--temp-warm)';
                } else {
                    memoryTempFill.style.backgroundColor = 'var(--temp-cool)';
                }
            }

            // Événement pour envoyer un message
            chatSendBtn.addEventListener('click', sendMessage);

            // Événement pour envoyer un message avec la touche Entrée
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Événement pour effacer la conversation (interface seulement, pas la mémoire)
            clearChatBtn.addEventListener('click', () => {
                if (confirm('Effacer l\'historique de conversation de l\'interface ? (La mémoire sera conservée)')) {
                    // Garder seulement le message de bienvenue et l'indicateur de frappe
                    const welcomeMessage = chatMessages.querySelector('.message.agent');
                    const typingIndicator = document.getElementById('typing-indicator');

                    // Vider le conteneur
                    chatMessages.innerHTML = '';

                    // Remettre le message de bienvenue et l'indicateur
                    if (welcomeMessage) {
                        chatMessages.appendChild(welcomeMessage);
                    }
                    if (typingIndicator) {
                        chatMessages.appendChild(typingIndicator);
                    }

                    // Ajouter un message système
                    addSystemMessage('Historique de conversation effacé (mémoire conservée)');
                }
            });

            // Événement pour voir la mémoire associée
            memoryBtn.addEventListener('click', () => {
                window.location.href = '/futuristic-interface.html';
            });

            // Événement pour activer/désactiver la synthèse vocale
            voiceBtn.addEventListener('click', () => {
                voiceEnabled = !voiceEnabled;
                voiceBtn.classList.toggle('active', voiceEnabled);

                if (voiceEnabled) {
                    // Tester la synthèse vocale
                    speakText("Synthèse vocale activée");
                } else {
                    // Arrêter toute synthèse vocale en cours
                    if ('speechSynthesis' in window) {
                        window.speechSynthesis.cancel();
                    }
                }
            });

            // Événement pour la reconnaissance vocale
            micBtn.addEventListener('click', () => {
                if (!recognition) {
                    const supported = initSpeechRecognition();
                    if (!supported) {
                        addSystemMessage("Reconnaissance vocale non prise en charge par ce navigateur.");
                        return;
                    }
                }

                if (isRecording) {
                    recognition.stop();
                    isRecording = false;
                    micBtn.classList.remove('active');
                    recordingIndicator.classList.remove('visible');
                    addSystemMessage("Enregistrement arrêté");
                } else {
                    try {
                        recognition.start();
                        isRecording = true;
                        micBtn.classList.add('active');
                        recordingIndicator.classList.add('visible');
                        addSystemMessage("Enregistrement démarré - Parlez maintenant");
                    } catch (error) {
                        console.error('Erreur lors du démarrage de la reconnaissance vocale:', error);
                        addSystemMessage("Erreur lors du démarrage de l'enregistrement");
                    }
                }
            });

            // Événement pour afficher les suggestions lorsque l'input est vide et reçoit le focus
            chatInput.addEventListener('focus', () => {
                if (chatInput.value.trim() === '') {
                    showSuggestions();
                }
            });

            // Événement pour masquer les suggestions lorsque l'input perd le focus
            chatInput.addEventListener('blur', () => {
                // Petit délai pour permettre de cliquer sur une suggestion
                setTimeout(() => {
                    suggestionsContainer.classList.remove('visible');
                }, 200);
            });

            // Événement pour mettre à jour les suggestions en fonction de la saisie
            chatInput.addEventListener('input', () => {
                if (chatInput.value.trim() === '') {
                    showSuggestions();
                } else {
                    suggestionsContainer.classList.remove('visible');
                }
            });

            // Initialiser la reconnaissance vocale si elle est prise en charge
            initSpeechRecognition();

            // Initialiser LTX Video seulement si disponible
            try {
                if (window.LTXVideo) {
                    window.ltxVideo = new LTXVideo({
                        captureWidth: 640,
                        captureHeight: 480,
                        frameRate: 30,
                        processingEnabled: true,
                        thermalAnalysisEnabled: true,
                        objectDetectionEnabled: true,
                        facialRecognitionEnabled: false
                    });

                    // Initialiser LTX Video
                    await window.ltxVideo.initialize();
                    addSystemMessage("Système LTX Video initialisé");
                } else {
                    console.log('LTX Video non disponible');
                }
            } catch (error) {
                console.error('Erreur lors de l\'initialisation LTX Video:', error);
            }

            // Référence au bouton de caméra et au conteneur vidéo
            const cameraBtn = document.getElementById('camera-btn');
            const videoContainer = document.getElementById('video-container');

            // Variable pour suivre l'état de la caméra
            let cameraActive = false;

            // Fonction pour activer/désactiver la caméra
            async function toggleCamera() {
                if (cameraActive) {
                    // Désactiver la caméra
                    try {
                        if (window.ltxVideo) {
                            await window.ltxVideo.stop();
                        }

                        // Arrêter tous les flux vidéo
                        const videoElement = document.getElementById('ltx-video-element');
                        if (videoElement && videoElement.srcObject) {
                            const tracks = videoElement.srcObject.getTracks();
                            tracks.forEach(track => track.stop());
                            videoElement.srcObject = null;
                        }

                        videoContainer.classList.remove('active');
                        cameraBtn.classList.remove('active');
                        cameraActive = false;

                        // Informer l'agent que la caméra est désactivée
                        addSystemMessage("Caméra désactivée");
                    } catch (error) {
                        console.error('Erreur lors de la désactivation de la caméra:', error);
                        addSystemMessage("Erreur lors de la désactivation de la caméra");
                    }
                } else {
                    try {
                        // Demander l'autorisation d'accès à la caméra
                        const stream = await navigator.mediaDevices.getUserMedia({
                            video: {
                                width: { ideal: 640 },
                                height: { ideal: 480 },
                                facingMode: 'user'
                            },
                            audio: false
                        });

                        // Préparer le conteneur vidéo
                        videoContainer.innerHTML = `
                            <video id="ltx-video-element" autoplay playsinline muted></video>
                            <div class="video-controls" style="position: absolute; top: 10px; right: 10px; z-index: 10;">
                                <button class="video-control-btn" id="close-video-btn" style="background: rgba(0,0,0,0.7); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="video-stats" id="video-stats" style="position: absolute; bottom: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px;">
                                Caméra active
                            </div>
                        `;

                        // Récupérer l'élément vidéo et y attacher le flux
                        const videoElement = document.getElementById('ltx-video-element');
                        videoElement.srcObject = stream;

                        // Afficher le conteneur vidéo
                        videoContainer.classList.add('active');
                        cameraBtn.classList.add('active');
                        cameraActive = true;

                        // Ajouter un gestionnaire d'événements pour le bouton de fermeture
                        document.getElementById('close-video-btn').addEventListener('click', toggleCamera);

                        // Informer l'agent que la caméra est activée
                        addSystemMessage("Caméra activée - Vision en cours");

                    } catch (error) {
                        console.error('Erreur lors de l\'activation de la caméra:', error);

                        if (error.name === 'NotAllowedError') {
                            addSystemMessage("Accès à la caméra refusé. Veuillez autoriser l'accès dans les paramètres du navigateur.");
                        } else if (error.name === 'NotFoundError') {
                            addSystemMessage("Aucune caméra trouvée sur cet appareil.");
                        } else {
                            addSystemMessage("Erreur lors de l'activation de la caméra: " + error.message);
                        }
                    }
                }
            }

            // Ajouter un gestionnaire d'événements pour le bouton de caméra
            cameraBtn.addEventListener('click', toggleCamera);

            // Événements pour le sélecteur d'agent
            agentSelectorBtn.addEventListener('click', () => {
                agentDropdown.classList.toggle('visible');
            });

            // Fermer le dropdown si on clique en dehors
            document.addEventListener('click', (event) => {
                if (!agentSelectorBtn.contains(event.target) && !agentDropdown.contains(event.target)) {
                    agentDropdown.classList.remove('visible');
                }
            });

            // Événement pour le bouton de rafraîchissement des agents
            refreshAgentsBtn.addEventListener('click', async () => {
                await loadAgents();
            });

            // Événement pour le bouton de gestion des agents
            manageAgentsBtn.addEventListener('click', () => {
                window.location.href = '/agents.html';
            });

            // Fonctions pour les raccourcis de code
            const codeEditorBtn = document.getElementById('code-editor-btn');
            const codeShortcuts = document.getElementById('code-shortcuts');
            const closeShortcutsBtn = document.getElementById('close-shortcuts-btn');
            const codeShortcutBtns = document.querySelectorAll('.code-shortcut-btn');

            // Fonction pour ouvrir dans l'éditeur de code
            function openInCodeEditor(content) {
                // Extraire le code du contenu
                let code = content;

                // Si le contenu contient des blocs de code markdown, les extraire
                const codeBlockRegex = /```[\s\S]*?\n([\s\S]*?)```/g;
                const matches = content.match(codeBlockRegex);

                if (matches && matches.length > 0) {
                    // Prendre le premier bloc de code trouvé
                    code = matches[0].replace(/```[\s\S]*?\n/, '').replace(/```$/, '');
                }

                // Ouvrir l'éditeur de code avec le contenu
                window.open(`/code-editor.html?code=${encodeURIComponent(code)}`, '_blank');
            }

            // Événement pour le bouton d'éditeur de code
            codeEditorBtn.addEventListener('click', () => {
                codeShortcuts.style.display = codeShortcuts.style.display === 'none' ? 'block' : 'none';
            });

            // Événement pour fermer les raccourcis
            closeShortcutsBtn.addEventListener('click', () => {
                codeShortcuts.style.display = 'none';
            });

            // Événements pour les raccourcis de code
            codeShortcutBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const prompt = btn.dataset.prompt;

                    // Si c'est un raccourci d'analyse/debug/optimisation, demander le code
                    if (prompt.includes('code :')) {
                        const userCode = window.prompt('Collez votre code ici :');
                        if (userCode) {
                            chatInput.value = `${prompt}\n\`\`\`\n${userCode}\n\`\`\``;
                            codeShortcuts.style.display = 'none';
                            sendMessage();
                        }
                    } else {
                        // Pour les autres raccourcis, demander la description
                        const description = window.prompt('Que voulez-vous créer ?');
                        if (description) {
                            chatInput.value = `${prompt} ${description}`;
                            codeShortcuts.style.display = 'none';
                            sendMessage();
                        }
                    }
                });
            });

            // Améliorer la fonction addMessage pour détecter et formater le code
            const originalAddMessage = addMessage;
            addMessage = function(content, isUser = false) {
                // Formater le code dans le contenu
                let formattedContent = content;

                // Détecter les blocs de code markdown et les formater
                formattedContent = formattedContent.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
                    return `<pre><code class="language-${language || 'text'}">${code.trim()}</code></pre>`;
                });

                // Détecter le code inline
                formattedContent = formattedContent.replace(/`([^`]+)`/g, '<code>$1</code>');

                // Appeler la fonction originale
                const messageDiv = originalAddMessage(formattedContent, isUser);

                // Si le message contient du code formaté, mettre à jour le contenu HTML
                if (formattedContent !== content && !isUser) {
                    const contentDiv = messageDiv.querySelector('.message-content');
                    contentDiv.innerHTML = formattedContent;
                }

                return messageDiv;
            };

            // Charger les agents au démarrage
            await loadAgents();
        });
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
