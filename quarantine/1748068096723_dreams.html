<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON><PERSON></title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .dreams-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .dream-card {
            background: linear-gradient(135deg, rgba(25, 25, 50, 0.8), rgba(10, 10, 30, 0.9));
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(100, 100, 255, 0.2);
        }

        .dream-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
        }

        .dream-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(to right, #3498db, #9b59b6);
            border-radius: 5px 5px 0 0;
        }

        .dream-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #e0e0ff;
        }

        .dream-date {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 15px;
        }

        .dream-content {
            font-size: 14px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
        }

        .dream-connections {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .dream-connections-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .dream-connection {
            display: inline-block;
            background-color: rgba(100, 100, 255, 0.2);
            padding: 3px 8px;
            border-radius: 10px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .dream-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dream-action {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            transition: color 0.3s ease;
        }

        .dream-action:hover {
            color: #3498db;
        }

        .dream-action i {
            margin-right: 5px;
        }

        .dream-glow {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(100, 100, 255, 0.2) 0%, rgba(100, 100, 255, 0) 70%);
            pointer-events: none;
            z-index: 0;
        }

        .dream-empty {
            grid-column: 1 / -1;
            text-align: center;
            padding: 50px;
            color: rgba(255, 255, 255, 0.5);
        }

        .dream-empty i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .dream-empty-title {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .dream-empty-text {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .dream-generate-btn {
            background-color: var(--accent);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: inline-flex;
            align-items: center;
        }

        .dream-generate-btn:hover {
            background-color: var(--primary-light);
            transform: scale(1.05);
        }

        .dream-generate-btn i {
            margin-right: 8px;
            font-size: 16px;
        }

        .dream-loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .dream-loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--accent);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .dream-loading-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
        }

        .dream-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .dream-filter {
            display: flex;
            align-items: center;
        }

        .dream-filter-label {
            margin-right: 10px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .dream-filter-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
        }

        .dream-filter-select option {
            background-color: #1a1a2e;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams.html" class="nav-item active">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Rêves</h1>
                <p class="interface-subtitle">Visualisation des rêves générés par la mémoire thermique</p>
            </div>

            <div class="status-container">
                <button class="action-button" id="generate-dream-btn">
                    <i class="fas fa-cloud-moon"></i> Générer un rêve
                </button>
            </div>
        </div>

        <!-- Contrôles des rêves -->
        <div class="dream-controls">
            <div class="dream-filter">
                <span class="dream-filter-label">Filtrer par :</span>
                <select class="dream-filter-select" id="dream-filter">
                    <option value="all">Tous les rêves</option>
                    <option value="recent">Rêves récents</option>
                    <option value="connections">Rêves avec le plus de connexions</option>
                </select>
            </div>

            <div class="dream-count">
                <span id="dream-count">0</span> rêves trouvés
            </div>
        </div>

        <!-- Chargement des rêves -->
        <div class="dream-loading" id="dream-loading">
            <div class="dream-loading-spinner"></div>
            <div class="dream-loading-text">Génération d'un rêve en cours...</div>
        </div>

        <!-- Conteneur des rêves -->
        <div class="dreams-container" id="dreams-container">
            <!-- Les rêves seront ajoutés ici dynamiquement -->

            <!-- Message si aucun rêve -->
            <div class="dream-empty" id="dream-empty">
                <i class="fas fa-cloud-moon"></i>
                <div class="dream-empty-title">Aucun rêve trouvé</div>
                <div class="dream-empty-text">La mémoire des rêves est vide. Générez un rêve pour commencer.</div>
                <button class="dream-generate-btn" id="dream-empty-btn">
                    <i class="fas fa-cloud-moon"></i> Générer un rêve
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser la mémoire thermique et les accélérateurs
            await window.thermalMemory.initialize();
            await window.kyberAccelerators.initialize();

            // Éléments du DOM
            const dreamsContainer = document.getElementById('dreams-container');
            const dreamEmpty = document.getElementById('dream-empty');
            const dreamCount = document.getElementById('dream-count');
            const dreamLoading = document.getElementById('dream-loading');
            const generateDreamBtn = document.getElementById('generate-dream-btn');
            const dreamEmptyBtn = document.getElementById('dream-empty-btn');
            const dreamFilter = document.getElementById('dream-filter');

            // Charger les rêves
            loadDreams();

            // Fonction pour charger les rêves
            async function loadDreams() {
                try {
                    // Récupérer les rêves de la mémoire thermique
                    const dreams = await window.thermalMemory.getEntriesFromZone('dreamMemory');

                    // Mettre à jour le compteur
                    dreamCount.textContent = dreams.length;

                    // Afficher ou masquer le message vide
                    if (dreams.length === 0) {
                        dreamEmpty.style.display = 'block';
                    } else {
                        dreamEmpty.style.display = 'none';

                        // Vider le conteneur
                        dreamsContainer.innerHTML = '';

                        // Filtrer les rêves
                        let filteredDreams = dreams;
                        const filterValue = dreamFilter.value;

                        if (filterValue === 'recent') {
                            filteredDreams = dreams.sort((a, b) => b.created - a.created);
                        } else if (filterValue === 'connections') {
                            filteredDreams = dreams.sort((a, b) => {
                                const connectionsA = a.connections ? a.connections.length : 0;
                                const connectionsB = b.connections ? b.connections.length : 0;
                                return connectionsB - connectionsA;
                            });
                        }

                        // Afficher les rêves
                        filteredDreams.forEach(dream => {
                            dreamsContainer.appendChild(createDreamCard(dream));
                        });
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des rêves:', error);
                }
            }

            // Fonction pour créer une carte de rêve
            function createDreamCard(dream) {
                const card = document.createElement('div');
                card.className = 'dream-card';

                // Créer un effet de lueur aléatoire
                const glow = document.createElement('div');
                glow.className = 'dream-glow';
                glow.style.left = `${Math.random() * 80}%`;
                glow.style.top = `${Math.random() * 80}%`;
                card.appendChild(glow);

                // Titre du rêve
                const title = document.createElement('div');
                title.className = 'dream-title';
                title.textContent = dream.key || 'Rêve sans titre';
                card.appendChild(title);

                // Date du rêve
                const date = document.createElement('div');
                date.className = 'dream-date';
                date.textContent = new Date(dream.created).toLocaleString();
                card.appendChild(date);

                // Contenu du rêve
                const content = document.createElement('div');
                content.className = 'dream-content';
                content.textContent = typeof dream.data === 'string' ? dream.data : JSON.stringify(dream.data);
                card.appendChild(content);

                // Connexions du rêve
                if (dream.connections && dream.connections.length > 0) {
                    const connectionsContainer = document.createElement('div');
                    connectionsContainer.className = 'dream-connections';

                    const connectionsTitle = document.createElement('div');
                    connectionsTitle.className = 'dream-connections-title';
                    connectionsTitle.textContent = 'Connexions:';
                    connectionsContainer.appendChild(connectionsTitle);

                    dream.connections.forEach(connection => {
                        const connectionEl = document.createElement('span');
                        connectionEl.className = 'dream-connection';
                        connectionEl.textContent = connection;
                        connectionsContainer.appendChild(connectionEl);
                    });

                    card.appendChild(connectionsContainer);
                }

                // Actions du rêve
                const actions = document.createElement('div');
                actions.className = 'dream-actions';

                const viewAction = document.createElement('button');
                viewAction.className = 'dream-action';
                viewAction.innerHTML = '<i class="fas fa-eye"></i> Voir';
                viewAction.addEventListener('click', () => {
                    // Afficher les détails du rêve
                    alert(JSON.stringify(dream, null, 2));
                });
                actions.appendChild(viewAction);

                const deleteAction = document.createElement('button');
                deleteAction.className = 'dream-action';
                deleteAction.innerHTML = '<i class="fas fa-trash"></i> Supprimer';
                deleteAction.addEventListener('click', async () => {
                    // Supprimer le rêve
                    if (confirm('Êtes-vous sûr de vouloir supprimer ce rêve ?')) {
                        try {
                            await window.thermalMemory.remove(dream.id);
                            card.remove();
                            loadDreams();
                        } catch (error) {
                            console.error('Erreur lors de la suppression du rêve:', error);
                        }
                    }
                });
                actions.appendChild(deleteAction);

                card.appendChild(actions);

                return card;
            }

            // Fonction pour générer un rêve
            async function generateDream() {
                try {
                    // Afficher le chargement
                    dreamLoading.style.display = 'block';

                    // Générer un rêve
                    const dream = await fetch('/api/thermal/memory/dream', {
                        method: 'POST'
                    }).then(response => response.json());

                    // Masquer le chargement
                    dreamLoading.style.display = 'none';

                    // Recharger les rêves
                    loadDreams();

                    return dream;
                } catch (error) {
                    console.error('Erreur lors de la génération du rêve:', error);
                    dreamLoading.style.display = 'none';
                    return null;
                }
            }

            // Événements
            generateDreamBtn.addEventListener('click', generateDream);
            dreamEmptyBtn.addEventListener('click', generateDream);
            dreamFilter.addEventListener('change', loadDreams);
        });
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
