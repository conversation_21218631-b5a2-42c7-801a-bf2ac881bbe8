<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - G<PERSON><PERSON> Mémoire</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <!-- D3.js pour la visualisation -->
    <script src="https://d3js.org/d3.v7.min.js"></script>

    <style>
        .graph-container {
            width: 100%;
            height: 600px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            margin-bottom: 20px;
        }

        .graph-controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .graph-filter {
            display: flex;
            align-items: center;
        }

        .graph-filter-label {
            margin-right: 10px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .graph-filter-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
        }

        .graph-filter-select option {
            background-color: #1a1a2e;
        }

        .graph-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .node-tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 100;
            max-width: 300px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .node-tooltip-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            color: var(--accent);
        }

        .node-tooltip-content {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .node-tooltip-meta {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.5);
            display: flex;
            justify-content: space-between;
        }

        .graph-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            z-index: 50;
        }

        .graph-info-item {
            margin-bottom: 5px;
        }

        .graph-info-value {
            font-weight: bold;
            color: var(--accent);
        }

        .graph-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }

        .graph-loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--accent);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .graph-loading-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
        }

        .graph-empty {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: rgba(255, 255, 255, 0.5);
        }

        .graph-empty i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .graph-empty-title {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .graph-empty-text {
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
            max-width: 400px;
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Louna</span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat.html" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
            <a href="/dreams.html" class="nav-item">
                <i class="fas fa-cloud-moon"></i>
                <span>Rêves</span>
            </a>
            <a href="/memory-graph.html" class="nav-item active">
                <i class="fas fa-project-diagram"></i>
                <span>Graphe de Mémoire</span>
            </a>
            <a href="/performance.html" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>Performances</span>
            </a>
            <a href="/settings.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Graphe de Mémoire</h1>
                <p class="interface-subtitle">Visualisation des connexions entre les entrées de la mémoire thermique</p>
            </div>

            <div class="status-container">
                <button class="action-button" id="refresh-graph-btn">
                    <i class="fas fa-sync-alt"></i> Rafraîchir
                </button>
                <button class="action-button" id="cycle-memory-btn">
                    <i class="fas fa-recycle"></i> Cycle de Mémoire
                </button>
            </div>
        </div>

        <!-- Contrôles du graphe -->
        <div class="graph-controls">
            <div class="graph-filter">
                <span class="graph-filter-label">Afficher :</span>
                <select class="graph-filter-select" id="graph-filter">
                    <option value="all">Toutes les zones</option>
                    <option value="instant">Mémoire Instantanée</option>
                    <option value="shortTerm">Mémoire à Court Terme</option>
                    <option value="workingMemory">Mémoire de Travail</option>
                    <option value="mediumTerm">Mémoire à Moyen Terme</option>
                    <option value="longTerm">Mémoire à Long Terme</option>
                    <option value="dreamMemory">Mémoire des Rêves</option>
                </select>
            </div>

            <div class="graph-filter">
                <span class="graph-filter-label">Connexions :</span>
                <select class="graph-filter-select" id="connection-threshold">
                    <option value="0">Toutes les connexions</option>
                    <option value="0.1">Connexions faibles et plus</option>
                    <option value="0.3" selected>Connexions moyennes et plus</option>
                    <option value="0.5">Connexions fortes uniquement</option>
                </select>
            </div>
        </div>

        <!-- Légende du graphe -->
        <div class="graph-legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ff6b6b;"></div>
                <span>Mémoire Instantanée</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ff9e7d;"></div>
                <span>Mémoire à Court Terme</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffd166;"></div>
                <span>Mémoire de Travail</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #06d6a0;"></div>
                <span>Mémoire à Moyen Terme</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #118ab2;"></div>
                <span>Mémoire à Long Terme</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b5de5;"></div>
                <span>Mémoire des Rêves</span>
            </div>
        </div>

        <!-- Conteneur du graphe -->
        <div class="graph-container" id="graph-container">
            <!-- Le graphe sera généré ici -->

            <!-- Tooltip pour les nœuds -->
            <div class="node-tooltip" id="node-tooltip">
                <div class="node-tooltip-title" id="tooltip-title"></div>
                <div class="node-tooltip-content" id="tooltip-content"></div>
                <div class="node-tooltip-meta" id="tooltip-meta"></div>
            </div>

            <!-- Informations sur le graphe -->
            <div class="graph-info">
                <div class="graph-info-item">Nœuds: <span class="graph-info-value" id="node-count">0</span></div>
                <div class="graph-info-item">Liens: <span class="graph-info-value" id="link-count">0</span></div>
                <div class="graph-info-item">Température moyenne: <span class="graph-info-value" id="avg-temp">0</span></div>
            </div>

            <!-- Chargement du graphe -->
            <div class="graph-loading" id="graph-loading">
                <div class="graph-loading-spinner"></div>
                <div class="graph-loading-text">Chargement du graphe de mémoire...</div>
            </div>

            <!-- Message si aucune donnée -->
            <div class="graph-empty" id="graph-empty" style="display: none;">
                <i class="fas fa-project-diagram"></i>
                <div class="graph-empty-title">Aucune donnée à afficher</div>
                <div class="graph-empty-text">La mémoire thermique ne contient pas assez d'entrées pour générer un graphe. Ajoutez des entrées ou effectuez un cycle de mémoire.</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialiser la mémoire thermique et les accélérateurs
            await window.thermalMemory.initialize();
            await window.kyberAccelerators.initialize();

            // Éléments du DOM
            const graphContainer = document.getElementById('graph-container');
            const graphLoading = document.getElementById('graph-loading');
            const graphEmpty = document.getElementById('graph-empty');
            const nodeTooltip = document.getElementById('node-tooltip');
            const tooltipTitle = document.getElementById('tooltip-title');
            const tooltipContent = document.getElementById('tooltip-content');
            const tooltipMeta = document.getElementById('tooltip-meta');
            const nodeCount = document.getElementById('node-count');
            const linkCount = document.getElementById('link-count');
            const avgTemp = document.getElementById('avg-temp');
            const refreshGraphBtn = document.getElementById('refresh-graph-btn');
            const cycleMemoryBtn = document.getElementById('cycle-memory-btn');
            const graphFilter = document.getElementById('graph-filter');
            const connectionThreshold = document.getElementById('connection-threshold');

            // Variables pour le graphe
            let simulation;
            let svg;
            let width = graphContainer.clientWidth;
            let height = graphContainer.clientHeight;

            // Charger le graphe
            loadGraph();

            // Fonction pour charger le graphe
            async function loadGraph() {
                // Afficher le chargement
                graphLoading.style.display = 'flex';
                graphEmpty.style.display = 'none';

                try {
                    // Récupérer toutes les entrées de la mémoire thermique
                    const entries = await getAllEntries();

                    // Vérifier s'il y a des entrées
                    if (entries.length < 2) {
                        graphLoading.style.display = 'none';
                        graphEmpty.style.display = 'flex';
                        return;
                    }

                    // Créer les nœuds et les liens
                    const { nodes, links } = createGraphData(entries);

                    // Mettre à jour les informations
                    nodeCount.textContent = nodes.length;
                    linkCount.textContent = links.length;
                    avgTemp.textContent = (nodes.reduce((sum, node) => sum + node.temperature, 0) / nodes.length).toFixed(2);

                    // Créer le graphe
                    createGraph(nodes, links);

                    // Masquer le chargement
                    graphLoading.style.display = 'none';
                } catch (error) {
                    console.error('Erreur lors du chargement du graphe:', error);
                    graphLoading.style.display = 'none';
                    graphEmpty.style.display = 'flex';
                }
            }

            // Fonction pour récupérer toutes les entrées
            async function getAllEntries() {
                const filter = graphFilter.value;

                if (filter === 'all') {
                    // Récupérer toutes les entrées
                    return window.thermalMemory.getAllEntries();
                } else {
                    // Récupérer les entrées d'une zone spécifique
                    return window.thermalMemory.getEntriesFromZone(filter);
                }
            }

            // Fonction pour créer les données du graphe
            function createGraphData(entries) {
                // Créer les nœuds
                const nodes = entries.map(entry => ({
                    id: entry.id,
                    key: entry.key,
                    data: entry.data,
                    category: entry.category,
                    temperature: entry.temperature,
                    zone: getZoneFromTemperature(entry.temperature),
                    created: entry.created,
                    lastAccessed: entry.lastAccessed
                }));

                // Créer les liens
                const threshold = parseFloat(connectionThreshold.value);
                const links = [];

                // Trouver des connexions entre les entrées
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        // Calculer la similarité entre les entrées
                        const similarity = calculateSimilarity(nodes[i], nodes[j]);

                        // Ajouter le lien si la similarité est supérieure au seuil
                        if (similarity > threshold) {
                            links.push({
                                source: nodes[i].id,
                                target: nodes[j].id,
                                value: similarity
                            });
                        }
                    }
                }

                return { nodes, links };
            }

            // Fonction pour calculer la similarité entre deux entrées
            function calculateSimilarity(nodeA, nodeB) {
                // Extraire les mots-clés des entrées
                const keywordsA = extractKeywords(nodeA);
                const keywordsB = extractKeywords(nodeB);

                // Trouver les mots-clés communs
                const commonKeywords = keywordsA.filter(keyword => keywordsB.includes(keyword));

                // Calculer la similarité
                const similarity = commonKeywords.length / Math.max(keywordsA.length, keywordsB.length, 1);

                return similarity;
            }

            // Fonction pour extraire les mots-clés d'une entrée
            function extractKeywords(node) {
                // Extraire le texte de l'entrée
                let text = '';

                // Ajouter la clé
                if (node.key) {
                    text += node.key + ' ';
                }

                // Ajouter les données
                if (typeof node.data === 'string') {
                    text += node.data;
                } else if (typeof node.data === 'object' && node.data !== null) {
                    text += JSON.stringify(node.data);
                }

                // Convertir en minuscules
                text = text.toLowerCase();

                // Supprimer la ponctuation
                text = text.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ' ');

                // Diviser en mots
                const words = text.split(/\s+/);

                // Filtrer les mots courts et les mots vides
                const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'a', 'à', 'au', 'aux', 'avec', 'ce', 'ces', 'dans', 'en', 'entre', 'il', 'ils', 'je', 'tu', 'nous', 'vous', 'elle', 'elles', 'on', 'par', 'pas', 'pour', 'sur', 'the', 'of', 'and', 'to', 'in', 'is', 'that', 'it', 'with', 'for', 'as', 'was', 'on'];

                const keywords = words.filter(word =>
                    word.length > 3 && !stopWords.includes(word)
                );

                // Supprimer les doublons
                return [...new Set(keywords)];
            }

            // Fonction pour déterminer la zone à partir de la température
            function getZoneFromTemperature(temperature) {
                if (temperature >= 0.8) return 'instant';
                if (temperature >= 0.6) return 'shortTerm';
                if (temperature >= 0.4) return 'workingMemory';
                if (temperature >= 0.2) return 'mediumTerm';
                if (temperature >= 0.1) return 'longTerm';
                return 'dreamMemory';
            }

            // Fonction pour créer le graphe
            function createGraph(nodes, links) {
                // Supprimer le graphe existant
                d3.select('#graph-container svg').remove();

                // Créer le SVG
                svg = d3.select('#graph-container')
                    .append('svg')
                    .attr('width', width)
                    .attr('height', height);

                // Créer la simulation
                simulation = d3.forceSimulation(nodes)
                    .force('link', d3.forceLink(links).id(d => d.id).distance(100))
                    .force('charge', d3.forceManyBody().strength(-300))
                    .force('center', d3.forceCenter(width / 2, height / 2))
                    .force('collision', d3.forceCollide().radius(30));

                // Créer les liens
                const link = svg.append('g')
                    .attr('class', 'links')
                    .selectAll('line')
                    .data(links)
                    .enter()
                    .append('line')
                    .attr('stroke-width', d => Math.max(1, d.value * 5))
                    .attr('stroke', 'rgba(255, 255, 255, 0.2)');

                // Créer les nœuds
                const node = svg.append('g')
                    .attr('class', 'nodes')
                    .selectAll('circle')
                    .data(nodes)
                    .enter()
                    .append('circle')
                    .attr('r', d => 5 + d.temperature * 15)
                    .attr('fill', d => getColorForZone(d.zone))
                    .call(d3.drag()
                        .on('start', dragstarted)
                        .on('drag', dragged)
                        .on('end', dragended));

                // Ajouter les événements de survol
                node.on('mouseover', showTooltip)
                    .on('mouseout', hideTooltip);

                // Mettre à jour la simulation
                simulation.on('tick', () => {
                    link
                        .attr('x1', d => d.source.x)
                        .attr('y1', d => d.source.y)
                        .attr('x2', d => d.target.x)
                        .attr('y2', d => d.target.y);

                    node
                        .attr('cx', d => d.x = Math.max(20, Math.min(width - 20, d.x)))
                        .attr('cy', d => d.y = Math.max(20, Math.min(height - 20, d.y)));
                });

                // Fonction pour démarrer le glissement
                function dragstarted(event, d) {
                    if (!event.active) simulation.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }

                // Fonction pour glisser
                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                }

                // Fonction pour terminer le glissement
                function dragended(event, d) {
                    if (!event.active) simulation.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }

                // Fonction pour afficher le tooltip
                function showTooltip(event, d) {
                    // Mettre à jour le contenu du tooltip
                    tooltipTitle.textContent = d.key;

                    // Formater le contenu
                    let content = '';
                    if (typeof d.data === 'string') {
                        content = d.data.length > 100 ? d.data.substring(0, 100) + '...' : d.data;
                    } else if (typeof d.data === 'object' && d.data !== null) {
                        content = JSON.stringify(d.data).substring(0, 100) + '...';
                    }
                    tooltipContent.textContent = content;

                    // Formater les métadonnées
                    tooltipMeta.innerHTML = `
                        <span>Catégorie: ${d.category}</span>
                        <span>Température: ${d.temperature.toFixed(2)}</span>
                    `;

                    // Positionner le tooltip
                    const x = event.pageX - graphContainer.getBoundingClientRect().left;
                    const y = event.pageY - graphContainer.getBoundingClientRect().top;

                    nodeTooltip.style.left = `${x + 10}px`;
                    nodeTooltip.style.top = `${y + 10}px`;
                    nodeTooltip.style.opacity = 1;
                }

                // Fonction pour masquer le tooltip
                function hideTooltip() {
                    nodeTooltip.style.opacity = 0;
                }
            }

            // Fonction pour obtenir la couleur en fonction de la zone
            function getColorForZone(zone) {
                switch (zone) {
                    case 'instant': return '#ff6b6b';
                    case 'shortTerm': return '#ff9e7d';
                    case 'workingMemory': return '#ffd166';
                    case 'mediumTerm': return '#06d6a0';
                    case 'longTerm': return '#118ab2';
                    case 'dreamMemory': return '#9b5de5';
                    default: return '#cccccc';
                }
            }

            // Événements
            refreshGraphBtn.addEventListener('click', loadGraph);

            cycleMemoryBtn.addEventListener('click', async () => {
                // Afficher le chargement
                graphLoading.style.display = 'flex';

                try {
                    // Effectuer un cycle de mémoire
                    await fetch('/api/thermal/memory/cycle', {
                        method: 'POST'
                    });

                    // Recharger le graphe
                    loadGraph();
                } catch (error) {
                    console.error('Erreur lors du cycle de mémoire:', error);
                    graphLoading.style.display = 'none';
                }
            });

            graphFilter.addEventListener('change', loadGraph);
            connectionThreshold.addEventListener('change', loadGraph);

            // Redimensionner le graphe lors du redimensionnement de la fenêtre
            window.addEventListener('resize', () => {
                width = graphContainer.clientWidth;
                height = graphContainer.clientHeight;

                if (svg) {
                    svg.attr('width', width).attr('height', height);

                    if (simulation) {
                        simulation.force('center', d3.forceCenter(width / 2, height / 2));
                        simulation.alpha(0.3).restart();
                    }
                }
            });
        });
    </script>
  <script src="js/native-app.js"></script>
</body>
</html>
