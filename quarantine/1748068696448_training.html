<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formation des Agents - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/css/styles.css">
    <style>
        :root {
            --primary: #ff69b4;
            --primary-light: #ffb6c1;
            --primary-dark: #c71585;
            --secondary: #4b0082;
            --success: #00c853;
            --warning: #ffd600;
            --danger: #ff1744;
            --info: #00b0ff;
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --bg-card: #0f3460;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: rgba(255, 255, 255, 0.1);
            --header-bg: #0a1128;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 80px;
            background-color: var(--bg-secondary);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
            transition: width 0.3s ease;
            overflow: hidden;
        }

        .sidebar:hover {
            width: 200px;
        }

        .logo {
            font-size: 24px;
            margin-bottom: 30px;
            color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }

        .logo i {
            margin-right: 15px;
        }

        .logo span {
            display: none;
            white-space: nowrap;
        }

        .sidebar:hover .logo span {
            display: inline;
        }

        .nav-items {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            margin-bottom: 5px;
            width: 100%;
            box-sizing: border-box;
        }

        .nav-item i {
            font-size: 20px;
            min-width: 40px;
            text-align: center;
        }

        .nav-item span {
            display: none;
            white-space: nowrap;
            margin-left: 10px;
        }

        .sidebar:hover .nav-item span {
            display: inline;
        }

        .nav-item:hover, .nav-item.active {
            background-color: rgba(255, 105, 180, 0.2);
            color: var(--primary);
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
            color: var(--primary);
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-secondary {
            background-color: var(--secondary);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #3a0066;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background-color: rgba(255, 105, 180, 0.1);
        }

        .card {
            background-color: var(--bg-card);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 18px;
            margin: 0;
            color: var(--primary-light);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .dataset-card {
            background-color: var(--bg-secondary);
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid var(--border-color);
        }

        .dataset-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            border-color: var(--primary);
        }

        .dataset-card.selected {
            border: 2px solid var(--primary);
            background-color: rgba(255, 105, 180, 0.1);
        }

        .dataset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .dataset-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .dataset-samples {
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
        }

        .dataset-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 10px;
            height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .dataset-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .agent-selector {
            margin-bottom: 20px;
        }

        .agent-selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .agent-selector-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agent-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .agent-item {
            background-color: var(--bg-secondary);
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            width: calc(33.33% - 10px);
            box-sizing: border-box;
        }

        .agent-item:hover {
            border-color: var(--primary);
            background-color: rgba(255, 105, 180, 0.05);
        }

        .agent-item.selected {
            border: 2px solid var(--primary);
            background-color: rgba(255, 105, 180, 0.1);
        }

        .agent-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .agent-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .agent-name {
            font-size: 14px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .agent-model {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .training-options {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .training-progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            height: 10px;
            background-color: var(--bg-secondary);
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .training-status {
            margin-top: 10px;
            font-size: 14px;
            color: var(--text-primary);
        }

        .training-history {
            margin-top: 30px;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .history-info {
            flex: 1;
        }

        .history-agent {
            font-weight: bold;
            color: var(--text-primary);
        }

        .history-dataset {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .history-stats {
            display: flex;
            gap: 15px;
        }

        .history-stat {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .history-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .history-actions {
            margin-left: 10px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .history-item {
            cursor: pointer;
        }

        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            background-color: var(--bg-card);
            color: var(--text-primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }

        .notification.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .notification.success {
            border-left: 4px solid var(--success);
        }

        .notification.error {
            border-left: 4px solid var(--danger);
        }

        .notification.info {
            border-left: 4px solid var(--info);
        }

        .notification.warning {
            border-left: 4px solid var(--warning);
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                flex-direction: row;
                padding: 10px;
            }

            .sidebar:hover {
                width: 100%;
            }

            .logo {
                margin-bottom: 0;
                margin-right: 20px;
            }

            .nav-items {
                flex-direction: row;
                justify-content: center;
                flex-wrap: wrap;
            }

            .nav-item {
                padding: 8px 15px;
                margin-bottom: 0;
                width: auto;
            }

            .nav-item span {
                display: none;
            }

            .sidebar:hover .nav-item span {
                display: none;
            }

            .main-content {
                padding: 15px;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .agent-item {
                width: 100%;
            }
        }
    </style>
  <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="nav-items">
                <a href="/" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="nav-item">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/futuristic-interface.html" class="nav-item">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire Thermique</span>
                </a>
                <a href="/brain-visualization.html" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Visualisation 3D</span>
                </a>
                <a href="/qi-neuron-monitor.html" class="nav-item">
                    <i class="fas fa-yin-yang"></i>
                    <span>Monitoring Qi</span>
                </a>
                <a href="/kyber-dashboard.html" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Accélérateurs Kyber</span>
                </a>
                <a href="/memory-fusion.html" class="nav-item">
                    <i class="fas fa-code-branch"></i>
                    <span>Fusion Mémoire</span>
                </a>
                <a href="/performance.html" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>Performances</span>
                </a>
                <a href="/agents.html" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>Agents</span>
                </a>
                <a href="/training.html" class="nav-item active">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Formation</span>
                </a>
                <a href="/settings" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <h1>Formation des Agents</h1>
                <div class="header-actions">
                    <button id="refresh-btn" class="btn btn-outline">
                        <i class="fas fa-sync-alt"></i> Actualiser
                    </button>
                    <button id="create-dataset-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouvel Ensemble de Données
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Lancer une Formation</h2>
                </div>

                <div class="agent-selector">
                    <div class="agent-selector-header">
                        <div class="agent-selector-title">Sélectionner un Agent</div>
                    </div>
                    <div class="agent-list" id="agent-list">
                        <!-- Les agents seront ajoutés ici dynamiquement -->
                    </div>
                </div>

                <div class="card-header">
                    <h3 class="card-title">Ensembles de Données</h3>
                </div>
                <div class="grid" id="datasets-grid">
                    <!-- Les ensembles de données seront ajoutés ici dynamiquement -->
                </div>

                <div class="training-options" id="training-options">
                    <div class="card-header">
                        <h3 class="card-title">Options de Formation</h3>
                    </div>
                    <div class="form-group">
                        <label for="epochs">Nombre d'époques</label>
                        <input type="number" id="epochs" class="form-control" value="1" min="1" max="10">
                    </div>
                    <div class="form-group">
                        <label for="batch-size">Taille des lots</label>
                        <input type="number" id="batch-size" class="form-control" value="10" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label for="learning-rate">Taux d'apprentissage</label>
                        <input type="number" id="learning-rate" class="form-control" value="0.001" min="0.0001" max="0.1" step="0.0001">
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="use-memory" checked>
                            <label for="use-memory">Utiliser la mémoire thermique pour le contexte</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="save-to-memory" checked>
                            <label for="save-to-memory">Sauvegarder les résultats dans la mémoire thermique</label>
                        </div>
                    </div>
                    <button id="start-training-btn" class="btn btn-primary" disabled>
                        <i class="fas fa-play"></i> Lancer la Formation
                    </button>
                </div>

                <div class="training-progress" id="training-progress">
                    <div class="card-header">
                        <h3 class="card-title">Progression de la Formation</h3>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-info">
                        <div id="progress-percentage">0%</div>
                        <div id="progress-samples">0/0 échantillons</div>
                    </div>
                    <div class="training-status" id="training-status">
                        En attente...
                    </div>
                    <button id="cancel-training-btn" class="btn btn-danger">
                        <i class="fas fa-stop"></i> Annuler la Formation
                    </button>
                </div>
            </div>

            <div class="card training-history">
                <div class="card-header">
                    <h2 class="card-title">Historique des Formations</h2>
                </div>
                <div id="history-list">
                    <!-- L'historique des formations sera ajouté ici dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification">
        <div id="notification-message"></div>
    </div>

    <!-- Scripts -->
    <script src="/js/thermal-memory.js"></script>
    <script src="/js/kyber-accelerators.js"></script>
    <script src="/js/agents.js"></script>
    <script src="/js/training.js"></script>
  <script src="js/native-app.js"></script>
</body>
</html>
