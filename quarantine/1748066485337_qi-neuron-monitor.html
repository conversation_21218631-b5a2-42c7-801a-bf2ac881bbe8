<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Qi & Neurones - Louna</title>
    <link rel="stylesheet" href="css/native-app.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="top-navbar">
        <div class="logo-container">
            <i class="fas fa-brain"></i>
            <span class="logo-text">Lou<PERSON></span>
        </div>

        <div class="nav-links">
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                <span>Accueil</span>
            </a>
            <a href="/chat" class="nav-item">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="/futuristic-interface.html" class="nav-item">
                <i class="fas fa-fire"></i>
                <span>Mémoire Thermique</span>
            </a>
            <a href="/brain-visualization.html" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>Visualisation 3D</span>
            </a>
            <a href="/qi-neuron-monitor.html" class="nav-item active">
                <i class="fas fa-yin-yang"></i>
                <span>Monitoring Qi</span>
            </a>
            <a href="/ltx-video.html" class="nav-item">
                <i class="fas fa-video"></i>
                <span>LTX Video</span>
            </a>
            <a href="/code-editor.html" class="nav-item">
                <i class="fas fa-code"></i>
                <span>Éditeur de Code</span>
            </a>
            <a href="/code-extensions.html" class="nav-item">
                <i class="fas fa-puzzle-piece"></i>
                <span>Extensions</span>
            </a>
            <a href="/generation-studio.html" class="nav-item">
                <i class="fas fa-magic"></i>
                <span>Studio de Génération</span>
            </a>
            <a href="/kyber-dashboard.html" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>Accélérateurs Kyber</span>
            </a>
        </div>

        <div class="nav-right">
            <a href="#" class="nav-item" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                <span>Actualiser</span>
            </a>
            <a href="#" class="nav-item" id="help-btn">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
        </div>
    </nav>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- En-tête de l'interface -->
        <div class="interface-header">
            <div>
                <h1 class="interface-title">Monitoring Qi & Neurones</h1>
                <p class="interface-subtitle">Évolution en temps réel de l'énergie vitale et de l'activité neuronale</p>
            </div>

            <div class="status-container">
                <span class="status-label">État:</span>
                <div class="status-indicator active" id="qi-status-indicator"></div>
                <span class="status-text" id="qi-status-text">Monitoring actif</span>

                <a href="/brain-visualization.html" class="action-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
                <a href="/futuristic-interface.html" class="action-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
            </div>
        </div>

        <!-- Grille de métriques -->
        <div class="metrics-grid">
            <!-- Carte QI -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-yin-yang"></i> Système QI</h3>
                    <div class="metric-status active" id="qi-system-status">Actif</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">QI Actuel</span>
                        <span class="metric-value" id="current-qi">1000</span>
                        <span class="metric-unit">points</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Niveau Cognitif</span>
                            <span class="value" id="cognitive-level">1</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Points XP</span>
                            <span class="value" id="experience-points">0</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Bonus Apprentissage</span>
                            <span class="value" id="learning-bonus">0%</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="qi-progress" style="width: 50%"></div>
                        </div>
                        <span class="progress-text">Progression vers niveau suivant</span>
                    </div>
                </div>
            </div>

            <!-- Carte Neurones -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-brain"></i> Réseau Neuronal</h3>
                    <div class="metric-status active" id="neuron-system-status">Actif</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">Neurones Totaux</span>
                        <span class="metric-value" id="total-neurons">71</span>
                        <span class="metric-unit">neurones</span>
                    </div>
                    <div class="secondary-metrics">
                        <div class="secondary-metric">
                            <span class="label">Neurones Actifs</span>
                            <span class="value" id="active-neurons">45</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Efficacité</span>
                            <span class="value" id="neuron-efficiency">85%</span>
                        </div>
                        <div class="secondary-metric">
                            <span class="label">Santé</span>
                            <span class="value" id="neuron-health">92%</span>
                        </div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="neuron-progress" style="width: 85%"></div>
                        </div>
                        <span class="progress-text">Activité neuronale globale</span>
                    </div>
                </div>
            </div>

            <!-- Carte Réseaux -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-network-wired"></i> Réseaux Spécialisés</h3>
                    <div class="metric-status active">6 Réseaux</div>
                </div>
                <div class="metric-content">
                    <div class="network-list">
                        <div class="network-item">
                            <span class="network-name">Sensoriel</span>
                            <span class="network-count" id="sensory-count">12</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Mémoire de Travail</span>
                            <span class="network-count" id="working-count">8</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Long Terme</span>
                            <span class="network-count" id="longterm-count">15</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Émotionnel</span>
                            <span class="network-count" id="emotional-count">10</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Exécutif</span>
                            <span class="network-count" id="executive-count">9</span>
                        </div>
                        <div class="network-item">
                            <span class="network-name">Créatif</span>
                            <span class="network-count" id="creative-count">7</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte Émotions -->
            <div class="metric-card">
                <div class="metric-header">
                    <h3><i class="fas fa-heart"></i> État Émotionnel</h3>
                    <div class="metric-status active" id="emotional-status">Curieux</div>
                </div>
                <div class="metric-content">
                    <div class="primary-metric">
                        <span class="metric-label">Humeur</span>
                        <span class="metric-value" id="current-mood">Curieux</span>
                        <span class="metric-unit" id="mood-intensity">100%</span>
                    </div>
                    <div class="emotion-grid">
                        <div class="emotion-item">
                            <span class="emotion-name">Bonheur</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="happiness-bar" style="width: 68%"></div>
                            </div>
                            <span class="emotion-value" id="happiness-value">68%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Curiosité</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="curiosity-bar" style="width: 100%"></div>
                            </div>
                            <span class="emotion-value" id="curiosity-value">100%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Confiance</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="confidence-bar" style="width: 60%"></div>
                            </div>
                            <span class="emotion-value" id="confidence-value">60%</span>
                        </div>
                        <div class="emotion-item">
                            <span class="emotion-name">Énergie</span>
                            <div class="emotion-bar">
                                <div class="emotion-fill" id="energy-bar" style="width: 75%"></div>
                            </div>
                            <span class="emotion-value" id="energy-value">75%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphique temps réel -->
        <div class="chart-section">
            <div class="chart-header">
                <h3><i class="fas fa-chart-line"></i> Évolution Temps Réel</h3>
                <div class="chart-controls">
                    <button class="control-btn" id="pause-chart">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="control-btn" id="reset-chart">
                        <i class="fas fa-redo"></i> Reset
                    </button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="monitoring-chart" width="800" height="300"></canvas>
            </div>
            <div class="chart-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>QI</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>Neurones Actifs</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>Efficacité</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96ceb4;"></div>
                    <span>Bonheur</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isMonitoring = false;
        let updateInterval = null;
        let chartData = {
            qi: [],
            neurons: [],
            timestamps: []
        };
        let maxDataPoints = 50;

        // Éléments DOM
        const elements = {
            status: document.getElementById('monitoring-status'),
            statusText: document.getElementById('status-text'),
            lastUpdate: document.getElementById('last-update'),
            historyCount: document.getElementById('history-count'),

            // Qi
            qiLevel: document.getElementById('qi-level'),
            qiProgress: document.getElementById('qi-progress'),
            qiFlow: document.getElementById('qi-flow'),
            qiFlowValue: document.getElementById('qi-flow-value'),
            qiStability: document.getElementById('qi-stability'),
            qiHarmony: document.getElementById('qi-harmony'),
            qiVitality: document.getElementById('qi-vitality'),

            // Neurones
            neuronActive: document.getElementById('neuron-active'),
            neuronDensity: document.getElementById('neuron-density'),
            neuronDensityProgress: document.getElementById('neuron-density-progress'),
            neuronStrength: document.getElementById('neuron-strength'),
            neuronActivity: document.getElementById('neuron-activity'),
            neuronPlasticity: document.getElementById('neuron-plasticity'),

            // Contrôles
            startBtn: document.getElementById('start-btn'),
            stopBtn: document.getElementById('stop-btn'),
            resetBtn: document.getElementById('reset-btn'),

            // Graphique
            chartCanvas: document.getElementById('chart-canvas')
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initialisation du monitoring Qi/Neurones...');

            // Configurer les boutons
            elements.startBtn.addEventListener('click', startMonitoring);
            elements.stopBtn.addEventListener('click', stopMonitoring);
            elements.resetBtn.addEventListener('click', resetData);

            // Initialiser le canvas
            initChart();

            // Charger l'état initial
            loadCurrentState();

            console.log('Monitoring Qi/Neurones initialisé');
        });

        // Fonctions principales
        async function loadCurrentState() {
            try {
                const response = await fetch('/api/qi-neuron/current');
                const data = await response.json();

                if (data.success) {
                    updateUI(data.state);
                    updateStatus('Connecté', true);
                } else {
                    console.error('Erreur lors du chargement de l\'état:', data.error);
                    updateStatus('Erreur', false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateStatus('Déconnecté', false);
            }
        }

        async function startMonitoring() {
            try {
                // Démarrer le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/start', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    isMonitoring = true;
                    updateStatus('Actif', true);

                    // Démarrer les mises à jour périodiques
                    updateInterval = setInterval(loadCurrentState, 2000);

                    // Mettre à jour les boutons
                    elements.startBtn.disabled = true;
                    elements.stopBtn.disabled = false;

                    console.log('Monitoring démarré');
                } else {
                    console.error('Erreur lors du démarrage:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors du démarrage:', error);
            }
        }

        async function stopMonitoring() {
            try {
                // Arrêter le monitoring côté serveur
                const response = await fetch('/api/qi-neuron/stop', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    isMonitoring = false;
                    updateStatus('Arrêté', false);

                    // Arrêter les mises à jour
                    if (updateInterval) {
                        clearInterval(updateInterval);
                        updateInterval = null;
                    }

                    // Mettre à jour les boutons
                    elements.startBtn.disabled = false;
                    elements.stopBtn.disabled = true;

                    console.log('Monitoring arrêté');
                } else {
                    console.error('Erreur lors de l\'arrêt:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'arrêt:', error);
            }
        }

        function resetData() {
            chartData = {
                qi: [],
                neurons: [],
                timestamps: []
            };

            elements.historyCount.textContent = '0';
            drawChart();

            console.log('Données réinitialisées');
        }

        function updateUI(state) {
            // Mettre à jour les métriques Qi
            if (state.qi) {
                const qi = state.qi;
                elements.qiLevel.textContent = qi.level.toFixed(3);
                elements.qiProgress.style.width = (qi.level * 100) + '%';
                elements.qiFlowValue.textContent = qi.flow.toFixed(3);
                elements.qiStability.textContent = qi.stability.toFixed(3);
                elements.qiHarmony.textContent = qi.harmony.toFixed(3);
                elements.qiVitality.textContent = qi.vitality.toFixed(3);

                // Indicateur de flux
                if (qi.flow > 0.001) {
                    elements.qiFlow.textContent = '↗️';
                    elements.qiFlow.className = 'flow-indicator flow-up';
                } else if (qi.flow < -0.001) {
                    elements.qiFlow.textContent = '↘️';
                    elements.qiFlow.className = 'flow-indicator flow-down';
                } else {
                    elements.qiFlow.textContent = '➡️';
                    elements.qiFlow.className = 'flow-indicator flow-stable';
                }
            }

            // Mettre à jour les métriques neuronales
            if (state.neurons) {
                const neurons = state.neurons;
                elements.neuronActive.textContent = neurons.activeConnections;
                elements.neuronDensity.textContent = neurons.networkDensity.toFixed(3);
                elements.neuronDensityProgress.style.width = (neurons.networkDensity * 100) + '%';
                elements.neuronStrength.textContent = neurons.synapticStrength.toFixed(3);
                elements.neuronActivity.textContent = neurons.neuralActivity.toFixed(3);
                elements.neuronPlasticity.textContent = neurons.plasticity.toFixed(3);
            }

            // Ajouter aux données du graphique
            if (state.qi && state.neurons) {
                addToChart(state.qi.level, state.neurons.networkDensity);
            }

            // Mettre à jour l'heure
            elements.lastUpdate.textContent = new Date().toLocaleTimeString();
        }

        function updateStatus(text, isActive) {
            elements.statusText.textContent = text;

            if (isActive) {
                elements.status.classList.add('status-active');
                elements.status.classList.add('pulse');
            } else {
                elements.status.classList.remove('status-active');
                elements.status.classList.remove('pulse');
            }
        }

        function initChart() {
            const canvas = elements.chartCanvas;
            const ctx = canvas.getContext('2d');

            // Configurer le canvas
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawChart();
        }

        function addToChart(qiValue, neuronValue) {
            const now = new Date();

            chartData.qi.push(qiValue);
            chartData.neurons.push(neuronValue);
            chartData.timestamps.push(now);

            // Limiter le nombre de points
            if (chartData.qi.length > maxDataPoints) {
                chartData.qi.shift();
                chartData.neurons.shift();
                chartData.timestamps.shift();
            }

            elements.historyCount.textContent = chartData.qi.length;
            drawChart();
        }

        function drawChart() {
            const canvas = elements.chartCanvas;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Effacer le canvas
            ctx.clearRect(0, 0, width, height);

            if (chartData.qi.length === 0) return;

            // Dessiner la grille
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Lignes horizontales
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Lignes verticales
            for (let i = 0; i <= 10; i++) {
                const x = (width / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Dessiner les courbes
            drawLine(ctx, chartData.qi, '#ff6b6b', width, height);
            drawLine(ctx, chartData.neurons, '#4ecdc4', width, height);

            // Légende
            ctx.font = '12px Arial';
            ctx.fillStyle = '#ff6b6b';
            ctx.fillText('Qi', 10, 20);
            ctx.fillStyle = '#4ecdc4';
            ctx.fillText('Neurones', 10, 35);
        }

        function drawLine(ctx, data, color, width, height) {
            if (data.length < 2) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < data.length; i++) {
                const x = (width / (maxDataPoints - 1)) * i;
                const y = height - (data[i] * height);

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        // Redimensionner le canvas quand la fenêtre change de taille
        window.addEventListener('resize', function() {
            setTimeout(initChart, 100);
        });
    </script>
    </div>
    </div>
  <script src="js/native-app.js"></script>
</body>
</html>
