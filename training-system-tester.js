/**
 * SYSTÈME DE TEST COMPLET POUR LA FORMATION DES AGENTS
 * Teste et valide le système de formation entre l'agent formateur et l'agent principal
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class TrainingSystemTester {
    constructor(options = {}) {
        this.baseURL = options.baseURL || 'http://localhost:3000';
        this.testResults = [];
        this.startTime = Date.now();

        this.config = {
            debug: options.debug || true,
            saveResults: options.saveResults !== false,
            timeout: options.timeout || 30000
        };

        console.log('🧪 SYSTÈME DE TEST DE FORMATION INITIALISÉ');
        console.log(`🌐 URL de base: ${this.baseURL}`);
        console.log(`⏱️ Timeout: ${this.config.timeout}ms`);
    }

    /**
     * Lance tous les tests du système de formation
     */
    async runCompleteTest() {
        console.log('\n🚀 DÉMARRAGE DES TESTS COMPLETS DU SYSTÈME DE FORMATION');
        console.log('='.repeat(70));
        console.log(`🕐 Heure de début: ${new Date().toLocaleString()}`);

        try {
            // Phase 1: Tests de connectivité
            await this.testConnectivity();

            // Phase 2: Tests des APIs de formation
            await this.testTrainingAPIs();

            // Phase 3: Tests des agents
            await this.testAgents();

            // Phase 4: Tests des datasets
            await this.testDatasets();

            // Phase 5: Tests de formation réelle
            await this.testActualTraining();

            // Phase 6: Tests de l'agent DeepSeek
            await this.testDeepSeekAgent();

            // Phase 7: Tests d'intégration
            await this.testIntegration();

            // Génération du rapport final
            this.generateFinalReport();

        } catch (error) {
            console.error('❌ Erreur lors des tests:', error.message);
            this.logResult('ERREUR_GLOBALE', false, error.message);
        }
    }

    /**
     * Phase 1: Tests de connectivité
     */
    async testConnectivity() {
        console.log('\n📡 PHASE 1: TESTS DE CONNECTIVITÉ');
        console.log('-'.repeat(50));

        const endpoints = [
            '/api/training/state',
            '/api/training/agents',
            '/api/training/datasets',
            '/api/training/history',
            '/api/agents/list',
            '/api/features/status'
        ];

        for (const endpoint of endpoints) {
            await this.testEndpoint(endpoint);
        }
    }

    /**
     * Phase 2: Tests des APIs de formation
     */
    async testTrainingAPIs() {
        console.log('\n🔧 PHASE 2: TESTS DES APIs DE FORMATION');
        console.log('-'.repeat(50));

        // Test de l'état de formation
        await this.testTrainingState();

        // Test de récupération des agents
        await this.testGetAgents();

        // Test de récupération des datasets
        await this.testGetDatasets();

        // Test de l'historique
        await this.testGetHistory();
    }

    /**
     * Phase 3: Tests des agents
     */
    async testAgents() {
        console.log('\n🤖 PHASE 3: TESTS DES AGENTS');
        console.log('-'.repeat(50));

        // Test de l'agent principal
        await this.testMainAgent();

        // Test de l'agent DeepSeek
        await this.testDeepSeekAvailability();

        // Test de communication entre agents
        await this.testAgentCommunication();
    }

    /**
     * Phase 4: Tests des datasets
     */
    async testDatasets() {
        console.log('\n📊 PHASE 4: TESTS DES DATASETS');
        console.log('-'.repeat(50));

        // Créer un dataset de test
        await this.createTestDataset();

        // Valider le dataset
        await this.validateTestDataset();
    }

    /**
     * Phase 5: Tests de formation réelle
     */
    async testActualTraining() {
        console.log('\n🎓 PHASE 5: TESTS DE FORMATION RÉELLE');
        console.log('-'.repeat(50));

        // Test de lancement de formation
        await this.testStartTraining();

        // Test de monitoring de formation
        await this.testTrainingMonitoring();

        // Test d'arrêt de formation
        await this.testStopTraining();
    }

    /**
     * Phase 6: Tests de l'agent DeepSeek
     */
    async testDeepSeekAgent() {
        console.log('\n🧠 PHASE 6: TESTS DE L\'AGENT DEEPSEEK');
        console.log('-'.repeat(50));

        // Test d'activation
        await this.testDeepSeekActivation();

        // Test de formation avec DeepSeek
        await this.testDeepSeekTraining();

        // Test d'évaluation avec DeepSeek
        await this.testDeepSeekEvaluation();
    }

    /**
     * Phase 7: Tests d'intégration
     */
    async testIntegration() {
        console.log('\n🔗 PHASE 7: TESTS D\'INTÉGRATION');
        console.log('-'.repeat(50));

        // Test d'intégration complète
        await this.testFullIntegration();

        // Test de performance
        await this.testPerformance();
    }

    /**
     * Test d'un endpoint
     */
    async testEndpoint(endpoint) {
        console.log(`🔍 Test endpoint: ${endpoint}`);

        try {
            const response = await axios.get(`${this.baseURL}${endpoint}`, {
                timeout: this.config.timeout
            });

            if (response.status === 200) {
                console.log(`✅ ${endpoint} - OK`);
                this.logResult(`ENDPOINT_${endpoint}`, true, response.data);
            } else {
                console.log(`⚠️ ${endpoint} - Status ${response.status}`);
                this.logResult(`ENDPOINT_${endpoint}`, false, `Status ${response.status}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint} - Erreur: ${error.message}`);
            this.logResult(`ENDPOINT_${endpoint}`, false, error.message);
        }
    }

    /**
     * Test de l'état de formation
     */
    async testTrainingState() {
        console.log('🔍 Test état de formation...');

        try {
            const response = await axios.get(`${this.baseURL}/api/training/state`);

            if (response.data.success !== undefined) {
                console.log('✅ État de formation accessible');
                console.log(`📊 Formation en cours: ${response.data.isTraining || false}`);
                this.logResult('TRAINING_STATE', true, response.data);
            } else {
                console.log('⚠️ Format de réponse inattendu');
                this.logResult('TRAINING_STATE', false, 'Format inattendu');
            }
        } catch (error) {
            console.log(`❌ Erreur état formation: ${error.message}`);
            this.logResult('TRAINING_STATE', false, error.message);
        }
    }

    /**
     * Test de récupération des agents
     */
    async testGetAgents() {
        console.log('🔍 Test récupération agents...');

        try {
            const response = await axios.get(`${this.baseURL}/api/training/agents`);

            if (response.data.success && response.data.agents) {
                console.log(`✅ ${response.data.agents.length} agents trouvés`);
                this.logResult('GET_AGENTS', true, response.data);
            } else {
                console.log('⚠️ Aucun agent trouvé ou erreur');
                this.logResult('GET_AGENTS', false, 'Aucun agent');
            }
        } catch (error) {
            console.log(`❌ Erreur récupération agents: ${error.message}`);
            this.logResult('GET_AGENTS', false, error.message);
        }
    }

    /**
     * Test de récupération des datasets
     */
    async testGetDatasets() {
        console.log('🔍 Test récupération datasets...');

        try {
            const response = await axios.get(`${this.baseURL}/api/training/datasets`);

            if (response.data.success && response.data.datasets) {
                console.log(`✅ ${response.data.datasets.length} datasets trouvés`);
                this.logResult('GET_DATASETS', true, response.data);
            } else {
                console.log('⚠️ Aucun dataset trouvé ou erreur');
                this.logResult('GET_DATASETS', false, 'Aucun dataset');
            }
        } catch (error) {
            console.log(`❌ Erreur récupération datasets: ${error.message}`);
            this.logResult('GET_DATASETS', false, error.message);
        }
    }

    /**
     * Test de récupération de l'historique
     */
    async testGetHistory() {
        console.log('🔍 Test récupération historique...');

        try {
            const response = await axios.get(`${this.baseURL}/api/training/history`);

            if (response.data.success !== undefined) {
                console.log(`✅ Historique accessible`);
                this.logResult('GET_HISTORY', true, response.data);
            } else {
                console.log('⚠️ Erreur accès historique');
                this.logResult('GET_HISTORY', false, 'Erreur accès');
            }
        } catch (error) {
            console.log(`❌ Erreur récupération historique: ${error.message}`);
            this.logResult('GET_HISTORY', false, error.message);
        }
    }

    /**
     * Test de l'agent principal
     */
    async testMainAgent() {
        console.log('🔍 Test agent principal...');

        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Test de fonctionnement de l\'agent principal',
                userId: 'test_training_system'
            });

            if (response.data.success && response.data.response) {
                console.log('✅ Agent principal répond correctement');
                this.logResult('MAIN_AGENT', true, response.data);
            } else {
                console.log('⚠️ Agent principal ne répond pas correctement');
                this.logResult('MAIN_AGENT', false, 'Pas de réponse');
            }
        } catch (error) {
            console.log(`❌ Erreur agent principal: ${error.message}`);
            this.logResult('MAIN_AGENT', false, error.message);
        }
    }

    /**
     * Test de disponibilité de DeepSeek
     */
    async testDeepSeekAvailability() {
        console.log('🔍 Test disponibilité DeepSeek...');

        try {
            // Vérifier si DeepSeek est disponible globalement
            const response = await axios.get(`${this.baseURL}/api/agents/status`);

            if (response.data.success) {
                console.log('✅ DeepSeek semble disponible');
                this.logResult('DEEPSEEK_AVAILABILITY', true, response.data);
            } else {
                console.log('⚠️ DeepSeek non disponible');
                this.logResult('DEEPSEEK_AVAILABILITY', false, 'Non disponible');
            }
        } catch (error) {
            console.log(`❌ Erreur vérification DeepSeek: ${error.message}`);
            this.logResult('DEEPSEEK_AVAILABILITY', false, error.message);
        }
    }

    /**
     * Créer un dataset de test
     */
    async createTestDataset() {
        console.log('🔍 Création dataset de test...');

        const testDataset = {
            id: `test_dataset_${Date.now()}`,
            name: 'Dataset de Test Formation',
            description: 'Dataset créé automatiquement pour tester le système de formation',
            data: [
                {
                    input: 'Quelle est la capitale de la France ?',
                    expectedOutput: 'La capitale de la France est Paris.'
                },
                {
                    input: 'Comment calculer 2 + 2 ?',
                    expectedOutput: '2 + 2 = 4'
                },
                {
                    input: 'Qu\'est-ce que l\'intelligence artificielle ?',
                    expectedOutput: 'L\'intelligence artificielle est une technologie qui permet aux machines de simuler l\'intelligence humaine.'
                }
            ],
            createdAt: new Date().toISOString()
        };

        try {
            // Sauvegarder le dataset
            const datasetPath = path.join(__dirname, 'data', 'training', 'datasets', `${testDataset.id}.json`);

            // Créer le dossier s'il n'existe pas
            const datasetDir = path.dirname(datasetPath);
            if (!fs.existsSync(datasetDir)) {
                fs.mkdirSync(datasetDir, { recursive: true });
            }

            fs.writeFileSync(datasetPath, JSON.stringify(testDataset, null, 2));

            console.log('✅ Dataset de test créé');
            this.logResult('CREATE_TEST_DATASET', true, testDataset);

            // Stocker l'ID pour les tests suivants
            this.testDatasetId = testDataset.id;

        } catch (error) {
            console.log(`❌ Erreur création dataset: ${error.message}`);
            this.logResult('CREATE_TEST_DATASET', false, error.message);
        }
    }

    /**
     * Valider le dataset de test
     */
    async validateTestDataset() {
        console.log('🔍 Validation dataset de test...');

        if (!this.testDatasetId) {
            console.log('⚠️ Aucun dataset de test à valider');
            this.logResult('VALIDATE_TEST_DATASET', false, 'Pas de dataset');
            return;
        }

        try {
            const response = await axios.get(`${this.baseURL}/api/training/datasets`);

            if (response.data.success && response.data.datasets) {
                const foundDataset = response.data.datasets.find(d => d.id === this.testDatasetId);

                if (foundDataset) {
                    console.log('✅ Dataset de test validé');
                    this.logResult('VALIDATE_TEST_DATASET', true, foundDataset);
                } else {
                    console.log('⚠️ Dataset de test non trouvé');
                    this.logResult('VALIDATE_TEST_DATASET', false, 'Dataset non trouvé');
                }
            } else {
                console.log('⚠️ Erreur récupération datasets');
                this.logResult('VALIDATE_TEST_DATASET', false, 'Erreur API');
            }
        } catch (error) {
            console.log(`❌ Erreur validation dataset: ${error.message}`);
            this.logResult('VALIDATE_TEST_DATASET', false, error.message);
        }
    }

    /**
     * Test de lancement de formation
     */
    async testStartTraining() {
        console.log('🔍 Test lancement formation...');

        if (!this.testDatasetId) {
            console.log('⚠️ Pas de dataset pour tester la formation');
            this.logResult('START_TRAINING', false, 'Pas de dataset');
            return;
        }

        try {
            // Récupérer les agents disponibles
            const agentsResponse = await axios.get(`${this.baseURL}/api/training/agents`);

            if (!agentsResponse.data.success || !agentsResponse.data.agents.length) {
                console.log('⚠️ Aucun agent disponible pour la formation');
                this.logResult('START_TRAINING', false, 'Aucun agent');
                return;
            }

            const agent = agentsResponse.data.agents[0];

            const trainingRequest = {
                agentId: agent.id,
                trainingAgentId: 'agent_training', // Agent de formation
                datasetId: this.testDatasetId,
                options: {
                    epochs: 1,
                    batchSize: 2,
                    learningRate: 0.001
                }
            };

            const response = await axios.post(`${this.baseURL}/api/training/train`, trainingRequest);

            if (response.data.success) {
                console.log('✅ Formation lancée avec succès');
                this.logResult('START_TRAINING', true, response.data);
            } else {
                console.log(`⚠️ Erreur lancement formation: ${response.data.error}`);
                this.logResult('START_TRAINING', false, response.data.error);
            }
        } catch (error) {
            console.log(`❌ Erreur test formation: ${error.message}`);
            this.logResult('START_TRAINING', false, error.message);
        }
    }

    /**
     * Test de monitoring de formation
     */
    async testTrainingMonitoring() {
        console.log('🔍 Test monitoring formation...');

        try {
            const response = await axios.get(`${this.baseURL}/api/training/state`);

            if (response.data.success !== undefined) {
                console.log('✅ Monitoring formation accessible');
                console.log(`📊 État: ${response.data.isTraining ? 'En cours' : 'Arrêtée'}`);
                this.logResult('TRAINING_MONITORING', true, response.data);
            } else {
                console.log('⚠️ Erreur monitoring formation');
                this.logResult('TRAINING_MONITORING', false, 'Erreur API');
            }
        } catch (error) {
            console.log(`❌ Erreur monitoring: ${error.message}`);
            this.logResult('TRAINING_MONITORING', false, error.message);
        }
    }

    /**
     * Test d'arrêt de formation
     */
    async testStopTraining() {
        console.log('🔍 Test arrêt formation...');

        try {
            const response = await axios.post(`${this.baseURL}/api/training/cancel`);

            if (response.data.success !== undefined) {
                console.log('✅ Arrêt formation accessible');
                this.logResult('STOP_TRAINING', true, response.data);
            } else {
                console.log('⚠️ Erreur arrêt formation');
                this.logResult('STOP_TRAINING', false, 'Erreur API');
            }
        } catch (error) {
            console.log(`❌ Erreur arrêt formation: ${error.message}`);
            this.logResult('STOP_TRAINING', false, error.message);
        }
    }

    /**
     * Test de communication entre agents
     */
    async testAgentCommunication() {
        console.log('🔍 Test communication entre agents...');

        try {
            // Test de communication avec l'agent cognitif
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Peux-tu me former sur un sujet spécifique ?',
                userId: 'test_training_system',
                useDeepSeek: true
            });

            if (response.data.success && response.data.response) {
                console.log('✅ Communication entre agents fonctionne');
                this.logResult('AGENT_COMMUNICATION', true, response.data);
            } else {
                console.log('⚠️ Problème communication agents');
                this.logResult('AGENT_COMMUNICATION', false, 'Pas de réponse');
            }
        } catch (error) {
            console.log(`❌ Erreur communication agents: ${error.message}`);
            this.logResult('AGENT_COMMUNICATION', false, error.message);
        }
    }

    /**
     * Test d'activation DeepSeek
     */
    async testDeepSeekActivation() {
        console.log('🔍 Test activation DeepSeek...');

        try {
            // Tester l'activation via l'API cognitive
            const response = await axios.post(`${this.baseURL}/api/cognitive/activate-deepseek`, {});

            if (response.data.success) {
                console.log('✅ DeepSeek activé avec succès');
                this.logResult('DEEPSEEK_ACTIVATION', true, response.data);
            } else {
                console.log('⚠️ Erreur activation DeepSeek');
                this.logResult('DEEPSEEK_ACTIVATION', false, response.data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.log(`❌ Erreur activation DeepSeek: ${error.message}`);
            this.logResult('DEEPSEEK_ACTIVATION', false, error.message);
        }
    }

    /**
     * Test de formation avec DeepSeek
     */
    async testDeepSeekTraining() {
        console.log('🔍 Test formation avec DeepSeek...');

        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Démarrer une formation cognitive avancée',
                userId: 'test_training_system',
                useDeepSeek: true
            });

            if (response.data.success && response.data.response) {
                const responseText = response.data.response.toLowerCase();

                if (responseText.includes('formation') || responseText.includes('apprentissage')) {
                    console.log('✅ Formation DeepSeek fonctionne');
                    this.logResult('DEEPSEEK_TRAINING', true, response.data);
                } else {
                    console.log('⚠️ Réponse DeepSeek non pertinente');
                    this.logResult('DEEPSEEK_TRAINING', false, 'Réponse non pertinente');
                }
            } else {
                console.log('⚠️ Pas de réponse DeepSeek');
                this.logResult('DEEPSEEK_TRAINING', false, 'Pas de réponse');
            }
        } catch (error) {
            console.log(`❌ Erreur formation DeepSeek: ${error.message}`);
            this.logResult('DEEPSEEK_TRAINING', false, error.message);
        }
    }

    /**
     * Test d'évaluation avec DeepSeek
     */
    async testDeepSeekEvaluation() {
        console.log('🔍 Test évaluation avec DeepSeek...');

        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Évaluer mes capacités cognitives actuelles',
                userId: 'test_training_system',
                useDeepSeek: true
            });

            if (response.data.success && response.data.response) {
                const responseText = response.data.response.toLowerCase();

                if (responseText.includes('évaluation') || responseText.includes('capacités') || responseText.includes('performance')) {
                    console.log('✅ Évaluation DeepSeek fonctionne');
                    this.logResult('DEEPSEEK_EVALUATION', true, response.data);
                } else {
                    console.log('⚠️ Évaluation DeepSeek non pertinente');
                    this.logResult('DEEPSEEK_EVALUATION', false, 'Réponse non pertinente');
                }
            } else {
                console.log('⚠️ Pas de réponse évaluation DeepSeek');
                this.logResult('DEEPSEEK_EVALUATION', false, 'Pas de réponse');
            }
        } catch (error) {
            console.log(`❌ Erreur évaluation DeepSeek: ${error.message}`);
            this.logResult('DEEPSEEK_EVALUATION', false, error.message);
        }
    }

    /**
     * Test d'intégration complète
     */
    async testFullIntegration() {
        console.log('🔍 Test intégration complète...');

        try {
            // Scénario complet : formation + évaluation
            console.log('   📚 Étape 1: Demande de formation...');
            const trainingResponse = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Je veux améliorer mes capacités de raisonnement logique',
                userId: 'test_integration'
            });

            if (!trainingResponse.data.success) {
                throw new Error('Échec étape formation');
            }

            console.log('   📊 Étape 2: Évaluation des progrès...');
            const evaluationResponse = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: 'Évaluer mes progrès en raisonnement logique',
                userId: 'test_integration'
            });

            if (!evaluationResponse.data.success) {
                throw new Error('Échec étape évaluation');
            }

            console.log('✅ Intégration complète réussie');
            this.logResult('FULL_INTEGRATION', true, {
                training: trainingResponse.data,
                evaluation: evaluationResponse.data
            });

        } catch (error) {
            console.log(`❌ Erreur intégration: ${error.message}`);
            this.logResult('FULL_INTEGRATION', false, error.message);
        }
    }

    /**
     * Test de performance
     */
    async testPerformance() {
        console.log('🔍 Test performance système...');

        const startTime = Date.now();
        let successfulRequests = 0;
        const totalRequests = 5;

        try {
            // Faire plusieurs requêtes en parallèle
            const promises = [];

            for (let i = 0; i < totalRequests; i++) {
                promises.push(
                    axios.post(`${this.baseURL}/api/chat/message`, {
                        message: `Test performance ${i + 1}`,
                        userId: 'test_performance'
                    }).then(() => {
                        successfulRequests++;
                    }).catch(() => {
                        // Ignorer les erreurs pour le test de performance
                    })
                );
            }

            await Promise.all(promises);

            const endTime = Date.now();
            const duration = endTime - startTime;
            const avgResponseTime = duration / totalRequests;

            console.log(`✅ Performance: ${successfulRequests}/${totalRequests} requêtes réussies`);
            console.log(`⏱️ Temps moyen: ${avgResponseTime.toFixed(2)}ms`);

            this.logResult('PERFORMANCE', true, {
                successfulRequests,
                totalRequests,
                duration,
                avgResponseTime
            });

        } catch (error) {
            console.log(`❌ Erreur test performance: ${error.message}`);
            this.logResult('PERFORMANCE', false, error.message);
        }
    }

    /**
     * Enregistrer un résultat de test
     */
    logResult(testName, success, data) {
        this.testResults.push({
            testName,
            success,
            data,
            timestamp: new Date().toISOString(),
            duration: Date.now() - this.startTime
        });
    }

    /**
     * Générer le rapport final
     */
    generateFinalReport() {
        console.log('\n📊 RAPPORT FINAL DES TESTS');
        console.log('='.repeat(70));

        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;
        const successRate = totalTests > 0 ? Math.round((successfulTests / totalTests) * 100) : 0;

        console.log(`📈 Tests réussis: ${successfulTests}/${totalTests} (${successRate}%)`);
        console.log(`❌ Tests échoués: ${failedTests}`);
        console.log(`⏱️ Durée totale: ${Math.round((Date.now() - this.startTime) / 1000)}s`);

        // Détail des échecs
        const failures = this.testResults.filter(r => !r.success);
        if (failures.length > 0) {
            console.log('\n❌ TESTS ÉCHOUÉS:');
            failures.forEach(failure => {
                console.log(`   • ${failure.testName}: ${failure.data}`);
            });
        }

        // Sauvegarder le rapport
        if (this.config.saveResults) {
            this.saveReport();
        }

        console.log('\n🎉 TESTS TERMINÉS !');
    }

    /**
     * Sauvegarder le rapport
     */
    saveReport() {
        try {
            const reportPath = path.join(__dirname, 'data', 'training', 'test-reports', `training_test_${Date.now()}.json`);

            // Créer le dossier s'il n'existe pas
            const reportDir = path.dirname(reportPath);
            if (!fs.existsSync(reportDir)) {
                fs.mkdirSync(reportDir, { recursive: true });
            }

            const report = {
                timestamp: new Date().toISOString(),
                duration: Date.now() - this.startTime,
                totalTests: this.testResults.length,
                successfulTests: this.testResults.filter(r => r.success).length,
                results: this.testResults
            };

            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            console.log(`💾 Rapport sauvegardé: ${reportPath}`);

        } catch (error) {
            console.log(`❌ Erreur sauvegarde rapport: ${error.message}`);
        }
    }
}

module.exports = TrainingSystemTester;

// Exécution directe si le script est lancé
if (require.main === module) {
    const tester = new TrainingSystemTester({
        debug: true,
        saveResults: true
    });

    tester.runCompleteTest().catch(console.error);
}
