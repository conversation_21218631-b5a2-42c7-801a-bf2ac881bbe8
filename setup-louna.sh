#!/bin/bash

# Script pour configurer l'application Louna
# Ce script crée les répertoires nécessaires, copie les fichiers et démarre le serveur

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
ROUTES_DIR="$APP_DIR/routes"
VIEWS_DIR="$APP_DIR/views"
LAYOUTS_DIR="$APP_DIR/views/layouts"
CSS_DIR="$APP_DIR/public/css"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo " ██████╗ ██████╗ ███╗   ██╗███████╗██╗ ██████╗ ██╗   ██╗██████╗  █████╗ ████████╗██╗ ██████╗ ███╗   ██╗"
echo "██╔════╝██╔═══██╗████╗  ██║██╔════╝██║██╔════╝ ██║   ██║██╔══██╗██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║"
echo "██║     ██║   ██║██╔██╗ ██║█████╗  ██║██║  ███╗██║   ██║██████╔╝███████║   ██║   ██║██║   ██║██╔██╗ ██║"
echo "██║     ██║   ██║██║╚██╗██║██╔══╝  ██║██║   ██║██║   ██║██╔══██╗██╔══██║   ██║   ██║██║   ██║██║╚██╗██║"
echo "╚██████╗╚██████╔╝██║ ╚████║██║     ██║╚██████╔╝╚██████╔╝██║  ██║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║"
echo " ╚═════╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝     ╚═╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝"
echo -e "${NC}"
echo -e "${CYAN}Configuration de l'application Louna${NC}"
echo ""
print_message "Début de la configuration de l'application Louna..."
sleep 1

# Étape 1 : Créer les répertoires nécessaires
print_message "Étape 1 : Création des répertoires nécessaires..."

mkdir -p "$ROUTES_DIR"
mkdir -p "$LAYOUTS_DIR"
mkdir -p "$CSS_DIR"

print_success "Répertoires créés."

# Étape 2 : Copier les fichiers de routes
print_message "Étape 2 : Copie des fichiers de routes..."

cp "index.js" "$ROUTES_DIR/index.js"
cp "luna.js" "$ROUTES_DIR/luna.js"
cp "thermal-memory.js" "$ROUTES_DIR/thermal-memory.js"
cp "louna-dashboard.js" "$ROUTES_DIR/louna-dashboard.js"

print_success "Fichiers de routes copiés."

# Étape 3 : Copier les fichiers de vues
print_message "Étape 3 : Copie des fichiers de vues..."

cp "error.ejs" "$VIEWS_DIR/error.ejs"
cp "thermal-memory.ejs" "$VIEWS_DIR/thermal-memory.ejs"
cp "louna-dashboard.ejs" "$VIEWS_DIR/louna-dashboard.ejs"
cp "layout.ejs" "$LAYOUTS_DIR/layout.ejs"
cp "louna-template.ejs" "$LAYOUTS_DIR/louna-template.ejs"

print_success "Fichiers de vues copiés."

# Étape 4 : Copier les fichiers CSS
print_message "Étape 4 : Copie des fichiers CSS..."

cp "louna-unified.css" "$CSS_DIR/louna-unified.css"

print_success "Fichiers CSS copiés."

# Étape 5 : Créer un fichier server.js minimal
print_message "Étape 5 : Création d'un fichier server.js minimal..."

cat > "$APP_DIR/server.js" << 'EOL'
const express = require('express');
const path = require('path');
const expressLayouts = require('express-ejs-layouts');

// Initialiser l'application Express
const app = express();

// Configuration de l'application
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(expressLayouts);
app.set('layout', 'layouts/layout');
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
const indexRouter = require('./routes/index');
const lunaRouter = require('./routes/luna');
const thermalMemoryRouter = require('./routes/thermal-memory');
const lounaDashboardRouter = require('./routes/louna-dashboard');

app.use('/', indexRouter);
app.use('/luna', lunaRouter);
app.use('/luna/thermal-memory', thermalMemoryRouter);
app.use('/louna', lounaDashboardRouter);

// Rediriger /luna vers /louna
app.get('/luna', (req, res) => {
  res.redirect('/louna');
});

app.get('/luna/:path', (req, res) => {
  res.redirect(`/louna/${req.params.path}`);
});

app.get('/luna/:path/:subpath', (req, res) => {
  res.redirect(`/louna/${req.params.path}/${req.params.subpath}`);
});

// Middleware de gestion des erreurs
app.use((req, res, next) => {
  res.status(404).render('error', {
    title: 'Erreur 404',
    message: 'Page non trouvée',
    error: { status: 404, stack: '' }
  });
});

app.use((err, req, res, next) => {
  res.status(err.status || 500).render('error', {
    title: `Erreur ${err.status || 500}`,
    message: err.message,
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

// Démarrer le serveur
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`);
});

// Exporter l'application
module.exports = app;
EOL

print_success "Fichier server.js créé."

# Étape 6 : Installer les dépendances
print_message "Étape 6 : Installation des dépendances..."

cd "$APP_DIR" || exit
npm install express express-ejs-layouts ejs

print_success "Dépendances installées."

# Étape 7 : Démarrer le serveur
print_message "Étape 7 : Démarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/louna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/louna"

print_success "Configuration de l'application Louna terminée !"
print_message "Toutes les interfaces ont maintenant le même aspect visuel et sont bien connectées les unes aux autres."
