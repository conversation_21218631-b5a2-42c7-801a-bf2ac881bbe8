/**
 * Système de monitoring du Qi et des neurones
 *
 * Ce module surveille en temps réel l'évolution du Qi (énergie vitale du système)
 * et l'activité neuronale (connexions entre les entrées de mémoire).
 */

const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');
const { promisify } = require('util');

// Promisify des fonctions fs
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, 'data/monitoring');
const QI_HISTORY_FILE = path.join(DATA_DIR, 'qi_history.json');
const NEURON_HISTORY_FILE = path.join(DATA_DIR, 'neuron_history.json');

/**
 * Classe QiNeuronMonitor - Surveille l'évolution du Qi et des neurones
 */
class QiNeuronMonitor extends EventEmitter {
  /**
   * Initialise le système de monitoring
   * @param {Object} config - Configuration du monitoring
   */
  constructor(config = {}) {
    super();

    // Configuration par défaut
    this.config = {
      updateInterval: config.updateInterval || 2000, // Mise à jour toutes les 2 secondes
      historyLength: config.historyLength || 1000, // Garder 1000 points d'historique
      qiCalculationMethod: config.qiCalculationMethod || 'harmonic', // 'harmonic', 'weighted', 'exponential'
      neuronThreshold: config.neuronThreshold || 0.3, // Seuil pour considérer une connexion neuronale active
      debug: config.debug || false,
      ...config
    };

    // État actuel du système
    this.currentState = {
      qi: {
        level: 0.5, // Niveau de Qi actuel (0-1)
        flow: 0, // Flux de Qi (-1 à 1, négatif = décroissant, positif = croissant)
        stability: 0.5, // Stabilité du Qi (0-1)
        harmony: 0.5, // Harmonie du système (0-1)
        vitality: 0.5, // Vitalité générale (0-1)
        timestamp: Date.now()
      },
      neurons: {
        activeConnections: 0, // Nombre de connexions neuronales actives
        totalConnections: 0, // Nombre total de connexions possibles
        networkDensity: 0, // Densité du réseau neuronal (0-1)
        synapticStrength: 0, // Force synaptique moyenne (0-1)
        neuralActivity: 0, // Activité neuronale globale (0-1)
        plasticity: 0.5, // Plasticité neuronale (capacité d'adaptation) (0-1)
        timestamp: Date.now()
      }
    };

    // Historique des données
    this.history = {
      qi: [],
      neurons: []
    };

    // Références aux systèmes externes
    this.thermalMemory = null;
    this.systemTemperature = null;
    this.learningSystem = null;

    // Timer de mise à jour
    this.updateTimer = null;
    this.isRunning = false;

    // Initialiser le dossier de données
    this.initDataDir();

    // Charger l'historique existant
    this.loadHistory();

    console.log('Système de monitoring Qi/Neurones initialisé');
  }

  /**
   * Initialise le dossier de données
   */
  async initDataDir() {
    try {
      if (!fs.existsSync(DATA_DIR)) {
        await promisify(fs.mkdir)(DATA_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du dossier de monitoring:', error);
    }
  }

  /**
   * Charge l'historique depuis les fichiers
   */
  async loadHistory() {
    try {
      // Charger l'historique du Qi
      if (fs.existsSync(QI_HISTORY_FILE)) {
        const qiData = await readFileAsync(QI_HISTORY_FILE, 'utf8');
        this.history.qi = JSON.parse(qiData);
      }

      // Charger l'historique des neurones
      if (fs.existsSync(NEURON_HISTORY_FILE)) {
        const neuronData = await readFileAsync(NEURON_HISTORY_FILE, 'utf8');
        this.history.neurons = JSON.parse(neuronData);
      }

      console.log(`Historique chargé: ${this.history.qi.length} points Qi, ${this.history.neurons.length} points neurones`);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
    }
  }

  /**
   * Sauvegarde l'historique dans les fichiers
   */
  async saveHistory() {
    try {
      // Sauvegarder l'historique du Qi
      await writeFileAsync(QI_HISTORY_FILE, JSON.stringify(this.history.qi, null, 2), 'utf8');

      // Sauvegarder l'historique des neurones
      await writeFileAsync(NEURON_HISTORY_FILE, JSON.stringify(this.history.neurons, null, 2), 'utf8');

      if (this.config.debug) {
        console.log('Historique sauvegardé');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'historique:', error);
    }
  }

  /**
   * Connecte le monitoring aux systèmes externes
   * @param {Object} systems - Systèmes à surveiller
   */
  connectSystems(systems) {
    this.thermalMemory = systems.thermalMemory;
    this.systemTemperature = systems.systemTemperature;
    this.learningSystem = systems.learningSystem;

    console.log('Systèmes connectés au monitoring');
  }

  /**
   * Démarre le monitoring en temps réel
   */
  start() {
    if (this.isRunning) {
      console.log('Le monitoring est déjà en cours');
      return;
    }

    this.isRunning = true;

    // Première mise à jour immédiate
    this.updateMetrics();

    // Planifier les mises à jour périodiques
    this.updateTimer = setInterval(() => {
      this.updateMetrics();
    }, this.config.updateInterval);

    console.log(`Monitoring démarré (mise à jour toutes les ${this.config.updateInterval}ms)`);
    this.emit('started');
  }

  /**
   * Arrête le monitoring
   */
  stop() {
    if (!this.isRunning) {
      console.log('Le monitoring n\'est pas en cours');
      return;
    }

    this.isRunning = false;

    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    // Sauvegarder l'historique avant l'arrêt
    this.saveHistory();

    console.log('Monitoring arrêté');
    this.emit('stopped');
  }

  /**
   * Met à jour toutes les métriques
   */
  updateMetrics() {
    try {
      // Mettre à jour le Qi
      this.updateQiMetrics();

      // Mettre à jour les neurones
      this.updateNeuronMetrics();

      // Ajouter à l'historique
      this.addToHistory();

      // Émettre un événement de mise à jour
      this.emit('updated', {
        qi: this.currentState.qi,
        neurons: this.currentState.neurons
      });

      if (this.config.debug) {
        // UTILISER LE QI RÉEL DE JEAN-LUC PASSAVE (203) - PAS DE CONVERSION !
        let realQI = 203; // QI RÉEL de Jean-Luc Passave

        // Si le système d'évaluation du QI est disponible, utiliser sa valeur
        if (global.realQIEvaluation) {
          const qiData = global.realQIEvaluation.getCurrentQI();
          realQI = qiData.qi; // QI RÉEL (203)
        }

        console.log(`Qi: ${realQI}, Neurones: ${this.currentState.neurons.activeConnections}`);
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour des métriques:', error);
    }
  }

  /**
   * Met à jour les métriques du Qi
   */
  updateQiMetrics() {
    const previousQi = this.currentState.qi.level;
    const timestamp = Date.now();

    // Calculer le niveau de Qi en fonction de différents facteurs
    let qiLevel = 0;

    if (this.thermalMemory) {
      // Facteur 1: Température du système (équilibre thermique)
      const memoryStats = this.thermalMemory.getMemoryStats();
      const systemTemp = memoryStats.systemTemperatures ? memoryStats.systemTemperatures.normalized : 0.5;
      const tempBalance = 1 - Math.abs(systemTemp - 0.5) * 2; // Optimal à 0.5

      // Facteur 2: Distribution de la mémoire (harmonie des zones)
      const totalEntries = memoryStats.totalMemories || 1;

      // Distribution idéale: [5%, 15%, 20%, 30%, 25%, 5%]
      const idealDistribution = [0.05, 0.15, 0.20, 0.30, 0.25, 0.05];
      const actualDistribution = [
        (memoryStats.zone1Count || 0) / totalEntries,
        (memoryStats.zone2Count || 0) / totalEntries,
        (memoryStats.zone3Count || 0) / totalEntries,
        (memoryStats.zone4Count || 0) / totalEntries,
        (memoryStats.zone5Count || 0) / totalEntries,
        (memoryStats.zone6Count || 0) / totalEntries
      ];

      // Calculer la distance de la distribution idéale
      let distributionHarmony = 0;
      for (let i = 0; i < 6; i++) {
        distributionHarmony += Math.abs(actualDistribution[i] - idealDistribution[i]);
      }
      distributionHarmony = Math.max(0, 1 - distributionHarmony);

      // Facteur 3: Activité du système d'apprentissage
      let learningActivity = 0.5;
      if (this.learningSystem) {
        const performanceMetrics = this.learningSystem.getPerformanceMetrics();
        learningActivity = performanceMetrics.overallPerformance || 0.5;
      }

      // Facteur 4: Stabilité des températures
      const tempStability = this.calculateTemperatureStability();

      // Calculer le Qi selon la méthode choisie
      switch (this.config.qiCalculationMethod) {
        case 'harmonic':
          // Moyenne harmonique (privilégie les valeurs équilibrées)
          qiLevel = 4 / (1/tempBalance + 1/distributionHarmony + 1/learningActivity + 1/tempStability);
          break;
        case 'weighted':
          // Moyenne pondérée
          qiLevel = (tempBalance * 0.3) + (distributionHarmony * 0.3) + (learningActivity * 0.2) + (tempStability * 0.2);
          break;
        case 'exponential':
          // Moyenne exponentielle (privilégie les valeurs élevées)
          qiLevel = Math.pow(tempBalance * distributionHarmony * learningActivity * tempStability, 0.25);
          break;
        default:
          qiLevel = (tempBalance + distributionHarmony + learningActivity + tempStability) / 4;
      }

      // Calculer les autres métriques du Qi
      this.currentState.qi.flow = qiLevel - previousQi;
      this.currentState.qi.stability = tempStability;
      this.currentState.qi.harmony = distributionHarmony;
      this.currentState.qi.vitality = Math.min(1, (tempBalance + learningActivity) / 2);
    } else {
      // Valeurs par défaut si les systèmes ne sont pas connectés
      qiLevel = 0.5 + (Math.random() - 0.5) * 0.1;
      this.currentState.qi.flow = qiLevel - previousQi;
      this.currentState.qi.stability = 0.5;
      this.currentState.qi.harmony = 0.5;
      this.currentState.qi.vitality = 0.5;
    }

    // Limiter le Qi entre 0 et 1
    this.currentState.qi.level = Math.max(0, Math.min(1, qiLevel));
    this.currentState.qi.timestamp = timestamp;
  }

  /**
   * Met à jour les métriques des neurones
   */
  updateNeuronMetrics() {
    const timestamp = Date.now();

    if (this.thermalMemory) {
      // Récupérer toutes les entrées de mémoire
      const allEntries = this.thermalMemory.getAllEntries();

      // Calculer les connexions neuronales
      let activeConnections = 0;
      let totalConnections = 0;
      let totalSynapticStrength = 0;
      let connectionCount = 0;

      // Analyser les connexions entre les entrées
      for (let i = 0; i < allEntries.length; i++) {
        for (let j = i + 1; j < allEntries.length; j++) {
          totalConnections++;

          const entry1 = allEntries[i];
          const entry2 = allEntries[j];

          // Calculer la force de connexion basée sur la similarité
          const connectionStrength = this.calculateConnectionStrength(entry1, entry2);

          if (connectionStrength > this.config.neuronThreshold) {
            activeConnections++;
            totalSynapticStrength += connectionStrength;
            connectionCount++;
          }
        }
      }

      // Calculer les métriques neuronales
      this.currentState.neurons.activeConnections = activeConnections;
      this.currentState.neurons.totalConnections = totalConnections;
      this.currentState.neurons.networkDensity = totalConnections > 0 ? activeConnections / totalConnections : 0;
      this.currentState.neurons.synapticStrength = connectionCount > 0 ? totalSynapticStrength / connectionCount : 0;

      // Activité neuronale basée sur la densité et la force synaptique
      this.currentState.neurons.neuralActivity = (this.currentState.neurons.networkDensity + this.currentState.neurons.synapticStrength) / 2;

      // Plasticité basée sur l'activité récente et la diversité des connexions
      this.currentState.neurons.plasticity = this.calculateNeuralPlasticity(allEntries);
    } else {
      // Valeurs par défaut si le système n'est pas connecté
      this.currentState.neurons.activeConnections = Math.floor(Math.random() * 10);
      this.currentState.neurons.totalConnections = 50;
      this.currentState.neurons.networkDensity = this.currentState.neurons.activeConnections / this.currentState.neurons.totalConnections;
      this.currentState.neurons.synapticStrength = Math.random();
      this.currentState.neurons.neuralActivity = Math.random();
      this.currentState.neurons.plasticity = 0.5;
    }

    this.currentState.neurons.timestamp = timestamp;
  }

  /**
   * Calcule la stabilité des températures
   * @returns {number} - Stabilité (0-1)
   */
  calculateTemperatureStability() {
    if (!this.thermalMemory) {
      return 0.5;
    }

    // Pour l'instant, utilisons une valeur basée sur la température actuelle
    const memoryStats = this.thermalMemory.getMemoryStats();
    const systemTemp = memoryStats.systemTemperatures ? memoryStats.systemTemperatures.normalized : 0.5;

    // Stabilité basée sur la proximité de la température optimale (0.5)
    const distanceFromOptimal = Math.abs(systemTemp - 0.5);
    return Math.max(0, 1 - (distanceFromOptimal * 2));
  }

  /**
   * Calcule la force de connexion entre deux entrées
   * @param {Object} entry1 - Première entrée
   * @param {Object} entry2 - Deuxième entrée
   * @returns {number} - Force de connexion (0-1)
   */
  calculateConnectionStrength(entry1, entry2) {
    let strength = 0;

    // Facteur 1: Similarité des mots-clés
    if (entry1.metadata && entry1.metadata.keywords && entry2.metadata && entry2.metadata.keywords) {
      const keywords1 = new Set(entry1.metadata.keywords);
      const keywords2 = new Set(entry2.metadata.keywords);
      const intersection = new Set([...keywords1].filter(x => keywords2.has(x)));
      const union = new Set([...keywords1, ...keywords2]);

      if (union.size > 0) {
        strength += (intersection.size / union.size) * 0.4;
      }
    }

    // Facteur 2: Proximité temporelle
    const timeDiff = Math.abs(entry1.created - entry2.created);
    const maxTimeDiff = 24 * 60 * 60 * 1000; // 24 heures
    const temporalProximity = Math.max(0, 1 - (timeDiff / maxTimeDiff));
    strength += temporalProximity * 0.2;

    // Facteur 3: Similarité d'importance
    const importanceDiff = Math.abs((entry1.importance || 0.5) - (entry2.importance || 0.5));
    const importanceSimilarity = 1 - importanceDiff;
    strength += importanceSimilarity * 0.2;

    // Facteur 4: Même catégorie
    if (entry1.category && entry2.category && entry1.category === entry2.category) {
      strength += 0.2;
    }

    return Math.min(1, strength);
  }

  /**
   * Calcule la plasticité neuronale
   * @param {Array} entries - Entrées de mémoire
   * @returns {number} - Plasticité (0-1)
   */
  calculateNeuralPlasticity(entries) {
    if (entries.length === 0) {
      return 0.5;
    }

    // Facteur 1: Diversité des catégories
    const categories = new Set(entries.map(e => e.category).filter(c => c));
    const categoryDiversity = Math.min(1, categories.size / 10); // Normaliser sur 10 catégories max

    // Facteur 2: Activité récente (entrées créées dans les dernières 24h)
    const now = Date.now();
    const recentEntries = entries.filter(e => (now - e.created) < 24 * 60 * 60 * 1000);
    const recentActivity = Math.min(1, recentEntries.length / entries.length);

    // Facteur 3: Variation des niveaux d'importance
    const importances = entries.map(e => e.importance || 0.5);
    const avgImportance = importances.reduce((sum, imp) => sum + imp, 0) / importances.length;
    const importanceVariance = importances.reduce((sum, imp) => sum + Math.pow(imp - avgImportance, 2), 0) / importances.length;
    const importanceDiversity = Math.min(1, importanceVariance * 4); // Normaliser

    // Calculer la plasticité
    return (categoryDiversity * 0.4) + (recentActivity * 0.4) + (importanceDiversity * 0.2);
  }

  /**
   * Ajoute les métriques actuelles à l'historique
   */
  addToHistory() {
    // Ajouter le Qi à l'historique
    this.history.qi.push({ ...this.currentState.qi });

    // Ajouter les neurones à l'historique
    this.history.neurons.push({ ...this.currentState.neurons });

    // Limiter la taille de l'historique
    if (this.history.qi.length > this.config.historyLength) {
      this.history.qi.shift();
    }

    if (this.history.neurons.length > this.config.historyLength) {
      this.history.neurons.shift();
    }

    // Sauvegarder périodiquement (toutes les 10 mises à jour)
    if (this.history.qi.length % 10 === 0) {
      this.saveHistory();
    }
  }

  /**
   * Obtient le QI RÉEL de Jean-Luc Passave (203)
   * @returns {number} - QI RÉEL (203)
   */
  getRealQI() {
    // UTILISER LE QI RÉEL DE JEAN-LUC PASSAVE (203)
    if (global.realQIEvaluation) {
      const qiData = global.realQIEvaluation.getCurrentQI();
      return qiData.qi; // QI RÉEL (203)
    }

    // Fallback : QI de Jean-Luc Passave
    return 203;
  }

  /**
   * Récupère l'état actuel
   * @returns {Object} - État actuel du Qi et des neurones
   */
  getCurrentState() {
    return {
      qi: {
        ...this.currentState.qi,
        realQI: this.getRealQI() // Ajouter le QI RÉEL
      },
      neurons: { ...this.currentState.neurons }
    };
  }

  /**
   * Récupère l'historique
   * @param {number} limit - Nombre maximum d'entrées à récupérer
   * @returns {Object} - Historique du Qi et des neurones
   */
  getHistory(limit = 100) {
    return {
      qi: this.history.qi.slice(-limit),
      neurons: this.history.neurons.slice(-limit)
    };
  }

  /**
   * Récupère les statistiques globales
   * @returns {Object} - Statistiques globales
   */
  getStats() {
    const qiHistory = this.history.qi;
    const neuronHistory = this.history.neurons;

    if (qiHistory.length === 0 || neuronHistory.length === 0) {
      return {
        qi: { min: 0, max: 1, avg: 0.5, current: this.currentState.qi.level },
        neurons: { min: 0, max: 0, avg: 0, current: this.currentState.neurons.activeConnections }
      };
    }

    // Statistiques du Qi
    const qiLevels = qiHistory.map(q => q.level);
    const qiStats = {
      min: Math.min(...qiLevels),
      max: Math.max(...qiLevels),
      avg: qiLevels.reduce((sum, level) => sum + level, 0) / qiLevels.length,
      current: this.currentState.qi.level
    };

    // Statistiques des neurones
    const neuronCounts = neuronHistory.map(n => n.activeConnections);
    const neuronStats = {
      min: Math.min(...neuronCounts),
      max: Math.max(...neuronCounts),
      avg: neuronCounts.reduce((sum, count) => sum + count, 0) / neuronCounts.length,
      current: this.currentState.neurons.activeConnections
    };

    return {
      qi: qiStats,
      neurons: neuronStats,
      isRunning: this.isRunning,
      historyLength: qiHistory.length,
      updateInterval: this.config.updateInterval
    };
  }
}

module.exports = QiNeuronMonitor;
