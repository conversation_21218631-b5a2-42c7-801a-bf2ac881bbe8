#!/bin/bash

# Script pour mettre à jour l'application Louna avec la page thermique
# Ce script copie les fichiers de vues et de routes, puis met à jour le fichier server.js

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
ROUTES_DIR="$APP_DIR/routes"
VIEWS_DIR="$APP_DIR/views"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "████████╗██╗  ██╗███████╗██████╗ ███╗   ███╗ █████╗ ██╗     "
echo "╚══██╔══╝██║  ██║██╔════╝██╔══██╗████╗ ████║██╔══██╗██║     "
echo "   ██║   ███████║█████╗  ██████╔╝██╔████╔██║███████║██║     "
echo "   ██║   ██╔══██║██╔══╝  ██╔══██╗██║╚██╔╝██║██╔══██║██║     "
echo "   ██║   ██║  ██║███████╗██║  ██║██║ ╚═╝ ██║██║  ██║███████╗"
echo "   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝╚══════╝"
echo -e "${NC}"
echo -e "${CYAN}Mise à jour de l'application Louna avec la page thermique${NC}"
echo ""
print_message "Début de la mise à jour de l'application Louna..."
sleep 1

# Étape 1 : Copier les fichiers de vues
print_message "Étape 1 : Copie des fichiers de vues..."

cp "thermal.ejs" "$VIEWS_DIR/thermal.ejs"

print_success "Fichiers de vues copiés."

# Étape 2 : Copier les fichiers de routes
print_message "Étape 2 : Copie des fichiers de routes..."

cp "thermal-route.js" "$ROUTES_DIR/thermal.js"

print_success "Fichiers de routes copiés."

# Étape 3 : Mettre à jour le fichier server.js
print_message "Étape 3 : Mise à jour du fichier server.js..."

# Sauvegarder le fichier original
cp "$APP_DIR/server.js" "$APP_DIR/server.js.bak"

# Vérifier si la route est déjà incluse
if ! grep -q "thermalRouter" "$APP_DIR/server.js"; then
  # Trouver la ligne après laquelle ajouter la nouvelle route
  ROUTES_LINE=$(grep -n "const.*Router" "$APP_DIR/server.js" | tail -1 | cut -d: -f1)
  
  # Ajouter la nouvelle route après la dernière ligne de route
  sed -i '' "${ROUTES_LINE}a\\
const thermalRouter = require('./routes/thermal');\\
" "$APP_DIR/server.js"
  
  # Trouver la ligne après laquelle ajouter l'utilisation de la route
  USE_LINE=$(grep -n "app.use.*Router" "$APP_DIR/server.js" | tail -1 | cut -d: -f1)
  
  # Ajouter l'utilisation de la route après la dernière ligne d'utilisation
  sed -i '' "${USE_LINE}a\\
app.use('/louna/thermal', thermalRouter);\\
" "$APP_DIR/server.js"
  
  print_success "Fichier server.js mis à jour."
else
  print_message "La route est déjà incluse dans le fichier server.js."
fi

# Étape 4 : Redémarrer le serveur
print_message "Étape 4 : Redémarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/louna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/louna/thermal"

print_success "Mise à jour de l'application Louna terminée !"
print_message "Toutes les interfaces ont maintenant le même aspect visuel et sont bien connectées les unes aux autres."
