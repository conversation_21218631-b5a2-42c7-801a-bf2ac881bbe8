#!/usr/bin/env node

/**
 * Script de test pour vérifier la connexion à l'agent Claude 4GB
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://127.0.0.1:3000';
const OLLAMA_URL = 'http://127.0.0.1:11434';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
    log(`\n${colors.bold}${colors.cyan}🔍 ${message}${colors.reset}`);
}

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Teste la connexion à Ollama
 */
async function testOllamaConnection() {
    logHeader('Test de connexion à Ollama');

    try {
        const response = await axios.get(`${OLLAMA_URL}/api/tags`);
        const data = response.data;

        if (response.status === 200) {
            logSuccess('Ollama est accessible');

            // Vérifier si le modèle Claude est disponible
            const claudeModel = data.models?.find(model =>
                model.name.includes('claude') || model.name.includes('incept5/llama3.1-claude')
            );

            if (claudeModel) {
                logSuccess(`Modèle Claude trouvé: ${claudeModel.name}`);
                return true;
            } else {
                logWarning('Modèle Claude non trouvé dans Ollama');
                log('Modèles disponibles:', 'yellow');
                data.models?.forEach(model => {
                    log(`  - ${model.name}`, 'yellow');
                });
                return false;
            }
        } else {
            logError('Erreur lors de la connexion à Ollama');
            return false;
        }
    } catch (error) {
        logError(`Impossible de se connecter à Ollama: ${error.message}`);
        return false;
    }
}

/**
 * Teste la configuration des agents
 */
async function testAgentConfiguration() {
    logHeader('Test de configuration des agents');

    try {
        // Vérifier le fichier agents.json
        const agentsPath = path.join(__dirname, 'data/config/agents.json');
        if (fs.existsSync(agentsPath)) {
            const agentsConfig = JSON.parse(fs.readFileSync(agentsPath, 'utf8'));

            if (agentsConfig.defaultAgent === 'agent_claude') {
                logSuccess('Agent Claude configuré comme agent par défaut');
            } else {
                logWarning(`Agent par défaut: ${agentsConfig.defaultAgent} (attendu: agent_claude)`);
            }

            if (agentsConfig.agents?.agent_claude) {
                logSuccess('Configuration de l\'agent Claude trouvée');
                const claude = agentsConfig.agents.agent_claude;
                logInfo(`Modèle: ${claude.model}`);
                logInfo(`Nom: ${claude.name}`);
                logInfo(`Température: ${claude.temperature}`);
                logInfo(`Max tokens: ${claude.maxTokens}`);
            } else {
                logError('Configuration de l\'agent Claude manquante');
                return false;
            }
        } else {
            logError('Fichier agents.json non trouvé');
            return false;
        }

        // Vérifier le fichier default-agent.json
        const defaultAgentPath = path.join(__dirname, 'data/config/default-agent.json');
        if (fs.existsSync(defaultAgentPath)) {
            const defaultAgent = JSON.parse(fs.readFileSync(defaultAgentPath, 'utf8'));

            if (defaultAgent.id === 'agent_claude') {
                logSuccess('Agent Claude configuré comme agent par défaut');
            } else {
                logWarning(`Agent par défaut: ${defaultAgent.id} (attendu: agent_claude)`);
            }
        }

        return true;
    } catch (error) {
        logError(`Erreur lors de la vérification de la configuration: ${error.message}`);
        return false;
    }
}

/**
 * Teste l'API des agents
 */
async function testAgentAPI() {
    logHeader('Test de l\'API des agents');

    try {
        // Test du statut des agents
        const response = await axios.get(`${BASE_URL}/api/agents`);
        const statusData = response.data;

        if (response.status === 200 && statusData.success) {
            logSuccess('API des agents accessible');

            if (statusData.defaultAgent === 'agent_claude') {
                logSuccess('Agent Claude défini comme agent par défaut');
            } else {
                logWarning(`Agent par défaut: ${statusData.defaultAgent}`);
            }

            if (statusData.agents?.agent_claude) {
                logSuccess('Agent Claude trouvé dans l\'API');
            } else {
                logError('Agent Claude non trouvé dans l\'API');
                return false;
            }
        } else {
            logError('Erreur lors de l\'accès à l\'API des agents');
            return false;
        }

        return true;
    } catch (error) {
        logError(`Erreur lors du test de l'API: ${error.message}`);
        return false;
    }
}

/**
 * Teste l'API de chat avec l'agent Claude
 */
async function testChatAPI() {
    logHeader('Test de l\'API de chat avec Claude');

    try {
        const testMessage = "Bonjour ! Peux-tu me dire qui tu es et confirmer que tu es l'agent Claude 4GB ?";

        logInfo(`Envoi du message de test: "${testMessage}"`);

        const response = await axios.post(`${BASE_URL}/api/chat/message`, {
            message: testMessage,
            conversationId: 'test-claude-connection'
        });

        const data = response.data;

        if (response.status === 200 && data.success) {
            logSuccess('Message envoyé avec succès');
            logInfo(`Réponse de Claude: "${data.response}"`);

            // Vérifier si la réponse contient des indices que c'est Claude
            if (data.response.toLowerCase().includes('claude') ||
                data.response.toLowerCase().includes('assistant') ||
                data.response.toLowerCase().includes('louna')) {
                logSuccess('L\'agent semble répondre correctement');
            } else {
                logWarning('La réponse ne semble pas venir de Claude');
            }

            return true;
        } else {
            logError(`Erreur lors de l'envoi du message: ${data.error || 'Erreur inconnue'}`);
            return false;
        }
    } catch (error) {
        logError(`Erreur lors du test de chat: ${error.message}`);
        return false;
    }
}

/**
 * Teste le statut du chat
 */
async function testChatStatus() {
    logHeader('Test du statut du chat');

    try {
        const response = await axios.get(`${BASE_URL}/api/chat/status`);
        const data = response.data;

        if (response.status === 200 && data.success) {
            logSuccess('API de statut du chat accessible');

            if (data.agent?.isOnline) {
                logSuccess('Agent en ligne');
            } else {
                logWarning('Agent hors ligne');
            }

            if (data.agent?.claudeAgent) {
                logSuccess('Agent Claude détecté');
                logInfo(`Nom: ${data.agent.claudeAgent.name}`);
            }

            return true;
        } else {
            logError('Erreur lors de la vérification du statut');
            return false;
        }
    } catch (error) {
        logError(`Erreur lors du test de statut: ${error.message}`);
        return false;
    }
}

/**
 * Fonction principale de test
 */
async function runTests() {
    log(`${colors.bold}${colors.magenta}🧪 Test de connexion à l'agent Claude 4GB${colors.reset}\n`);

    const tests = [
        { name: 'Connexion Ollama', fn: testOllamaConnection },
        { name: 'Configuration des agents', fn: testAgentConfiguration },
        { name: 'API des agents', fn: testAgentAPI },
        { name: 'Statut du chat', fn: testChatStatus },
        { name: 'API de chat', fn: testChatAPI }
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) {
                passed++;
            } else {
                failed++;
            }
        } catch (error) {
            logError(`Erreur dans le test ${test.name}: ${error.message}`);
            failed++;
        }

        // Petite pause entre les tests
        await sleep(500);
    }

    // Résumé
    logHeader('Résumé des tests');
    logSuccess(`Tests réussis: ${passed}`);
    if (failed > 0) {
        logError(`Tests échoués: ${failed}`);
    }

    if (failed === 0) {
        log(`\n${colors.bold}${colors.green}🎉 Tous les tests sont passés ! L'agent Claude 4GB est correctement connecté !${colors.reset}`);
    } else {
        log(`\n${colors.bold}${colors.red}⚠️  Certains tests ont échoué. Vérifiez la configuration.${colors.reset}`);
    }

    return failed === 0;
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { runTests };
