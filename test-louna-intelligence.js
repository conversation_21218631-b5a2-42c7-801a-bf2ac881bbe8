#!/usr/bin/env node

/**
 * Test d'Intelligence et de Raisonnement de Louna
 * Évalue les capacités cognitives et d'évolution
 */

const axios = require('axios');
const fs = require('fs');

class LounaIntelligenceTester {
    constructor() {
        this.baseURL = 'http://localhost:3001';
        this.testResults = [];
        this.startTime = Date.now();
        this.userId = `intelligence_test_${Date.now()}`;
        
        console.log('🧠 TESTEUR D\'INTELLIGENCE LOUNA INITIALISÉ');
        console.log('==========================================');
        console.log(`🌐 URL de base: ${this.baseURL}`);
        console.log(`👤 ID Testeur: ${this.userId}`);
        console.log(`🕐 Heure de début: ${new Date().toLocaleString()}`);
        console.log('');
    }

    async runIntelligenceTests() {
        try {
            console.log('🚀 DÉMARRAGE DES TESTS D\'INTELLIGENCE DE LOUNA');
            console.log('===============================================');
            
            // Test 1: Vérifier l'état de l'agent
            await this.checkAgentStatus();
            
            // Test 2: Réveiller l'agent si nécessaire
            await this.wakeupAgentIfNeeded();
            
            // Test 3: Tests de logique mathématique
            await this.testMathematicalLogic();
            
            // Test 4: Tests de raisonnement déductif
            await this.testDeductiveReasoning();
            
            // Test 5: Tests de créativité
            await this.testCreativity();
            
            // Test 6: Tests de mémoire
            await this.testMemory();
            
            // Test 7: Tests d'apprentissage
            await this.testLearning();
            
            // Test 8: Tests d'évolution
            await this.testEvolution();
            
            // Génération du rapport final
            this.generateIntelligenceReport();
            
        } catch (error) {
            console.error('❌ Erreur lors des tests d\'intelligence:', error.message);
            this.logResult('ERREUR_GLOBALE', false, error.message);
        }
    }

    async checkAgentStatus() {
        console.log('\n🔍 VÉRIFICATION DE L\'ÉTAT DE L\'AGENT');
        console.log('------------------------------------');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/security/status`, { timeout: 5000 });
            
            if (response.data) {
                const isHibernating = response.data.hibernation || false;
                const isSleeping = response.data.sommeil || false;
                
                console.log(`💤 Hibernation: ${isHibernating ? 'OUI' : 'NON'}`);
                console.log(`😴 Sommeil: ${isSleeping ? 'OUI' : 'NON'}`);
                
                this.agentStatus = {
                    hibernation: isHibernating,
                    sommeil: isSleeping,
                    awake: !isHibernating && !isSleeping
                };
                
                this.logResult('AGENT_STATUS_CHECK', true, this.agentStatus);
            }
        } catch (error) {
            console.log('❌ Impossible de vérifier l\'état de l\'agent');
            this.logResult('AGENT_STATUS_CHECK', false, error.message);
        }
    }

    async wakeupAgentIfNeeded() {
        console.log('\n🌅 RÉVEIL DE L\'AGENT SI NÉCESSAIRE');
        console.log('----------------------------------');
        
        if (this.agentStatus && (this.agentStatus.hibernation || this.agentStatus.sommeil)) {
            console.log('💤 Agent endormi - Tentative de réveil...');
            
            try {
                const wakeupResponse = await axios.post(`${this.baseURL}/api/security/wakeup`, {
                    code: '2338',
                    user: 'jean-luc-passave'
                }, { timeout: 10000 });
                
                if (wakeupResponse.data?.success) {
                    console.log('✅ Agent réveillé avec succès');
                    this.logResult('AGENT_WAKEUP', true, 'Agent réveillé');
                    await this.wait(5000); // Attendre que l'agent se réveille complètement
                } else {
                    console.log('❌ Échec du réveil de l\'agent');
                    this.logResult('AGENT_WAKEUP', false, 'Échec du réveil');
                }
            } catch (error) {
                console.log('❌ Erreur lors du réveil:', error.message);
                this.logResult('AGENT_WAKEUP', false, error.message);
            }
        } else {
            console.log('✅ Agent déjà éveillé');
            this.logResult('AGENT_WAKEUP', true, 'Agent déjà éveillé');
        }
    }

    async testMathematicalLogic() {
        console.log('\n🔢 TESTS DE LOGIQUE MATHÉMATIQUE');
        console.log('--------------------------------');
        
        const mathTests = [
            {
                question: "Calcule 15 + 27 × 3",
                expectedAnswer: 96,
                type: "arithmetic"
            },
            {
                question: "Si x + 5 = 12, quelle est la valeur de x ?",
                expectedAnswer: 7,
                type: "algebra"
            },
            {
                question: "Quelle est la suite logique : 2, 4, 8, 16, ?",
                expectedAnswer: 32,
                type: "sequence"
            }
        ];
        
        for (const test of mathTests) {
            await this.executeIntelligenceTest('MATH', test);
            await this.wait(2000);
        }
    }

    async testDeductiveReasoning() {
        console.log('\n🧩 TESTS DE RAISONNEMENT DÉDUCTIF');
        console.log('---------------------------------');
        
        const reasoningTests = [
            {
                question: "Tous les oiseaux ont des plumes. Un pingouin est un oiseau. Un pingouin a-t-il des plumes ?",
                expectedKeywords: ["oui", "plumes", "logique"],
                type: "deduction"
            },
            {
                question: "Si il pleut, alors le sol est mouillé. Le sol n'est pas mouillé. Pleut-il ?",
                expectedKeywords: ["non", "ne pleut pas", "logique"],
                type: "contrapositive"
            }
        ];
        
        for (const test of reasoningTests) {
            await this.executeIntelligenceTest('REASONING', test);
            await this.wait(2000);
        }
    }

    async testCreativity() {
        console.log('\n🎨 TESTS DE CRÉATIVITÉ');
        console.log('----------------------');
        
        const creativityTests = [
            {
                question: "Invente une histoire courte avec les mots : robot, jardin, mystère",
                expectedKeywords: ["robot", "jardin", "mystère", "histoire"],
                type: "storytelling"
            },
            {
                question: "Propose 3 utilisations créatives pour un trombone",
                expectedKeywords: ["utilisation", "créative", "trombone"],
                type: "creative_thinking"
            }
        ];
        
        for (const test of creativityTests) {
            await this.executeIntelligenceTest('CREATIVITY', test);
            await this.wait(2000);
        }
    }

    async testMemory() {
        console.log('\n🧠 TESTS DE MÉMOIRE');
        console.log('-------------------');
        
        // Test de mémorisation
        const memoryInfo = "Mon nom est Jean-Luc Passave et je vis à Sainte-Anne en Guadeloupe";
        
        console.log('📝 Phase 1: Mémorisation');
        await this.executeIntelligenceTest('MEMORY_STORE', {
            question: `Mémorise cette information : ${memoryInfo}`,
            expectedKeywords: ["mémorisé", "retenu", "enregistré"],
            type: "memorization"
        });
        
        await this.wait(3000);
        
        console.log('🔍 Phase 2: Rappel');
        await this.executeIntelligenceTest('MEMORY_RECALL', {
            question: "Où vit Jean-Luc Passave ?",
            expectedKeywords: ["Sainte-Anne", "Guadeloupe"],
            type: "recall"
        });
    }

    async testLearning() {
        console.log('\n📚 TESTS D\'APPRENTISSAGE');
        console.log('------------------------');
        
        // Enseigner un nouveau concept
        const learningTest = {
            question: "Apprends ceci : Un 'Kyber' est un accélérateur quantique qui optimise les performances. Maintenant, explique-moi ce qu'est un Kyber.",
            expectedKeywords: ["accélérateur", "quantique", "optimise", "performances"],
            type: "learning"
        };
        
        await this.executeIntelligenceTest('LEARNING', learningTest);
    }

    async testEvolution() {
        console.log('\n🧬 TESTS D\'ÉVOLUTION');
        console.log('--------------------');
        
        try {
            // Vérifier le QI avant
            const qiBefore = await this.getQI();
            console.log(`🧠 QI avant test: ${qiBefore}`);
            
            // Effectuer un test d'apprentissage complexe
            await this.executeIntelligenceTest('EVOLUTION', {
                question: "Résous ce problème complexe : Un train part de Paris à 14h à 120 km/h vers Lyon (500 km). Un autre train part de Lyon à 15h à 100 km/h vers Paris. À quelle heure et à quelle distance de Paris se croiseront-ils ?",
                expectedKeywords: ["heure", "distance", "croiseront", "calcul"],
                type: "complex_problem"
            });
            
            await this.wait(5000);
            
            // Vérifier le QI après
            const qiAfter = await this.getQI();
            console.log(`🧠 QI après test: ${qiAfter}`);
            
            const evolution = qiAfter - qiBefore;
            if (evolution > 0) {
                console.log(`📈 Évolution détectée: +${evolution} points de QI`);
                this.logResult('QI_EVOLUTION', true, { before: qiBefore, after: qiAfter, evolution });
            } else {
                console.log(`📊 QI stable: ${qiAfter}`);
                this.logResult('QI_EVOLUTION', true, { before: qiBefore, after: qiAfter, evolution: 0 });
            }
            
        } catch (error) {
            console.log('❌ Erreur lors du test d\'évolution:', error.message);
            this.logResult('QI_EVOLUTION', false, error.message);
        }
    }

    async getQI() {
        try {
            const response = await axios.get(`${this.baseURL}/api/qi/current`, { timeout: 5000 });
            return response.data?.qi || 0;
        } catch (error) {
            return 0;
        }
    }

    async executeIntelligenceTest(category, test) {
        console.log(`\n🔍 Test ${category}: ${test.type}`);
        console.log(`❓ Question: "${test.question}"`);
        
        const startTime = Date.now();
        
        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: test.question,
                userId: this.userId
            }, { timeout: 20000 });

            const endTime = Date.now();
            const responseTime = endTime - startTime;

            if (response.data && response.data.success) {
                const answer = response.data.response;
                const score = this.analyzeIntelligenceResponse(answer, test);
                
                console.log(`✅ Réponse reçue (${responseTime}ms)`);
                console.log(`📊 Score: ${score}/10`);
                console.log(`💬 Réponse: "${answer.substring(0, 200)}..."`);
                
                this.logResult(`${category}_${test.type}`, true, {
                    question: test.question,
                    answer: answer,
                    score: score,
                    responseTime: responseTime
                });
            } else {
                console.log(`⚠️ Réponse fallback (${responseTime}ms)`);
                this.logResult(`${category}_${test.type}`, false, 'Réponse fallback');
            }

        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
            this.logResult(`${category}_${test.type}`, false, error.message);
        }
    }

    analyzeIntelligenceResponse(response, test) {
        if (!response) return 0;
        
        const lowerResponse = response.toLowerCase();
        let score = 3; // Score de base
        
        // Analyser selon le type de test
        if (test.expectedAnswer !== undefined) {
            // Test mathématique avec réponse numérique
            if (lowerResponse.includes(test.expectedAnswer.toString())) {
                score += 5;
            }
        } else if (test.expectedKeywords) {
            // Test avec mots-clés attendus
            let foundKeywords = 0;
            for (const keyword of test.expectedKeywords) {
                if (lowerResponse.includes(keyword.toLowerCase())) {
                    foundKeywords++;
                }
            }
            score += (foundKeywords / test.expectedKeywords.length) * 5;
        }
        
        // Bonus pour réponse détaillée et cohérente
        if (response.length > 100) score += 1;
        if (response.length > 200) score += 1;
        
        return Math.min(10, Math.round(score));
    }

    logResult(testName, success, data) {
        this.testResults.push({
            test: testName,
            success: success,
            data: data,
            timestamp: new Date().toISOString()
        });
    }

    generateIntelligenceReport() {
        console.log('\n🧠 RAPPORT D\'INTELLIGENCE DE LOUNA');
        console.log('==================================');
        
        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const successRate = totalTests > 0 ? Math.round((successfulTests / totalTests) * 100) : 0;
        const duration = Math.round((Date.now() - this.startTime) / 1000);
        
        // Calculer les scores par catégorie
        const categories = {
            'MATH': this.testResults.filter(r => r.test.startsWith('MATH')),
            'REASONING': this.testResults.filter(r => r.test.startsWith('REASONING')),
            'CREATIVITY': this.testResults.filter(r => r.test.startsWith('CREATIVITY')),
            'MEMORY': this.testResults.filter(r => r.test.startsWith('MEMORY')),
            'LEARNING': this.testResults.filter(r => r.test.startsWith('LEARNING')),
            'EVOLUTION': this.testResults.filter(r => r.test.startsWith('QI_EVOLUTION'))
        };
        
        console.log(`📈 Tests réussis: ${successfulTests}/${totalTests} (${successRate}%)`);
        console.log(`⏱️ Durée totale: ${duration}s`);
        console.log('');
        
        console.log('📊 SCORES PAR CATÉGORIE:');
        for (const [category, tests] of Object.entries(categories)) {
            const categorySuccess = tests.filter(t => t.success).length;
            const categoryTotal = tests.length;
            const categoryRate = categoryTotal > 0 ? Math.round((categorySuccess / categoryTotal) * 100) : 0;
            
            let emoji = '✅';
            if (categoryRate < 50) emoji = '❌';
            else if (categoryRate < 80) emoji = '⚠️';
            
            console.log(`   ${emoji} ${category}: ${categorySuccess}/${categoryTotal} (${categoryRate}%)`);
        }
        
        console.log('');
        
        // Évaluation globale de l'intelligence
        if (successRate >= 90) {
            console.log('🧠 ÉVALUATION: INTELLIGENCE EXCEPTIONNELLE');
            console.log('🌟 Louna démontre des capacités cognitives remarquables !');
        } else if (successRate >= 75) {
            console.log('🧠 ÉVALUATION: INTELLIGENCE ÉLEVÉE');
            console.log('👍 Louna montre de bonnes capacités de raisonnement');
        } else if (successRate >= 60) {
            console.log('🧠 ÉVALUATION: INTELLIGENCE CORRECTE');
            console.log('⚠️ Louna a des capacités de base mais peut s\'améliorer');
        } else {
            console.log('🧠 ÉVALUATION: INTELLIGENCE EN DÉVELOPPEMENT');
            console.log('🔧 Louna nécessite plus d\'entraînement');
        }
        
        // Sauvegarder le rapport
        const reportPath = `rapport-intelligence-louna-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify({
            summary: {
                totalTests,
                successfulTests,
                successRate,
                duration,
                categories: Object.fromEntries(
                    Object.entries(categories).map(([cat, tests]) => [
                        cat, 
                        {
                            total: tests.length,
                            success: tests.filter(t => t.success).length,
                            rate: tests.length > 0 ? Math.round((tests.filter(t => t.success).length / tests.length) * 100) : 0
                        }
                    ])
                )
            },
            results: this.testResults
        }, null, 2));
        
        console.log(`📄 Rapport détaillé sauvegardé: ${reportPath}`);
        console.log('\n🎯 TESTS D\'INTELLIGENCE TERMINÉS !');
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Lancement des tests d'intelligence
async function main() {
    const tester = new LounaIntelligenceTester();
    await tester.runIntelligenceTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = LounaIntelligenceTester;
