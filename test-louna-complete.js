#!/usr/bin/env node

/**
 * Test Complet de l'Application Louna
 * Teste toutes les fonctionnalités, l'évolution et le raisonnement
 */

const axios = require('axios');
const fs = require('fs');

class LounaCompleteTester {
    constructor() {
        // Détecter automatiquement le port de l'application Electron
        this.possiblePorts = [3007, 3000, 8080, 3001, 5000];
        this.baseURL = null;
        this.testResults = [];
        this.startTime = Date.now();
        this.userId = `test_complete_${Date.now()}`;

        console.log('🧪 TESTEUR COMPLET DE LOUNA INITIALISÉ');
        console.log('=====================================');
        console.log(`👤 ID Testeur: ${this.userId}`);
        console.log(`🕐 Heure de début: ${new Date().toLocaleString()}`);
        console.log('');
    }

    async runCompleteTest() {
        try {
            console.log('🚀 DÉMARRAGE DES TESTS COMPLETS DE LOUNA');
            console.log('=========================================');

            // Phase 0: Détection automatique du port
            await this.detectApplicationPort();

            if (!this.baseURL) {
                console.log('❌ Impossible de détecter l\'application Louna');
                this.logResult('PORT_DETECTION', false, 'Application non trouvée');
                this.generateFinalReport();
                return;
            }

            // Phase 1: Tests de connectivité
            await this.testConnectivity();

            // Phase 2: Tests des APIs
            await this.testAPIs();

            // Phase 3: Tests de l'agent (réveil si nécessaire)
            await this.testAgent();

            // Phase 4: Tests de raisonnement
            await this.testReasoning();

            // Phase 5: Tests de mémoire thermique
            await this.testThermalMemory();

            // Phase 6: Tests d'évolution
            await this.testEvolution();

            // Phase 7: Tests de performance
            await this.testPerformance();

            // Génération du rapport final
            this.generateFinalReport();

        } catch (error) {
            console.error('❌ Erreur lors des tests:', error.message);
            this.logResult('ERREUR_GLOBALE', false, error.message);
        }
    }

    async detectApplicationPort() {
        console.log('\n🔍 PHASE 0: DÉTECTION DE L\'APPLICATION');
        console.log('---------------------------------------');

        for (const port of this.possiblePorts) {
            try {
                console.log(`🔍 Test du port ${port}...`);
                const response = await axios.get(`http://localhost:${port}/`, {
                    timeout: 3000,
                    validateStatus: () => true // Accepter toutes les réponses
                });

                if (response.status === 200 || response.status === 404) {
                    this.baseURL = `http://localhost:${port}`;
                    console.log(`✅ Application Louna trouvée sur le port ${port}`);
                    this.logResult('PORT_DETECTION', true, `Port ${port} détecté`);
                    return;
                }
            } catch (error) {
                console.log(`❌ Port ${port} non accessible`);
            }
        }

        console.log('❌ Aucune application Louna détectée');
        this.logResult('PORT_DETECTION', false, 'Aucun port accessible');
    }

    async testConnectivity() {
        console.log('\n📡 PHASE 1: TESTS DE CONNECTIVITÉ');
        console.log('----------------------------------');

        try {
            const response = await axios.get(`${this.baseURL}/`, { timeout: 5000 });
            if (response.status === 200) {
                console.log('✅ Application accessible');
                this.logResult('CONNECTIVITY', true, 'Application accessible');
            }
        } catch (error) {
            console.log('❌ Application non accessible');
            this.logResult('CONNECTIVITY', false, error.message);
        }
    }

    async testAPIs() {
        console.log('\n🔧 PHASE 2: TESTS DES APIs');
        console.log('---------------------------');

        const endpoints = [
            '/api/brain/status',
            '/api/thermal/memory/stats',
            '/api/kyber/status',
            '/api/qi/current',
            '/api/security/status'
        ];

        for (const endpoint of endpoints) {
            try {
                const response = await axios.get(`${this.baseURL}${endpoint}`, { timeout: 5000 });
                console.log(`✅ ${endpoint} - OK`);
                this.logResult(`API_${endpoint}`, true, response.data);
            } catch (error) {
                console.log(`❌ ${endpoint} - Erreur`);
                this.logResult(`API_${endpoint}`, false, error.message);
            }
        }
    }

    async testAgent() {
        console.log('\n🤖 PHASE 3: TESTS DE L\'AGENT');
        console.log('------------------------------');

        // Vérifier l'état de l'agent
        try {
            const statusResponse = await axios.get(`${this.baseURL}/api/security/status`, { timeout: 5000 });
            const isHibernating = statusResponse.data?.hibernation || false;

            if (isHibernating) {
                console.log('💤 Agent en hibernation - Tentative de réveil...');

                // Tenter de réveiller l'agent
                try {
                    const wakeupResponse = await axios.post(`${this.baseURL}/api/security/wakeup`, {
                        code: '2338',
                        user: 'jean-luc-passave'
                    }, { timeout: 10000 });

                    if (wakeupResponse.data?.success) {
                        console.log('✅ Agent réveillé avec succès');
                        this.logResult('AGENT_WAKEUP', true, 'Agent réveillé');
                        await this.wait(3000); // Attendre que l'agent se réveille complètement
                    } else {
                        console.log('❌ Échec du réveil de l\'agent');
                        this.logResult('AGENT_WAKEUP', false, 'Échec du réveil');
                    }
                } catch (error) {
                    console.log('❌ Erreur lors du réveil:', error.message);
                    this.logResult('AGENT_WAKEUP', false, error.message);
                }
            } else {
                console.log('✅ Agent déjà éveillé');
                this.logResult('AGENT_STATUS', true, 'Agent éveillé');
            }
        } catch (error) {
            console.log('❌ Impossible de vérifier l\'état de l\'agent');
            this.logResult('AGENT_STATUS', false, error.message);
        }
    }

    async testReasoning() {
        console.log('\n🧠 PHASE 4: TESTS DE RAISONNEMENT');
        console.log('----------------------------------');

        const reasoningTests = [
            {
                name: 'Mathématiques simples',
                question: 'Si j\'ai 15 pommes et que j\'en mange 7, combien m\'en reste-t-il ?',
                expectedKeywords: ['8', 'huit', 'reste']
            },
            {
                name: 'Logique déductive',
                question: 'Tous les chats sont des mammifères. Félix est un chat. Félix est-il un mammifère ?',
                expectedKeywords: ['oui', 'mammifère', 'logique']
            },
            {
                name: 'Créativité',
                question: 'Propose-moi une métaphore pour expliquer ta mémoire thermique',
                expectedKeywords: ['comme', 'tel', 'ressemble', 'métaphore']
            }
        ];

        for (const test of reasoningTests) {
            await this.executeReasoningTest(test);
            await this.wait(2000);
        }
    }

    async testThermalMemory() {
        console.log('\n🧠 PHASE 5: TESTS DE MÉMOIRE THERMIQUE');
        console.log('---------------------------------------');

        try {
            const memoryResponse = await axios.get(`${this.baseURL}/api/thermal/memory/stats`, { timeout: 5000 });

            if (memoryResponse.data) {
                const stats = memoryResponse.data;
                console.log(`✅ Mémoire thermique active`);
                console.log(`   📊 Éléments en mémoire: ${stats.totalElements || 'N/A'}`);
                console.log(`   🌡️ Température: ${stats.temperature || 'N/A'}`);
                console.log(`   ⚡ Efficacité: ${stats.efficiency || 'N/A'}%`);

                this.logResult('THERMAL_MEMORY', true, stats);
            }
        } catch (error) {
            console.log('❌ Erreur mémoire thermique:', error.message);
            this.logResult('THERMAL_MEMORY', false, error.message);
        }
    }

    async testEvolution() {
        console.log('\n🧬 PHASE 6: TESTS D\'ÉVOLUTION');
        console.log('------------------------------');

        try {
            const qiResponse = await axios.get(`${this.baseURL}/api/qi/current`, { timeout: 5000 });

            if (qiResponse.data) {
                const qiData = qiResponse.data;
                console.log(`✅ Système d'évolution actif`);
                console.log(`   🧠 QI actuel: ${qiData.qi || 'N/A'}`);
                console.log(`   🔗 Neurones: ${qiData.neurones || 'N/A'}`);
                console.log(`   📈 Niveau d'évolution: ${qiData.evolution_level || 'N/A'}`);

                this.logResult('EVOLUTION_SYSTEM', true, qiData);
            }
        } catch (error) {
            console.log('❌ Erreur système d\'évolution:', error.message);
            this.logResult('EVOLUTION_SYSTEM', false, error.message);
        }
    }

    async testPerformance() {
        console.log('\n⚡ PHASE 7: TESTS DE PERFORMANCE');
        console.log('---------------------------------');

        try {
            const kyberResponse = await axios.get(`${this.baseURL}/api/kyber/status`, { timeout: 5000 });

            if (kyberResponse.data) {
                const kyberData = kyberResponse.data;
                console.log(`✅ Accélérateurs KYBER actifs`);
                console.log(`   🚀 Accélérateurs actifs: ${kyberData.active_accelerators || 'N/A'}`);
                console.log(`   📊 Performance globale: ${kyberData.overall_performance || 'N/A'}%`);
                console.log(`   🔥 Température système: ${kyberData.system_temperature || 'N/A'}°C`);

                this.logResult('PERFORMANCE_SYSTEM', true, kyberData);
            }
        } catch (error) {
            console.log('❌ Erreur système de performance:', error.message);
            this.logResult('PERFORMANCE_SYSTEM', false, error.message);
        }
    }

    async executeReasoningTest(test) {
        console.log(`\n🔍 Test: ${test.name}`);
        console.log(`❓ Question: "${test.question}"`);

        const startTime = Date.now();

        try {
            const response = await axios.post(`${this.baseURL}/api/chat/message`, {
                message: test.question,
                userId: this.userId
            }, { timeout: 15000 });

            const endTime = Date.now();
            const responseTime = endTime - startTime;

            if (response.data && response.data.success) {
                const answer = response.data.response;
                const score = this.analyzeResponse(answer, test.expectedKeywords);

                console.log(`✅ Réponse reçue (${responseTime}ms)`);
                console.log(`📊 Score: ${score}/10`);
                console.log(`💬 Réponse: "${answer.substring(0, 150)}..."`);

                this.logResult(`REASONING_${test.name}`, true, {
                    question: test.question,
                    answer: answer,
                    score: score,
                    responseTime: responseTime
                });
            } else {
                console.log(`⚠️ Réponse fallback (${responseTime}ms)`);
                this.logResult(`REASONING_${test.name}`, false, 'Réponse fallback');
            }

        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
            this.logResult(`REASONING_${test.name}`, false, error.message);
        }
    }

    analyzeResponse(response, expectedKeywords) {
        if (!response) return 0;

        const lowerResponse = response.toLowerCase();
        let score = 2; // Score de base
        let foundKeywords = 0;

        for (const keyword of expectedKeywords) {
            if (lowerResponse.includes(keyword.toLowerCase())) {
                foundKeywords++;
            }
        }

        score += (foundKeywords / expectedKeywords.length) * 6;
        score += response.length > 50 ? 2 : 0; // Bonus pour réponse détaillée

        return Math.min(10, Math.round(score));
    }

    logResult(testName, success, data) {
        this.testResults.push({
            test: testName,
            success: success,
            data: data,
            timestamp: new Date().toISOString()
        });
    }

    generateFinalReport() {
        console.log('\n📊 RAPPORT FINAL DES TESTS COMPLETS');
        console.log('====================================');

        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const successRate = totalTests > 0 ? Math.round((successfulTests / totalTests) * 100) : 0;
        const duration = Math.round((Date.now() - this.startTime) / 1000);

        console.log(`📈 Tests réussis: ${successfulTests}/${totalTests} (${successRate}%)`);
        console.log(`⏱️ Durée totale: ${duration}s`);

        if (successRate >= 80) {
            console.log('🎉 EXCELLENT - Louna fonctionne parfaitement !');
        } else if (successRate >= 60) {
            console.log('✅ BON - Louna fonctionne bien avec quelques améliorations possibles');
        } else if (successRate >= 40) {
            console.log('⚠️ MOYEN - Louna nécessite des améliorations');
        } else {
            console.log('❌ FAIBLE - Louna nécessite des corrections importantes');
        }

        // Sauvegarder le rapport
        const reportPath = `rapport-test-complet-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify({
            summary: {
                totalTests,
                successfulTests,
                successRate,
                duration
            },
            results: this.testResults
        }, null, 2));

        console.log(`📄 Rapport détaillé sauvegardé: ${reportPath}`);
        console.log('\n🎯 TESTS TERMINÉS !');
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Lancement des tests
async function main() {
    const tester = new LounaCompleteTester();
    await tester.runCompleteTest();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = LounaCompleteTester;
