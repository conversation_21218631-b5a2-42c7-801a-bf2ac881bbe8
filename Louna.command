#!/bin/bash

# Ra<PERSON>urci pour lancer Louna
# Ce script lance l'application Louna en mode application

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"
SERVER_FILE="$APP_DIR/server.js"
NAVIGATION_FIX_SCRIPT="$APP_DIR/fix-navigation.js"

# Vérifier si le fichier server.js existe
if [ ! -f "$SERVER_FILE" ]; then
  print_error "Le fichier server.js n'existe pas à l'emplacement: $SERVER_FILE"
  exit 1
fi

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  print_error "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
  exit 1
fi

# Créer le script de correction de navigation
print_message "Création du script de correction de navigation..."
cat > "$NAVIGATION_FIX_SCRIPT" << 'EOL'
/**
 * Script de correction de navigation pour Louna
 * Ce script corrige les problèmes de navigation entre les interfaces
 */

// Fonction pour corriger les liens incorrects
function fixIncorrectLinks() {
  // Sélectionner tous les liens dans la barre latérale
  const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

  // Parcourir tous les liens et corriger les chemins incorrects
  sidebarLinks.forEach(link => {
    const href = link.getAttribute('href');

    // Corriger les liens qui commencent par /louna/ au lieu de /luna/
    if (href && href.startsWith('/louna/')) {
      const correctedHref = href.replace('/louna/', '/luna/');
      link.setAttribute('href', correctedHref);
      console.log(`Lien corrigé: ${href} -> ${correctedHref}`);
    }
  });

  console.log('Correction des liens terminée');
}

// Fonction pour ajouter un gestionnaire d'erreurs de navigation
function addNavigationErrorHandler() {
  // Stocker l'URL actuelle dans le stockage local
  if (!window.location.pathname.includes('/error')) {
    localStorage.setItem('lastValidUrl', window.location.href);
    console.log('URL valide stockée:', window.location.href);
  }

  // Ajouter un gestionnaire d'événements pour les erreurs
  window.addEventListener('error', function(event) {
    console.error('Erreur détectée:', event.error);

    // Vérifier si l'erreur est liée à la navigation
    if (event.error && (
        event.error.message.includes('navigation') ||
        event.error.message.includes('route') ||
        event.error.message.includes('undefined') ||
        event.error.message.includes('Cannot read') ||
        event.error.message.includes('null') ||
        event.error.message.includes('not defined')
      )) {
      console.log('Erreur de navigation détectée, tentative de récupération...');

      // Récupérer la dernière URL valide
      const lastValidUrl = localStorage.getItem('lastValidUrl') || '/luna';

      // Rediriger vers la dernière URL valide
      window.location.href = lastValidUrl;
    }
  });

  console.log('Gestionnaire d\'erreurs de navigation ajouté');
}

// Fonction pour intercepter les clics sur les liens
function interceptLinkClicks() {
  // Intercepter tous les clics sur les liens
  document.addEventListener('click', function(event) {
    // Vérifier si l'élément cliqué est un lien ou est contenu dans un lien
    const link = event.target.closest('a');

    if (link) {
      const href = link.getAttribute('href');

      // Ne pas intercepter les liens externes ou les liens avec des attributs spéciaux
      if (!href || href.startsWith('http') || href.startsWith('#') ||
          link.getAttribute('target') === '_blank' ||
          link.getAttribute('data-no-intercept')) {
        return;
      }

      // Corriger les liens qui commencent par /louna/ au lieu de /luna/
      if (href.startsWith('/louna/')) {
        event.preventDefault();
        const correctedHref = href.replace('/louna/', '/luna/');
        console.log(`Navigation corrigée: ${href} -> ${correctedHref}`);
        window.location.href = correctedHref;
      }

      // Stocker l'URL actuelle dans le stockage local
      localStorage.setItem('lastValidUrl', window.location.href);
      console.log('URL valide stockée (clic):', window.location.href);
    }
  });

  console.log('Interception des clics sur les liens activée');
}

// Exécuter les fonctions lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
  console.log('Script de correction de navigation chargé');

  // Corriger les liens incorrects
  fixIncorrectLinks();

  // Ajouter un gestionnaire d'erreurs de navigation
  addNavigationErrorHandler();

  // Intercepter les clics sur les liens
  interceptLinkClicks();

  console.log('Correction de navigation terminée');
});
EOL

# Vérifier si le port 3005 est déjà utilisé
if lsof -i:3005 -t &> /dev/null; then
  print_message "Le port 3005 est déjà utilisé. Voulez-vous arrêter le processus existant ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Arrêt du processus existant..."
    kill -9 $(lsof -i:3005 -t) 2>/dev/null
    sleep 2
    print_success "Processus arrêté."
  else
    print_message "Lancement de l'application sur un autre port..."
    export PORT=3006
  fi
fi

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Système Cognitif Avancé avec Mémoire Thermique${NC}"
echo -e "${YELLOW}Développé par Jean Passave, Sainte-Anne, Guadeloupe (97180)${NC}"
echo ""
print_message "Initialisation du système..."
sleep 1

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  print_error "npm n'est pas installé. Vérification des dépendances impossible."
else
  # Vérifier si les dépendances sont installées
  print_message "Vérification des dépendances..."
  cd "$APP_DIR" || exit
  if [ ! -d "node_modules" ]; then
    print_message "Installation des dépendances..."
    npm install --silent
    if [ $? -ne 0 ]; then
      print_error "Erreur lors de l'installation des dépendances."
      exit 1
    fi
    print_success "Dépendances installées avec succès."
  else
    print_success "Dépendances déjà installées."
  fi
fi

# Vérifier si les répertoires de données existent
print_message "Vérification des répertoires de données..."
mkdir -p "$APP_DIR/data/memory"
mkdir -p "$APP_DIR/data/logs"
print_success "Répertoires de données vérifiés."

# Créer un script d'injection pour corriger la navigation
print_message "Création du script d'injection pour corriger la navigation..."
cat > "$APP_DIR/public/js/navigation-fix-injector.js" << 'EOL'
// Injecter le script de correction de navigation
document.addEventListener('DOMContentLoaded', function() {
  // Créer un élément script
  const script = document.createElement('script');
  script.src = '/js/fix-navigation.js';
  script.async = true;

  // Ajouter le script au document
  document.head.appendChild(script);

  console.log('Script de correction de navigation injecté');
});
EOL

# Copier le script de correction de navigation
cp "$NAVIGATION_FIX_SCRIPT" "$APP_DIR/public/js/fix-navigation.js"

# Lancer l'application
print_message "Lancement de Louna..."
cd "$APP_DIR" || exit

# Déterminer le port à utiliser
PORT=${PORT:-3005}
print_message "L'application sera accessible à l'adresse: http://localhost:$PORT"

# Ouvrir l'application Electron après un court délai
(sleep 3 && npm run electron) &

# Lancer le serveur
node server.js

# Ce code ne sera exécuté que si le serveur s'arrête
print_message "Louna s'est arrêté."
