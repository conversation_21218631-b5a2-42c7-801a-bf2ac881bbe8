/**
 * Script pour construire l'application Louna pour macOS
 */

const builder = require('electron-builder');
const path = require('path');
const fs = require('fs');

// Vérifier si le dossier dist existe, sinon le créer
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Configuration de la construction
const config = {
  appId: 'com.louna.thermal-memory',
  productName: 'Louna',
  directories: {
    output: 'dist'
  },
  files: [
    '**/*',
    '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}',
    '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}',
    '!**/node_modules/*.d.ts',
    '!**/node_modules/.bin',
    '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}',
    '!.editorconfig',
    '!**/._*',
    '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}',
    '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}',
    '!**/{appveyor.yml,.travis.yml,circle.yml}',
    '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
  ],
  mac: {
    category: 'public.app-category.productivity',
    target: [
      'dmg',
      'zip'
    ],
    icon: 'public/img/louna-icon.icns',
    darkModeSupport: true,
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: 'entitlements.plist',
    entitlementsInherit: 'entitlements.plist'
  },
  dmg: {
    background: 'public/img/dmg-background.png',
    icon: 'public/img/louna-icon.icns',
    iconSize: 128,
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: 'link',
        path: '/Applications'
      }
    ],
    window: {
      width: 540,
      height: 380
    }
  },
  afterSign: 'notarize.js'
};

// Construire l'application
builder.build({
  targets: builder.Platform.MAC.createTarget(),
  config: config
})
  .then(() => {
    console.log('Construction terminée avec succès !');
  })
  .catch((error) => {
    console.error('Erreur lors de la construction :', error);
    process.exit(1);
  });
