/**
 * Système d'analyse vidéo YouTube pour Louna
 * Permet d'analyser des vidéos YouTube pour l'apprentissage de l'agent
 * Créé pour <PERSON><PERSON>
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');

class YouTubeVideoAnalyzer extends EventEmitter {
    constructor(options = {}) {
        super();

        this.config = {
            // Configuration de base
            outputDir: options.outputDir || path.join(__dirname, 'data', 'youtube_analysis'),
            tempDir: options.tempDir || path.join(__dirname, 'temp', 'youtube'),
            maxDuration: options.maxDuration || 3600, // 1 heure max
            quality: options.quality || 'best[height<=720]',

            // Configuration d'analyse
            frameExtraction: options.frameExtraction !== false,
            audioExtraction: options.audioExtraction !== false,
            transcription: options.transcription !== false,
            objectDetection: options.objectDetection !== false,
            sceneAnalysis: options.sceneAnalysis !== false,

            // Configuration apprentissage
            autoLearning: options.autoLearning !== false,
            memoryIntegration: options.memoryIntegration !== false,

            // Options avancées
            debug: options.debug || false,
            concurrent: options.concurrent || 2
        };

        this.state = {
            isActive: false,
            currentJobs: new Map(),
            completedAnalyses: [],
            stats: {
                videosAnalyzed: 0,
                totalDuration: 0,
                framesExtracted: 0,
                conceptsLearned: 0,
                startTime: Date.now()
            }
        };

        this.initializeDirectories();
        this.log('🎬 Système d'analyse vidéo YouTube initialisé');
    }

    /**
     * Initialise les répertoires nécessaires
     */
    initializeDirectories() {
        const dirs = [
            this.config.outputDir,
            this.config.tempDir,
            path.join(this.config.outputDir, 'videos'),
            path.join(this.config.outputDir, 'frames'),
            path.join(this.config.outputDir, 'audio'),
            path.join(this.config.outputDir, 'transcripts'),
            path.join(this.config.outputDir, 'analysis'),
            path.join(this.config.outputDir, 'learning_data')
        ];

        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                this.log(`📁 Répertoire créé: ${dir}`);
            }
        });
    }

    /**
     * Démarre le système d'analyse
     */
    async start() {
        try {
            this.log('🚀 Démarrage du système d\'analyse vidéo YouTube...');

            // Vérifier les dépendances
            await this.checkDependencies();

            this.state.isActive = true;
            this.state.stats.startTime = Date.now();

            this.emit('analyzerStarted');
            this.log('✅ Système d\'analyse vidéo démarré');

            return true;
        } catch (error) {
            this.log(`❌ Erreur démarrage: ${error.message}`);
            this.emit('error', error);
            return false;
        }
    }

    /**
     * Vérifie les dépendances nécessaires
     */
    async checkDependencies() {
        this.log('🔍 Vérification des dépendances...');

        const dependencies = {
            'yt-dlp': false,
            'ffmpeg': false,
            'python3': false,
            'whisper': false
        };

        try {
            // Vérifier yt-dlp
            await this.execCommand('yt-dlp --version');
            dependencies['yt-dlp'] = true;
            this.log('✅ yt-dlp disponible');
        } catch (error) {
            this.log('⚠️ yt-dlp non disponible, installation...');
            try {
                await this.execCommand('python3 -m pip install yt-dlp');
                dependencies['yt-dlp'] = true;
                this.log('✅ yt-dlp installé');
            } catch (installError) {
                this.log('❌ Impossible d\'installer yt-dlp');
            }
        }

        try {
            // Vérifier FFmpeg
            await this.execCommand('ffmpeg -version');
            dependencies.ffmpeg = true;
            this.log('✅ FFmpeg disponible');
        } catch (error) {
            this.log('⚠️ FFmpeg non disponible');
        }

        try {
            // Vérifier Python3
            await this.execCommand('python3 --version');
            dependencies.python3 = true;
            this.log('✅ Python3 disponible');
        } catch (error) {
            this.log('⚠️ Python3 non disponible');
        }

        // Installer les dépendances Python
        if (dependencies.python3) {
            await this.installPythonDependencies();
        }

        this.dependencies = dependencies;
        return dependencies;
    }

    /**
     * Installe les dépendances Python
     */
    async installPythonDependencies() {
        const packages = [
            'opencv-python',
            'numpy',
            'pillow',
            'whisper',
            'torch',
            'transformers',
            'moviepy'
        ];

        for (const pkg of packages) {
            try {
                await this.execCommand(`python3 -m pip install ${pkg}`);
                this.log(`✅ ${pkg} installé`);
            } catch (error) {
                this.log(`⚠️ Erreur installation ${pkg}`);
            }
        }
    }

    /**
     * Analyse une vidéo YouTube
     */
    async analyzeVideo(url, options = {}) {
        if (!this.state.isActive) {
            throw new Error('Système d\'analyse non actif');
        }

        const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.log(`🎬 Démarrage analyse vidéo: ${url} (Job: ${jobId})`);

        const job = {
            id: jobId,
            url: url,
            status: 'starting',
            progress: 0,
            startTime: Date.now(),
            options: { ...this.config, ...options },
            results: {}
        };

        this.state.currentJobs.set(jobId, job);
        this.emit('analysisStarted', { jobId, url });

        try {
            // Étape 1: Télécharger les métadonnées
            job.status = 'metadata';
            job.progress = 10;
            this.emit('analysisProgress', job);

            const metadata = await this.getVideoMetadata(url);
            job.results.metadata = metadata;
            this.log(`📋 Métadonnées récupérées: ${metadata.title}`);

            // Vérifier la durée
            if (metadata.duration > this.config.maxDuration) {
                throw new Error(`Vidéo trop longue: ${metadata.duration}s (max: ${this.config.maxDuration}s)`);
            }

            // Étape 2: Télécharger la vidéo
            job.status = 'downloading';
            job.progress = 20;
            this.emit('analysisProgress', job);

            const videoPath = await this.downloadVideo(url, jobId);
            job.results.videoPath = videoPath;
            this.log(`📥 Vidéo téléchargée: ${videoPath}`);

            // Étape 3: Extraire l'audio si demandé
            if (this.config.audioExtraction) {
                job.status = 'audio_extraction';
                job.progress = 30;
                this.emit('analysisProgress', job);

                const audioPath = await this.extractAudio(videoPath, jobId);
                job.results.audioPath = audioPath;
                this.log(`🎵 Audio extrait: ${audioPath}`);
            }

            // Étape 4: Transcription si demandée
            if (this.config.transcription && job.results.audioPath) {
                job.status = 'transcription';
                job.progress = 50;
                this.emit('analysisProgress', job);

                const transcript = await this.transcribeAudio(job.results.audioPath, jobId);
                job.results.transcript = transcript;
                this.log(`📝 Transcription terminée: ${transcript.text.length} caractères`);
            }

            // Étape 5: Extraction de frames si demandée
            if (this.config.frameExtraction) {
                job.status = 'frame_extraction';
                job.progress = 70;
                this.emit('analysisProgress', job);

                const frames = await this.extractFrames(videoPath, jobId);
                job.results.frames = frames;
                this.log(`🖼️ ${frames.length} frames extraites`);
            }

            // Étape 6: Analyse des scènes si demandée
            if (this.config.sceneAnalysis && job.results.frames) {
                job.status = 'scene_analysis';
                job.progress = 85;
                this.emit('analysisProgress', job);

                const sceneAnalysis = await this.analyzeScenes(job.results.frames, jobId);
                job.results.sceneAnalysis = sceneAnalysis;
                this.log(`🎭 Analyse de scènes terminée: ${sceneAnalysis.scenes.length} scènes`);
            }

            // Étape 7: Intégration à la mémoire thermique
            if (this.config.memoryIntegration) {
                job.status = 'memory_integration';
                job.progress = 95;
                this.emit('analysisProgress', job);

                await this.integrateToMemory(job.results, jobId);
                this.log(`🧠 Intégration à la mémoire thermique terminée`);
            }

            // Finalisation
            job.status = 'completed';
            job.progress = 100;
            job.endTime = Date.now();
            job.duration = job.endTime - job.startTime;

            this.state.stats.videosAnalyzed++;
            this.state.stats.totalDuration += metadata.duration;
            if (job.results.frames) {
                this.state.stats.framesExtracted += job.results.frames.length;
            }

            this.state.completedAnalyses.push(job);
            this.state.currentJobs.delete(jobId);

            this.emit('analysisCompleted', job);
            this.log(`✅ Analyse terminée: ${job.duration}ms`);

            return job.results;

        } catch (error) {
            job.status = 'error';
            job.error = error.message;
            job.endTime = Date.now();

            this.state.currentJobs.delete(jobId);
            this.emit('analysisError', { jobId, error: error.message });
            this.log(`❌ Erreur analyse: ${error.message}`);

            throw error;
        }
    }

    /**
     * Récupère les métadonnées d'une vidéo YouTube
     */
    async getVideoMetadata(url) {
        const command = `yt-dlp --dump-json "${url}"`;
        const output = await this.execCommand(command);
        const metadata = JSON.parse(output);

        return {
            id: metadata.id,
            title: metadata.title,
            description: metadata.description,
            duration: metadata.duration,
            uploader: metadata.uploader,
            upload_date: metadata.upload_date,
            view_count: metadata.view_count,
            like_count: metadata.like_count,
            thumbnail: metadata.thumbnail,
            categories: metadata.categories || [],
            tags: metadata.tags || []
        };
    }

    /**
     * Télécharge une vidéo YouTube
     */
    async downloadVideo(url, jobId) {
        const outputPath = path.join(this.config.tempDir, `${jobId}.%(ext)s`);
        const command = `yt-dlp -f "${this.config.quality}" -o "${outputPath}" "${url}"`;

        await this.execCommand(command);

        // Trouver le fichier téléchargé
        const files = fs.readdirSync(this.config.tempDir);
        const videoFile = files.find(file => file.startsWith(jobId));

        if (!videoFile) {
            throw new Error('Fichier vidéo non trouvé après téléchargement');
        }

        return path.join(this.config.tempDir, videoFile);
    }

    /**
     * Extrait l'audio d'une vidéo
     */
    async extractAudio(videoPath, jobId) {
        const audioPath = path.join(this.config.outputDir, 'audio', `${jobId}.wav`);
        const command = `ffmpeg -i "${videoPath}" -vn -acodec pcm_s16le -ar 16000 -ac 1 "${audioPath}"`;

        await this.execCommand(command);
        return audioPath;
    }

    /**
     * Transcrit un fichier audio
     */
    async transcribeAudio(audioPath, jobId) {
        this.log('🎤 Démarrage de la transcription...');

        // Créer le script Python pour Whisper
        const transcriptScript = this.createTranscriptionScript();
        const scriptPath = path.join(this.config.tempDir, `transcribe_${jobId}.py`);
        fs.writeFileSync(scriptPath, transcriptScript);

        try {
            const config = {
                audioPath: audioPath,
                model: 'base',
                language: 'fr'
            };

            const result = await this.execPythonScript(scriptPath, JSON.stringify(config));
            const transcription = JSON.parse(result);

            // Sauvegarder la transcription
            const transcriptPath = path.join(this.config.outputDir, 'transcripts', `${jobId}.json`);
            fs.writeFileSync(transcriptPath, JSON.stringify(transcription, null, 2));

            // Nettoyer le script temporaire
            fs.unlinkSync(scriptPath);

            return transcription;
        } catch (error) {
            fs.unlinkSync(scriptPath);
            throw error;
        }
    }

    /**
     * Crée le script Python pour la transcription
     */
    createTranscriptionScript() {
        return `#!/usr/bin/env python3
import whisper
import json
import sys

def transcribe_audio(config):
    try:
        # Charger le modèle Whisper
        model = whisper.load_model(config.get('model', 'base'))

        # Transcrire l'audio
        result = model.transcribe(
            config['audioPath'],
            language=config.get('language', 'fr'),
            verbose=False
        )

        return {
            'success': True,
            'text': result['text'],
            'segments': result['segments'],
            'language': result['language']
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    config_json = sys.stdin.read()
    config = json.loads(config_json)
    result = transcribe_audio(config)
    print(json.dumps(result))
`;
    }

    /**
     * Extrait des frames d'une vidéo
     */
    async extractFrames(videoPath, jobId) {
        const framesDir = path.join(this.config.outputDir, 'frames', jobId);
        if (!fs.existsSync(framesDir)) {
            fs.mkdirSync(framesDir, { recursive: true });
        }

        // Extraire une frame toutes les 10 secondes
        const command = `ffmpeg -i "${videoPath}" -vf fps=1/10 "${framesDir}/frame_%04d.jpg"`;
        await this.execCommand(command);

        // Lister les frames extraites
        const frames = fs.readdirSync(framesDir)
            .filter(file => file.endsWith('.jpg'))
            .map(file => path.join(framesDir, file));

        return frames;
    }

    /**
     * Analyse les scènes d'une vidéo
     */
    async analyzeScenes(frames, jobId) {
        this.log('🎭 Analyse des scènes...');

        // Simulation d'analyse de scènes
        // Dans une implémentation réelle, on utiliserait des modèles de vision par ordinateur
        const scenes = [];

        for (let i = 0; i < frames.length; i += 5) {
            const sceneFrames = frames.slice(i, i + 5);
            scenes.push({
                id: `scene_${i / 5 + 1}`,
                startFrame: i,
                endFrame: Math.min(i + 4, frames.length - 1),
                frames: sceneFrames,
                description: `Scène ${i / 5 + 1}`,
                objects: ['personne', 'bureau', 'ordinateur'], // Simulation
                emotions: ['neutre'], // Simulation
                concepts: ['travail', 'technologie'] // Simulation
            });
        }

        return {
            totalScenes: scenes.length,
            scenes: scenes,
            analysisTime: Date.now()
        };
    }

    /**
     * Intègre les résultats à la mémoire thermique
     */
    async integrateToMemory(results, jobId) {
        if (!global.thermalMemory) {
            this.log('⚠️ Mémoire thermique non disponible');
            return;
        }

        this.log('🧠 Intégration à la mémoire thermique...');

        // Ajouter les métadonnées de la vidéo
        if (results.metadata) {
            global.thermalMemory.addInformation({
                content: `Vidéo YouTube analysée: "${results.metadata.title}" par ${results.metadata.uploader}`,
                source: 'youtube_analyzer',
                importance: 0.7,
                tags: ['youtube', 'video', 'apprentissage', ...(results.metadata.tags || [])],
                category: 'learning_content'
            });
        }

        // Ajouter la transcription
        if (results.transcript && results.transcript.success) {
            global.thermalMemory.addInformation({
                content: `Transcription vidéo: ${results.transcript.text}`,
                source: 'youtube_transcription',
                importance: 0.8,
                tags: ['transcription', 'apprentissage', 'contenu'],
                category: 'knowledge'
            });

            this.state.stats.conceptsLearned++;
        }

        // Ajouter l'analyse de scènes
        if (results.sceneAnalysis) {
            for (const scene of results.sceneAnalysis.scenes) {
                global.thermalMemory.addInformation({
                    content: `Scène vidéo: ${scene.description} - Objets: ${scene.objects.join(', ')} - Concepts: ${scene.concepts.join(', ')}`,
                    source: 'youtube_scene_analysis',
                    importance: 0.6,
                    tags: ['scene', 'vision', ...scene.concepts],
                    category: 'visual_learning'
                });
            }
        }

        this.log('✅ Intégration mémoire terminée');
    }

    /**
     * Exécute une commande système
     */
    execCommand(command) {
        return new Promise((resolve, reject) => {
            const { exec } = require('child_process');
            exec(command, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout.trim());
                }
            });
        });
    }

    /**
     * Exécute un script Python
     */
    execPythonScript(scriptPath, inputData = '') {
        return new Promise((resolve, reject) => {
            // Utiliser l'environnement virtuel Python
            const pythonPath = path.join(__dirname, 'venv_camera', 'bin', 'python');
            const python = spawn(pythonPath, [scriptPath]);
            let output = '';
            let errorOutput = '';

            python.stdout.on('data', (data) => {
                output += data.toString();
            });

            python.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            python.on('close', (code) => {
                if (code === 0) {
                    resolve(output.trim());
                } else {
                    reject(new Error(`Script Python échoué: ${errorOutput}`));
                }
            });

            if (inputData) {
                python.stdin.write(inputData);
            }
            python.stdin.end();
        });
    }

    /**
     * Obtient les statistiques du système
     */
    getStats() {
        const uptime = Date.now() - this.state.stats.startTime;

        return {
            isActive: this.state.isActive,
            uptime: Math.floor(uptime / 1000),
            videosAnalyzed: this.state.stats.videosAnalyzed,
            totalDuration: this.state.stats.totalDuration,
            framesExtracted: this.state.stats.framesExtracted,
            conceptsLearned: this.state.stats.conceptsLearned,
            currentJobs: this.state.currentJobs.size,
            completedAnalyses: this.state.completedAnalyses.length
        };
    }

    /**
     * Arrête le système d'analyse
     */
    async stop() {
        this.log('⏹️ Arrêt du système d\'analyse...');

        this.state.isActive = false;

        // Arrêter tous les jobs en cours
        for (const [jobId, job] of this.state.currentJobs) {
            job.status = 'cancelled';
            this.emit('analysisError', { jobId, error: 'Système arrêté' });
        }
        this.state.currentJobs.clear();

        this.emit('analyzerStopped');
        this.log('✅ Système d\'analyse arrêté');
    }

    /**
     * Log avec timestamp
     */
    log(message) {
        if (this.config.debug) {
            const timestamp = new Date().toISOString();
            console.log(`[${timestamp}] [YouTubeAnalyzer] ${message}`);
        }
    }
}

module.exports = YouTubeVideoAnalyzer;
