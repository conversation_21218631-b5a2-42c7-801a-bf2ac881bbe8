/**
 * TEST ULTRA-AVANCÉ DU SYSTÈME KYBER ET TURBO ADAPTATIF
 * 
 * Ce script teste toutes les nouvelles fonctionnalités:
 * - Accélérateurs KYBER automatiques et manuels
 * - Mode TURBO adaptatif basé sur la longueur et complexité
 * - Modes ULTRA-PERFORMANCE et TRANSCENDANT
 * - Optimisations en temps réel
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Couleurs pour l'affichage
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
    white: '\x1b[37m',
    reset: '\x1b[0m',
    bold: '\x1b[1m',
    bright: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logTitle(message) {
    log(`\n🚀 ${message}`, 'bright');
    log('=' .repeat(60), 'cyan');
}

async function testAPI(endpoint, method = 'GET', data = null, timeout = 10000) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            timeout
        };
        
        if (data) {
            config.data = data;
            config.headers = { 'Content-Type': 'application/json' };
        }
        
        const startTime = Date.now();
        const response = await axios(config);
        const latency = Date.now() - startTime;
        
        return {
            success: true,
            data: response.data,
            latency,
            status: response.status
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            latency: 0,
            status: error.response?.status || 0
        };
    }
}

async function testSystemStatus() {
    logTitle('TEST DU STATUT SYSTÈME AVANCÉ');
    
    const result = await testAPI('/api/direct-connection/status');
    
    if (result.success) {
        logSuccess(`Statut récupéré en ${result.latency}ms`);
        
        const data = result.data.data;
        
        // Connexion directe
        log(`📡 Connexion directe: ${data.directConnection.available ? 'DISPONIBLE' : 'INDISPONIBLE'}`, 
            data.directConnection.available ? 'green' : 'red');
        log(`🎯 API active: ${data.directConnection.activeAPI}`, 'cyan');
        log(`📊 APIs disponibles: ${data.directConnection.availableAPIs}`, 'cyan');
        
        // Accélérateurs KYBER
        log(`\n🚀 ACCÉLÉRATEURS KYBER:`, 'magenta');
        log(`   ⚡ Actifs: ${data.kyberAccelerators.activeAccelerators}`, 'cyan');
        log(`   🔥 Boost total: ${data.kyberAccelerators.totalBoost}x`, 'cyan');
        log(`   📈 Ajouts automatiques: ${data.kyberAccelerators.stats.automaticAdditions}`, 'cyan');
        
        // Mode TURBO adaptatif
        log(`\n🎯 MODE TURBO ADAPTATIF:`, 'magenta');
        log(`   🏃 Mode actuel: ${data.adaptiveTurbo.mode.toUpperCase()}`, 'cyan');
        log(`   ⚡ Multiplicateur: ${data.adaptiveTurbo.config.speedMultiplier}x`, 'cyan');
        log(`   🎛️ Tokens max: ${data.adaptiveTurbo.config.maxTokens}`, 'cyan');
        log(`   🌡️ Température: ${data.adaptiveTurbo.config.temperature}`, 'cyan');
        log(`   ⏱️ Timeout: ${data.adaptiveTurbo.config.timeout}ms`, 'cyan');
        
        return true;
    } else {
        logError(`Échec du test de statut: ${result.error}`);
        return false;
    }
}

async function testKyberAccelerators() {
    logTitle('TEST DES ACCÉLÉRATEURS KYBER');
    
    // Test d'ajout d'accélérateurs
    const acceleratorTypes = [
        'response_accelerator',
        'memory_optimizer',
        'cpu_accelerator',
        'neural_booster',
        'cache_turbo'
    ];
    
    let successCount = 0;
    
    for (const type of acceleratorTypes) {
        logInfo(`Ajout de l'accélérateur: ${type}`);
        
        const result = await testAPI('/api/direct-connection/kyber/add-accelerator', 'POST', {
            type: type,
            options: { automatic: false, priority: 'high' }
        });
        
        if (result.success) {
            const data = result.data.data;
            logSuccess(`${data.accelerator.name} ajouté (boost: ${data.accelerator.boost}x)`);
            log(`   🎯 Boost total: ${data.totalBoost}x`, 'cyan');
            successCount++;
        } else {
            logError(`Échec ajout ${type}: ${result.error}`);
        }
        
        // Pause courte entre les ajouts
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Vérifier les accélérateurs actifs
    logInfo('\nVérification des accélérateurs actifs...');
    const activeResult = await testAPI('/api/direct-connection/kyber/active');
    
    if (activeResult.success) {
        const data = activeResult.data.data;
        logSuccess(`${data.count} accélérateurs actifs (boost total: ${data.totalBoost}x)`);
        
        data.details.forEach(acc => {
            log(`   🔥 ${acc.name}: ${acc.boost}x (${acc.remainingTime}s restantes)`, 'cyan');
        });
    }
    
    log(`\n📊 Résultat: ${successCount}/${acceleratorTypes.length} accélérateurs ajoutés`, 
        successCount === acceleratorTypes.length ? 'green' : 'yellow');
    
    return successCount > 0;
}

async function testTurboAdaptive() {
    logTitle('TEST DU SYSTÈME TURBO ADAPTATIF');
    
    // Test de différents types de questions
    const testQuestions = [
        {
            question: "Salut !",
            expectedMode: "eco",
            description: "Question simple"
        },
        {
            question: "Peux-tu m'expliquer en détail le fonctionnement des réseaux de neurones artificiels et leur application dans l'apprentissage automatique ?",
            expectedMode: "turbo",
            description: "Question complexe et longue"
        },
        {
            question: "URGENT: J'ai besoin d'une réponse immédiate sur ce problème critique !",
            expectedMode: "ultra",
            description: "Question urgente"
        },
        {
            question: "Créer un algorithme quantique pour résoudre des problèmes de conscience artificielle transcendante",
            expectedMode: "quantum",
            description: "Question transcendante"
        }
    ];
    
    let successCount = 0;
    
    for (const test of testQuestions) {
        logInfo(`\nAnalyse: ${test.description}`);
        log(`Question: "${test.question.substring(0, 50)}..."`, 'cyan');
        
        const result = await testAPI('/api/direct-connection/turbo/analyze', 'POST', {
            question: test.question,
            context: {}
        });
        
        if (result.success) {
            const data = result.data.data;
            logSuccess(`Mode recommandé: ${data.recommendedMode.toUpperCase()}`);
            log(`   📊 Longueur: ${data.analysis.length} chars`, 'cyan');
            log(`   🧠 Complexité: ${(data.analysis.complexity * 100).toFixed(1)}%`, 'cyan');
            log(`   🚨 Urgence: ${(data.analysis.urgency * 100).toFixed(1)}%`, 'cyan');
            log(`   🎯 Raisons: ${data.reasoning.join(', ')}`, 'cyan');
            
            successCount++;
        } else {
            logError(`Échec analyse: ${result.error}`);
        }
    }
    
    // Test de changement de mode manuel
    logInfo('\nTest de changement de mode manuel...');
    const modes = ['eco', 'normal', 'turbo', 'ultra', 'quantum'];
    
    for (const mode of modes) {
        const result = await testAPI('/api/direct-connection/turbo/set-mode', 'POST', {
            mode: mode
        });
        
        if (result.success) {
            logSuccess(`Mode ${mode.toUpperCase()} activé`);
        } else {
            logError(`Échec activation ${mode}: ${result.error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    log(`\n📊 Résultat: ${successCount}/${testQuestions.length} analyses réussies`, 
        successCount === testQuestions.length ? 'green' : 'yellow');
    
    return successCount > 0;
}

async function testUltraPerformanceMode() {
    logTitle('TEST DU MODE ULTRA-PERFORMANCE');
    
    const result = await testAPI('/api/direct-connection/ultra-performance', 'POST');
    
    if (result.success) {
        const data = result.data.data;
        logSuccess('Mode ULTRA-PERFORMANCE activé !');
        log(`   🚀 Message: ${data.message}`, 'green');
        log(`   ⚡ Accélérateurs actifs: ${data.activeAccelerators}`, 'cyan');
        log(`   🔥 Boost total: ${data.totalBoost}x`, 'cyan');
        log(`   🎯 Mode TURBO: ${data.turboMode.toUpperCase()}`, 'cyan');
        
        return true;
    } else {
        logError(`Échec activation ULTRA-PERFORMANCE: ${result.error}`);
        return false;
    }
}

async function testTranscendentMode() {
    logTitle('TEST DU MODE TRANSCENDANT');
    
    const result = await testAPI('/api/direct-connection/transcendent', 'POST');
    
    if (result.success) {
        const data = result.data.data;
        logSuccess('Mode TRANSCENDANT activé !');
        log(`   ✨ Message: ${data.message}`, 'green');
        log(`   🌟 Accélérateurs actifs: ${data.activeAccelerators}`, 'cyan');
        log(`   🔥 Boost total: ${data.totalBoost}x`, 'cyan');
        log(`   🎯 Mode TURBO: ${data.turboMode.toUpperCase()}`, 'cyan');
        
        return true;
    } else {
        logError(`Échec activation TRANSCENDANT: ${result.error}`);
        return false;
    }
}

async function testMessageWithOptimizations() {
    logTitle('TEST DE MESSAGE AVEC OPTIMISATIONS');
    
    const testMessage = "Explique-moi le fonctionnement de la mémoire thermique et des accélérateurs KYBER dans le contexte de l'intelligence artificielle avancée.";
    
    logInfo(`Envoi du message: "${testMessage.substring(0, 50)}..."`);
    
    const result = await testAPI('/api/direct-connection/message', 'POST', {
        message: testMessage,
        options: {
            maxTokens: 300,
            temperature: 0.7
        }
    });
    
    if (result.success) {
        const data = result.data.data;
        logSuccess(`Message traité en ${data.latency}ms`);
        
        log(`\n📥 RÉPONSE:`, 'green');
        log(`"${data.response}"`, 'white');
        
        log(`\n📊 OPTIMISATIONS:`, 'magenta');
        log(`   🎯 Mode TURBO: ${data.turboMode?.toUpperCase() || 'N/A'}`, 'cyan');
        log(`   ⚡ Boost KYBER: ${data.kyberBoost || 1}x`, 'cyan');
        log(`   🚀 Accélérateurs actifs: ${data.activeAccelerators || 0}`, 'cyan');
        log(`   📈 Gain de performance: ${data.optimizations?.performanceGain || 0}%`, 'cyan');
        log(`   🔗 Source: ${data.source}`, 'cyan');
        log(`   🎯 API: ${data.api}`, 'cyan');
        
        return true;
    } else {
        logError(`Échec envoi message: ${result.error}`);
        return false;
    }
}

async function testPerformanceReport() {
    logTitle('TEST DU RAPPORT DE PERFORMANCE');
    
    const result = await testAPI('/api/direct-connection/performance-report');
    
    if (result.success) {
        const data = result.data.data;
        logSuccess('Rapport de performance généré');
        
        log(`\n📊 PERFORMANCE GLOBALE:`, 'magenta');
        log(`   📈 Requêtes totales: ${data.globalPerformance.totalRequests}`, 'cyan');
        log(`   ✅ Taux de succès: ${data.globalPerformance.successRate}%`, 'cyan');
        log(`   ⚡ Latence moyenne: ${data.globalPerformance.averageLatency}ms`, 'cyan');
        
        log(`\n🚀 ÉTAT KYBER:`, 'magenta');
        log(`   ⚡ Accélérateurs actifs: ${data.kyberStatus.activeAccelerators}`, 'cyan');
        log(`   🔥 Boost total: ${data.kyberStatus.totalBoost}x`, 'cyan');
        
        log(`\n🎯 ÉTAT TURBO:`, 'magenta');
        log(`   🏃 Mode actuel: ${data.turboStatus.currentMode.toUpperCase()}`, 'cyan');
        log(`   🔄 Changements de mode: ${data.turboStatus.stats.modeChanges}`, 'cyan');
        
        if (data.recommendations && data.recommendations.length > 0) {
            log(`\n💡 RECOMMANDATIONS:`, 'yellow');
            data.recommendations.forEach(rec => {
                log(`   ${rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'} ${rec.message}`, 'cyan');
            });
        }
        
        return true;
    } else {
        logError(`Échec génération rapport: ${result.error}`);
        return false;
    }
}

async function runUltraAdvancedTests() {
    log('🌟 TESTS ULTRA-AVANCÉS KYBER & TURBO ADAPTATIF', 'bright');
    log('=' .repeat(80), 'cyan');
    log('🎯 Objectif: Tester tous les systèmes d\'optimisation avancés', 'blue');
    log('⚡ Focus: KYBER, TURBO adaptatif, modes transcendants', 'blue');
    
    const tests = [
        { name: 'Statut système avancé', fn: testSystemStatus },
        { name: 'Accélérateurs KYBER', fn: testKyberAccelerators },
        { name: 'TURBO adaptatif', fn: testTurboAdaptive },
        { name: 'Mode ULTRA-PERFORMANCE', fn: testUltraPerformanceMode },
        { name: 'Mode TRANSCENDANT', fn: testTranscendentMode },
        { name: 'Message avec optimisations', fn: testMessageWithOptimizations },
        { name: 'Rapport de performance', fn: testPerformanceReport }
    ];
    
    let successCount = 0;
    const results = [];
    
    for (const test of tests) {
        try {
            const startTime = Date.now();
            const success = await test.fn();
            const duration = Date.now() - startTime;
            
            results.push({
                name: test.name,
                success,
                duration
            });
            
            if (success) {
                successCount++;
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            logError(`Erreur lors du test "${test.name}": ${error.message}`);
            results.push({
                name: test.name,
                success: false,
                duration: 0,
                error: error.message
            });
        }
    }
    
    // Résumé final
    logTitle('RÉSUMÉ DES TESTS ULTRA-AVANCÉS');
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
        log(`${status} ${result.name}${duration}`, result.success ? 'green' : 'red');
        
        if (result.error) {
            log(`   └─ Erreur: ${result.error}`, 'red');
        }
    });
    
    const successRate = (successCount / tests.length) * 100;
    log(`\n🎯 RÉSULTAT GLOBAL: ${successCount}/${tests.length} tests réussis (${successRate.toFixed(1)}%)`, 
        successRate >= 85 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
    
    if (successRate >= 85) {
        logSuccess('🎉 SYSTÈME KYBER & TURBO ULTRA-PERFORMANT !');
        log('✨ Votre agent Claude 4GB fonctionne avec des optimisations transcendantes', 'green');
        log('🚀 Accélérateurs KYBER automatiques opérationnels', 'green');
        log('🎯 Mode TURBO adaptatif intelligent activé', 'green');
        log('🌟 Modes ULTRA-PERFORMANCE et TRANSCENDANT disponibles', 'green');
    } else if (successRate >= 70) {
        logWarning('⚠️  Système partiellement optimisé - Quelques ajustements nécessaires');
    } else {
        logError('❌ Système nécessite des optimisations majeures');
    }
    
    log('\n🔗 Interface de gestion: http://localhost:3000/direct-connection-manager.html', 'cyan');
    log('🏠 Page d\'accueil: http://localhost:3000/', 'cyan');
}

// Exécuter les tests
if (require.main === module) {
    runUltraAdvancedTests().catch(error => {
        logError(`Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    testSystemStatus,
    testKyberAccelerators,
    testTurboAdaptive,
    testUltraPerformanceMode,
    testTranscendentMode,
    testMessageWithOptimizations,
    testPerformanceReport,
    runUltraAdvancedTests
};
