/**
 * Script de correction automatique des problèmes d'interface Louna
 * Corrige les boutons non connectés, les APIs manquantes, etc.
 */

const fs = require('fs');
const path = require('path');

class LounaInterfaceFixer {
    constructor() {
        this.publicDir = path.join(__dirname, 'public');
        this.fixes = [];
        this.errors = [];
    }

    async fixAllIssues() {
        console.log('🔧 DÉMARRAGE DE LA CORRECTION AUTOMATIQUE');
        console.log('=' .repeat(50));

        try {
            // 1. Corriger les boutons manquants
            await this.fixMissingButtons();
            
            // 2. Corriger les liens de navigation
            await this.fixNavigationLinks();
            
            // 3. Corriger les permissions média
            await this.fixMediaPermissions();
            
            // 4. Corriger les connexions API
            await this.fixAPIConnections();
            
            // 5. Ajouter les fonctionnalités manquantes
            await this.addMissingFeatures();
            
            // 6. Générer le rapport de correction
            this.generateFixReport();
            
        } catch (error) {
            console.error('❌ Erreur lors de la correction:', error);
        }
    }

    async fixMissingButtons() {
        console.log('\n🔘 CORRECTION DES BOUTONS MANQUANTS');
        console.log('-'.repeat(30));

        const chatFile = path.join(this.publicDir, 'chat.html');
        
        if (fs.existsSync(chatFile)) {
            let content = fs.readFileSync(chatFile, 'utf8');
            let modified = false;

            // Vérifier et ajouter le bouton micro s'il manque
            if (!content.includes('microphone') && !content.includes('mic-btn')) {
                const micButton = `
                <button id="micButton" class="chat-option-btn" title="Activer/Désactiver le microphone">
                    <i class="fas fa-microphone"></i>
                    <span>Micro</span>
                </button>`;
                
                // Insérer après les autres boutons d'options
                content = content.replace(
                    /<div class="chat-options">/,
                    `<div class="chat-options">${micButton}`
                );
                modified = true;
                this.fixes.push('Bouton microphone ajouté au chat');
            }

            // Vérifier et ajouter le bouton caméra s'il manque
            if (!content.includes('camera') && !content.includes('video-btn')) {
                const cameraButton = `
                <button id="cameraButton" class="chat-option-btn" title="Activer/Désactiver la caméra">
                    <i class="fas fa-video"></i>
                    <span>Caméra</span>
                </button>`;
                
                content = content.replace(
                    /<button id="micButton"/,
                    `${cameraButton}\n                <button id="micButton"`
                );
                modified = true;
                this.fixes.push('Bouton caméra ajouté au chat');
            }

            // Ajouter les gestionnaires d'événements JavaScript
            if (!content.includes('micButton.addEventListener')) {
                const jsCode = `
    <script>
        // Gestionnaires pour les boutons média
        document.addEventListener('DOMContentLoaded', function() {
            const micButton = document.getElementById('micButton');
            const cameraButton = document.getElementById('cameraButton');
            
            if (micButton) {
                micButton.addEventListener('click', function() {
                    toggleMicrophone();
                });
            }
            
            if (cameraButton) {
                cameraButton.addEventListener('click', function() {
                    toggleCamera();
                });
            }
        });

        function toggleMicrophone() {
            console.log('🎤 Basculement du microphone');
            // Logique de basculement du microphone
            const button = document.getElementById('micButton');
            const icon = button.querySelector('i');
            
            if (icon.classList.contains('fa-microphone')) {
                icon.classList.remove('fa-microphone');
                icon.classList.add('fa-microphone-slash');
                button.classList.add('active');
            } else {
                icon.classList.remove('fa-microphone-slash');
                icon.classList.add('fa-microphone');
                button.classList.remove('active');
            }
        }

        function toggleCamera() {
            console.log('📹 Basculement de la caméra');
            // Logique de basculement de la caméra
            const button = document.getElementById('cameraButton');
            const icon = button.querySelector('i');
            
            if (icon.classList.contains('fa-video')) {
                icon.classList.remove('fa-video');
                icon.classList.add('fa-video-slash');
                button.classList.add('active');
            } else {
                icon.classList.remove('fa-video-slash');
                icon.classList.add('fa-video');
                button.classList.remove('active');
            }
        }
    </script>`;
                
                content = content.replace('</body>', `${jsCode}\n</body>`);
                modified = true;
                this.fixes.push('Gestionnaires d\'événements ajoutés pour les boutons média');
            }

            if (modified) {
                fs.writeFileSync(chatFile, content);
                console.log('  ✅ Fichier chat.html corrigé');
            } else {
                console.log('  ℹ️ Aucune correction nécessaire pour chat.html');
            }
        }
    }

    async fixNavigationLinks() {
        console.log('\n🧭 CORRECTION DES LIENS DE NAVIGATION');
        console.log('-'.repeat(30));

        const files = fs.readdirSync(this.publicDir).filter(file => file.endsWith('.html'));
        
        for (const file of files) {
            const filePath = path.join(this.publicDir, file);
            let content = fs.readFileSync(filePath, 'utf8');
            let modified = false;

            // Ajouter un menu de navigation s'il manque
            if (!content.includes('nav') && !content.includes('menu') && file !== 'index.html') {
                const navigation = `
    <nav class="main-navigation" style="background: linear-gradient(135deg, #e91e63, #ad1457); padding: 10px; margin-bottom: 20px;">
        <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
            <a href="/" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🏠 Accueil</a>
            <a href="/chat.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">💬 Chat</a>
            <a href="/futuristic-interface.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🧠 Mémoire</a>
            <a href="/brain-visualization.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🎯 3D</a>
            <a href="/qi-neuron-monitor.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">☯️ Qi</a>
            <a href="/kyber-dashboard.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">⚡ Kyber</a>
            <a href="/advanced-course-monitor.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🎓 Cours</a>
            <a href="/agents.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🤖 Agents</a>
            <a href="/training.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">📚 Formation</a>
            <a href="/audio-video-controls.html" style="color: white; text-decoration: none; padding: 8px 12px; border-radius: 5px; transition: background 0.3s;">🎵 A/V</a>
        </div>
    </nav>`;

                content = content.replace('<body>', `<body>${navigation}`);
                modified = true;
            }

            if (modified) {
                fs.writeFileSync(filePath, content);
                this.fixes.push(`Navigation ajoutée à ${file}`);
                console.log(`  ✅ Navigation ajoutée à ${file}`);
            }
        }
    }

    async fixMediaPermissions() {
        console.log('\n🎵 CORRECTION DES PERMISSIONS MÉDIA');
        console.log('-'.repeat(30));

        const audioVideoFile = path.join(this.publicDir, 'audio-video-controls.html');
        
        if (fs.existsSync(audioVideoFile)) {
            let content = fs.readFileSync(audioVideoFile, 'utf8');
            let modified = false;

            // Ajouter une fonction de demande de permissions automatique
            if (!content.includes('requestPermissions()')) {
                const permissionScript = `
    <script>
        // Demande automatique de permissions au chargement
        window.addEventListener('load', async function() {
            try {
                // Vérifier si les permissions sont déjà accordées
                const permissions = await Promise.all([
                    navigator.permissions.query({ name: 'microphone' }),
                    navigator.permissions.query({ name: 'camera' })
                ]);
                
                const micPermission = permissions[0];
                const cameraPermission = permissions[1];
                
                if (micPermission.state === 'prompt' || cameraPermission.state === 'prompt') {
                    // Afficher la demande de permissions
                    document.getElementById('permissionRequest').style.display = 'block';
                } else if (micPermission.state === 'granted' && cameraPermission.state === 'granted') {
                    console.log('✅ Permissions déjà accordées');
                    document.getElementById('systemStatus').className = 'status-indicator active';
                    document.getElementById('systemStatusText').textContent = 'Système prêt';
                }
            } catch (error) {
                console.log('ℹ️ Vérification des permissions non supportée sur ce navigateur');
            }
        });
    </script>`;

                content = content.replace('</body>', `${permissionScript}\n</body>`);
                modified = true;
                this.fixes.push('Demande automatique de permissions ajoutée');
            }

            if (modified) {
                fs.writeFileSync(audioVideoFile, content);
                console.log('  ✅ Permissions média corrigées');
            }
        }
    }

    async fixAPIConnections() {
        console.log('\n🔌 CORRECTION DES CONNEXIONS API');
        console.log('-'.repeat(30));

        // Créer un fichier de test API si il n'existe pas
        const apiTestFile = path.join(this.publicDir, 'api-test.html');
        
        if (!fs.existsSync(apiTestFile)) {
            const apiTestContent = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - Louna</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.2); border: 1px solid #f44336; }
        button { background: #e91e63; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔌 Test des APIs Louna</h1>
    <button onclick="testAllAPIs()">Tester toutes les APIs</button>
    <div id="results"></div>
    
    <script>
        async function testAllAPIs() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Test en cours...</p>';
            
            const apis = [
                { name: 'Mémoire Thermique', url: '/api/thermal/memory/stats' },
                { name: 'Accélérateurs', url: '/api/thermal/accelerators/stats' },
                { name: 'Agents', url: '/api/agents/list' },
                { name: 'Formation', url: '/api/training/state' },
                { name: 'Performance', url: '/api/performance/stats' },
                { name: 'Cerveau', url: '/api/brain/activity' }
            ];
            
            results.innerHTML = '';
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    const data = await response.json();
                    
                    results.innerHTML += \`<div class="test-result success">
                        ✅ \${api.name}: OK (Status: \${response.status})
                    </div>\`;
                } catch (error) {
                    results.innerHTML += \`<div class="test-result error">
                        ❌ \${api.name}: Erreur - \${error.message}
                    </div>\`;
                }
            }
        }
    </script>
</body>
</html>`;

            fs.writeFileSync(apiTestFile, apiTestContent);
            this.fixes.push('Page de test API créée');
            console.log('  ✅ Page de test API créée');
        }
    }

    async addMissingFeatures() {
        console.log('\n✨ AJOUT DES FONCTIONNALITÉS MANQUANTES');
        console.log('-'.repeat(30));

        // Ajouter un bouton de retour à l'accueil sur toutes les pages
        const files = fs.readdirSync(this.publicDir).filter(file => file.endsWith('.html'));
        
        for (const file of files) {
            if (file === 'index.html') continue;
            
            const filePath = path.join(this.publicDir, file);
            let content = fs.readFileSync(filePath, 'utf8');
            let modified = false;

            // Ajouter un bouton de retour flottant
            if (!content.includes('home-button') && !content.includes('back-to-home')) {
                const homeButton = `
    <div id="homeButton" style="position: fixed; top: 20px; left: 20px; z-index: 1000;">
        <a href="/" style="background: linear-gradient(135deg, #e91e63, #ad1457); color: white; padding: 10px 15px; border-radius: 25px; text-decoration: none; box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3); display: flex; align-items: center; gap: 8px; font-weight: bold; transition: all 0.3s ease;">
            <i class="fas fa-home"></i>
            <span>Accueil</span>
        </a>
    </div>`;

                content = content.replace('<body>', `<body>${homeButton}`);
                modified = true;
            }

            if (modified) {
                fs.writeFileSync(filePath, content);
                this.fixes.push(`Bouton d'accueil ajouté à ${file}`);
            }
        }

        console.log('  ✅ Boutons d\'accueil ajoutés');
    }

    generateFixReport() {
        console.log('\n📊 RAPPORT DE CORRECTION');
        console.log('='.repeat(50));
        
        console.log(`\n✅ CORRECTIONS APPLIQUÉES (${this.fixes.length}):`);
        this.fixes.forEach(fix => {
            console.log(`   • ${fix}`);
        });
        
        if (this.errors.length > 0) {
            console.log(`\n❌ ERREURS RENCONTRÉES (${this.errors.length}):`);
            this.errors.forEach(error => {
                console.log(`   • ${error}`);
            });
        }
        
        console.log('\n🎉 CORRECTION TERMINÉE !');
        console.log('\n📋 PROCHAINES ÉTAPES:');
        console.log('   1. Redémarrer le serveur Louna');
        console.log('   2. Tester les interfaces corrigées');
        console.log('   3. Vérifier les permissions média');
        console.log('   4. Valider les connexions API');
        console.log('\n🌐 Accéder aux interfaces:');
        console.log('   • Chat: http://localhost:3007/chat.html');
        console.log('   • Audio/Vidéo: http://localhost:3007/audio-video-controls.html');
        console.log('   • Test API: http://localhost:3007/api-test.html');
        console.log('   • Cours: http://localhost:3007/advanced-course-monitor.html');
    }
}

// Exécuter la correction
const fixer = new LounaInterfaceFixer();
fixer.fixAllIssues();
