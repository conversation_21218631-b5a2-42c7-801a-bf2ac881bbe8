/**
 * Script de formation poussée pour tester les réflexions internes
 * Utilise l'agent de formation pour générer des questions variées
 */

const axios = require('axios');

const SERVER_URL = 'http://localhost:3004';

// Questions de formation pour tester les réflexions internes
const questionsFormation = [
    {
        type: "géographique",
        question: "Quelle est la capitale du Japon ?",
        description: "Test de classification géographique et recherche de base"
    },
    {
        type: "technique_complexe", 
        question: "Explique-moi en détail comment fonctionne l'algorithme de backpropagation dans un réseau de neurones, avec les formules mathématiques",
        description: "Test de question technique complexe nécessitant des explications détaillées"
    },
    {
        type: "actualité",
        question: "Quelles sont les dernières avancées en intelligence artificielle en 2024 ?",
        description: "Test de déclenchement de recherche Internet pour informations récentes"
    },
    {
        type: "mémoire_personnelle",
        question: "Rappelle-toi que je suis un expert en IA, que je travaille chez Google, et que je préfère les explications techniques détaillées",
        description: "Test de stockage en mémoire thermique"
    },
    {
        type: "utilisation_mémoire",
        question: "Basé sur mon profil, peux-tu me recommander des ressources avancées en deep learning ?",
        description: "Test d'utilisation de la mémoire thermique précédente"
    },
    {
        type: "code_génération",
        question: "Écris-moi un code Python complet pour implémenter un réseau de neurones convolutionnel avec TensorFlow",
        description: "Test de génération de code technique"
    },
    {
        type: "philosophique",
        question: "Quelle est la nature de la conscience et peut-elle émerger dans une intelligence artificielle ?",
        description: "Test de question philosophique complexe"
    },
    {
        type: "multi_étapes",
        question: "Comment créer une startup en IA ? Donne-moi un plan détaillé étape par étape avec budget et timeline",
        description: "Test de question nécessitant une réponse structurée multi-étapes"
    },
    {
        type: "créatif",
        question: "Invente une histoire de science-fiction sur une IA qui développe des émotions",
        description: "Test de créativité et génération narrative"
    },
    {
        type: "analyse_comparative",
        question: "Compare les avantages et inconvénients de PyTorch vs TensorFlow pour un projet de recherche en IA",
        description: "Test d'analyse comparative technique"
    }
];

/**
 * Envoie une question et analyse les réflexions internes
 */
async function testerQuestion(questionData, index) {
    console.log(`\n🧪 TEST ${index + 1}/${questionsFormation.length}: ${questionData.type.toUpperCase()}`);
    console.log(`📝 Question: ${questionData.question}`);
    console.log(`🎯 Objectif: ${questionData.description}`);
    console.log('⏳ Envoi en cours...\n');

    try {
        const startTime = Date.now();
        
        const response = await axios.post(`${SERVER_URL}/api/chat/message`, {
            message: questionData.question,
            history: []
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        if (response.data.success) {
            console.log(`✅ RÉPONSE REÇUE (${duration}ms)`);
            console.log(`📊 Tokens: ${response.data.usage?.total_tokens || 'N/A'}`);
            
            // Analyser les réflexions internes
            const thoughts = response.data.internalThoughts;
            if (thoughts) {
                console.log('\n🧠 RÉFLEXIONS INTERNES CAPTURÉES:');
                
                // Type de question détecté
                if (thoughts.analysisSteps) {
                    thoughts.analysisSteps.forEach(step => {
                        console.log(`   🔍 ${step.step}: ${step.result}`);
                        console.log(`      💭 Raisonnement: ${step.reasoning}`);
                    });
                }
                
                // Mémoire thermique
                if (thoughts.memoryAnalysis) {
                    console.log(`   🧠 Mémoires utilisées: ${thoughts.memoryAnalysis.totalMemories}`);
                    if (thoughts.memoryAnalysis.memoriesUsed.length > 0) {
                        thoughts.memoryAnalysis.memoriesUsed.forEach(memory => {
                            console.log(`      📚 Mémoire ${memory.index}: ${memory.content.substring(0, 50)}...`);
                            console.log(`         ⏰ Âge: ${memory.age}, Importance: ${memory.importance}`);
                        });
                    }
                }
                
                // Recherche Internet
                if (thoughts.internetSearch) {
                    console.log(`   🌐 Recherche Internet: ${thoughts.internetSearch.needed ? 'OUI' : 'NON'}`);
                    console.log(`      📝 Raison: ${thoughts.internetSearch.reason}`);
                    if (thoughts.internetSearch.results) {
                        console.log(`      📊 Résultats: ${thoughts.internetSearch.results.substring(0, 100)}...`);
                    }
                }
                
                // Processus de réflexion
                if (thoughts.reasoning && thoughts.reasoning.length > 0) {
                    console.log('   💭 Processus de réflexion:');
                    thoughts.reasoning.forEach((reason, i) => {
                        console.log(`      ${i + 1}. ${reason}`);
                    });
                }
            }
            
            // Afficher un extrait de la réponse
            console.log(`\n📝 RÉPONSE (extrait): ${response.data.response.substring(0, 200)}...`);
            
        } else {
            console.log(`❌ ERREUR: ${response.data.error}`);
        }
        
    } catch (error) {
        console.log(`💥 ERREUR DE CONNEXION: ${error.message}`);
    }
    
    console.log('\n' + '='.repeat(80));
}

/**
 * Lance la formation complète
 */
async function lancerFormationPoussee() {
    console.log('🚀 FORMATION POUSSÉE - SYSTÈME DE RÉFLEXIONS INTERNES');
    console.log('🎯 Objectif: Tester tous les aspects du système de réflexions');
    console.log(`📊 Nombre de tests: ${questionsFormation.length}`);
    console.log('🤖 Agent: Claude 4GB avec réflexions internes');
    console.log('\n' + '='.repeat(80));

    // Vérifier que le serveur est accessible
    try {
        await axios.get(`${SERVER_URL}/`);
        console.log('✅ Serveur accessible');
    } catch (error) {
        console.log('❌ Serveur non accessible. Assurez-vous que l\'application est lancée.');
        return;
    }

    // Exécuter tous les tests
    for (let i = 0; i < questionsFormation.length; i++) {
        await testerQuestion(questionsFormation[i], i);
        
        // Pause entre les questions pour éviter la surcharge
        if (i < questionsFormation.length - 1) {
            console.log('⏳ Pause de 3 secondes...\n');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n🎉 FORMATION TERMINÉE !');
    console.log('📊 Résumé: Tous les types de questions ont été testés');
    console.log('🧠 Le système de réflexions internes a été entièrement validé');
}

// Lancer la formation
lancerFormationPoussee().catch(console.error);
