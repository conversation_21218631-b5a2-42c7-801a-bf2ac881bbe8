<!-- Interface de mémoire -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-brain card-icon"></i>
    <h2 class="card-title">Gestionnaire de Mémoire</h2>
  </div>
  
  <div style="padding: 20px;">
    <div class="grid-container">
      <div class="grid-item" style="grid-column: span 2;">
        <div class="memory-search" style="margin-bottom: 20px;">
          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <input type="text" placeholder="Rechercher dans la mémoire..." style="flex-grow: 1; padding: 10px 15px; border-radius: 5px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
            <button class="action-button">
              <i class="bi bi-search"></i> Rechercher
            </button>
          </div>
          
          <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone1" checked style="margin-right: 5px;">
              <label for="zone1" style="font-size: 14px;">Zone 1 (Instant)</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone2" checked style="margin-right: 5px;">
              <label for="zone2" style="font-size: 14px;">Zone 2 (Court terme)</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone3" checked style="margin-right: 5px;">
              <label for="zone3" style="font-size: 14px;">Zone 3 (Moyen)</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone4" checked style="margin-right: 5px;">
              <label for="zone4" style="font-size: 14px;">Zone 4 (Moyen terme)</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone5" checked style="margin-right: 5px;">
              <label for="zone5" style="font-size: 14px;">Zone 5 (Long terme)</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-right: 15px;">
              <input type="checkbox" id="zone6" checked style="margin-right: 5px;">
              <label for="zone6" style="font-size: 14px;">Zone 6 (Rêve)</label>
            </div>
          </div>
        </div>
        
        <div class="memory-results" style="margin-bottom: 20px;">
          <h3 style="margin-bottom: 15px;">Résultats de recherche</h3>
          
          <div class="memory-item" style="padding: 15px; border-radius: 5px; margin-bottom: 15px; background-color: rgba(255, 107, 107, 0.1); border-left: 4px solid var(--temp-hot);">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <div style="font-weight: bold;">Configuration des accélérateurs thermiques</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Zone 1 (48°C)</div>
            </div>
            <div style="margin-bottom: 10px;">Les accélérateurs thermiques ont été configurés pour optimiser le transfert de données entre les zones 1 et 2, avec un facteur d'accélération de x2.7.</div>
            <div style="font-size: 12px; color: var(--text-secondary);">Créé il y a 5 minutes</div>
          </div>
          
          <div class="memory-item" style="padding: 15px; border-radius: 5px; margin-bottom: 15px; background-color: rgba(255, 159, 67, 0.1); border-left: 4px solid var(--temp-warm);">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <div style="font-weight: bold;">Discussion sur les zones thermiques</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Zone 2 (35°C)</div>
            </div>
            <div style="margin-bottom: 10px;">Explication détaillée du fonctionnement des six zones thermiques et de leur rôle dans la gestion de la mémoire.</div>
            <div style="font-size: 12px; color: var(--text-secondary);">Créé il y a 1 heure</div>
          </div>
          
          <div class="memory-item" style="padding: 15px; border-radius: 5px; margin-bottom: 15px; background-color: rgba(29, 209, 161, 0.1); border-left: 4px solid var(--temp-medium);">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <div style="font-weight: bold;">Analyse des performances système</div>
              <div style="font-size: 12px; color: var(--text-secondary);">Zone 3 (20°C)</div>
            </div>
            <div style="margin-bottom: 10px;">Rapport d'analyse des performances du système, avec des recommandations pour l'optimisation des ressources.</div>
            <div style="font-size: 12px; color: var(--text-secondary);">Créé il y a 1 jour</div>
          </div>
        </div>
        
        <div class="memory-details" style="background-color: rgba(255, 255, 255, 0.05); padding: 15px; border-radius: 5px;">
          <h3 style="margin-bottom: 15px;">Détails de la mémoire sélectionnée</h3>
          
          <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 5px;">Configuration des accélérateurs thermiques</div>
            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 10px;">Zone 1 (48°C) • Créé il y a 5 minutes</div>
            
            <div style="margin-bottom: 15px;">
              Les accélérateurs thermiques ont été configurés pour optimiser le transfert de données entre les zones 1 et 2, avec un facteur d'accélération de x2.7. Cette configuration permet une meilleure fluidité dans le passage des informations de la mémoire immédiate vers la mémoire à court terme.
              <br><br>
              Les paramètres suivants ont été ajustés :
              <ul style="list-style-type: disc; margin-left: 20px; margin-top: 10px;">
                <li>Seuil de transfert : 42°C</li>
                <li>Vitesse de refroidissement : 0.5°C/minute</li>
                <li>Facteur de rétention : 85%</li>
                <li>Priorité des données : Dynamique</li>
              </ul>
              <br>
              Ces ajustements devraient améliorer les performances globales du système de mémoire thermique de 15 à 20%.
            </div>
            
            <div style="display: flex; gap: 10px;">
              <button class="action-button" style="background-color: rgba(29, 209, 161, 0.8);">
                <i class="bi bi-arrow-up"></i> Promouvoir
              </button>
              
              <button class="action-button" style="background-color: rgba(255, 107, 107, 0.8);">
                <i class="bi bi-arrow-down"></i> Dégrader
              </button>
              
              <button class="action-button" style="background-color: rgba(84, 160, 255, 0.8);">
                <i class="bi bi-pencil"></i> Éditer
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="card" style="height: 100%;">
          <div class="card-header">
            <i class="bi bi-thermometer-half card-icon"></i>
            <h3 class="card-title">État des Zones</h3>
          </div>
          
          <div style="padding: 15px;">
            <div class="thermal-zone">
              <span class="zone-name">Zone 1 (Instant)</span>
              <span class="zone-temp hot">48°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill hot" style="width: 90%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">90/100 unités</div>
            
            <div class="thermal-zone">
              <span class="zone-name">Zone 2 (Court terme)</span>
              <span class="zone-temp warm">35°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill warm" style="width: 70%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">105/150 unités</div>
            
            <div class="thermal-zone">
              <span class="zone-name">Zone 3 (Moyen)</span>
              <span class="zone-temp medium">20°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill medium" style="width: 50%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">100/200 unités</div>
            
            <div class="thermal-zone">
              <span class="zone-name">Zone 4 (Moyen terme)</span>
              <span class="zone-temp cool">10°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill cool" style="width: 30%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">75/250 unités</div>
            
            <div class="thermal-zone">
              <span class="zone-name">Zone 5 (Long terme)</span>
              <span class="zone-temp cool">5°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill cool" style="width: 20%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">60/300 unités</div>
            
            <div class="thermal-zone">
              <span class="zone-name">Zone 6 (Rêve)</span>
              <span class="zone-temp cool">3°C</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill cool" style="width: 10%;"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px; text-align: right;">25/250 unités</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Statistiques de mémoire -->
<div class="card" style="margin-top: 20px;">
  <div class="card-header">
    <i class="bi bi-graph-up card-icon"></i>
    <h2 class="card-title">Statistiques de Mémoire</h2>
  </div>
  
  <div style="padding: 20px;">
    <div class="grid-container">
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Capacité totale</div>
          <div class="stat-value">1250 unités</div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Utilisation actuelle</div>
          <div class="stat-value">42%</div>
        </div>
      </div>
      
      <div class="grid-item">
        <div class="stat-item">
          <div class="stat-label">Transferts par heure</div>
          <div class="stat-value">24</div>
        </div>
      </div>
    </div>
    
    <div style="margin-top: 20px;">
      <h3 style="margin-bottom: 15px;">Activité récente</h3>
      
      <div class="activity-item" style="display: flex; margin-bottom: 10px;">
        <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:01:15</div>
        <div style="flex-grow: 1;">
          <i class="bi bi-arrow-down" style="color: var(--temp-cool);"></i>
          Transfert de "Analyse des performances système" de Zone 2 vers Zone 3
        </div>
      </div>
      
      <div class="activity-item" style="display: flex; margin-bottom: 10px;">
        <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:00:45</div>
        <div style="flex-grow: 1;">
          <i class="bi bi-plus" style="color: var(--temp-hot);"></i>
          Création de "Configuration des accélérateurs thermiques" en Zone 1
        </div>
      </div>
      
      <div class="activity-item" style="display: flex; margin-bottom: 10px;">
        <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">08:00:30</div>
        <div style="flex-grow: 1;">
          <i class="bi bi-arrow-up" style="color: var(--temp-hot);"></i>
          Promotion de "Discussion sur les zones thermiques" de Zone 3 vers Zone 2
        </div>
      </div>
      
      <div class="activity-item" style="display: flex; margin-bottom: 10px;">
        <div style="width: 80px; font-size: 12px; color: var(--text-secondary);">07:55:20</div>
        <div style="flex-grow: 1;">
          <i class="bi bi-arrow-down" style="color: var(--temp-cool);"></i>
          Transfert automatique de 5 éléments de Zone 1 vers Zone 2 (refroidissement)
        </div>
      </div>
    </div>
  </div>
</div>
