#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3007';

async function testVoiceSystem() {
    console.log('🎤 Test du système vocal de Louna...\n');
    
    try {
        // 1. Vérifier l'état du système
        console.log('1. Vérification du système cognitif...');
        const status = await axios.get(`${BASE_URL}/api/cognitive/status`);
        
        if (status.data.success) {
            console.log('✅ Système cognitif actif');
            console.log(`   - Reconnaissance vocale: ${status.data.state.capabilities.speechRecognition ? 'OUI' : 'NON'}`);
            console.log(`   - Synthèse vocale: ${status.data.state.capabilities.speechSynthesis ? 'OUI' : 'NON'}`);
        }
        
        // 2. Test de synthèse vocale naturelle
        console.log('\n2. Test de synthèse vocale naturelle...');
        const testPhrases = [
            "Bonjour ! Je suis Louna avec une voix naturelle.",
            "Je peux maintenant vous reconnaître grâce à votre voix.",
            "Mon cerveau artificiel fonctionne parfaitement."
        ];
        
        for (const phrase of testPhrases) {
            console.log(`   🗣️ "${phrase}"`);
            const speakResult = await axios.post(`${BASE_URL}/api/cognitive/speak`, {
                text: phrase
            });
            
            if (speakResult.data.success) {
                console.log('   ✅ Synthèse réussie');
            } else {
                console.log('   ❌ Erreur synthèse');
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // 3. Test de reconnaissance vocale
        console.log('\n3. Test de reconnaissance vocale...');
        console.log('   🎤 Démarrage de l\'écoute...');
        
        const listenResult = await axios.post(`${BASE_URL}/api/cognitive/listen`);
        if (listenResult.data.success) {
            console.log('   ✅ Écoute démarrée');
            
            // Attendre un peu
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Vérifier le résultat
            const statusCheck = await axios.get(`${BASE_URL}/api/cognitive/status`);
            if (statusCheck.data.success && statusCheck.data.state.lastInput) {
                console.log(`   🗣️ Texte reconnu: "${statusCheck.data.state.lastInput}"`);
            }
        }
        
        // 4. Vérifier l'interface de monitoring
        console.log('\n4. Interface de monitoring du cerveau...');
        try {
            const monitorCheck = await axios.get(`${BASE_URL}/brain-monitor`);
            if (monitorCheck.status === 200) {
                console.log('   ✅ Interface accessible: http://localhost:3007/brain-monitor');
            }
        } catch (error) {
            console.log('   ❌ Interface non accessible');
        }
        
        console.log('\n🎉 Test terminé avec succès !');
        console.log('\n📋 Fonctionnalités disponibles:');
        console.log('✅ Reconnaissance vocale avec analyse utilisateur');
        console.log('✅ Synthèse vocale naturelle (macOS say)');
        console.log('✅ Interface de monitoring du cerveau en temps réel');
        console.log('✅ Profil vocal utilisateur avec apprentissage');
        
    } catch (error) {
        console.log(`❌ Erreur: ${error.message}`);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Vérifiez que le serveur est démarré sur le port 3007');
        }
    }
}

testVoiceSystem();
