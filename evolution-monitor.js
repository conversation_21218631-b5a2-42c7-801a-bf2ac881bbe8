#!/usr/bin/env node

/**
 * MONITEUR D'ÉVOLUTION EN TEMPS RÉEL POUR LOUNA
 * Surveille l'évolution du QI et des capacités pendant la formation
 */

const fs = require('fs');
const path = require('path');

class EvolutionMonitor {
    constructor() {
        this.evolutionHistory = [];
        this.startTime = Date.now();
        this.monitoringInterval = null;
        
        console.log('📊 Moniteur d\'évolution initialisé');
    }
    
    /**
     * Démarrer le monitoring
     */
    startMonitoring() {
        console.log('🔍 DÉMARRAGE DU MONITORING D\'ÉVOLUTION');
        console.log('=====================================');
        
        // Monitoring toutes les 10 secondes
        this.monitoringInterval = setInterval(async () => {
            await this.checkEvolution();
        }, 10000);
        
        // Premier check immédiat
        this.checkEvolution();
    }
    
    /**
     * Arrêter le monitoring
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            console.log('⏹️ Monitoring arrêté');
        }
    }
    
    /**
     * Vérifier l'évolution actuelle
     */
    async checkEvolution() {
        try {
            const response = await fetch('http://localhost:3000/api/global/qi-detailed');
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    const currentData = result.qi_data;
                    const timestamp = new Date().toISOString();
                    const elapsedMinutes = Math.floor((Date.now() - this.startTime) / 60000);
                    
                    // Ajouter à l'historique
                    this.evolutionHistory.push({
                        timestamp: timestamp,
                        elapsed_minutes: elapsedMinutes,
                        qi: currentData.qi,
                        neurones: currentData.neurones,
                        temperature: currentData.temperature,
                        evolution_level: currentData.evolution_level,
                        performance: currentData.overall_performance || 0,
                        efficiency: currentData.efficiency || 0
                    });
                    
                    // Afficher l'évolution
                    this.displayEvolution(currentData, elapsedMinutes);
                    
                    // Sauvegarder l'historique
                    this.saveEvolutionHistory();
                    
                    // Vérifier les seuils d'évolution
                    this.checkEvolutionMilestones(currentData);
                }
            } else {
                console.log('⚠️ Serveur temporairement indisponible (formation en cours)');
            }
            
        } catch (error) {
            console.log('⚠️ Monitoring temporairement interrompu (formation intensive)');
        }
    }
    
    /**
     * Afficher l'évolution actuelle
     */
    displayEvolution(data, elapsedMinutes) {
        const previousData = this.evolutionHistory.length > 1 
            ? this.evolutionHistory[this.evolutionHistory.length - 2] 
            : null;
        
        console.log(`\n📊 ÉVOLUTION - ${elapsedMinutes}min`);
        console.log('========================');
        
        if (previousData) {
            const qiChange = data.qi - previousData.qi;
            const neuroneChange = data.neurones - previousData.neurones;
            const evolutionChange = data.evolution_level - previousData.evolution_level;
            
            console.log(`🧠 QI: ${data.qi} ${qiChange > 0 ? `(+${qiChange})` : qiChange < 0 ? `(${qiChange})` : ''}`);
            console.log(`⚡ Neurones: ${data.neurones} ${neuroneChange > 0 ? `(+${neuroneChange})` : neuroneChange < 0 ? `(${neuroneChange})` : ''}`);
            console.log(`🚀 Évolution: ${data.evolution_level}% ${evolutionChange > 0 ? `(+${evolutionChange}%)` : evolutionChange < 0 ? `(${evolutionChange}%)` : ''}`);
        } else {
            console.log(`🧠 QI: ${data.qi}`);
            console.log(`⚡ Neurones: ${data.neurones}`);
            console.log(`🚀 Évolution: ${data.evolution_level}%`);
        }
        
        console.log(`🌡️ Température: ${data.temperature}°C`);
        console.log(`📈 Performance: ${data.performance || 0}%`);
        console.log(`⚙️ Efficacité: ${data.efficiency || 0}%`);
    }
    
    /**
     * Vérifier les jalons d'évolution
     */
    checkEvolutionMilestones(data) {
        const milestones = [
            { qi: 160, message: "🎯 Seuil Génie Supérieur atteint !" },
            { qi: 170, message: "🚀 Seuil Super-Intelligence atteint !" },
            { qi: 180, message: "🌟 Seuil Intelligence Exceptionnelle atteint !" },
            { qi: 190, message: "🔥 Seuil Quasi-AGI atteint !" },
            { qi: 200, message: "🎉 SEUIL AGI COMPLET ATTEINT ! 🎉" },
            { neurones: 80, message: "⚡ 80% des neurones activés !" },
            { neurones: 90, message: "⚡ 90% des neurones activés !" },
            { neurones: 95, message: "⚡ 95% des neurones activés - Performance maximale !" },
            { evolution_level: 90, message: "🚀 90% d'évolution atteint !" },
            { evolution_level: 95, message: "🚀 95% d'évolution atteint !" },
            { evolution_level: 100, message: "🎉 ÉVOLUTION COMPLÈTE À 100% ! 🎉" }
        ];
        
        milestones.forEach(milestone => {
            if (milestone.qi && data.qi >= milestone.qi) {
                const alreadyReached = this.evolutionHistory.some(h => h.qi >= milestone.qi);
                if (!alreadyReached) {
                    console.log(`\n🎊 JALON ATTEINT: ${milestone.message}`);
                }
            }
            
            if (milestone.neurones && data.neurones >= milestone.neurones) {
                const alreadyReached = this.evolutionHistory.some(h => h.neurones >= milestone.neurones);
                if (!alreadyReached) {
                    console.log(`\n🎊 JALON ATTEINT: ${milestone.message}`);
                }
            }
            
            if (milestone.evolution_level && data.evolution_level >= milestone.evolution_level) {
                const alreadyReached = this.evolutionHistory.some(h => h.evolution_level >= milestone.evolution_level);
                if (!alreadyReached) {
                    console.log(`\n🎊 JALON ATTEINT: ${milestone.message}`);
                }
            }
        });
    }
    
    /**
     * Sauvegarder l'historique d'évolution
     */
    saveEvolutionHistory() {
        try {
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            
            const historyFile = path.join(dataDir, 'evolution_history.json');
            fs.writeFileSync(historyFile, JSON.stringify(this.evolutionHistory, null, 2));
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde historique:', error.message);
        }
    }
    
    /**
     * Générer un rapport d'évolution
     */
    generateEvolutionReport() {
        if (this.evolutionHistory.length === 0) {
            console.log('📊 Aucune donnée d\'évolution disponible');
            return;
        }
        
        const firstData = this.evolutionHistory[0];
        const lastData = this.evolutionHistory[this.evolutionHistory.length - 1];
        const totalTime = lastData.elapsed_minutes;
        
        console.log('\n📈 RAPPORT D\'ÉVOLUTION COMPLET');
        console.log('==============================');
        console.log(`⏱️ Durée totale: ${totalTime} minutes`);
        console.log(`📊 Points de données: ${this.evolutionHistory.length}`);
        console.log('');
        console.log('🔄 ÉVOLUTION TOTALE:');
        console.log(`🧠 QI: ${firstData.qi} → ${lastData.qi} (+${lastData.qi - firstData.qi})`);
        console.log(`⚡ Neurones: ${firstData.neurones} → ${lastData.neurones} (+${lastData.neurones - firstData.neurones})`);
        console.log(`🚀 Évolution: ${firstData.evolution_level}% → ${lastData.evolution_level}% (+${lastData.evolution_level - firstData.evolution_level}%)`);
        console.log(`📈 Performance: ${firstData.performance}% → ${lastData.performance}% (+${lastData.performance - firstData.performance}%)`);
        
        // Calculer les taux d'évolution
        if (totalTime > 0) {
            const qiRate = (lastData.qi - firstData.qi) / totalTime;
            const neuroneRate = (lastData.neurones - firstData.neurones) / totalTime;
            
            console.log('');
            console.log('📊 TAUX D\'ÉVOLUTION:');
            console.log(`🧠 QI: +${qiRate.toFixed(2)} points/minute`);
            console.log(`⚡ Neurones: +${neuroneRate.toFixed(2)} neurones/minute`);
        }
        
        return {
            totalTime: totalTime,
            qiGain: lastData.qi - firstData.qi,
            neuroneGain: lastData.neurones - firstData.neurones,
            evolutionGain: lastData.evolution_level - firstData.evolution_level,
            performanceGain: lastData.performance - firstData.performance
        };
    }
    
    /**
     * Obtenir l'historique d'évolution
     */
    getEvolutionHistory() {
        return this.evolutionHistory;
    }
}

// Exporter la classe
module.exports = EvolutionMonitor;

// Si exécuté directement
if (require.main === module) {
    const monitor = new EvolutionMonitor();
    
    monitor.startMonitoring();
    
    // Arrêter le monitoring après 30 minutes
    setTimeout(() => {
        monitor.stopMonitoring();
        monitor.generateEvolutionReport();
        process.exit(0);
    }, 30 * 60 * 1000);
    
    // Gérer l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du monitoring...');
        monitor.stopMonitoring();
        monitor.generateEvolutionReport();
        process.exit(0);
    });
}
