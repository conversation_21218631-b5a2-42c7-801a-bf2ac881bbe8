/**
 * Script pour créer une icône ICNS à partir d'une icône PNG
 */

const fs = require('fs');
const path = require('path');
const png2icons = require('png2icons');

// Chemin vers l'icône PNG
const pngPath = path.join(__dirname, 'public', 'img', 'louna-icon.png');

// Chemin vers l'icône ICNS
const icnsPath = path.join(__dirname, 'public', 'img', 'louna-icon.icns');

// Vérifier si l'icône PNG existe
if (!fs.existsSync(pngPath)) {
  console.error(`L'icône PNG n'existe pas : ${pngPath}`);
  process.exit(1);
}

// Lire l'icône PNG
const pngBuffer = fs.readFileSync(pngPath);

// Convertir l'icône PNG en ICNS
const icnsBuffer = png2icons.createICNS(png<PERSON><PERSON><PERSON>, png2icons.BILINEAR, 0);

if (icnsBuffer) {
  // Écrire l'icône ICNS
  fs.writeFileSync(icnsPath, icnsBuffer);
  console.log(`Icône ICNS créée : ${icnsPath}`);
} else {
  console.error('Erreur lors de la conversion en ICNS');
}
