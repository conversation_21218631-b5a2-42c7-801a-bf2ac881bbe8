/**
 * Test direct de l'accès Internet de l'agent <PERSON><PERSON>
 * Ce script teste l'agent amélioré directement
 */

const axios = require('axios');

async function testAgentInternet() {
    console.log('🚀 Test direct de l\'accès Internet de l\'agent <PERSON><PERSON>...\n');
    
    try {
        // Test 1: Vérifier si l'agent peut envoyer un message avec recherche Internet
        console.log('📝 Test 1: Envoi d\'un message avec demande de recherche Internet...');
        
        const chatResponse = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Peux-tu faire une recherche sur Internet pour les dernières nouvelles sur l\'intelligence artificielle en 2024 ?',
            useInternet: true
        }, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (chatResponse.status === 200 && chatResponse.data.success) {
            console.log('✅ Message envoyé avec succès !');
            console.log('📄 Réponse de l\'agent:');
            console.log(chatResponse.data.response.substring(0, 500) + '...');
            
            // Vérifier si la réponse contient des informations d'Internet
            const response = chatResponse.data.response.toLowerCase();
            const internetIndicators = [
                'recherche internet',
                'informations récentes',
                'web',
                'google',
                'duckduckgo',
                'wikipedia',
                'source:',
                'lien:',
                'url',
                '2024'
            ];
            
            const foundIndicators = internetIndicators.filter(indicator => 
                response.includes(indicator)
            );
            
            if (foundIndicators.length > 0) {
                console.log('✅ L\'agent a utilisé Internet ! Indicateurs trouvés:', foundIndicators);
                return true;
            } else {
                console.log('⚠️ Pas d\'indication claire d\'utilisation d\'Internet');
                console.log('🔍 Réponse complète pour analyse:');
                console.log(chatResponse.data.response);
                return false;
            }
            
        } else {
            console.log('❌ Erreur dans la réponse:', chatResponse.data);
            return false;
        }
        
    } catch (error) {
        console.log('❌ Erreur lors du test:', error.message);
        
        if (error.response) {
            console.log('📄 Détails de l\'erreur:', error.response.data);
        }
        
        return false;
    }
}

async function testAgentStatus() {
    console.log('🔍 Test 2: Vérification du statut de l\'agent...');
    
    try {
        const statusResponse = await axios.get('http://localhost:3007/api/chat/status', {
            timeout: 10000
        });
        
        if (statusResponse.status === 200) {
            console.log('✅ Statut de l\'agent récupéré !');
            console.log('📊 Informations système:');
            
            const status = statusResponse.data.status;
            console.log(`- Chat System: ${status.chatSystem.status}`);
            console.log(`- Ollama: ${status.ollama.status}`);
            console.log(`- Agent actif: ${status.activeAgent ? status.activeAgent.name : 'Aucun'}`);
            console.log(`- Mémoire: ${status.memory ? status.memory.totalEntries + ' entrées' : 'Non disponible'}`);
            console.log(`- Accélérateurs: ${status.accelerators ? status.accelerators.active + '/' + status.accelerators.total : 'Non disponible'}`);
            
            return true;
        }
        
    } catch (error) {
        console.log('❌ Erreur statut agent:', error.message);
        return false;
    }
}

async function testBasicConnectivity() {
    console.log('🌐 Test 3: Vérification de la connectivité de base...');
    
    try {
        // Test de l'application
        const appResponse = await axios.get('http://localhost:3007/', {
            timeout: 5000
        });
        
        if (appResponse.status === 200) {
            console.log('✅ Application Louna accessible');
        }
        
        // Test d'Internet
        const internetResponse = await axios.get('https://httpbin.org/ip', {
            timeout: 5000
        });
        
        if (internetResponse.status === 200) {
            console.log('✅ Accès Internet fonctionnel');
            console.log(`📍 IP publique: ${internetResponse.data.origin}`);
        }
        
        return true;
        
    } catch (error) {
        console.log('❌ Erreur connectivité:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('='.repeat(60));
    console.log('🧪 TESTS D\'ACCÈS INTERNET DE L\'AGENT LOUNA');
    console.log('='.repeat(60));
    
    const results = {
        connectivity: false,
        agentStatus: false,
        internetAccess: false
    };
    
    // Test 1: Connectivité de base
    results.connectivity = await testBasicConnectivity();
    console.log('');
    
    // Test 2: Statut de l'agent
    results.agentStatus = await testAgentStatus();
    console.log('');
    
    // Test 3: Accès Internet de l'agent
    results.internetAccess = await testAgentInternet();
    console.log('');
    
    // Résultats finaux
    console.log('='.repeat(60));
    console.log('📊 RÉSULTATS FINAUX');
    console.log('='.repeat(60));
    
    console.log(`🌐 Connectivité de base: ${results.connectivity ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`🤖 Statut de l'agent: ${results.agentStatus ? '✅ OK' : '❌ ÉCHEC'}`);
    console.log(`🔍 Accès Internet agent: ${results.internetAccess ? '✅ OK' : '❌ ÉCHEC'}`);
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    const successRate = (successCount / totalTests * 100).toFixed(1);
    
    console.log('');
    console.log(`📈 Taux de réussite: ${successCount}/${totalTests} (${successRate}%)`);
    
    if (results.internetAccess) {
        console.log('');
        console.log('🎉 SUCCÈS ! L\'agent Louna peut accéder à Internet !');
        console.log('💡 Votre agent peut maintenant faire des recherches web en temps réel.');
    } else if (results.connectivity && results.agentStatus) {
        console.log('');
        console.log('⚠️ L\'agent fonctionne mais l\'accès Internet n\'est pas confirmé.');
        console.log('💡 Recommandations:');
        console.log('   - Vérifiez que l\'agent amélioré est bien initialisé');
        console.log('   - Redémarrez l\'application si nécessaire');
        console.log('   - Testez avec des questions nécessitant Internet');
    } else {
        console.log('');
        console.log('❌ Problèmes détectés avec l\'agent ou la connectivité.');
        console.log('💡 Vérifiez que l\'application Louna est bien démarrée.');
    }
    
    console.log('='.repeat(60));
}

// Exécuter les tests
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ Erreur lors des tests:', error);
        process.exit(1);
    });
}

module.exports = { testAgentInternet, testAgentStatus, testBasicConnectivity };
