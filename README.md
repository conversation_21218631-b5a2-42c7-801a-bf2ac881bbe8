# Louna - Agent à Mémoire Thermique

Louna est un agent intelligent doté d'une mémoire thermique et d'accélérateurs Kyber pour optimiser ses performances. Cette application fournit une interface utilisateur pour visualiser et interagir avec la mémoire thermique et les accélérateurs.

## Fonctionnalités

### Mémoire Thermique
- **Zones de mémoire** : La mémoire est divisée en 6 zones avec des températures différentes
  - Mémoire Instantanée (la plus chaude)
  - Mémoire à Court Terme
  - Mémoire de Travail
  - Mémoire à Moyen Terme
  - Mémoire à Long Terme
  - Mémoire des Rêves (la plus froide)
- **Cycle de mémoire** : Les informations se déplacent entre les zones en fonction de leur importance et de leur fraîcheur
- **Génération de rêves** : Création de nouvelles connexions entre les informations stockées

### Accélérateurs Kyber
- **Accélérateur Réflexif** : Améliore la vitesse de traitement des informations
- **Accélérateur Thermique** : Optimise les transferts entre zones de mémoire thermique
- **Connecteur Thermique** : Facilite les connexions entre informations dans différentes zones

## Installation

1. Clonez ce dépôt
2. Installez les dépendances :
   ```
   npm install
   ```
3. Démarrez l'application :
   ```
   npm start
   ```
4. Ouvrez votre navigateur à l'adresse : `http://localhost:3000`

## Structure du projet

```
louna-thermal-memory/
├── data/                  # Dossier de stockage des données
│   ├── memory/            # Données de la mémoire thermique
│   └── accelerators/      # Données des accélérateurs Kyber
├── public/                # Fichiers statiques
│   ├── css/               # Styles CSS
│   ├── js/                # Scripts JavaScript
│   ├── thermal-memory.html # Interface de la mémoire thermique
│   └── kyber-dashboard.html # Interface des accélérateurs Kyber
├── routes/                # Routes API
│   └── thermal-api.js     # API pour la mémoire thermique et les accélérateurs
├── thermal-memory-complete.js # Module de mémoire thermique
├── kyber-accelerators.js  # Module d'accélérateurs Kyber
├── server.js              # Point d'entrée de l'application
└── package.json           # Configuration du projet
```

## API

### Mémoire Thermique
- `GET /api/thermal/memory/stats` - Récupérer les statistiques de la mémoire
- `GET /api/thermal/memory/zone/:zone` - Récupérer les entrées d'une zone spécifique
- `POST /api/thermal/memory/add` - Ajouter une entrée à la mémoire
- `GET /api/thermal/memory/entry/:id` - Récupérer une entrée spécifique
- `GET /api/thermal/memory/context` - Récupérer les entrées récentes pour un contexte donné
- `POST /api/thermal/memory/dream` - Générer un rêve
- `POST /api/thermal/memory/cycle` - Effectuer un cycle de mémoire
- `POST /api/thermal/memory/reset` - Réinitialiser la mémoire

### Accélérateurs Kyber
- `GET /api/thermal/accelerators/stats` - Récupérer les statistiques des accélérateurs
- `POST /api/thermal/accelerators/toggle/:id` - Activer ou désactiver un accélérateur
- `POST /api/thermal/accelerators/boost/:id` - Ajuster le facteur de boost d'un accélérateur
- `POST /api/thermal/accelerators/reset` - Réinitialiser les accélérateurs
- `POST /api/thermal/accelerators/optimize` - Optimiser les accélérateurs
- `POST /api/thermal/accelerators/apply` - Appliquer un boost à une valeur

## Utilisation

### Interface de la Mémoire Thermique
- Visualisez les différentes zones de mémoire et leur contenu
- Ajoutez de nouvelles entrées à la mémoire
- Effectuez des cycles de mémoire pour simuler le passage du temps
- Générez des rêves pour créer de nouvelles connexions

### Tableau de Bord des Accélérateurs Kyber
- Activez ou désactivez les accélérateurs
- Ajustez les facteurs de boost pour optimiser les performances
- Surveillez la température et la stabilité des accélérateurs
- Réinitialisez ou optimisez automatiquement les accélérateurs

## Développement

Pour lancer l'application en mode développement avec rechargement automatique :
```
npm run dev
```

## Licence

ISC
